# XianshiApp

### 产品需求
项目名称：闲时有伴 --- 陪玩APP项目
1. 用户采用微信登录或者手机验证码的形式进行登录或注册行为（，参考以下常见的做法）
2. 用户分为普通用户（发单方）、达人（接单方），默认角色为普通用户、普通用户可认证为达人，认证要求：1.实名认证 2.个人资料完善度>=50% 3.用户满足前2个后提交申请，由后台进行审核，核通过后，该账号可任意切换角色。每次登录记住上次角色
3. 会员相关信息：头像、昵称、系统ID、当前角色、人气、粉丝、关注、年龄、身高、星座、体重、简介、视频、照片集合、风格标签集合、身份标签集、金币余额
4. 相关功能：浏览/收藏/签到历史、用户之间消息聊天；
5. 面板：默认页（logo+简介+开启旅程）->
   登录页（手机号验证码登录、本机号码一键登录）->
   首页（banner+订单列表+陪玩类目筛选）->
   达人库（展示达人的信息，用户可点击达人主页选择约单，约旦流程与上述一致，只是去掉了选择达人模块）->
   消息（系统消息（系统通知、订单通知）、~~好友消息~~）->
   我的(展示当前角色信息、金币、主页、收藏、签到、发单管理、约单管理、联系管理、角色切换等按钮) 达人角色额外看到2个按钮（接单项目设置，可选择接哪些分类、城市设置）

订单操作流程：
5. 普通角色发单流程： 
   字段要求：
   3.1 根据地图选择一个地点、选择时间段后获得陪玩时长、选择陪玩项目（分为休闲娱乐（KTV、桌球）、运动（健身、篮球））、输入小时费用、输入性别要求、参与人数、描述；
   3.2 点击提交进入待审核（此阶段提交需要支付金币，金币跟陪玩项目相关，在后台配置）  
   3.3 审核通过后变为报名中 （每有报名，发单人会收到系统通知，开始选择达人（调整为：如果人数不足，则提醒用户 是否继续选择开单。~~这里的逻辑是：如需求人数是2人，报名了3人，需要选择2名进行开单~~））
   3.4 当报名人数达到要求转为待选择达人
   3.5 选择完达人后订单进入进行中 （达人跟用户都会收到系统通知）
4. 达人角色接单流程：
   4.1 达人选择已出发 （用户会收到系统订单通知）
   4.2 达人选择 已到达 （用户会收到系统订单通知）
5. 由发单方选中某一个达人开始订单，此时子订单状态改为服务中，从主表读到服务时长开始倒计时，并显示加时按钮。
6. 由达人端点击“服务完成”（此时达人端会在平台收到此次订单的金额 ！！！？？？）当排除所有的已取消状态时，如果所有子订单都为服务完成，则主订单结束变为已完成。
7. 用户端选择确认“确认完成”，后选择评价，主要字段包括：星级、标签选择、评价内容；（由上所述，评价为1对多，关联子订单那表）

0. 订单列表展示：
   1. 总订单状态：待发布（草稿仅我可见）、待审核（支付金币、后台审核）、发布中（达人报名）、待选择达人（选择达人开单）、进行中、已完成 【已取消】

[//]: # (      1.1 表结构： order主表 ： order_enroll_detail子表 )

[//]: # (            主订单直至“发布中”都是一张order表)

[//]: # (            达人报名 每次插入一张order_enroll_detail表关联主表  （有2字段，1个是“是否被选择” 2.子订单的状态）)

[//]: # (            子订单状态：达人已报名、被选中、已出发、已到达、服务中（开始时间、从主表读到时长计算结束时间、前台展示倒计时）、服务完成、已评价)
                
1.用户发单
 1.1 地点
 1.2 开始结束时间
 1.3 选择陪玩项目
 1.4 小时费
 1.5 性别要求
 1.6 参与人数
 1.7 描述
  + 待审核 审核通过、审核未通过
 1.8 点击提交进入预发布订单
 1.9 发布需要指定金币（金币跟陪玩项目相关，在后台可自由配置）



### 项目结构
1. xianshi-admin 后台管理系统的API
2. xianshi-app 前台的API
3. xianshi-business 业务逻辑公共模块
4. xianshi-vue 后台的vue前端
5. xianshi_app_uniapp_07012 是前台uniapp前端
#### 安装教程

MySQL
主机:http://43.142.2.35/
用户名:xianshi
密码:CmBs454APM


面板地址:http://43.142.2.35:8888/5d4025aa
用户名:wiafsws0
密码:b46a9889


JenKins:http://43.142.2.35:14808/
账号:admin
密码:abcd@1234

#### 使用说明
