-- 为b_order表添加order_type字段
ALTER TABLE `b_order` ADD COLUMN `order_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单类型：DISPATCH-派单模式，APPOINTMENT-约单模式' AFTER `target_id`;

-- 为现有数据设置默认值
-- 如果target_id为空或null，设置为DISPATCH（派单模式）
-- 如果target_id有值，设置为APPOINTMENT（约单模式）
UPDATE `b_order` SET `order_type` = 'DISPATCH' WHERE `target_id` IS NULL OR `target_id` = '';
UPDATE `b_order` SET `order_type` = 'APPOINTMENT' WHERE `target_id` IS NOT NULL AND `target_id` != '';