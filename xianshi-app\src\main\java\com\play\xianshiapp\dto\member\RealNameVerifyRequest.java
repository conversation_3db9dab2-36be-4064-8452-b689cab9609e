package com.play.xianshiapp.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 实名认证请求DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@ApiModel(value = "实名认证请求")
public class RealNameVerifyRequest {

    @ApiModelProperty(value = "真实姓名", required = true)
    @NotBlank(message = "请输入真实姓名")
    private String realName;

    @ApiModelProperty(value = "身份证号码", required = true)
    @NotBlank(message = "请输入身份证号码")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号码格式不正确")
    private String idCardNumber;

    @ApiModelProperty(value = "手机号码", required = true)
    @NotBlank(message = "请输入手机号码")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String phone;
}