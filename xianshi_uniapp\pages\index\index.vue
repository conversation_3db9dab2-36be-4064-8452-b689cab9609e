<template>
  <view class="container">
    
    <video
      class="bg-video"
      src="../../static/videos/login-bg.mp4"
      object-fit="cover"
      autoplay
      loop
      muted
      :controls="false"
      show-center-play-btn="false"
      show-fullscreen-btn="false"
      show-play-btn="false"
      enable-progress-gesture="false"
      disable-progress="true"
    />
    <view class="content">
      <view class="title">
        <view class="logo">
          <text class="logo-text">FREE TIME</text>
        </view>
        <text class="subtitle">闲时有伴</text>
      </view>
      <view class="slogan">
        <text class="slogan-cn">玩咖聚集，等你召唤</text>
        <text class="slogan-en">PLAY TOGETHER | MORE FUN</text>
      </view>
      <button class="start-btn" @click="goToLogin">开始体验</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: "Hello",
    };
  },
  onLoad() {},
  methods: {
    goToLogin() {
      uni.navigateTo({
        url: "/pages/login/login",
      });
    },
  },
};
</script>

<style>
.container {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.bg-video {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  object-fit: cover;
}

.content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 100rpx 40rpx;
  box-sizing: border-box;
}

.title {
  text-align: center;
  color: #fff;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 200rpx;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.logo-text {
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1.3;
  letter-spacing: 4rpx;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 32rpx;
  margin-top: 10rpx;
}

.slogan {
  text-align: center;
  color: #fff;
  margin-top: auto;
  margin-bottom: 100rpx;
}

.slogan-cn {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.slogan-en {
  font-size: 32rpx;
  display: block;
}

.start-btn {
  width: 670rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  margin-bottom: 40rpx;
}
</style>
