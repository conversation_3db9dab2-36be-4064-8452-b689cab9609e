package com.play.xianshibusiness.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会员资料更新DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "会员资料更新DTO")
public class MemberUpdateDTO {
    
    @ApiModelProperty(value = "昵称")
    private String nickname;
    
    @ApiModelProperty(value = "头像")
    private String avatar;
    
    @ApiModelProperty(value = "性别：1-男，0-女")
    private Integer gender;
    
    @ApiModelProperty(value = "年龄")
    private Integer age;
    
    @ApiModelProperty(value = "星座")
    private String constellation;
    
    @ApiModelProperty(value = "身高")
    private String height;

    @ApiModelProperty(value = "体重")
    private String weight;
    
    @ApiModelProperty(value = "简介")
    private String context;
    
    @ApiModelProperty(value = "视频")
    private String video;
    
    @ApiModelProperty(value = "照片集合（JSON字符串）")
    private String images;
    
    @ApiModelProperty(value = "身份字典IDS（逗号分隔）")
    private String identify;
    
    @ApiModelProperty(value = "风格字典IDS（逗号分隔）")
    private String styleIds;
}