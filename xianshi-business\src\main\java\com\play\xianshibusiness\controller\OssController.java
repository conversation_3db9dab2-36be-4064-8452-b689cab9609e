package com.play.xianshibusiness.controller;

import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * OSS文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/oss")
@Api(tags = "OSS文件上传")
public class OssController {
    
    @Autowired
    private OssService ossService;
    
    @Autowired
    private com.play.xianshibusiness.service.impl.OssServiceImpl ossServiceImpl;
    
    @PostMapping("/upload")
    @ApiOperation("上传文件")
    public Result<String> uploadFile(
            @ApiParam(value = "文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "存储文件夹", required = true) @RequestParam("folder") String folder) {
        try {
            String fileUrl = ossService.uploadFile(file, folder);
            return ResultUtils.success(fileUrl);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            return ResultUtils.error(500, "文件上传失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/upload/image")
    @ApiOperation("上传图片")
    public Result<String> uploadImage(
            @ApiParam(value = "图片文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "存储文件夹", required = true) @RequestParam("folder") String folder) {
        try {
            String imageUrl = ossService.uploadImage(file, folder);
            return ResultUtils.success(imageUrl);
        } catch (Exception e) {
            log.error("图片上传失败: {}", e.getMessage(), e);
            return ResultUtils.error(500, "图片上传失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/delete")
    @ApiOperation("删除文件")
    public Result<Boolean> deleteFile(
            @ApiParam(value = "文件URL", required = true) @RequestParam("fileUrl") String fileUrl) {
        try {
            boolean success = ossService.deleteFile(fileUrl);
            if (success) {
                return ResultUtils.success(true);
            } else { 
                return ResultUtils.error(500, "文件删除失败");
            }
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            return ResultUtils.error(500, "文件删除失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/test")
    @ApiOperation("测试OSS连接")
    public Result<Boolean> testOssConnection() {
        try {
            boolean success = ossServiceImpl.testOssConnection();
            if (success) {
                return ResultUtils.success(true);
            } else {
                return ResultUtils.error(500, "OSS连接测试失败");
            }
        } catch (Exception e) {
            log.error("OSS连接测试失败: {}", e.getMessage(), e);
            return ResultUtils.error(500, "OSS连接测试失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/sts")
    @ApiOperation("获取OSS STS临时凭证")
    public Result<Map<String, String>> getStsCredential() {
        try {
            Map<String, String> credentials = ossServiceImpl.generateStsCredentials();
            return ResultUtils.success(credentials);
        } catch (Exception e) {
            log.error("获取STS临时凭证失败: {}", e.getMessage(), e);
            return ResultUtils.error(500, "获取STS临时凭证失败: " + e.getMessage());
        }
    }
} 