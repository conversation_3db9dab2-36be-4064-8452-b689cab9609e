package com.play.xianshibusiness.enums;

import lombok.Getter;

/**
 * 消息类型枚举
 */
@Getter
public enum MessageTypeEnum {

    /**
     * 系统消息
     */
    SYSTEM("SYSTEM", "系统消息"),
    
    /**
     * 订单消息
     */
    ORDER("ORDER", "订单消息"),
    
    /**
     * 所有人可见
     */
    ALL("ALL", "所有人可见");
    
    /**
     * 编码
     */
    private final String code;
    
    /**
     * 描述
     */
    private final String desc;
    
    MessageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
} 