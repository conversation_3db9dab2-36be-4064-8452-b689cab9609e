package com.play.xianshibusiness.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 会员收藏DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("会员收藏DTO")
public class MemberCollectDTO {
    
    @ApiModelProperty("收藏ID")
    private String id;
    
    @ApiModelProperty("会员ID")
    private String memberId;
    
    @ApiModelProperty("目标ID（会员ID、订单ID等）")
    private String targetId;
    
    @ApiModelProperty("收藏类型")
    private String type;
    
    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    
    // 扩展字段，用于显示
    @ApiModelProperty("目标名称")
    private String targetName;
    
    @ApiModelProperty("目标头像")
    private String targetAvatar;
    
    @ApiModelProperty("其他信息（JSON格式，根据类型不同而不同）")
    private String extraInfo;
} 