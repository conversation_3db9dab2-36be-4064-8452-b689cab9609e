package com.play.xianshiapp.controller.member;

import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.member.CMemberDto;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.CMemberFansAttentionService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 会员关注/粉丝控制器
 */
@RestController
@RequestMapping("/app/follow")
@Api(tags = "会员关注/粉丝-API")
public class MemberFollowController {

    @Resource
    private CMemberFansAttentionService memberFansAttentionService;
    
    /**
     * 关注会员
     */
    @ApiOperation(value = "关注会员")
    @PostMapping("/{targetId}")
    @RequireLogin
    public Result<Boolean> followMember(
            @ApiParam(value = "目标会员ID", required = true) 
            @PathVariable("targetId") String targetId) {
        String currentMemberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberFansAttentionService.followMember(currentMemberId, targetId));
    }
    
    /**
     * 取消关注会员
     */
    @ApiOperation(value = "取消关注会员")
    @DeleteMapping("/{targetId}")
    @RequireLogin
    public Result<Boolean> unfollowMember(
            @ApiParam(value = "目标会员ID", required = true) 
            @PathVariable("targetId") String targetId) {
        String currentMemberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberFansAttentionService.unfollowMember(currentMemberId, targetId));
    }
    
    /**
     * 判断是否关注
     */
    @ApiOperation(value = "判断是否关注")
    @GetMapping("/check/{targetId}")
    @RequireLogin
    public Result<Boolean> isFollowing(
            @ApiParam(value = "目标会员ID", required = true) 
            @PathVariable("targetId") String targetId) {
        String currentMemberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberFansAttentionService.isFollowing(currentMemberId, targetId));
    }
    
    /**
     * 获取用户的关注列表
     */
    @ApiOperation(value = "获取用户的关注列表")
    @GetMapping("/list")
    @RequireLogin
    public Result<List<CMemberDto>> getFollowingList(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                                     @RequestParam(value = "size", defaultValue = "10") Integer size) {
        String currentMemberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberFansAttentionService.getFollowingList(currentMemberId, page, size));
    }
    
    /**
     * 获取用户的粉丝列表
     */
    @ApiOperation(value = "获取用户的粉丝列表")
    @GetMapping("/fans")
    @RequireLogin
    public Result<List<CMemberDto>> getFansList(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                                @RequestParam(value = "size", defaultValue = "10") Integer size) {
        String currentMemberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberFansAttentionService.getFansList(currentMemberId, page, size));
    }
    
    /**
     * 获取当前用户的关注数和粉丝数
     */
    @ApiOperation(value = "获取当前用户的关注数和粉丝数")
    @GetMapping("/counts")
    @RequireLogin
    public Result<Map<String, Object>> getCounts() {
        String currentMemberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberFansAttentionService.getFollowCounts(currentMemberId));
    }
} 