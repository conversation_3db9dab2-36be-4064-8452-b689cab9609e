<template>
  <!-- 达人选择页面 -->
  <view class="container">
    <!-- 加载中显示 -->
    <view class="loading-container" v-if="isLoading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>

    <!-- 订单信息 -->
    <view class="order-info" v-if="!isLoading && order">
      <view class="order-title">
        <text class="order-id">订单编号: {{ order.orderNo }}</text>
        <text :class="['order-status', order.status]" style="width: 100rpx;">{{
          order.statusText || "已发布"
        }}</text>
      </view>
      <view class="order-detail">
        <view class="item">
          <text class="label">活动类型</text>
          <text class="value activity-type"
            >{{ order.service }} > {{ order.serviceSubType }}</text
          >
        </view>
        <view class="item">
          <text class="label">活动时间</text>
          <text class="value activity-time">{{ formatOrderTime(order.date, order.time) }}</text>
        </view>
        <view class="item">
          <text class="label">活动时长</text>
          <text class="value activity-duration">{{ order.duration }}小时</text>
        </view>
        <view class="item">
          <text class="label">活动地点</text>
          <text class="value activity-location">{{ order.location || order.address || '未知地点' }}</text>
        </view>
      </view>
    </view>

    <!-- 选择进度条 - 简约版本 -->
<!--    <view class="progress-bar-simple" v-if="isOrderPublisher && isMultiSelect">-->
<!--      <view class="progress-info">-->
<!--        <text class="progress-text">已选择 {{ selectedExpertIds.length }}/{{ maxSelectCount }} 人</text>-->
<!--      </view>-->
<!--      <view class="progress-track-simple">-->
<!--        <view class="progress-fill-simple" :style="{ width: (selectedExpertIds.length / maxSelectCount * 100) + '%' }"></view>-->
<!--      </view>-->
<!--    </view>-->

    <!-- 申请人列表 -->
    <view class="section-title" v-if="!isLoading && applicants.length > 0">
      <text>已报名达人 ({{ applicants.length }}人)</text>
    </view>

    <view class="applicants-list" v-if="!isLoading && applicants.length > 0">
      <view
        :class="[
          'applicant-item-simple',
          { 'selected': isExpertSelected(item.id) },
        ]"
        v-for="item in applicants"
        :key="item.id"
      >
        <!-- 达人基本信息 - 简化版本 -->
        <view class="expert-info-simple" @tap="viewExpertProfile" :data-id="item.id">
          <image
            class="expert-avatar-simple"
            :src="item?.avatar || '/images/default-avatar.png'"
            mode="aspectFill"
          ></image>
          <view class="expert-details-simple">
            <text class="expert-name-simple">{{ item.name }}</text>
            <view class="expert-meta">
              <text class="rating">{{ item.rating || '暂无评分' }}</text>
              <text class="order-count">{{ item.orderCount || 0 }}单</text>
            </view>
          </view>
        </view>

        <!-- 选择按钮 - 简约版本 -->
        <view class="select-area" v-if="isOrderPublisher">
          <view 
            :class="['select-btn-simple', { 
              'selected': isExpertSelected(item.id),
              'disabled': !isExpertSelected(item.id) && selectedExpertIds.length >= maxSelectCount
            }]"
            @tap="toggleExpertSelection"
            :data-expert-id="item.id"
          >
            <text class="select-text">{{ isExpertSelected(item.id) ? '已选' : '选择' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 无申请人提示 -->
    <view class="no-data" v-if="!isLoading && applicants.length === 0">
      <view class="no-data-icon-container">
        <!-- /static/images/icons/no-data.png -->
        <image class="no-data-icon" src="" mode="aspectFit"></image>
        <view class="fallback-icon" v-if="true">📝</view>
      </view>
      <text class="no-data-text">暂无达人报名</text>
      <text class="no-data-tip"
        >请耐心等待达人报名，或修改订单条件吸引更多达人</text
      >
    </view>

    <!-- 底部按钮区域 - 简约版本 -->
    <view class="bottom-buttons-simple" v-if="isOrderPublisher && selectedExpertIds.length > 0">
      <button
        :class="['confirm-btn-simple', { 
          'disabled': !canConfirmSelection
        }]"
        @tap="confirmSelection"
        :disabled="!canConfirmSelection"
      >
        <text>{{ getConfirmButtonText() }}</text>
      </button>
    </view>
  </view>
</template>

<script>
// 导入时间格式化工具
import { formatDateTime } from "../../utils/time-formatter";
import * as storageUtils from "../../utils/storage";
import * as orderState from "../../utils/order-state";
export default {
  data() {
    return {
      orderId: null,
      order: null,
      applicants: [],
      isLoading: true,
      showSelectionFeedback: false, // 控制选择反馈提示的显示
      isOrderPublisher: false, // 是否为订单发布者
      selectedExpertIds: [], // 多选达人ID数组
      maxSelectCount: 1, // 最大可选择人数，从订单信息中获取
      isMultiSelect: false, // 是否为多选模式
    };
  },
  onLoad(options) {
    if (options.id) {
      const orderId = options.id;
      this.orderId = orderId;
      this.loadOrderDetail(orderId);
    } else {
      uni.showToast({
        title: "订单ID不存在",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 加载订单详情和申请人列表
    async loadOrderDetail(orderId) {
      this.isLoading = true;

      console.log("查找订单ID:", orderId, "类型:", typeof orderId);

      // 获取应用实例
      const app = getApp();
      let currentUserId = app.globalData.currentUserId;
      
      // 如果 currentUserId 为空，尝试初始化用户信息
      if (!currentUserId) {
        await app.initUserInfo();
        currentUserId = app.globalData.currentUserId;
        
        // 如果仍然为空，尝试从 getUserInfo 获取
        if (!currentUserId) {
          await app.getUserInfo();
          currentUserId = app.globalData.currentUserId;
        }
      }

      try {
        // 优先从后端API获取最新数据
        const apiUrl = app.globalData.commonApi.getOrderDetail(orderId);
        
        let res = await this.$requestHttp.get(apiUrl, {
          data: {}
        });
        
        if (res.code === 200 && res.data) {
          const orderData = res.data;
          
          // 处理报名列表数据，优先使用后端返回的完整数据
          let applicantsList = [];
          
          // 优先使用enrollList字段
          if (orderData.enrollList && Array.isArray(orderData.enrollList)) {
            applicantsList = orderData.enrollList.map(enroll => ({
              id: enroll.talentId || enroll.enrollId || enroll.id,
              name: enroll.talentName || enroll.name || '未知达人',
              avatar: enroll.talentAvatar || enroll.avatar || '/images/default-avatar.png',
              applyTime: enroll.createTime || enroll.applyTime,
              isSelected: enroll.isSelected || false,
              status: enroll.status
            }));

          } 
          // 其次使用enrollDetails字段
          else if (orderData.enrollDetails && Array.isArray(orderData.enrollDetails)) {
            applicantsList = orderData.enrollDetails.map(detail => ({
              id: detail.memberId || detail.talentId || detail.id,
              name: detail.memberNickname || detail.talentName || detail.name || '未知达人',
              avatar: detail.memberAvatar || detail.talentAvatar || detail.avatar || '/images/default-avatar.png',
              applyTime: detail.createTime || detail.applyTime,
              isSelected: detail.isSelected || false,
              status: detail.status
            }));

          }
          // 最后使用appliedExperts字段
          else if (orderData.appliedExperts && Array.isArray(orderData.appliedExperts)) {
            applicantsList = orderData.appliedExperts.map(expert => ({
              id: expert.id || expert.talentId,
              name: expert.name || expert.talentName || '未知达人',
              avatar: expert.avatar || expert.talentAvatar || '/images/default-avatar.png',
              applyTime: expert.applyTime || expert.createTime,
              isSelected: expert.isSelected || false,
              status: expert.status
            }));

          }
          
          // 移除占位数据生成逻辑，仅使用真实的后端数据
          // 如果只有enrollCount但没有具体列表，不再创建假数据
          
          // 设置订单数据，使用后端返回的完整信息
          const processedOrder = {
            ...orderData,
            // 确保订单基本信息正确显示
            orderNo: orderData.orderNo || orderData.orderId || orderId,
            statusText: orderData.statusDesc || orderData.statusText || "已发布",
            service: orderData.typeName || orderData.activityTypeName || "未知服务",
            serviceSubType: orderData.subTypeName || "",
            date: orderData.startTime ? new Date(orderData.startTime).toLocaleDateString() : "未知日期",
            time: orderData.startTime ? new Date(orderData.startTime).toLocaleTimeString() : "未知时间",
            duration: orderData.duration || "未知",
            location: orderData.location || "未知地点",
            addressDetail: orderData.address || "",
            appliedExperts: applicantsList,
            selectedExpertId: orderData.selectedExpertId || null
          };
          
          this.order = processedOrder;
          
          // 同时更新全局数据，确保selectExpert方法能正常工作
          const app = getApp();
          let publishedOrders = app.globalData.publishedOrders || [];
          
          // 查找并更新全局数据中的对应订单
          const existingOrderIndex = publishedOrders.findIndex(o => 
            String(o.id || o.orderId) === String(orderId)
          );
          
          if (existingOrderIndex >= 0) {
            // 更新现有订单
            publishedOrders[existingOrderIndex] = {
              ...publishedOrders[existingOrderIndex],
              ...processedOrder
            };
          } else {
            // 添加新订单到全局数据
            publishedOrders.push(processedOrder);
          }
          
          app.globalData.publishedOrders = publishedOrders;
          
          // 保存到本地存储
          try {
            uni.setStorageSync('publishedOrders', publishedOrders);
          } catch (e) {
            console.error('保存订单到本地存储失败:', e);
          }
          
          // 判断当前用户是否为订单发布者
          const orderPublisherId = orderData.memberId || orderData.publisherId || orderData.userId || orderData.creatorId;
          this.isOrderPublisher = !!(orderPublisherId && currentUserId && String(orderPublisherId) === String(currentUserId));
          
          // 初始化多选相关数据
          this.maxSelectCount = orderData.personCount || orderData.people || 1;
          this.isMultiSelect = this.maxSelectCount > 1;
          
          // 初始化已选择的达人ID数组
          if (orderData.selectedList && orderData.selectedList.length > 0) {
            this.selectedExpertIds = orderData.selectedList.map(expert => expert.talentId || expert.id);
          } else if (orderData.selectedExpertId) {
            // 兼容旧的单选数据
            this.selectedExpertIds = [orderData.selectedExpertId];
          } else {
            this.selectedExpertIds = [];
          }
          
          // 处理申请人列表，使用真实数据
          this.applicants = applicantsList.map((expert) => {
            // 格式化报名时间
            let applyTimeFormatted = "未知";
            if (expert.applyTime || expert.createTime) {
              try {
                applyTimeFormatted = this.formatApplyTime(expert.applyTime || expert.createTime);
              } catch (error) {
                console.error("格式化报名时间出错:", error);
              }
            }

            return {
              ...expert,
              // 使用后端返回的真实数据，如果没有则使用默认值
              rating: expert.rating || expert.score || "暂无评分",
              orderCount: expert.orderCount || expert.completedOrders || 0,
              goodRating: expert.goodRating || expert.goodRate || "暂无评价",
              applyTimeFormatted: applyTimeFormatted,
            };
          });
          
          this.isLoading = false;
          return;
        }
      } catch (error) {
        
        // API失败时，显示错误信息并提供重试选项
        uni.showModal({
          title: "加载失败",
          content: "无法获取订单详情，请检查网络连接后重试",
          showCancel: true,
          cancelText: "返回",
          confirmText: "重试",
          success: (res) => {
            if (res.confirm) {
              // 重试加载
              this.loadOrderDetail(orderId);
            } else {
              // 返回上一页
              uni.navigateBack();
            }
          }
        });
        
        this.isLoading = false;
        return;
      }
      
      // API没有返回有效数据，显示错误
      uni.showToast({
        title: "获取订单详情失败",
        icon: "none"
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    },
    




    // 查看达人资料
    viewExpertProfile(e) {
      const expertId = e.currentTarget.dataset.id;
      if (!expertId) return;

      // 找到对应的达人信息
      const expert = this.applicants.find((expert) => expert.id === expertId);

      if (expert) {
        // 保存达人信息到本地存储以供profile页面使用
        uni.setStorageSync("currentExpertInfo", expert);
      }

      uni.navigateTo({
        url: `/pages/profile/preview?id=${expertId}`,
      });
    },

    // 选择达人
    selectExpert(e) {
      const expertId = e.currentTarget.dataset.expertId;
      if (!expertId) return;

      // 直接使用当前页面的订单数据，而不是从全局数据中查找
      const order = this.order;
      
      if (!order) {
        uni.showToast({
          title: "订单数据不存在",
          icon: "none",
        });
        return;
      }

      // 定义安全比较函数
      const safeCompare = (a, b) => {
        if (a === b) return true;
        if (a === undefined || b === undefined || a === null || b === null)
          return false;

        // 转换为字符串比较
        const strA = String(a || "");
        const strB = String(b || "");

        return strA === strB;
      };

      // 检查是否已选中同一达人，如果是则执行取消选择操作
      if (safeCompare(order.selectedExpertId, expertId)) {
        uni.showModal({
          title: "取消选择",
          content: "确定要取消选择该达人吗？",
          success: (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: "处理中...",
                mask: true,
              });

              // 取消选择该达人
              order.selectedExpertId = null;
              order.selected = false;
              order.tempSelectedExpert = null;

              // 更新全局数据
              const app = getApp();
              let publishedOrders = app.globalData.publishedOrders || [];
              
              // 更新全局数据中对应的订单
              app.globalData.publishedOrders = publishedOrders.map((o) => {
                if (
                  safeCompare(o.id, order.id) ||
                  safeCompare(o.orderId, order.orderId) ||
                  safeCompare(o.id, this.orderId) ||
                  safeCompare(o.orderId, this.orderId)
                ) {
                  return {
                    ...o,
                    selectedExpertId: null,
                    selected: false,
                    tempSelectedExpert: null,
                  };
                }
                return o;
              });

              // 保存到本地存储
              try {
                uni.setStorageSync(
                  "publishedOrders",
                  app.globalData.publishedOrders
                );
              } catch (e) {
                // 保存失败，继续处理
              }

              // 更新页面数据
              this.order = {
                ...this.order,
                selectedExpertId: null,
                selected: false,
                tempSelectedExpert: null,
              };
              this.showSelectionFeedback = false;

              uni.hideLoading();
              uni.showToast({
                title: "已取消选择",
                icon: "success",
              });
            }
          },
        });
      } else {
        // 正常选择达人流程
        uni.showModal({
          title: "确认选择",
          content: "确定选择该达人接单吗？",
          success: (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: "处理中...",
                mask: true,
              });

              // 找到选中的达人
              const selectedExpert = this.applicants.find((expert) =>
                safeCompare(expert.id, expertId)
              );
              if (!selectedExpert) {
                uni.hideLoading();
                uni.showToast({
                  title: "达人不存在",
                  icon: "none",
                });
                return;
              }

              // 设置选中状态，但不改变订单状态（保持published），等待用户确认选人结束
              order.selectedExpertId = expertId;
              order.selected = true;
              order.tempSelectedExpert = selectedExpert; // 临时保存已选达人信息，等待确认后使用

              // 更新全局数据
              const app = getApp();
              let publishedOrders = app.globalData.publishedOrders || [];
              
              // 更新全局数据中对应的订单
              app.globalData.publishedOrders = publishedOrders.map((o) => {
                if (
                  safeCompare(o.id, order.id) ||
                  safeCompare(o.orderId, order.orderId) ||
                  safeCompare(o.id, this.orderId) ||
                  safeCompare(o.orderId, this.orderId)
                ) {
                  return {
                    ...o,
                    selectedExpertId: expertId,
                    selected: true,
                    tempSelectedExpert: selectedExpert,
                  };
                }
                return o;
              });

              // 保存到本地存储
              try {
                uni.setStorageSync(
                  "publishedOrders",
                  app.globalData.publishedOrders
                );
              } catch (e) {
                // 保存失败，继续处理
              }

              // 显示反馈提示
              this.order = {
                ...this.order,
                selectedExpertId: expertId,
                selected: true,
                tempSelectedExpert: selectedExpert,
              };
              this.showSelectionFeedback = true;

              uni.hideLoading();

              // 3秒后自动隐藏反馈提示
              setTimeout(() => {
                this.showSelectionFeedback = false;
              }, 3000);
            }
          },
        });
      }
    },

    // 确认选人结束
    async confirmSelection() {
      const that = this;
      const app = getApp();
      const orderId = this.orderId;

      // 验证选择状态
      if (!this.canConfirmSelection()) {
        const requiredCount = this.maxSelectCount;
        const currentCount = this.selectedExpertIds.length;
        
        if (currentCount === 0) {
          uni.showToast({
            title: "请先选择达人",
            icon: "none",
          });
        } else if (currentCount < requiredCount) {
          uni.showToast({
            title: `还需要选择${requiredCount - currentCount}名达人`,
            icon: "none",
          });
        }
        return;
      }

      // 构建确认消息
      const selectedExperts = this.applicants.filter(expert => 
        this.selectedExpertIds.includes(expert.id)
      );
      const expertNames = selectedExperts.map(expert => expert.name || '未知达人').join('、');
      const confirmMessage = this.isMultiSelect 
        ? `确定选择「${expertNames}」等${this.selectedExpertIds.length}名达人为服务达人吗？选择后将通知所有报名者。`
        : `确定选择「${expertNames}」为服务达人吗？选择后将通知所有报名者。`;

      uni.showModal({
        title: "确认选择",
        content: confirmMessage,
        success: async function (res) {
          if (res.confirm) {
            // 1. 显示加载提示
            uni.showLoading({
              title: "正在处理...",
              mask: true,
            });

            try {
              // 调用真实的selectTalents API，支持多人选择
              const response = await that.$requestHttp.post(
                app.globalData.commonApi.selectTalents(orderId),
                {
                  data: that.selectedExpertIds
                }
              );

              if (response.code === 200) {
                // API调用成功，重新获取订单详情
                await that.loadOrderDetail();
                
                uni.hideLoading();
                const successMessage = that.isMultiSelect 
                  ? `已选择${that.selectedExpertIds.length}名达人`
                  : "已选择达人";
                
                uni.showToast({
                  title: successMessage,
                  icon: "success",
                  duration: 2000,
                });

                // 设置一个标志，表示订单已更新，需要刷新列表
                uni.setStorageSync("orderUpdated", true);

                // 返回上一页
                setTimeout(() => {
                  uni.navigateBack();
                }, 1500);
              } else {
                uni.hideLoading();
                uni.showToast({
                  title: response.message || "选择失败，请重试",
                  icon: "none",
                  duration: 2000,
                });
              }
            } catch (error) {
              console.error("选择达人失败:", error);
              uni.hideLoading();
              uni.showToast({
                title: "网络错误，请重试",
                icon: "none",
                duration: 2000,
              });
            }
          }
        },
      });
    },







    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 更新页面数据
    updatePage(order) {
      if (!order) return;

      // 更新页面数据，显示选中的达人信息
      this.order = order;
      this.isLoading = false;
    },

    // 判断达人是否已选中
    isExpertSelected(expertId) {
      return this.selectedExpertIds.includes(expertId);
    },

    // 切换达人选择状态
    toggleExpertSelection(e) {
      const expertId = e.currentTarget.dataset.expertId || e.target.dataset.expertId;
      if (!expertId) return;

      const isCurrentlySelected = this.isExpertSelected(expertId);
      
      if (isCurrentlySelected) {
        // 取消选择
        this.selectedExpertIds = this.selectedExpertIds.filter(id => id !== expertId);
      } else {
        // 添加选择
        if (this.selectedExpertIds.length < this.maxSelectCount) {
          this.selectedExpertIds.push(expertId);
        } else {
          uni.showToast({
            title: `最多只能选择${this.maxSelectCount}人`,
            icon: 'none'
          });
          return;
        }
      }

      // 显示选择反馈
      this.showSelectionFeedback = true;
      setTimeout(() => {
        this.showSelectionFeedback = false;
      }, 2000);
    },

    // 清空所有选择
    clearAllSelection() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有已选择的达人吗？',
        success: (res) => {
          if (res.confirm) {
            this.selectedExpertIds = [];
            uni.showToast({
              title: '已清空选择',
              icon: 'success'
            });
          }
        }
      });
    },

    // 推荐选择（按评分等条件自动选择）
    selectTopExperts() {
      const remainingCount = this.maxSelectCount - this.selectedExpertIds.length;
      if (remainingCount <= 0) return;

      // 过滤掉已选择的达人，按评分排序
      const availableExperts = this.applicants
        .filter(expert => !this.isExpertSelected(expert.id))
        .sort((a, b) => (b.rating || 0) - (a.rating || 0))
        .slice(0, remainingCount);

      if (availableExperts.length === 0) {
        uni.showToast({
          title: '没有可推荐的达人',
          icon: 'none'
        });
        return;
      }

      uni.showModal({
        title: '推荐选择',
        content: `系统将为您推荐${availableExperts.length}名优质达人，是否确认选择？`,
        success: (res) => {
          if (res.confirm) {
            availableExperts.forEach(expert => {
              this.selectedExpertIds.push(expert.id);
            });
            uni.showToast({
              title: `已推荐选择${availableExperts.length}人`,
              icon: 'success'
            });
          }
        }
      });
    },

    // 是否可以确认选择
    canConfirmSelection() {
      if (this.isMultiSelect) {
        // 多选模式：必须选满所需人数
        return this.selectedExpertIds.length === this.maxSelectCount;
      } else {
        // 单选模式：选择了一个即可
        return this.selectedExpertIds.length === 1;
      }
    },

    // 获取确认按钮文本
    getConfirmButtonText() {
      if (this.isMultiSelect) {
        const selected = this.selectedExpertIds.length;
        const total = this.maxSelectCount;
        if (selected < total) {
          return `请选择${total - selected}人后确认（${selected}/${total}）`;
        } else {
          return `确认选择${total}人并通知达人`;
        }
      } else {
        return '确认选人结束并通知达人';
      }
    },

    // 格式化订单时间显示
    formatOrderTime(date, time) {
      if (!date || !time) return '时间待定';
      
      try {
        // 解析日期和时间
        const dateStr = date.toString();
        const timeStr = time.toString();
        
        // 创建完整的日期时间字符串
        const fullDateTime = `${dateStr} ${timeStr}`;
        const orderDate = new Date(fullDateTime);
        
        // 检查日期是否有效
        if (isNaN(orderDate.getTime())) {
          return `${dateStr} ${timeStr}`;
        }
        
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const orderDay = new Date(orderDate.getFullYear(), orderDate.getMonth(), orderDate.getDate());
        
        const diffDays = Math.floor((orderDay - today) / (1000 * 60 * 60 * 24));
        
        // 格式化时间部分
        const timeFormatted = orderDate.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
        
        if (diffDays === 0) {
          return `今天 ${timeFormatted}`;
        } else if (diffDays === 1) {
          return `明天 ${timeFormatted}`;
        } else if (diffDays === 2) {
          return `后天 ${timeFormatted}`;
        } else if (diffDays > 0 && diffDays <= 7) {
          const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
          return `${weekdays[orderDate.getDay()]} ${timeFormatted}`;
        } else {
          // 超过一周或过去的时间，显示完整日期
          const month = (orderDate.getMonth() + 1).toString().padStart(2, '0');
          const day = orderDate.getDate().toString().padStart(2, '0');
          return `${month}-${day} ${timeFormatted}`;
        }
      } catch (error) {
        return `${date} ${time}`;
      }
    },
    formatApplyTime(time) {
      if (!time) return "未知";
      
      try {
        let date;
        
        // 处理不同类型的时间输入
        if (time instanceof Date) {
          date = time;
        } else if (typeof time === 'number') {
          const timestamp = time > 9999999999 ? time : time * 1000;
          date = new Date(timestamp);
        } else if (typeof time === 'string') {
          if (time.trim() === '') return "未知";
          
          let timeStr = time;
          
          // 处理环境信息
          if (timeStr.includes('(env:')) {
            timeStr = timeStr.split('(')[0].trim();
          }
          
          // 处理上午/下午格式
          if (timeStr.includes('上午') || timeStr.includes('下午')) {
            let datePart = '';
            let timePart = '';
            let isPM = false;
            
            if (timeStr.includes('上午')) {
              const parts = timeStr.split('上午');
              datePart = parts[0].trim();
              timePart = parts[1].trim();
              isPM = false;
            } else if (timeStr.includes('下午')) {
              const parts = timeStr.split('下午');
              datePart = parts[0].trim();
              timePart = parts[1].trim();
              isPM = true;
            }
            
            if (timePart) {
              const timeComponents = timePart.split(':');
              let hours = parseInt(timeComponents[0], 10);
              const minutes = timeComponents.length > 1 ? parseInt(timeComponents[1], 10) : 0;
              
              if (isPM && hours < 12) {
                hours += 12;
              } else if (!isPM && hours === 12) {
                hours = 0;
              }
              
              timeStr = `${datePart} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
            } else {
              timeStr = `${datePart} 00:00:00`;
            }
          }
          
          // 修复格式
          if (timeStr.includes('-')) {
            if (timeStr.includes('T')) {
              const parts = timeStr.split('T');
              parts[0] = parts[0].replace(/-/g, '/');
              timeStr = parts.join('T');
            } else {
              timeStr = timeStr.replace(/-/g, '/');
            }
          }
          
          if (!timeStr.includes(':')) {
            timeStr += ' 00:00:00';
          }
          
          date = new Date(timeStr);
          
          if (isNaN(date.getTime()) && timeStr.includes('T')) {
            timeStr = timeStr.replace('T', ' ').substring(0, 19);
            date = new Date(timeStr);
          }
          
          if (isNaN(date.getTime())) {
            const dateParts = timeStr.split(/[\/\-\s:]/);
            if (dateParts.length >= 3) {
              const year = parseInt(dateParts[0], 10);
              const month = parseInt(dateParts[1], 10) - 1;
              const day = parseInt(dateParts[2], 10);
              const hour = dateParts.length > 3 ? parseInt(dateParts[3], 10) : 0;
              const minute = dateParts.length > 4 ? parseInt(dateParts[4], 10) : 0;
              
              date = new Date(year, month, day, hour, minute);
            }
          }
        } else {
          return "未知";
        }
        
        if (isNaN(date.getTime())) {
          return "未知";
        }
        
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        
        // 刚刚（1分钟内）
        if (diffMinutes < 1) {
          return "刚刚申请";
        }
        
        // xx分钟前（1小时内）
        if (diffMinutes < 60) {
          return `${diffMinutes}分钟前申请`;
        }
        
        // xx小时前（24小时内）
        const diffHours = Math.floor(diffMinutes / 60);
        if (diffHours < 24) {
          return `${diffHours}小时前申请`;
        }
        
        // xx天前（7天内）
        const diffDays = Math.floor(diffHours / 24);
        if (diffDays < 7) {
          return `${diffDays}天前申请`;
        }
        
        // 超过7天显示具体日期
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hour = date.getHours().toString().padStart(2, '0');
        const minute = date.getMinutes().toString().padStart(2, '0');
        
        // 如果是今年，不显示年份
        if (date.getFullYear() === now.getFullYear()) {
          return `${month}-${day} ${hour}:${minute} 申请`;
        } else {
          const year = date.getFullYear();
          return `${year}-${month}-${day} ${hour}:${minute} 申请`;
        }
        
      } catch (error) {
        return "未知";
      }
    },
  },
};
</script>

<style scoped>
/* 页面容器 */
.container {
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(64, 169, 255, 0.03));
  z-index: -1;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 订单信息 */
.order-info {
  background: linear-gradient(135deg, #ffffff, #fafbfc);
  border-radius: 20rpx;
  padding: 36rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #e8f4fd;
  position: relative;
  overflow: hidden;
}

.order-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #1890ff, #40a9ff, #69c0ff);
}

.order-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid rgba(24, 144, 255, 0.1);
}

.order-id {
  font-size: 26rpx;
  color: #8c8c8c;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.order-status {
  font-size: 24rpx;
  font-weight: 600;
  padding: 8rpx 20rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #e6f7ff, #bae7ff);
  color: #1890ff;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.15);
  border: 1rpx solid rgba(24, 144, 255, 0.2);
}

.order-status.published {
  background: linear-gradient(135deg, #fff9e6, #ffed4e);
  color: #b8860b;
  border: 1rpx solid rgba(255, 215, 0, 0.3);
}

.order-status.expertSelected {
  background: linear-gradient(135deg, #d9f7be, #b7eb8f);
  color: #52c41a;
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.15);
  border: 1rpx solid rgba(82, 196, 26, 0.2);
}

.order-detail .item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.order-detail .label {
  width: 180rpx;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  position: relative;
}

.order-detail .label::after {
  content: ':';
  margin-left: 4rpx;
  color: #bbb;
}

.order-detail .value {
  flex: 1;
  color: #333;
  font-weight: 400;
  word-break: break-all;
  line-height: 1.6;
}

/* 特殊值的样式 */
.order-detail .value.activity-type {
  font-size: 26rpx;
  font-weight: 600;
  display: inline-block;
}

.order-detail .value.activity-time {
  font-weight: 600;
}

.order-detail .value.activity-duration {
  font-weight: 600;
}

.order-detail .value.activity-location {
  font-weight: 600;
}

/* 标题样式 */
.section-title {
  font-size: 34rpx;
  font-weight: 700;
  margin: 0;
  padding: 32rpx 36rpx;
  background: linear-gradient(135deg, #fff9e6, #ffffff);
  border-left: 6rpx solid #ffd700;
  line-height: 42rpx;
  color: #333;
  border-bottom: 1rpx solid #fff2cc;
  position: relative;
  overflow: hidden;
}

.section-title::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(180deg, #ffd700, #ffed4e, #fff2cc);
  box-shadow: 2rpx 0 8rpx rgba(255, 215, 0, 0.3);
}

/* 申请人列表 */
.applicants-list {
  padding: 0 30rpx 120rpx 30rpx;
}

.applicant-item {
  background: linear-gradient(135deg, #ffffff, #fafbfc);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 28rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #e8f4fd;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.applicant-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #fff2cc);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.applicant-item:hover::before {
  opacity: 1;
}

.expert-info {
  display: flex;
  margin-bottom: 20rpx;
}

.expert-avatar-wrapper {
  position: relative;
  margin-right: 24rpx;
}

.expert-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 3rpx solid #f0f0f0;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.online-indicator {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: #ffd700;
  border: 3rpx solid #fff;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(255, 215, 0, 0.3);
}

.verified-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

.expert-details {
  flex: 1;
  min-width: 0;
}

.expert-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.expert-name {
  font-size: 34rpx;
  font-weight: 600;
  margin-right: 16rpx;
  color: #333;
}

.selected-tag {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

.expert-stats {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  min-width: 120rpx;
  flex: 1;
  justify-content: center;
}

.stat-icon {
  font-size: 20rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.apply-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 16rpx;
  background: linear-gradient(135deg, #fff9e6, #fffbf0);
  border-radius: 16rpx;
  border: 1rpx solid #ffd700;
  box-shadow: 0 2rpx 6rpx rgba(255, 215, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.apply-time::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #fff2cc);
}

.time-icon {
  font-size: 22rpx;
  color: #b8860b;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.time-text {
  font-size: 26rpx;
  color: #b8860b;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.action-area {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.select-btn {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  font-size: 28rpx;
  font-weight: 600;
  padding: 18rpx 48rpx;
  border-radius: 48rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 215, 0, 0.3);
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.select-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.select-btn:active::before {
  left: 100%;
}

.select-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
}

.select-btn.selected {
  background: linear-gradient(135deg, #b8860b, #daa520);
  color: #fff;
  box-shadow: 0 6rpx 16rpx rgba(184, 134, 11, 0.3);
}

.select-btn.disabled {
  background: #f5f5f5;
  color: #bbb;
  box-shadow: none;
  transform: none;
}

/* 无数据样式 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-data-icon-container {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-data-icon {
  width: 200rpx;
  height: 200rpx;
}

.fallback-icon {
  position: absolute;
  font-size: 120rpx;
  color: #bbbbbb;
}

.no-data-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.no-data-tip {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  padding: 0 60rpx;
}

/* 底部按钮区域样式 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.confirm-btn {
  flex: 2;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  border: none;
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 20rpx rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.confirm-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.confirm-btn:active::before {
  left: 100%;
}

.confirm-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 6rpx 16rpx rgba(255, 215, 0, 0.4);
}

.back-btn {
  flex: 1;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #495057;
  border: 2rpx solid #dee2e6;
  height: 96rpx;
  line-height: 92rpx;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 20rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.back-btn:active {
  transform: translateY(2rpx);
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 选择反馈提示的样式 */
.selection-feedback {
  position: fixed;
  top: 120rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 10rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  animation: fadeInOut 3s ease;
  max-width: 90%;
  width: auto;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.feedback-content {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex-wrap: wrap;
}

.feedback-content text {
  color: #ffffff;
  font-size: 28rpx;
  flex: 1;
  text-align: center;
  min-width: 200rpx;
}

/* 选中的申请人项样式 */
.applicant-item.selected-item {
  border: 2rpx solid #ffd700;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(255, 237, 78, 0.03));
  box-shadow: 0 12rpx 32rpx rgba(255, 215, 0, 0.2);
  transform: translateY(-4rpx) scale(1.02);
}

.applicant-item.selected-item::before {
  opacity: 1;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #fff2cc);
}



/* 选中状态的按钮样式 */
.select-btn.selected {
  background-color: #b8860b;
  color: white;
  border: none;
}

.select-btn.disabled {
  background-color: #f5f5f5;
  color: #999999;
  border: 1rpx solid #dddddd;
}

/* 多选区域样式 */
.multi-select-area {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx 0;
}

.multi-select-area checkbox {
  transform: scale(1.2);
}

.select-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 选择状态显示样式 */
.selection-status {
  position: fixed;
  bottom: 140rpx;
  left: 30rpx;
  right: 30rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 99;
  border: 2rpx solid #f0f0f0;
}

.status-info {
  margin-bottom: 20rpx;
}

.status-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 15rpx;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.quick-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.quick-btn {
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
  border-radius: 20rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.quick-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

/* 确认按钮禁用状态 */
.confirm-btn.disabled {
  background: #f5f5f5;
  color: #bbb;
  box-shadow: none;
  transform: none;
}

.confirm-btn.disabled::before {
  display: none;
}

/* ===== 简约风格样式 ===== */

/* 简约进度条样式 */
.progress-bar-simple {
  margin: 20rpx 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
  border-radius: 12rpx;
  border: 1px solid #ffd700;
}

.progress-info {
  margin-bottom: 12rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.progress-track-simple {
  width: 100%;
  height: 8rpx;
  background: #f5f5f5;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill-simple {
  height: 100%;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-radius: 4rpx;
  transition: width 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(255, 215, 0, 0.3);
}

/* 简约申请人卡片样式 */
.applicant-item-simple {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  margin-bottom: 16rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.applicant-item-simple.selected {
  border: 2rpx solid #ffd700;
  background: linear-gradient(135deg, #fff9e6 0%, #fffbf0 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.2);
}

.expert-info-simple {
  display: flex;
  align-items: center;
  flex: 1;
}

.expert-avatar-simple {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 24rpx;
}

.expert-details-simple {
  flex: 1;
}

.expert-name-simple {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.expert-meta {
  display: flex;
  gap: 24rpx;
}

.rating, .order-count {
  font-size: 26rpx;
  color: #666;
}

/* 简约选择按钮样式 */
.select-area {
  margin-left: 20rpx;
}

.select-btn-simple {
  padding: 16rpx 32rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 24rpx;
  background: #fff;
  transition: all 0.3s ease;
}

.select-btn-simple.selected {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-color: #ffd700;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

.select-btn-simple.disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
}

.select-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.select-btn-simple.selected .select-text {
  color: #333;
  font-weight: 600;
}

.select-btn-simple.disabled .select-text {
  color: #bbb;
}

/* 简约底部按钮样式 */
.bottom-buttons-simple {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.confirm-btn-simple {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
}

.confirm-btn-simple.disabled {
  background: #d9d9d9;
  color: #999;
  box-shadow: none;
}

.confirm-btn-simple:active:not(.disabled) {
  transform: scale(0.98);
  background: linear-gradient(135deg, #e6c200, #f0d000);
  box-shadow: 0 2rpx 6rpx rgba(255, 215, 0, 0.4);
}
</style>
