<template>
	<!-- 订单评价页面 -->
	<view class="container">
		<!-- 加载中提示 -->
		<view class="loading-container" v-if="isLoading">
			<view class="loading-spinner"></view>
			<text>加载中...</text>
		</view>

		<block v-else>
			<!-- 订单信息 -->
			<view class="order-info">
				<view class="section-title">订单信息</view>
				<view class="order-card">
					<view class="order-title">{{ orderDetail.title || (orderDetail.service + '>' + orderDetail.serviceSubType) }}
					</view>
					<view class="order-meta">
						<text>订单号：{{ orderDetail.orderNo || orderDetail.id }}</text>
						<text>下单时间：{{ orderDetail.createTime }}</text>
					</view>
					<view class="order-price">¥{{ orderDetail.price || orderDetail.hourlyRate || 0 }}/小时</view>
				</view>
			</view>

			<!-- 达人信息 -->
			<view class="expert-info" v-if="expert">
				<view class="section-title">达人信息</view>
				<view class="expert-card">
					<image class="expert-avatar" :src="expert?.avatar || '/images/default-avatar.png'"></image>
					<view class="expert-detail">
						<text class="expert-name">{{ expert.name || '达人' }}</text>
						<text class="expert-label">为您提供了服务</text>
					</view>
				</view>
			</view>

			<!-- 评价表单/评价详情 -->
			<view class="evaluation-form">
				<view class="section-title">{{ viewMode ? '用户评价' : '您的评价' }}</view>

				<!-- 评分 -->
				<view class="rating-section">
					<text class="rating-label">服务评分</text>
					<view class="rating-stars">
						<text v-for="(_, index) in 5" :key="index" :class="['star-icon', index < rating ? 'star-filled' : 'star-empty']"
							@tap="!viewMode ? setRating(index + 1) : null" :data-rating="index + 1">★</text>
						<text class="rating-text">{{ rating }}分</text>
					</view>
				</view>

				<!-- 评价标签 -->
				<view class="tags-section">
					<text class="tags-label">评价标签</text>
					<view class="tags-list">
						<view v-for="(item, index) in tags" :key="item.id"
							:class="['tag-item', item.selected ? 'selected' : '', viewMode && !item.selected ? 'hidden' : '']"
							@tap="!viewMode ? toggleTag(index) : null" :data-index="index">
							{{ item.name }}
						</view>
					</view>
				</view>

				<!-- 评价内容 -->
				<view class="content-section">
					<text class="content-label">评价内容</text>
					<block v-if="!viewMode">
						<textarea class="content-textarea" placeholder="请输入您对本次服务的评价..." @input="onContentInput"
							:value="content" maxlength="200"></textarea>
						<view class="word-count">{{ content.length }}/200</view>
					</block>
					<block v-else>
						<view class="content-view">{{ content || '暂无评价内容' }}</view>
					</block>
				</view>

				<!-- 提交按钮 - 仅编辑模式显示 -->
				<button class="submit-btn" @tap="submitEvaluation" :disabled="submitDisabled" v-if="!viewMode">
					提交评价
				</button>

				<!-- 返回按钮 - 仅查看模式显示 -->
				<button class="back-btn" @tap="navigateBack" v-if="viewMode">
					返回
				</button>
			</view>
		</block>
	</view>
</template>

<script>
// 引入API接口
import { submitOrderComment, updateOrderEnrollDetailStatus } from "../../api/index.js";

export default {
	data() {
		return {
			orderId: '',
			orderDetail: {},
			expert: null, // 订单相关的达人信息
			rating: 5, // 默认评分为5星
			content: '', // 评价内容
			tags: [
				{ id: 1, name: '服务周到', selected: false },
				{ id: 2, name: '态度友好', selected: false },
				{ id: 3, name: '专业技能强', selected: false },
				{ id: 4, name: '准时守信', selected: false },
				{ id: 5, name: '沟通顺畅', selected: false },
			],
			isLoading: true,
			submitDisabled: false, // 防止重复提交
			viewMode: false, // 是否为查看模式
			evaluation: null, // 存储评价数据供查看模式使用
			submitted: false // 新增已提交标志
		}
	},
	onLoad(options) {
		const { id, mode } = options;

		// 检查是否为查看模式
		if (mode === 'view') {
			this.viewMode = true;
		}

		if (!id) {
			uni.showToast({
				title: '订单ID不存在',
				icon: 'none'
			});
			uni.navigateBack();
			return;
		}

		this.orderId = id;
		this.isLoading = true;

		this.loadOrderDetail(id);
	},
	methods: {
		// 加载订单详情
		loadOrderDetail(orderId) {
			console.log('开始加载订单详情', {
				传入ID: orderId,
				ID类型: typeof orderId
			});

			// 定义安全比较函数
			const safeCompare = (a, b) => {
				if (a === b) return true;
				if (a === undefined || b === undefined || a === null || b === null) return false;

				const strA = String(a || '');
				const strB = String(b || '');

				return strA === strB;
			};

			// 先从缓存或全局数据中获取订单详情
			let orderDetail = null;

			// 尝试从当前页面缓存获取
			const currentOrderDetail = uni.getStorageSync('currentOrderDetail');
			if (currentOrderDetail) {
				console.log('从缓存获取的订单详情', {
					缓存订单ID: currentOrderDetail.id || currentOrderDetail.orderId
				});

				if (safeCompare(currentOrderDetail.id, orderId) || safeCompare(currentOrderDetail.orderId, orderId)) {
					orderDetail = currentOrderDetail;
					console.log('在缓存中找到匹配订单');
				}
			}

			if (!orderDetail) {
				// 从全局已发布订单中查找
				const app = getApp();
				const publishedOrders = app.globalData.publishedOrders || [];
				console.log('从全局数据查找订单', {
					订单ID: orderId,
					全局订单数量: publishedOrders.length,
					全局订单IDs: publishedOrders.map(o => o.id || o.orderId).join(',')
				});

				orderDetail = publishedOrders.find(order =>
					safeCompare(order.id, orderId) ||
					safeCompare(order.orderId, orderId)
				);

				// 如果找不到，尝试从存储中获取所有可能的订单列表
				if (!orderDetail) {
					console.log('在全局数据中未找到订单，尝试从其他存储获取');

					// 尝试从已报名订单列表查找
					let appliedOrders = [];
					try {
						appliedOrders = JSON.parse(uni.getStorageSync('expertAppliedOrders') || '[]');
					} catch (e) {
						appliedOrders = [];
					}

					if (Array.isArray(appliedOrders) && appliedOrders.length > 0) {
						console.log('从已报名订单列表中查找', {
							已报名订单数: appliedOrders.length
						});

						orderDetail = appliedOrders.find(order =>
							safeCompare(order.id, orderId) ||
							safeCompare(order.orderId, orderId)
						);
					}

					// 尝试从接单列表查找
					if (!orderDetail) {
						let expertOrders = [];
						try {
							expertOrders = JSON.parse(uni.getStorageSync('expertOrders') || '[]');
						} catch (e) {
							expertOrders = [];
						}

						if (Array.isArray(expertOrders) && expertOrders.length > 0) {
							console.log('从接单列表中查找', {
								接单列表数量: expertOrders.length
							});

							orderDetail = expertOrders.find(order =>
								safeCompare(order.id, orderId) ||
								safeCompare(order.orderId, orderId)
							);
						}
					}
				}
			}

			if (orderDetail) {
				console.log('成功找到订单详情', {
					订单ID: orderDetail.id || orderDetail.orderId,
					订单标题: orderDetail.title,
					订单状态: orderDetail.status
				});

				// 获取达人信息
				let expertInfo = null;
				if (orderDetail.selectedExpertId) {
					if (orderDetail.appliedExperts && Array.isArray(orderDetail.appliedExperts)) {
						expertInfo = orderDetail.appliedExperts.find(expert =>
							safeCompare(expert.id, orderDetail.selectedExpertId)
						);
					}

					if (!expertInfo) {
						// 创建基本达人信息
						expertInfo = {
							id: orderDetail.selectedExpertId,
							name: orderDetail.selectedExpertName || '达人',
							avatar: orderDetail.selectedExpertAvatar || '/images/default-avatar.png'
						};
					}
				}

				this.orderDetail = orderDetail;
				this.expert = expertInfo;
				this.isLoading = false;
			} else {
				console.error('未找到订单信息', {
					查找ID: orderId,
					全局订单数: app.globalData.publishedOrders?.length || 0
				});

				uni.showToast({
					title: '未找到订单信息',
					icon: 'none'
				});

				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},

		// 加载评价数据
		loadEvaluation(order) {
			// 从订单中获取评价数据
			const evaluation = order.evaluation || {};

			if (!evaluation || !evaluation.rating) {
				uni.showToast({
					title: '暂无评价数据',
					icon: 'none'
				});
				setTimeout(() => {
					// 使用reLaunch替代navigateBack，避免可能的switchTab调用
					uni.reLaunch({
						url: '/pages/publish-orders/publish-orders'
					});
				}, 1500);
				return;
			}

			// 获取专家信息
			const expertId = order.selectedExpertId;
			let expert = null;

			if (expertId) {
				if (order.selectedExpert) {
					expert = order.selectedExpert;
				} else if (order.appliedExperts && Array.isArray(order.appliedExperts)) {
					expert = order.appliedExperts.find(e => String(e.id) === String(expertId));
				}
			}

			// 如果评价中有标签，则处理标签数据
			let tags = [...this.tags];
			if (evaluation.tags && Array.isArray(evaluation.tags)) {
				tags = tags.map(tag => ({
					...tag,
					selected: evaluation.tags.includes(tag.id) || evaluation.tags.includes(tag.name)
				}));
			}

			this.orderDetail = order;
			this.expert = expert;
			this.rating = evaluation.rating || 5;
			this.content = evaluation.content || '';
			this.tags = tags;
			this.evaluation = evaluation;
			this.isLoading = false;
		},

		// 设置评分
		setRating(rating) {
			if (this.viewMode) return; // 查看模式不允许修改
			this.rating = rating;
		},

		// 切换标签选择状态
		toggleTag(index) {
			if (this.viewMode) return; // 查看模式不允许修改
			const tags = [...this.tags];
			tags[index].selected = !tags[index].selected;
			this.tags = tags;
		},

		// 评价内容输入
		onContentInput(e) {
			if (this.viewMode) return; // 查看模式不允许修改
			this.content = e.detail.value;
		},

		// 提交评价
		submitEvaluation() {
			if (this.viewMode) return; // 查看模式不允许提交

			if (this.rating < 1) {
				uni.showToast({
					title: '请至少选择1颗星',
					icon: 'none'
				});
				return;
			}

			this.submitDisabled = true;

			// 获取选中的标签
			const selectedTags = this.tags
				.filter(tag => tag.selected)
				.map(tag => tag.id);

			// 准备评价数据
			const evaluationData = {
				orderId: this.orderDetail.id || this.orderDetail.orderId,
				expertId: this.orderDetail.selectedExpertId,
				rating: this.rating,
				content: this.content,
				tags: selectedTags,
				createTime: new Date().toISOString()
			};

			uni.showLoading({
				title: '提交中...',
				mask: true
			});

			// 更新订单状态
			const app = getApp();
			let allOrders = app.globalData.publishedOrders || [];
			const orderId = this.orderDetail.id || this.orderDetail.orderId;

			const orderIndex = allOrders.findIndex(o =>
				String(o.id) === String(orderId) || String(o.orderId) === String(orderId)
			);

			if (orderIndex !== -1) {
				// 更新订单状态为已评价
				allOrders[orderIndex].status = 'evaluated';
				allOrders[orderIndex].statusText = '已评价';
				allOrders[orderIndex].evaluation = evaluationData;

				// 更新用户进度为5，对应评价阶段
				allOrders[orderIndex].userProgress = 5;

				// 更新全局数据
				app.globalData.publishedOrders = allOrders;

				// 调用API提交评价
				try {
					const result = await submitOrderComment({
						orderId: this.orderId,
						rating: evaluationData.rating,
						content: evaluationData.content,
						tags: evaluationData.tags.map(tag => tag.name).join(',')
					});
					
					if (result.code === 200) {
				
				uni.hideLoading();
				uni.showToast({
					title: '评价提交成功',
					icon: 'success'
				});

				// 设置已提交标志
				this.submitted = true;

				// 延迟返回，让用户看到提示
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
				// }
			} else {
				uni.hideLoading();
				uni.showToast({
					title: '提交失败，找不到订单',
					icon: 'none'
				});
				this.submitDisabled = false;
			}
		},

		// 保存评价到订单数据中
		saveEvaluationToOrder(evaluation) {
			console.log('保存评价数据到订单', {
				订单ID: evaluation.orderId,
				评分: evaluation.rating,
				标签数量: evaluation.tags.length
			});

			// 获取全局订单数据
			const app = getApp();
			const publishedOrders = app.globalData.publishedOrders || [];

			// 安全比较函数
			const safeCompare = (a, b) => {
				if (a === b) return true;
				if (a === undefined || b === undefined || a === null || b === null) return false;

				const strA = String(a || '');
				const strB = String(b || '');

				return strA === strB;
			};

			// 在全局数据中查找并更新订单
			const orderIndex = publishedOrders.findIndex(order =>
				safeCompare(order.id, this.orderId) ||
				safeCompare(order.orderId, this.orderId)
			);

			if (orderIndex !== -1) {
				console.log('在全局数据中找到需要更新的订单', {
					订单索引: orderIndex,
					订单ID: publishedOrders[orderIndex].id || publishedOrders[orderIndex].orderId
				});

				// 更新订单状态和评价数据
				publishedOrders[orderIndex].evaluation = evaluation;
				publishedOrders[orderIndex].status = 'evaluated';
				publishedOrders[orderIndex].userProgress = 5; // 用户进度更新到评价阶段

				// 添加状态日志
				if (!publishedOrders[orderIndex].stateLogs) {
					publishedOrders[orderIndex].stateLogs = [];
				}

				publishedOrders[orderIndex].stateLogs.unshift({
					state: 'evaluated',
					time: new Date().toISOString(),
					role: 'user',
					remark: '用户已评价'
				});

				// 调用API更新订单评价状态
				try {
					await updateOrderEnrollDetailStatus({
						orderId: this.orderId,
						status: 5 // 已评价状态
					});
				} catch (error) {
					console.error('更新订单评价状态失败:', error);
				}

				console.log('订单评价已保存到全局数据');

				// 同步更新达人相关的订单列表
				this.updateExpertOrderLists(publishedOrders[orderIndex], evaluation);
			} else {
				console.error('未找到需要更新的订单', {
					查找ID: this.orderId,
					全局订单数: publishedOrders.length,
					全局订单IDs: publishedOrders.map(o => o.id || o.orderId).join(',')
				});

				// 尝试从所有可能的存储中找到并更新订单
				this.findAndUpdateOrder(this.orderId, evaluation);
			}
		},

		// 在所有可能的存储中查找并更新订单
		findAndUpdateOrder(orderId, evaluation) {
			console.log('尝试在所有存储中查找订单', orderId);

			const safeCompare = (a, b) => {
				if (a === b) return true;
				if (a === undefined || b === undefined || a === null || b === null) return false;

				const strA = String(a || '');
				const strB = String(b || '');

				return strA === strB;
			};

			// 尝试从接单列表查找
			let expertOrders = [];
			try {
				expertOrders = JSON.parse(uni.getStorageSync('expertOrders') || '[]');
			} catch (e) {
				expertOrders = [];
			}

			if (Array.isArray(expertOrders) && expertOrders.length > 0) {
				const orderIndex = expertOrders.findIndex(order =>
					safeCompare(order.id, orderId) ||
					safeCompare(order.orderId, orderId)
				);

				if (orderIndex !== -1) {
					console.log('在接单列表中找到订单');
					const order = expertOrders[orderIndex];

					// 更新订单状态
					order.status = 'evaluated';
					order.evaluation = evaluation;

					// 添加状态日志
					if (!order.stateLogs) {
						order.stateLogs = [];
					}

					order.stateLogs.unshift({
						state: 'evaluated',
						time: new Date().toISOString(),
						role: 'user',
						remark: '用户已评价'
					});

					// 调用API更新订单状态
					try {
						await updateOrderEnrollDetailStatus({
							orderId: orderId,
							status: 5 // 已评价状态
						});
					} catch (error) {
						console.error('更新订单状态失败:', error);
					}
					console.log('接单列表中的订单已更新');
				}
			}

			// 尝试从已报名订单列表查找
			let appliedOrders = [];
			try {
				appliedOrders = JSON.parse(uni.getStorageSync('expertAppliedOrders') || '[]');
			} catch (e) {
				appliedOrders = [];
			}

			if (Array.isArray(appliedOrders) && appliedOrders.length > 0) {
				const orderIndex = appliedOrders.findIndex(order =>
					safeCompare(order.id, orderId) ||
					safeCompare(order.orderId, orderId)
				);

				if (orderIndex !== -1) {
					console.log('在已报名列表中找到订单');
					const order = appliedOrders[orderIndex];

					// 更新订单状态
					order.status = 'evaluated';
					order.evaluation = evaluation;

					// 添加状态日志
					if (!order.stateLogs) {
						order.stateLogs = [];
					}

					order.stateLogs.unshift({
						state: 'evaluated',
						time: new Date().toISOString(),
						role: 'user',
						remark: '用户已评价'
					});

					// TODO: 调用API更新订单状态
					// await updateOrderEvaluationAPI(orderId, evaluation);
					console.log('已报名列表中的订单已更新');
				}
			}
		},

		// 更新达人相关的订单列表
		updateExpertOrderLists(order, evaluation) {
			console.log('更新达人相关的订单列表');

			const safeCompare = (a, b) => {
				if (a === b) return true;
				if (a === undefined || b === undefined || a === null || b === null) return false;

				const strA = String(a || '');
				const strB = String(b || '');

				return strA === strB;
			};

			// 更新达人接单列表
			let expertOrders = [];
			try {
				expertOrders = JSON.parse(uni.getStorageSync('expertOrders') || '[]');
			} catch (e) {
				expertOrders = [];
			}

			if (Array.isArray(expertOrders) && expertOrders.length > 0) {
				const orderIndex = expertOrders.findIndex(o =>
					safeCompare(o.id, order.id) ||
					safeCompare(o.orderId, order.orderId)
				);

				if (orderIndex !== -1) {
					expertOrders[orderIndex].status = 'evaluated';
					expertOrders[orderIndex].evaluation = evaluation;

					// 添加状态日志
					if (!expertOrders[orderIndex].stateLogs) {
						expertOrders[orderIndex].stateLogs = [];
					}

					expertOrders[orderIndex].stateLogs.unshift({
						state: 'evaluated',
						time: new Date().toISOString(),
						role: 'user',
						remark: '用户已评价'
					});

					uni.setStorageSync('expertOrders', JSON.stringify(expertOrders));
					console.log('已更新达人接单列表');
				}
			}

			// 更新已报名订单列表
			let appliedOrders = [];
			try {
				appliedOrders = JSON.parse(uni.getStorageSync('expertAppliedOrders') || '[]');
			} catch (e) {
				appliedOrders = [];
			}

			if (Array.isArray(appliedOrders) && appliedOrders.length > 0) {
				const orderIndex = appliedOrders.findIndex(o =>
					safeCompare(o.id, order.id) ||
					safeCompare(o.orderId, order.orderId)
				);

				if (orderIndex !== -1) {
					appliedOrders[orderIndex].status = 'evaluated';
					appliedOrders[orderIndex].evaluation = evaluation;

					// 添加状态日志
					if (!appliedOrders[orderIndex].stateLogs) {
						appliedOrders[orderIndex].stateLogs = [];
					}

					appliedOrders[orderIndex].stateLogs.unshift({
						state: 'evaluated',
						time: new Date().toISOString(),
						role: 'user',
						remark: '用户已评价'
					});

					uni.setStorageSync('expertAppliedOrders', JSON.stringify(appliedOrders));
					console.log('已更新达人已报名订单列表');
				}
			}
		},

		// 返回上一页
		navigateBack() {
			// 如果已经提交过评价，直接返回
			if (this.submitted) {
				uni.navigateBack();
				return;
			}

			// 确认是否放弃评价
			uni.showModal({
				title: '确认返回',
				content: '是否放弃当前评价？',
				success: (res) => {
					if (res.confirm) {
						uni.navigateBack();
					}
				}
			});
		}
	}
}
</script>

<style scoped>
/* 评价页面样式 */
.container {
	padding: 30rpx;
	background-color: #f8f8f8;
	min-height: 100vh;
}

/* 加载中提示 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	border: 6rpx solid #f3f3f3;
	border-top: 6rpx solid #ff9800;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

/* 通用section标题 */
.section-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	color: #333;
}

/* 订单信息卡片 */
.order-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.order-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 16rpx;
}

.order-meta {
	display: flex;
	flex-direction: column;
	font-size: 26rpx;
	color: #999;
	line-height: 1.6;
}

.order-meta text {
	margin-bottom: 8rpx;
}

.order-price {
	font-size: 28rpx;
	color: #ff6b00;
	margin-top: 16rpx;
	font-weight: 500;
}

/* 达人信息卡片 */
.expert-card {
	display: flex;
	align-items: center;
	background-color: #fff;
	border-radius: 12rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.expert-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.expert-detail {
	display: flex;
	flex-direction: column;
}

.expert-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 8rpx;
}

.expert-label {
	font-size: 26rpx;
	color: #999;
}

/* 评价表单 */
.evaluation-form {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 评分部分 */
.rating-section {
	margin-bottom: 30rpx;
}

.rating-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.rating-stars {
	display: flex;
	align-items: center;
}

.star-icon {
	font-size: 48rpx;
	margin-right: 10rpx;
}

.star-filled {
	color: #ffb400;
}

.star-empty {
	color: #ddd;
}

.rating-text {
	font-size: 28rpx;
	color: #666;
	margin-left: 10rpx;
}

/* 标签部分 */
.tags-section {
	margin-bottom: 30rpx;
}

.tags-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.tags-list {
	display: flex;
	flex-wrap: wrap;
}

.tag-item {
	padding: 10rpx 20rpx;
	background-color: #f5f5f5;
	border-radius: 30rpx;
	margin-right: 16rpx;
	margin-bottom: 16rpx;
	font-size: 26rpx;
	color: #666;
}

.tag-item.selected {
	background-color: #ffebcc;
	color: #ff9800;
}

/* 评价内容部分 */
.content-section {
	margin-bottom: 30rpx;
}

.content-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.content-textarea {
	width: 100%;
	height: 200rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 28rpx;
	box-sizing: border-box;
}

.word-count {
	text-align: right;
	font-size: 24rpx;
	color: #999;
	margin-top: 8rpx;
}

/* 提交按钮 */
.submit-btn {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	background-color: #ff9800;
	color: #fff;
	font-size: 30rpx;
	border-radius: 44rpx;
	text-align: center;
}

.submit-btn[disabled] {
	background-color: #ffc57d;
	color: rgba(255, 255, 255, 0.6);
}

.content-view {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx;
	font-size: 28rpx;
	line-height: 1.6;
	color: #333;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	box-sizing: border-box;
	white-space: pre-wrap;
}

.back-btn {
	margin-top: 40rpx;
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	background-color: #666;
	color: #fff;
	border-radius: 8rpx;
}

/* 查看模式下隐藏未选中的标签 */
.tag-item.hidden {
	display: none;
}
</style>
