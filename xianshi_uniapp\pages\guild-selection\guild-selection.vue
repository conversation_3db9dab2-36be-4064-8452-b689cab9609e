<template>
  <view class="container">
    <view class="header">
      <view class="title">选择公会加入</view>
      <view class="subtitle">请选择一个当地闲时公会加入，成为认证达人</view>
    </view>

    <!-- 搜索区域 -->
    <view class="search-area">
      <view class="search-box">
        <view class="search-icon">🔍</view>
        <input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索公会名称或地区" @input="filterGuilds" />
      </view>
      
      <view class="filter-tabs">
        <view 
          class="filter-tab" 
          :class="{ active: activeFilter === 'all' }" 
          @tap="setFilter('all')"
        >全部</view>
        <view 
          class="filter-tab" 
          :class="{ active: activeFilter === 'nearby' }" 
          @tap="setFilter('nearby')"
        >附近</view>
        <view 
          class="filter-tab" 
          :class="{ active: activeFilter === 'popular' }" 
          @tap="setFilter('popular')"
        >热门</view>
      </view>
    </view>

    <!-- 公会列表 -->
    <view class="guild-list">
      <block v-for="(guild, index) in filteredGuilds" :key="guild.id">
        <view class="guild-card" @tap="showGuildDetail(guild)">
          <view class="guild-info">
            <image class="guild-logo" :src="guild.logo" mode="aspectFill"></image>
            <view class="guild-details">
              <view class="guild-name">{{ guild.name }}
                <text class="official-tag" v-if="guild.isOfficial">官方</text>
              </view>
              <view class="guild-location">{{ guild.location }}</view>
              <view class="guild-stats">
                <text class="stats-item">{{ guild.memberCount }}位成员</text>
                <text class="stats-item">{{ guild.orderCount }}单/月</text>
              </view>
            </view>
          </view>
          <view class="join-btn" @tap.stop="joinGuild(guild)">加入</view>
        </view>
      </block>
      
      <!-- 无搜索结果 -->
      <view class="empty-result" v-if="filteredGuilds.length === 0">
        <view class="empty-icon">📭</view>
        <view class="empty-text">未找到符合条件的公会</view>
        <view class="empty-hint">请尝试其他搜索词或切换筛选条件</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      activeFilter: 'all',
      guildList: [
        {
          id: 'g001',
          name: '闲时北京公会',
          logo: '/static/images/default-avatar.png', // 使用默认头像
          location: '北京市海淀区',
          description: '北京地区官方公会，专注游戏陪玩与社交活动',
          memberCount: 86,
          orderCount: 324,
          isOfficial: true,
          tags: ['游戏', '桌游', '派对'],
          distance: 0.8
        },
        {
          id: 'g002',
          name: '闲时上海公会',
          logo: '/static/images/default-avatar.png', // 使用默认头像
          location: '上海市徐汇区',
          description: '上海地区官方公会，专注时尚社交与文化活动',
          memberCount: 124,
          orderCount: 467,
          isOfficial: true,
          tags: ['时尚', '摄影', '派对'],
          distance: 1200 // 单位公里
        },
        {
          id: 'g003',
          name: '闲时成都公会',
          logo: '/static/images/default-avatar.png', // 使用默认头像
          location: '成都市武侯区',
          description: '成都地区官方公会，专注休闲娱乐与美食活动',
          memberCount: 62,
          orderCount: 215,
          isOfficial: true,
          tags: ['美食', '桌游', '旅行'],
          distance: 1600
        },
        {
          id: 'g004',
          name: '闲时电竞公会',
          logo: '/static/images/default-avatar.png', // 使用默认头像
          location: '北京市朝阳区',
          description: '专注电子竞技领域，拥有多名职业电竞选手',
          memberCount: 48,
          orderCount: 187,
          isOfficial: false,
          tags: ['电竞', '游戏', '直播'],
          distance: 3.2
        },
        {
          id: 'g005',
          name: '闲时音乐公会',
          logo: '/static/images/default-avatar.png', // 使用默认头像
          location: '北京市西城区',
          description: '音乐领域专业公会，拥有众多专业音乐人',
          memberCount: 37,
          orderCount: 143,
          isOfficial: false,
          tags: ['音乐', '演奏', 'K歌'],
          distance: 5.6
        }
      ],
      filteredGuilds: []
    };
  },
  mounted() {
    // 初始加载所有公会
    this.filteredGuilds = [...this.guildList];
  },
  methods: {
    // 筛选公会
    filterGuilds() {
      // 先按关键字筛选
      let result = this.guildList.filter(guild => {
        return guild.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) || 
               guild.location.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
               guild.tags.some(tag => tag.toLowerCase().includes(this.searchKeyword.toLowerCase()));
      });
      
      // 然后根据当前选择的标签进一步筛选
      if (this.activeFilter === 'nearby') {
        result.sort((a, b) => a.distance - b.distance);
      } else if (this.activeFilter === 'popular') {
        result.sort((a, b) => b.orderCount - a.orderCount);
      }
      
      this.filteredGuilds = result;
    },
    
    // 设置筛选标签
    setFilter(filter) {
      this.activeFilter = filter;
      this.filterGuilds();
    },
    
    // 显示公会详情
    showGuildDetail(guild) {
      uni.showModal({
        title: guild.name,
        content: `${guild.description}\n\n地区：${guild.location}\n成员数：${guild.memberCount}人\n月接单量：${guild.orderCount}单`,
        confirmText: '加入该公会',
        cancelText: '返回列表',
        success: (res) => {
          if (res.confirm) {
            this.joinGuild(guild);
          }
        }
      });
    },
    
    // 加入公会
    joinGuild(guild) {
      uni.showLoading({
        title: '处理中...',
        mask: true
      });
      
      // 模拟加入公会过程
      setTimeout(() => {
        uni.hideLoading();
        
        // 保存所选公会信息到本地
        const guildInfo = {
          hasJoined: true,
          guildId: guild.id,
          guildName: guild.name,
          joinDate: new Date().toISOString(),
          memberId: "M" + Math.floor(Math.random() * 100000)
        };
        uni.setStorageSync('guildInfo', guildInfo);
        
        uni.showModal({
          title: '加入成功',
          content: `您已成功加入「${guild.name}」\n\n现在可以继续完成达人认证流程`,
          showCancel: false,
          success: () => {
            uni.navigateBack();
          }
        });
      }, 1000);
    }
  }
};
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: 30rpx;
}

.header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #999;
}

.search-area {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f2f2f2;
  border-radius: 36rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
}

.filter-tabs {
  display: flex;
  border-bottom: 1rpx solid #eee;
  padding: 0 10rpx;
}

.filter-tab {
  padding: 20rpx 10rpx;
  margin-right: 40rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.filter-tab.active {
  color: #ff8c00;
  font-weight: bold;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff8c00;
}

.guild-list {
  padding: 0 30rpx;
}

.guild-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.guild-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.guild-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}

.guild-details {
  flex: 1;
}

.guild-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.official-tag {
  display: inline-block;
  font-size: 20rpx;
  color: #fff;
  background-color: #ff8c00;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
  vertical-align: middle;
}

.guild-location {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.guild-stats {
  font-size: 24rpx;
  color: #999;
}

.stats-item {
  margin-right: 20rpx;
}

.join-btn {
  background-color: #ff8c00;
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 140, 0, 0.3);
}

.join-btn:active {
  transform: translateY(2rpx);
  opacity: 0.9;
}

.empty-result {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 120rpx;
  color: #cccccc;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #bbb;
}
</style> 