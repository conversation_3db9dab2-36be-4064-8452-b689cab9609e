package com.play.xianshibusiness.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(value = "订单报名视图对象")
public class OrderEnrollVO {
    
    @ApiModelProperty(value = "报名ID")
    private String enrollId;
    
    @ApiModelProperty(value = "订单ID")
    private String orderId;
    
    @ApiModelProperty(value = "达人ID")
    private String talentId;
    
    @ApiModelProperty(value = "会员ID（与talentId相同，用于前端统一判断）")
    private String memberId;
    
    @ApiModelProperty(value = "达人昵称")
    private String talentName;
    
    @ApiModelProperty(value = "达人头像")
    private String talentAvatar;
    
    @ApiModelProperty(value = "达人性别：1-男，2-女")
    private Integer gender;
    
    @ApiModelProperty(value = "达人年龄")
    private Integer age;
    
    @ApiModelProperty(value = "达人星座")
    private String constellation;
    
    @ApiModelProperty(value = "是否被选中")
    private Boolean isSelected;
    
    @ApiModelProperty(value = "子订单状态：0-已报名，1-被选中，2-已出发，3-已到达，4-服务中，5-服务完成，6-已评价")
    private Integer status;
    
    @ApiModelProperty(value = "状态描述")
    private String statusDesc;
    
    @ApiModelProperty(value = "报名时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "选中时间")
    private LocalDateTime selectTime;
    
    @ApiModelProperty(value = "出发时间")
    private LocalDateTime departureTime;
    
    @ApiModelProperty(value = "到达时间")
    private LocalDateTime arrivalTime;
    
    @ApiModelProperty(value = "服务开始时间")
    private LocalDateTime serviceStartTime;
    
    @ApiModelProperty(value = "服务结束时间")
    private LocalDateTime serviceEndTime;
    
    @ApiModelProperty(value = "评分")
    private Integer rating;
    
    @ApiModelProperty(value = "评价标签")
    private List<String> commentTags;
    
    @ApiModelProperty(value = "评价内容")
    private String commentContent;
    
    @ApiModelProperty(value = "评价时间")
    private LocalDateTime commentTime;
}