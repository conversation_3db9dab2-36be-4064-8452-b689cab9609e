<template>
  <view class="container">
    <view class="callback-content">
      <view class="callback-icon">
        <text class="icon">💰</text>
      </view>
      <view class="callback-title">{{ callbackTitle }}</view>
      <view class="callback-message">{{ callbackMessage }}</view>
      <view class="callback-details" v-if="callbackData">
        <view class="detail-item">
          <text class="label">订单号:</text>
          <text class="value">{{ callbackData.orderNo }}</text>
        </view>
        <view class="detail-item">
          <text class="label">交易号:</text>
          <text class="value">{{ callbackData.transactionId }}</text>
        </view>
        <view class="detail-item">
          <text class="label">支付金额:</text>
          <text class="value">¥{{ (callbackData.totalFee / 100).toFixed(2) }}</text>
        </view>
      </view>
      <view class="callback-actions">
        <button class="action-btn primary" @tap="goToCoinCenter">查看金币</button>
        <button class="action-btn secondary" @tap="goBack">返回</button>
      </view>
    </view>
  </view>
</template>

<script>
import wechatPay from '@/utils/wechat-pay.js';

export default {
  data() {
    return {
      callbackData: null,
      callbackTitle: '支付处理中...',
      callbackMessage: '正在验证支付结果，请稍候...',
      isSuccess: false
    };
  },
  onLoad(options) {
    console.log('支付回调页面参数:', options);
    this.handlePaymentCallback(options);
  },
  methods: {
    /**
     * 处理支付回调
     * @param {Object} callbackParams 回调参数
     */
    async handlePaymentCallback(callbackParams) {
      try {
        console.log('开始处理支付回调...');
        
        // 模拟回调数据（实际项目中从微信服务器接收）
        const mockCallbackData = {
          return_code: 'SUCCESS',
          return_msg: 'OK',
          result_code: 'SUCCESS',
          out_trade_no: callbackParams.orderNo || 'ORDER_' + Date.now(),
          transaction_id: 'wx' + Date.now(),
          total_fee: callbackParams.totalFee || 9800, // 98.00元，单位分
          trade_state: 'SUCCESS',
          sign: 'mock_sign_' + Date.now()
        };

        console.log('模拟回调数据:', mockCallbackData);

        // 处理支付回调
        const result = wechatPay.handlePaymentCallback(mockCallbackData);
        
        if (result.success) {
          this.callbackData = result;
          this.isSuccess = true;
          this.callbackTitle = '支付成功';
          this.callbackMessage = '您的金币充值已成功，金币已添加到您的账户中。';
          
          // 更新本地金币余额
          this.updateCoinBalance(callbackParams);
          
          console.log('支付回调处理成功:', result);
        } else {
          this.callbackTitle = '支付失败';
          this.callbackMessage = '支付验证失败，请联系客服处理。';
          console.error('支付回调处理失败:', result);
        }
        
      } catch (error) {
        console.error('处理支付回调异常:', error);
        this.callbackTitle = '处理异常';
        this.callbackMessage = '支付处理过程中出现异常，请联系客服。';
      }
    },

    /**
     * 更新金币余额
     * @param {Object} callbackParams 回调参数
     */
    updateCoinBalance(callbackParams) {
      try {
        // 从回调参数中获取充值金币数量
        const coins = callbackParams.coins || 100; // 默认100金币
        
        // 获取当前金币余额
        const currentBalance = uni.getStorageSync('coinBalance') || 0;
        
        // 更新金币余额
        const newBalance = currentBalance + parseInt(coins);
        uni.setStorageSync('coinBalance', newBalance);
        
        console.log('金币余额更新:', {
          oldBalance: currentBalance,
          addedCoins: coins,
          newBalance: newBalance
        });
        
        // 发送事件通知其他页面更新金币余额
        uni.$emit('coinBalanceUpdated', newBalance);
        
      } catch (error) {
        console.error('更新金币余额失败:', error);
      }
    },

    /**
     * 跳转到金币中心
     */
    goToCoinCenter() {
      uni.navigateTo({
        url: '/pages/coin-center/coin-center'
      });
    },

    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.callback-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  max-width: 600rpx;
  width: 100%;
}

.callback-icon {
  margin-bottom: 30rpx;
}

.callback-icon .icon {
  font-size: 80rpx;
}

.callback-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.callback-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.callback-details {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  font-size: 26rpx;
  color: #666;
}

.detail-item .value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.callback-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

.action-btn:active {
  transform: scale(0.95);
}
</style> 