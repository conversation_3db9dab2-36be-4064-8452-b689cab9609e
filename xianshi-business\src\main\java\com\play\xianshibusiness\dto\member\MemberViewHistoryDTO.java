package com.play.xianshibusiness.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 会员浏览历史DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("会员浏览历史DTO")
public class MemberViewHistoryDTO {
    
    @ApiModelProperty("浏览历史ID")
    private String id;
    
    @ApiModelProperty("会员ID")
    private String memberId;
    
    @ApiModelProperty("目标会员ID（被浏览的会员）")
    private String targetMemberId;
    
    @ApiModelProperty("目标会员昵称")
    private String targetMemberNickname;
    
    @ApiModelProperty("目标会员头像")
    private String targetMemberAvatar;
    
    @ApiModelProperty("浏览次数")
    private Integer viewCount;
    
    @ApiModelProperty("首次浏览时间")
    private LocalDateTime firstViewTime;
    
    @ApiModelProperty("最近浏览时间")
    private LocalDateTime lastViewTime;
    
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
} 