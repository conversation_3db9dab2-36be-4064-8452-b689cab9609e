package com.play.xianshibusiness.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记该方法需要登录验证
 * 只有加了此注解的方法才会验证JWT令牌
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireLogin {
    /**
     * 是否要求用户角色权限验证
     */
    boolean checkRole() default false;
    
    /**
     * 允许访问的角色，仅当checkRole=true时有效
     */
    String[] roles() default {};
} 