package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 充值套餐实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_recharge_package")
@ApiModel("充值套餐")
public class CRechargePackage extends BaseObjPo {

    @ApiModelProperty(value = "套餐名称")
    private String name;
    
    @ApiModelProperty(value = "套餐金额(元)")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "赠送金币数量")
    private BigDecimal bonusGold;
    
    @ApiModelProperty(value = "基础金币数量")
    private BigDecimal baseGold;
    
    @ApiModelProperty(value = "总金币数量")
    private BigDecimal totalGold;
    
    @ApiModelProperty(value = "折扣")
    private BigDecimal discount;
    
    @ApiModelProperty(value = "套餐描述")
    private String description;
    
    @ApiModelProperty(value = "套餐图标")
    private String icon;
    
    @ApiModelProperty(value = "是否推荐")
    private Boolean isRecommend;
    
    @ApiModelProperty(value = "排序")
    private Integer sort;
} 