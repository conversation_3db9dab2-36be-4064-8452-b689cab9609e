/**
 * 安全操作工具模块 - 提供各种类型的安全操作
 */

/**
 * 安全比较两个值是否相等，处理类型和null/undefined情况
 * @param {*} a - 第一个值
 * @param {*} b - 第二个值
 * @returns {boolean} 是否相等
 */
export const safeEqual = (a, b) => {
  // 处理null和undefined的情况
  if (a == null && b == null) {
    return true;
  }

  // 处理其中一个是null/undefined的情况
  if (a == null || b == null) {
    return false;
  }

  // 字符串比较（兼容字符串和数字类型混用的场景）
  const strA = String(a).trim();
  const strB = String(b).trim();
  return strA === strB;
}

/**
 * 安全获取对象的属性值，处理嵌套属性和可能不存在的情况
 * @param {Object} obj - 要获取属性的对象
 * @param {string} path - 属性路径，可以是点分隔的字符串或者数组
 * @param {*} defaultValue - 当属性不存在时的默认值
 * @returns {*} 属性值或默认值
 */
export const safeGet = (obj, path, defaultValue = undefined) => {
  // 处理obj为null或undefined的情况
  if (obj == null) {
    return defaultValue;
  }

  // 将路径转换为数组
  const keys = Array.isArray(path) ? path : path.split('.');
  let result = obj;

  // 遍历路径
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    // 如果当前结果为null或undefined，返回默认值
    if (result == null) {
      return defaultValue;
    }
    // 获取下一级属性
    result = result[key];
  }

  // 如果最终结果为undefined，返回默认值
  return result === undefined ? defaultValue : result;
}

/**
 * 安全设置对象的属性值，处理嵌套属性和可能不存在的情况
 * @param {Object} obj - 要设置属性的对象
 * @param {string} path - 属性路径，可以是点分隔的字符串或者数组
 * @param {*} value - 要设置的值
 * @returns {Object} 更新后的对象
 */
export const safeSet = (obj, path, value) => {
  if (obj == null) {
    return obj;
  }

  const keys = Array.isArray(path) ? path : path.split('.');
  let current = obj;

  // 遍历路径直到最后一个属性
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    // 如果当前路径不存在，创建一个空对象
    if (current[key] == null) {
      current[key] = {};
    }
    current = current[key];
  }

  // 设置最后一个属性的值
  const lastKey = keys[keys.length - 1];
  current[lastKey] = value;

  return obj;
}

/**
 * 安全移除对象中的属性，处理嵌套属性
 * @param {Object} obj - 要移除属性的对象
 * @param {string} path - 属性路径，可以是点分隔的字符串或者数组
 * @returns {Object} 更新后的对象
 */
export const safeRemove = (obj, path) => {
  if (obj == null) {
    return obj;
  }

  const keys = Array.isArray(path) ? path : path.split('.');
  let current = obj;

  // 遍历路径直到最后一个属性
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    // 如果当前路径不存在，无需继续
    if (current[key] == null) {
      return obj;
    }
    current = current[key];
  }

  // 移除最后一个属性
  const lastKey = keys[keys.length - 1];
  delete current[lastKey];

  return obj;
}

/**
 * 安全深拷贝对象，处理循环引用
 * @param {*} obj - 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
export const safeClone = (obj) => {
  if (obj == null || typeof obj !== 'object') {
    return obj;
  }

  // 使用WeakMap处理循环引用
  const visited = new WeakMap();

  function clone(item) {
    // 非对象直接返回
    if (item == null || typeof item !== 'object') {
      return item;
    }

    // 处理Date对象
    if (item instanceof Date) {
      return new Date(item);
    }

    // 处理正则表达式
    if (item instanceof RegExp) {
      return new RegExp(item.source, item.flags);
    }

    // 处理数组
    if (Array.isArray(item)) {
      return item.map(i => clone(i));
    }

    // 检查循环引用
    if (visited.has(item)) {
      return visited.get(item);
    }

    // 创建新对象
    const copy = {};
    visited.set(item, copy);

    // 复制所有属性
    Object.keys(item).forEach(key => {
      copy[key] = clone(item[key]);
    });

    return copy;
  }

  return clone(obj);
}

/**
 * 安全合并对象，处理null和undefined
 * @param {Object} target - 目标对象
 * @param {...Object} sources - 源对象数组
 * @returns {Object} 合并后的对象
 */
export const safeMerge = (target, ...sources) => {
  if (target == null) {
    target = {};
  }

  // 过滤掉null和undefined的源对象
  const validSources = sources.filter(source => source != null);
  
  // 如果没有有效的源对象，直接返回目标对象
  if (validSources.length === 0) {
    return target;
  }

  // 遍历所有有效的源对象
  validSources.forEach(source => {
    Object.keys(source).forEach(key => {
      const targetValue = target[key];
      const sourceValue = source[key];

      // 如果两者都是对象且不是数组，递归合并
      if (
        targetValue != null && typeof targetValue === 'object' && !Array.isArray(targetValue) &&
        sourceValue != null && typeof sourceValue === 'object' && !Array.isArray(sourceValue)
      ) {
        target[key] = safeMerge(targetValue, sourceValue);
      } else {
        // 否则直接覆盖
        target[key] = sourceValue;
      }
    });
  });

  return target;
}

/**
 * 安全转换为数字，处理无效输入
 * @param {*} value - 要转换的值
 * @param {number} defaultValue - 转换失败时的默认值
 * @returns {number} 转换后的数字或默认值
 */
export const safeNumber = (value, defaultValue = 0) => {
  if (value == null) {
    return defaultValue;
  }

  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 安全格式化字符串，替换参数
 * @param {string} template - 模板字符串，使用{0}、{1}等占位符
 * @param {...*} args - 替换参数
 * @returns {string} 格式化后的字符串
 */
export const safeFormat = (template, ...args) => {
  if (template == null) {
    return '';
  }

  return String(template).replace(/{(\d+)}/g, (match, index) => {
    const argIndex = Number(index);
    return argIndex < args.length ? (args[argIndex] == null ? '' : String(args[argIndex])) : match;
  });
}

/**
 * 安全检查对象是否有特定属性，处理嵌套属性
 * @param {Object} obj - 要检查的对象
 * @param {string} path - 属性路径
 * @returns {boolean} 是否存在该属性
 */
export const safeHas = (obj, path) => {
  if (obj == null) {
    return false;
  }

  const keys = Array.isArray(path) ? path : path.split('.');
  let current = obj;

  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    
    if (current == null || !Object.prototype.hasOwnProperty.call(current, key)) {
      return false;
    }
    
    current = current[key];
  }

  return true;
}