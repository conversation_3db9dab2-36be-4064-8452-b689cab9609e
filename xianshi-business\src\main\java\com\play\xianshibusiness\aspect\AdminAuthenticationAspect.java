package com.play.xianshibusiness.aspect;

import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.enums.ResultCode;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.utils.JwtSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.Optional;

/**
 * 管理员身份验证切面
 * 拦截标记了@RequireAdmin注解的方法，进行JWT令牌验证
 */
@Aspect
@Component
@Slf4j
public class AdminAuthenticationAspect {

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String ADMIN_ID_HEADER = "AdminId";
    private static final String ROLES_HEADER = "Roles";
    private static final String PERMISSIONS_HEADER = "Permissions";

    /**
     * 环绕通知，拦截标记了@RequireAdmin注解的方法
     */
    @Around("@annotation(com.play.xianshibusiness.annotation.RequireAdmin) || @within(com.play.xianshibusiness.annotation.RequireAdmin)")
    public Object checkAdminAuth(ProceedingJoinPoint joinPoint) throws Throwable {
        log.debug("进入管理员身份验证切面: {}", joinPoint.getSignature());
        
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取注解信息（优先从方法上获取，如果没有则从类上获取）
        RequireAdmin requireAdmin = method.getAnnotation(RequireAdmin.class);
        if (requireAdmin == null) {
            requireAdmin = method.getDeclaringClass().getAnnotation(RequireAdmin.class);
        }
        
        if (requireAdmin == null) {
            // 如果没有注解（理论上不会发生，因为切点表达式已经过滤），直接执行原方法
            log.debug("未找到RequireAdmin注解，直接执行原方法");
            return joinPoint.proceed();
        }
        
        // 从请求中获取令牌
        HttpServletRequest request = getRequest();
        if (request == null) {
            log.error("无法获取请求信息");
            throw new GlobalException(ResultCode.INVALID_TOKEN, "无法获取请求信息");
        }

        log.debug("请求URI: {}, 方法: {}", request.getRequestURI(), request.getMethod());
        String token = request.getHeader(AUTHORIZATION_HEADER);
        if (token == null || token.isEmpty()) {
            log.error("请求未提供认证令牌");
            throw new GlobalException(ResultCode.INVALID_TOKEN, "未提供认证令牌");
        }
        log.debug("获取到令牌: {}", token);
        
        // 验证令牌
        Authentication authentication = JwtSignUtils.validateToken(token);
        if (authentication == null) {
            log.error("无效的认证令牌");
            throw new GlobalException(ResultCode.INVALID_TOKEN, "无效的认证令牌");
        }
        log.debug("令牌验证通过: {}", authentication);
        
        // 检查是否包含adminId声明
        String adminId = extractAdminIdFromToken(token);
        if (adminId == null) {
            log.error("非管理员令牌");
            throw new GlobalException(ResultCode.INVALID_TOKEN, "非管理员令牌");
        }
        log.debug("从令牌中提取到管理员ID: {}", adminId);
        
        // 设置管理员ID到请求头，供后续使用
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest()
            .setAttribute(ADMIN_ID_HEADER, adminId);

        // 设置认证信息到安全上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);
        log.debug("已设置认证信息到安全上下文");
        
        // 如果需要检查角色
        if (requireAdmin.checkRole() && requireAdmin.roles().length > 0) {
            boolean hasRequiredRole = false;
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            log.debug("需要检查角色: {}, 用户拥有的权限: {}", Arrays.toString(requireAdmin.roles()), authorities);
            
            for (String requiredRole : requireAdmin.roles()) {
                // 检查是否有要求的任一角色
                if (authorities.stream().anyMatch(auth -> auth.getAuthority().equals(requiredRole) || 
                        auth.getAuthority().equals("ROLE_" + requiredRole))) {
                    hasRequiredRole = true;
                    log.debug("找到匹配的角色: {}", requiredRole);
                    break;
                }
            }
            
            if (!hasRequiredRole) {
                log.warn("管理员 {} 没有所需角色: {}", adminId, Arrays.toString(requireAdmin.roles()));
                throw new GlobalException(ResultCode.Login_No_Role, "没有所需的管理员角色");
            }
        }
        
        // 验证通过，执行原方法
        log.debug("管理员身份验证通过，执行原方法");
        return joinPoint.proceed();
    }
    
    /**
     * 从JWT令牌中提取管理员ID
     */
    private String extractAdminIdFromToken(String token) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return null;
            }
            
            // 从JWT中提取adminId
            String actualToken = token.replace("Bearer ", "");
            return JwtSignUtils.getAdminIdFromToken(actualToken);
        } catch (Exception e) {
            log.error("从令牌中提取管理员ID失败", e);
            return null;
        }
    }
    
    /**
     * 获取当前请求
     */
    private HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return Optional.ofNullable(attributes)
            .map(ServletRequestAttributes::getRequest)
            .orElse(null);
    }
} 