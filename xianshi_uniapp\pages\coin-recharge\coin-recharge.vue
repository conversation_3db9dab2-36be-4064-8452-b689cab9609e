<template>
  <view class="container">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">充值</view>
      <view class="nav-actions">
        <text class="nav-icon">⋮</text>
        <text class="nav-icon">─</text>
        <text class="nav-icon">●</text>
      </view>
    </view>

    <!-- 激励横幅 -->
    <view class="motivation-banner">
      <view class="banner-content">
        <view class="banner-text">
          <text class="banner-title">您的支持</text>
          <text class="banner-subtitle">是我们继续的动力</text>
        </view>
        <view class="banner-graphic">
          <view class="coin-graphic">
            <text class="coin-logo">M</text>
            <text class="heart-icon">♥</text>
          </view>
          <view class="city-graphic"></view>
        </view>
      </view>
    </view>

    <!-- 充值选项 -->
    <view class="recharge-options">
      <view
        v-for="(item, index) in rechargeOptions"
        :key="index"
        :class="['recharge-item', {'selected': selectedOption === index}]"
        @click="selectOption"
        :data-index="index"
      >
        <view class="option-tag" v-if="item.tag">{{ item.tag }}</view>
        <view class="option-content">
          <view class="option-coins">{{ item.coins }}金币</view>
          <view class="option-price">¥{{ item.price }}</view>
        </view>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="warm-reminder">
      <view class="reminder-title">温馨提示:</view>
      <view class="reminder-list">
        <view class="reminder-item">
          <text class="item-number">1.</text>
          <text class="item-text">金币为本平台向您提供的用于在本平台上进行相关消费的虚拟货币,充值成功后不支持兑换人民币、转让,不支持退款,充值前请慎重选择。</text>
        </view>
        <view class="reminder-item">
          <text class="item-number">2.</text>
          <text class="item-text">金币可购买的产品/服务范围以本平台相关页面展示的可使用金币购买的产品/服务为准。</text>
        </view>
        <view class="reminder-item">
          <text class="item-number">3.</text>
          <text class="item-text">模卡不鼓励未成年人使用充值服务。</text>
        </view>
      </view>
      <view class="agreement-text">
        充值即代表同意平台<text class="agreement-link">《用户充值协议》</text>
      </view>
    </view>

    <!-- 充值按钮 -->
    <view class="recharge-button-container">
      <button class="recharge-btn" @click="showPaymentModal">立即充值</button>
    </view>

    <!-- 支付方式弹窗 -->
    <view class="payment-modal" v-if="showPaymentModal_flag">
      <view class="payment-mask" @click="hidePaymentModal"></view>
      <view class="payment-container">
        <view class="payment-header">
          <text class="payment-title">选择支付方式</text>
          <view class="payment-close" @click="hidePaymentModal">×</view>
        </view>
        <view class="payment-amount">
          <text class="payment-label">支付金额</text>
          <text class="payment-price" v-if="selectedPayment !== 'coupon'"
            >¥{{ rechargeOptions[selectedOption].price }}</text
          >
          <text class="payment-free" v-else>¥0 (使用金币券)</text>
        </view>
        <view class="payment-methods">
          <view
            class="payment-method"
            @click="selectPayment"
            data-method="wechat"
          >
            <view class="method-icon wechat-icon">
              <text class="fab fa-weixin"></text>
            </view>
            <view class="method-info">
              <text class="method-name">微信支付</text>
              <text class="method-desc">推荐使用微信支付</text>
            </view>
            <view
              :class="['method-select', {'selected': selectedPayment === 'wechat'}]"
            >
              <text class="fas fa-check"></text>
            </view>
          </view>
          <view
            class="payment-method"
            @click="selectPayment"
            data-method="alipay"
          >
            <view class="method-icon alipay-icon">
              <text class="fab fa-alipay"></text>
            </view>
            <view class="method-info">
              <text class="method-name">支付宝</text>
              <text class="method-desc">使用支付宝付款</text>
            </view>
            <view
              :class="['method-select', {'selected': selectedPayment === 'alipay'}]"
            >
              <text class="fas fa-check"></text>
            </view>
          </view>
          <!-- 金币券支付 -->
          <view class="payment-method" @click="showCouponSelect">
            <view class="method-icon coupon-icon">
              <text class="fas fa-ticket-alt"></text>
            </view>
            <view class="method-info">
              <text class="method-name">金币券支付</text>
              <text class="method-desc" v-if="!selectedCoupon"
                >仅可使用{{
                  rechargeOptions[selectedOption].coins
                }}金币面值券</text
              >
              <text class="method-desc" v-else
                >已选{{ selectedCoupon.value }}金币券(免费)</text
              >
            </view>
            <view
              :class="['method-select', {'selected': selectedPayment === 'coupon'}]"
            >
              <text class="fas fa-check"></text>
            </view>
          </view>
        </view>
        <button class="confirm-payment-btn" @click="confirmPayment">
          {{ selectedPayment === "coupon" ? "确认兑换" : "确认支付" }}
        </button>
      </view>
    </view>

    <!-- 金币券选择弹窗 -->
    <view class="coupon-modal" v-if="showCouponModal">
      <view class="payment-mask" @click="hideCouponSelect"></view>
      <view class="coupon-container">
        <view class="payment-header">
          <text class="payment-title">选择金币券</text>
          <view class="payment-close" @click="hideCouponSelect">×</view>
        </view>
        <view class="coupon-list">
          <view v-if="availableCoupons.length === 0" class="no-coupons">
            <text>暂无可用金币券</text>
          </view>
          <view
            v-for="item in availableCoupons"
            :key="item.id"
            :class="['coupon-item', {'selected': item.selected}]"
            @click="selectCoupon"
            :data-id="item.id"
          >
            <view class="coupon-left">
              <text class="coupon-value">{{ item.value }}</text>
              <text class="coupon-unit">金币券</text>
            </view>
            <view class="coupon-right">
              <text class="coupon-name">{{ item.value }}金币充值券</text>
              <text class="coupon-desc">可抵扣{{ item.value }}金币充值</text>
            </view>
            <view class="coupon-check">
              <text class="fas fa-check"></text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import wechatPay from '@/utils/wechat-pay.js';

export default {
  data() {
    return {
      coinBalance: 0, // 用户金币余额
      rechargeOptions: [
        // 充值选项 - 根据图片调整
        { coins: 100, price: "98.00", tag: "" },
        { coins: 210, price: "188.00", tag: "9折" },
        { coins: 460, price: "388.00", tag: "8.5折" },
        { coins: 620, price: "488.00", tag: "8折" },
      ],
      selectedOption: 0, // 默认选中的充值选项
      showPaymentModal_flag: false, // 是否显示支付弹窗
      showCouponModal: false, // 是否显示金币券弹窗
      selectedPayment: "wechat", // 默认支付方式
      coinCoupons: [
        // 金币券列表
        { id: 1, value: 100, selected: false },
        { id: 2, value: 210, selected: false },
        { id: 3, value: 460, selected: false },
        { id: 4, value: 620, selected: false },
      ],
      selectedCoupon: null, // 当前选中的金币券
      availableCoupons: [], // 可用的金币券
    };
  },
  onLoad() {
    // 从本地存储获取金币余额
    const coinBalance = uni.getStorageSync("coinBalance") || 100;
    this.coinBalance = coinBalance;
  },
  methods: {
    // 选择充值选项
    selectOption(e) {
      const index = e.currentTarget.dataset.index;
      this.selectedOption = index;
    },

    // 显示支付弹窗
    showPaymentModal() {
      this.showPaymentModal_flag = true;
      this.selectedPayment = "wechat"; // 默认选择微信支付
    },

    // 隐藏支付弹窗
    hidePaymentModal() {
      this.showPaymentModal_flag = false;
    },

    // 选择支付方式
    selectPayment(e) {
      const method = e.currentTarget.dataset.method;
      this.selectedPayment = method;
    },

    // 显示金币券弹窗
    showCouponSelect() {
      // 获取当前选择的充值选项金币数量
      const selectedOption = this.rechargeOptions[this.selectedOption];
      const selectedCoins = selectedOption.coins;

      // 过滤出对应面值的金币券
      const availableCoupons = this.coinCoupons.filter(
        (coupon) => coupon.value === selectedCoins
      );

      if (availableCoupons.length === 0) {
        uni.showToast({
          title: `暂无${selectedCoins}金币面值的金币券`,
          icon: "none",
        });
        return;
      }

      // 设置可用的金币券
      this.availableCoupons = availableCoupons;
      this.showCouponModal = true;
    },

    // 隐藏金币券弹窗
    hideCouponSelect() {
      this.showCouponModal = false;
    },

    // 选择金币券
    selectCoupon(e) {
      const couponId = parseInt(e.currentTarget.dataset.id);
      const coupons = [...this.coinCoupons];

      // 取消之前选中的券
      coupons.forEach((coupon) => {
        coupon.selected = coupon.id === couponId;
      });

      // 找到选中的券
      const selectedCoupon = coupons.find((coupon) => coupon.id === couponId);

      this.coinCoupons = coupons;
      this.selectedCoupon = selectedCoupon;
      this.selectedPayment = "coupon";
      this.showCouponModal = false;
    },

    // 确认金币券支付
    confirmCouponPayment() {
      if (!this.selectedCoupon) {
        uni.showToast({
          title: "请先选择金币券",
          icon: "none",
        });
        return;
      }

      const option = this.rechargeOptions[this.selectedOption];

      // 验证金币券面值是否与充值选项匹配
      if (this.selectedCoupon.value !== option.coins) {
        uni.showToast({
          title: `该金币券只能用于${this.selectedCoupon.value}金币充值`,
          icon: "none",
        });
        return;
      }

      uni.showLoading({ title: "处理中..." });

      // 模拟使用金币券支付
      setTimeout(() => {
        // 更新金币余额
        const newBalance = this.coinBalance + option.coins;
        uni.setStorageSync("coinBalance", newBalance);

        // 移除已使用的金币券
        const coupons = this.coinCoupons.filter(
          (coupon) => coupon.id !== this.selectedCoupon.id
        );

        this.coinCoupons = coupons;
        this.selectedCoupon = null;
        this.coinBalance = newBalance;
        this.showPaymentModal_flag = false;

        uni.hideLoading();
        uni.showToast({
          title: "充值成功",
          icon: "success",
          success: () => {
            // 短暂延迟后返回上一页
            setTimeout(() => {
              // 返回到上一页，让上一页刷新金币余额
              uni.navigateBack();
            }, 1500);
          },
        });
      }, 1000);
    },

    // 确认支付
    confirmPayment() {
      const option = this.rechargeOptions[this.selectedOption];
      const paymentMethod = this.selectedPayment;

      uni.showLoading({ title: "处理中..." });

      // 根据支付方式调用不同的支付接口
      if (paymentMethod === "wechat") {
        this.processWechatPay(option);
      } else if (paymentMethod === "alipay") {
        this.processAlipayPay(option);
      } else if (paymentMethod === "coupon") {
        this.confirmCouponPayment();
        return; // 提前返回避免重复处理
      }
    },

    // 处理微信支付
    async processWechatPay(option) {
      try {
        // 获取用户openid（实际项目中需要从用户信息中获取）
        const userOpenid = uni.getStorageSync('userOpenid') || 'mock_openid_' + Date.now();
        
        // 生成订单号
        const orderNo = 'ORDER_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        // 构建订单信息
        const orderInfo = {
          orderNo: orderNo,
          totalFee: Math.round(parseFloat(option.price) * 100), // 转换为分
          body: `${option.coins}金币充值`,
          openid: userOpenid
        };

        console.log('微信支付订单信息:', orderInfo);

        // 调用微信支付
        const payResult = await wechatPay.requestPayment(orderInfo);
        
        if (payResult.success) {
          // 支付成功，处理业务逻辑
          console.log('微信支付成功:', payResult);
          
          // 查询订单状态确认支付
          const orderStatus = await wechatPay.queryOrder(orderNo);
          
          if (orderStatus.trade_state === 'SUCCESS') {
            // 支付确认成功，更新金币余额
            this.processPaymentSuccess(option);
          } else {
            throw new Error('支付状态异常');
          }
        } else {
          throw new Error('支付失败');
        }
        
      } catch (error) {
        console.error('微信支付失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '支付失败: ' + error.message,
          icon: 'none'
        });
      }
    },

    // 处理支付宝支付
    processAlipayPay(option) {
      // 这里应该调用真实的支付宝支付接口
      // 模拟支付过程
      setTimeout(() => {
        this.processPaymentSuccess(option);
      }, 1500);
    },

    // 处理支付成功
    processPaymentSuccess(option) {
      // 更新金币余额
      const newBalance = this.coinBalance + option.coins;
      uni.setStorageSync("coinBalance", newBalance);

      this.coinBalance = newBalance;
      this.showPaymentModal_flag = false;

      uni.hideLoading();

      // 显示充值成功提示
      uni.showToast({
        title: "充值成功",
        icon: "success",
        success: () => {
          // 短暂延迟后返回上一页
          setTimeout(() => {
            // 返回到上一页，让上一页刷新金币余额
            uni.navigateBack();
          }, 1500);
        },
      });
    },

    // 返回上一页
    goBack() {
      if (this.showPaymentModal_flag) {
        // 如果支付弹窗打开，先关闭弹窗
        this.hidePaymentModal();
        return;
      }

      // 返回上一页面
      uni.navigateBack({
        fail: function () {
          // 如果返回失败（没有上一页），则跳转到首页
          uni.navigateTo({
            url: "/pages/index/index",
          });
        },
      });
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
  padding-bottom: 40rpx;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: relative;
  border-bottom: 1rpx solid #f0f0f0;
}

.nav-back {
  font-size: 36rpx;
  color: #333;
  width: 60rpx;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 10rpx;
}

.nav-icon {
  font-size: 24rpx;
  color: #666;
  width: 30rpx;
  text-align: center;
}

/* 激励横幅 */
.motivation-banner {
  background: linear-gradient(135deg, #fff8e8, #fff);
  padding: 40rpx 30rpx;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 140, 0, 0.1);
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.banner-text {
  flex: 1;
}

.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 8rpx;
}

.banner-subtitle {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.banner-graphic {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20rpx;
}

.coin-graphic {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.coin-logo {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.heart-icon {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 20rpx;
  color: #ff4757;
}

.city-graphic {
  width: 60rpx;
  height: 30rpx;
  background: linear-gradient(90deg, #74b9ff, #0984e3);
  border-radius: 8rpx;
  position: relative;
}

.city-graphic::before {
  content: "";
  position: absolute;
  top: -8rpx;
  left: 8rpx;
  width: 8rpx;
  height: 8rpx;
  background: #74b9ff;
  border-radius: 2rpx;
}

.city-graphic::after {
  content: "";
  position: absolute;
  top: -8rpx;
  right: 8rpx;
  width: 8rpx;
  height: 8rpx;
  background: #74b9ff;
  border-radius: 2rpx;
}

/* 充值选项 */
.recharge-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin: 30rpx;
  padding: 0 20rpx;
}

.recharge-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  position: relative;
  border: 2rpx solid #eee;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  text-align: center;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.recharge-item.selected {
  border-color: #ff8c00;
  background-color: #fff9f2;
}

.option-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #ff8c00;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 0 10rpx 0 10rpx;
}

.option-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.option-coins {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.option-price {
  font-size: 24rpx;
  color: #ff8c00;
  font-weight: 500;
}

/* 温馨提示 */
.warm-reminder {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.reminder-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.reminder-list {
  margin-bottom: 20rpx;
}

.reminder-item {
  display: flex;
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.item-number {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
  flex-shrink: 0;
  width: 30rpx;
}

.item-text {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.agreement-link {
  color: #1677ff;
}

/* 充值按钮 */
.recharge-button-container {
  padding: 0 30rpx;
  margin-top: 40rpx;
}

.recharge-btn {
  background-color: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  height: 88rpx;
  line-height: 88rpx;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
}

/* 支付弹窗样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.payment-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.payment-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.payment-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.payment-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-amount {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-label {
  font-size: 28rpx;
  color: #666;
}

.payment-price {
  font-size: 36rpx;
  color: #ff8c00;
  font-weight: 500;
}

.payment-free {
  font-size: 36rpx;
  color: #09bb07;
  font-weight: 500;
}

.payment-methods {
  margin-bottom: 40rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.payment-method.disabled {
  opacity: 0.6;
}

.method-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 40rpx;
}

.wechat-icon {
  background-color: #07c160;
  color: #fff;
}

.alipay-icon {
  background-color: #1677ff;
  color: #fff;
}

.coupon-icon {
  background-color: #ff8c00;
  color: #fff;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 6rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.method-select {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: transparent;
}

.method-select.selected {
  background-color: #ff8c00;
  border-color: #ff8c00;
  color: #fff;
}

.confirm-payment-btn {
  background-color: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  height: 88rpx;
  line-height: 88rpx;
  width: 90%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 金币券选择弹窗 */
.coupon-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.coupon-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  animation: slideUp 0.3s ease-out;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.coupon-list {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx 0;
  max-height: 60vh;
}

.no-coupons {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
  font-size: 28rpx;
}

.coupon-item {
  margin-bottom: 30rpx;
  background: linear-gradient(90deg, #fff8e8 30%, #fff 70%);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  height: 180rpx;
  display: flex;
  position: relative;
  border: 2rpx solid #ffe0b2;
  overflow: hidden;
}

.coupon-item.selected {
  border-color: #ff8c00;
  background: linear-gradient(90deg, #fff0d6 30%, #fff8e8 70%);
}

.coupon-item::before {
  content: "";
  position: absolute;
  left: 30%;
  top: -10rpx;
  bottom: -10rpx;
  width: 2rpx;
  background: repeating-linear-gradient(
    to bottom,
    #ffe0b2 0,
    #ffe0b2 6rpx,
    transparent 6rpx,
    transparent 12rpx
  );
  z-index: 1;
}

.coupon-left {
  width: 30%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  padding-right: 20rpx;
}

.coupon-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff8c00;
}

.coupon-unit {
  font-size: 20rpx;
  color: #ff8c00;
  margin-top: 6rpx;
}

.coupon-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-left: 40rpx;
}

.coupon-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.coupon-desc {
  font-size: 24rpx;
  color: #999;
}

.coupon-check {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: transparent;
}

.coupon-check.selected {
  background-color: #ff8c00;
  border-color: #ff8c00;
  color: #fff;
}
</style>
