package com.play.xianshiapp.controller.dataDict;



import com.play.xianshibusiness.service.TDataDictTypeService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * (TDatadictType)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:16
 */
@RestController
@RequestMapping("/app/datadict")
@Api(tags = "数据字典模块-API")
public class TDataDictTypeController {
    @Resource
    private TDataDictTypeService tDataDictTypeService;

}

