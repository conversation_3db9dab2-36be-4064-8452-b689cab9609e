package com.play.xianshibusiness.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 订单评价DTO
 */
@Data
@ApiModel("订单评价DTO")
public class OrderCommentDTO {

    @ApiModelProperty(value = "订单报名详情ID", required = true)
    @NotBlank(message = "订单报名详情ID不能为空")
    private String enrollDetailId;
    
    @ApiModelProperty(value = "评价星级", required = true)
    @NotNull(message = "评价星级不能为空")
    @Min(value = 1, message = "评价星级最小为1")
    @Max(value = 5, message = "评价星级最大为5")
    private Integer stars;
    
    @ApiModelProperty(value = "评价标签列表")
    private List<String> tagList;
    
    @ApiModelProperty(value = "评价内容")
    private String comment;
} 