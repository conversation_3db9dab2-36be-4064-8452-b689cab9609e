package com.play.xianshibusiness.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.enums.OrderDataBaseType;
import com.play.xianshibusiness.enums.OrderEnrollDetailStatusEnum;
import com.play.xianshibusiness.enums.OrderStatusEnum;
import com.play.xianshibusiness.mapper.BOrderStatusMapper;
import com.play.xianshibusiness.pojo.BOrderStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 订单状态流转服务
 */
@Service
public class BOrderStatusService {

    @Resource
    private BOrderStatusMapper orderStatusMapper;

    /**
     * 记录订单主表状态变更
     * @param orderId 订单ID
     * @param status 目标状态
     */
    public void recordOrderStatus(String orderId, OrderStatusEnum status) {
        BOrderStatus orderStatus = new BOrderStatus();
        orderStatus.setId(UUID.randomUUID().toString());
        orderStatus.setType(OrderDataBaseType.Order);
        orderStatus.setBussinessId(orderId);
        orderStatus.setStatus(status.ordinal());
        orderStatus.setDescription(status.getDesc());
        orderStatus.setCreateTime(LocalDateTime.now());
        orderStatus.setUpdateTime(LocalDateTime.now());
        orderStatus.setDeleted(false);
        orderStatus.setAvailable(true);
        
        orderStatusMapper.insert(orderStatus);
    }
    
    /**
     * 记录子订单状态变更
     * @param detailId 子订单ID
     * @param status 目标状态
     */
    public void recordOrderDetailStatus(String detailId, OrderEnrollDetailStatusEnum status) {
        BOrderStatus orderStatus = new BOrderStatus();
        orderStatus.setId(UUID.randomUUID().toString());
        orderStatus.setType(OrderDataBaseType.Order_Detail);
        orderStatus.setBussinessId(detailId);
        orderStatus.setStatus(status.ordinal());
        orderStatus.setDescription(status.getDesc());
        orderStatus.setCreateTime(LocalDateTime.now());
        orderStatus.setUpdateTime(LocalDateTime.now());
        orderStatus.setDeleted(false);
        orderStatus.setAvailable(true);
        
        orderStatusMapper.insert(orderStatus);
    }
    
    /**
     * 获取订单状态流转记录
     * @param orderId 订单ID
     * @return 状态流转记录列表
     */
    public List<BOrderStatus> getOrderStatusRecords(String orderId) {
        // 查询主订单状态记录
        List<BOrderStatus> orderStatusList = orderStatusMapper.selectList(
                new LambdaQueryWrapper<BOrderStatus>()
                        .eq(BOrderStatus::getType, OrderDataBaseType.Order.getValue())
                        .eq(BOrderStatus::getBussinessId, orderId)
                        .eq(BOrderStatus::getDeleted, false)
                        .orderByDesc(BOrderStatus::getCreateTime)
        );
        
        // 查询子订单状态记录
        List<BOrderStatus> detailStatusList = orderStatusMapper.selectOrderDetailStatusByOrderId(orderId);
        
        // 合并两个列表
        if (detailStatusList != null && !detailStatusList.isEmpty()) {
            orderStatusList.addAll(detailStatusList);
        }
        
        return orderStatusList;
    }
}

