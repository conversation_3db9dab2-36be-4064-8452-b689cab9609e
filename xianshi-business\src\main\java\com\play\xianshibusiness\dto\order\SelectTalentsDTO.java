package com.play.xianshibusiness.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 选择达人DTO
 */
@Data
@ApiModel("选择达人DTO")
public class SelectTalentsDTO {

    @ApiModelProperty(value = "订单ID", required = true)
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    @ApiModelProperty(value = "达人ID列表", required = true)
    @NotEmpty(message = "达人ID列表不能为空")
    @Size(min = 1, message = "至少选择一个达人")
    private List<String> memberIds;
} 