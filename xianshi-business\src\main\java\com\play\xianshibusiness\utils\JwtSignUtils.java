//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.play.xianshibusiness.utils;

import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.pojo.Admin;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Arrays;

/**
 * JWT token生成工具
 */
@Slf4j
@Component
public class JwtSignUtils {

    // JWT密钥（固定密钥，确保应用重启后token仍然有效）
    private static final String SECRET_KEY_STRING = "xianshi-jwt-secret-key-2025-very-long-and-secure-key-for-production-use-only";
    private static final Key SECRET_KEY = Keys.hmacShaKeyFor(SECRET_KEY_STRING.getBytes());

    // Token有效期（7天）
    private static final long EXPIRATION_TIME = 7 * 24 * 60 * 60 * 1000;

    // Token前缀
    private static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 根据会员信息创建JWT令牌
     * 
     * @param cMember 会员信息
     * @return JWT令牌字符串
     */
    public static String createJWtTokenByMemberInfo(CMember cMember) {
        if (cMember == null) {
            return null;
        }

        // 设置权限
        List<String> authorities = Arrays.asList("ROLE_USER");

        // 创建JWT令牌
        String token = Jwts.builder()
                .setSubject(cMember.getId())
                .claim("memberId", cMember.getId())
                .claim("tel", cMember.getTel())
                .claim("nickname", cMember.getNickname())
                .claim("authorities", authorities)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATION_TIME))
                .signWith(SECRET_KEY)
                .compact();

        log.info("Created JWT token for member: {}", cMember.getId());
        return TOKEN_PREFIX + token;
    }

    /**
     * 根据管理员信息创建JWT令牌
     * 
     * @param admin 管理员信息
     * @return JWT令牌字符串
     */
    public static String createJWtTokenByAdminInfo(Admin admin) {
        if (admin == null) {
            return null;
        }

        // 解析角色字符串为列表
        List<String> roles = Arrays.asList(admin.getRoles().split(","));

        // 创建JWT令牌
        String token = Jwts.builder()
                .setSubject(admin.getId())
                .claim("adminId", admin.getId())
                .claim("username", admin.getUsername())
                .claim("nickname", admin.getNickname())
                .claim("roles", roles)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATION_TIME))
                .signWith(SECRET_KEY)
                .compact();

        log.info("Created JWT token for admin: {}", admin.getId());
        return TOKEN_PREFIX + token;
    }

    /**
     * 验证JWT令牌并提取认证信息
     * 
     * @param token JWT令牌
     * @return 认证对象
     */
    public static Authentication validateToken(String token) {
        if (token == null || !token.startsWith(TOKEN_PREFIX)) {
            log.debug("令牌为空或不是Bearer格式: {}", token);
            return null;
        }

        try {
            // 去掉前缀
            String actualToken = token.replace(TOKEN_PREFIX, "");
            log.debug("正在验证令牌: {}", actualToken);

            // 解析JWT令牌
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(SECRET_KEY)
                    .build()
                    .parseClaimsJws(actualToken)
                    .getBody();

            // 提取会员ID
            String memberId = claims.getSubject();
            log.debug("从令牌中提取到会员ID: {}", memberId);

            // 检查是否包含authorities或roles声明
            List<String> authorities;

            if (claims.containsKey("authorities")) {
                // App用户权限声明
                @SuppressWarnings("unchecked")
                List<String> authList = claims.get("authorities", List.class);
                authorities = authList != null ? authList : new ArrayList<>();
                log.debug("从令牌中提取到用户权限: {}", authorities);
            } else if (claims.containsKey("roles")) {
                // 管理员角色声明
                @SuppressWarnings("unchecked")
                List<String> roleList = claims.get("roles", List.class);
                authorities = roleList != null ? roleList.stream()
                        .map(role -> "ROLE_" + role)
                        .collect(Collectors.toList()) : new ArrayList<>();
                log.debug("从令牌中提取到管理员角色: {}", authorities);
            } else {
                // 没有找到权限信息，使用空列表
                authorities = new ArrayList<>();
                log.debug("令牌中未找到任何权限信息");
            }

            if (memberId != null) {
                // 创建认证对象
                Authentication authentication = new UsernamePasswordAuthenticationToken(
                        memberId,
                        null,
                        authorities.stream()
                                .map(SimpleGrantedAuthority::new)
                                .collect(Collectors.toList()));
                log.debug("创建认证对象成功: {}", authentication);
                return authentication;
            } else {
                log.debug("令牌中未找到memberId");
            }
        } catch (ExpiredJwtException e) {
            log.warn("JWT令牌已过期: {}", e.getMessage());
        } catch (io.jsonwebtoken.security.SignatureException e) {
            log.warn("JWT令牌签名无效，可能是密钥更改导致，请重新登录: {}", e.getMessage());
            // 对于签名异常，抛出特定异常让前端知道需要清除token重新登录
            throw new RuntimeException("INVALID_SIGNATURE");
        } catch (Exception e) {
            log.error("JWT令牌验证错误: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 从JWT中提取会员ID
     * 
     * @param token JWT令牌
     * @return 会员ID
     */
    public static String getMemberIdFromToken(String token) {
        if (token == null || !token.startsWith(TOKEN_PREFIX)) {
            return null;
        }

        try {
            String actualToken = token.replace(TOKEN_PREFIX, "");
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(SECRET_KEY)
                    .build()
                    .parseClaimsJws(actualToken)
                    .getBody();

            return claims.getSubject();
        } catch (Exception e) {
            log.error("Error extracting member ID from token", e);
            return null;
        }
    }

    /**
     * 从JWT中提取管理员ID
     * 
     * @param token JWT令牌
     * @return 管理员ID
     */
    public static String getAdminIdFromToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(SECRET_KEY)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            // 检查是否包含adminId声明，这是管理员令牌的特征
            if (claims.containsKey("adminId")) {
                return claims.get("adminId", String.class);
            }
            return null;
        } catch (Exception e) {
            log.error("Error extracting admin ID from token", e);
            return null;
        }
    }

    /**
     * 验证令牌是否有效
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    public static boolean validateTokenExpiration(String token) {
        if (token == null || !token.startsWith(TOKEN_PREFIX)) {
            return false;
        }

        try {
            String actualToken = token.replace(TOKEN_PREFIX, "");
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(SECRET_KEY)
                    .build()
                    .parseClaimsJws(actualToken)
                    .getBody();

            // 检查令牌是否过期
            return !claims.getExpiration().before(new Date());
        } catch (ExpiredJwtException e) {
            log.debug("JWT令牌已过期: {}", e.getMessage());
            return false;
        } catch (io.jsonwebtoken.security.SignatureException e) {
            log.warn("JWT令牌签名无效，可能是密钥更改导致，请重新登录: {}", e.getMessage());
            // 对于签名异常，我们仍然返回false，但会在validateToken方法中特殊处理
            return false;
        } catch (Exception e) {
            log.error("Error validating token: {}", e.getMessage(), e);
            return false;
        }
    }
}
