package com.play.xianshibusiness.utils;

import com.play.xianshibusiness.enums.ResultCode;
import com.play.xianshibusiness.exception.GlobalException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * 获取当前登录用户工具类
 */
@Slf4j
public class PrincipalUtil {

    // 会员ID请求头
    private static final String MEMBER_ID_HEADER = "MemberId";
    
    // 管理员ID请求头
    private static final String ADMIN_ID_HEADER = "AdminId";
    
    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID，如果未登录则返回null
     */
    public static String getMemberIdOrNull() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }
        
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            return ((UserDetails) principal).getUsername();
        } else if (principal instanceof String) {
            return (String) principal;
        }
        
        return null;
    }
    
    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     * @throws IllegalStateException 如果未登录
     */
    public static String getMemberId() {
        String memberId = getMemberIdOrNull();
        if (memberId == null) {
            throw new IllegalStateException("用户未登录");
        }
        return memberId;
    }
    
    /**
     * 判断当前是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isAuthenticated() {
        return getMemberIdOrNull() != null;
    }
    
    /**
     * 获取当前登录管理员ID
     * 
     * @return 管理员ID
     */
    public static String getAdminId() {
        HttpServletRequest request = getRequest();
        
        // 先尝试从请求属性中获取
        Object adminIdAttr = request.getAttribute(ADMIN_ID_HEADER);
        if (adminIdAttr != null) {
            return adminIdAttr.toString();
        }
        
        // 如果请求属性中没有，再尝试从请求头中获取（兼容旧代码）
        String adminId = request.getHeader(ADMIN_ID_HEADER);
        if (!StringUtils.hasText(adminId)) {
            throw new GlobalException(ResultCode.INVALID_TOKEN, "管理员未登录");
        }
        return adminId;
    }
    
    /**
     * 获取当前登录管理员角色列表
     * 
     * @return 角色列表
     */
    public static List<String> getAdminRoles() {
        String rolesStr = getRequest().getHeader("Roles");
        if (!StringUtils.hasText(rolesStr)) {
            return new ArrayList<>();
        }
        return Arrays.stream(rolesStr.split(",")).collect(Collectors.toList());
    }
    
    /**
     * 获取当前登录管理员权限列表
     * 
     * @return 权限列表
     */
    public static List<String> getAdminPermissions() {
        String permsStr = getRequest().getHeader("Permissions");
        if (!StringUtils.hasText(permsStr)) {
            return new ArrayList<>();
        }
        return Arrays.stream(permsStr.split(",")).collect(Collectors.toList());
    }
    
    /**
     * 获取当前请求对象
     * 
     * @return 请求对象
     */
    private static HttpServletRequest getRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                throw new GlobalException(ResultCode.PROGRAM_ERROR, "无法获取请求信息");
            }
            return attributes.getRequest();
        } catch (Exception e) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "获取请求信息异常");
        }
    }
    
    /**
     * 检查当前登录用户是否拥有指定角色
     * 
     * @param requiredRoles 所需角色列表
     * @return 是否拥有权限
     */
    public static boolean hasAnyRole(String... requiredRoles) {
        List<String> userRoles = getAdminRoles();
        if (userRoles.isEmpty()) {
            return false;
        }
        
        for (String required : requiredRoles) {
            if (userRoles.contains(required)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查当前登录用户是否拥有指定权限
     * 
     * @param requiredPermissions 所需权限列表
     * @return 是否拥有权限
     */
    public static boolean hasAnyPermission(String... requiredPermissions) {
        List<String> userPermissions = getAdminPermissions();
        if (userPermissions.isEmpty()) {
            return false;
        }
        
        for (String required : requiredPermissions) {
            if (userPermissions.contains(required)) {
                return true;
            }
        }
        
        return false;
    }
}
