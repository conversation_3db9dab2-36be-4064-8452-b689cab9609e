package com.play.xianshiadmin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.dto.gold.GoldAdjustDTO;
import com.play.xianshibusiness.dto.gold.GoldRecordQueryDTO;
import com.play.xianshibusiness.dto.gold.GoldRecordVO;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.CMemberGoldRecordService;
import com.play.xianshibusiness.service.CMemberService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 管理端金币管理控制器
 */
@RestController
@RequestMapping("/admin/gold")
@Api(tags = "管理端金币管理API")
public class GoldAdminController {

    @Resource
    private CMemberGoldRecordService goldRecordService;
    
    @Resource
    private CMemberService memberService;
    
    /**
     * 分页查询金币记录
     */
    @GetMapping("/records")
    @ApiOperation(value = "分页查询金币记录")
    @RequireAdmin
    public Result<Page<GoldRecordVO>> pageGoldRecords(GoldRecordQueryDTO queryDTO) {
        return ResultUtils.success(goldRecordService.pageGoldRecords(queryDTO));
    }
    
    /**
     * 获取金币记录详情
     */
    @GetMapping("/record/{id}")
    @ApiOperation(value = "获取金币记录详情")
    @RequireAdmin
    public Result<GoldRecordVO> getGoldRecordDetail(
            @ApiParam(value = "记录ID", required = true) @PathVariable String id) {
        return ResultUtils.success(goldRecordService.getGoldRecordDetail(id));
    }
    
    /**
     * 手动调整会员金币
     */
    @PostMapping("/adjust")
    @ApiOperation(value = "手动调整会员金币")
    @RequireAdmin
    public Result<Boolean> adjustMemberGold(@RequestBody @Valid GoldAdjustDTO adjustDTO) {
        // 设置操作人ID为当前管理员
        String adminId = PrincipalUtil.getMemberIdOrNull();
        if (adminId != null) {
            adjustDTO.setOperatorId(adminId);
        }
        
        return ResultUtils.success(goldRecordService.adjustMemberGold(adjustDTO));
    }
    
    /**
     * 获取金币统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取金币统计信息")
    @RequireAdmin
    public Result<Map<String, Object>> getGoldStatistics() {
        return ResultUtils.success(goldRecordService.getGoldStatistics());
    }
    
    /**
     * 查询会员金币余额
     */
    @GetMapping("/balance/{memberId}")
    @ApiOperation(value = "查询会员金币余额")
    @RequireAdmin
    public Result<Integer> getMemberGold(
            @ApiParam(value = "会员ID", required = true) @PathVariable String memberId) {
        return ResultUtils.success(memberService.getMemberGoldBalance(memberId));
    }
} 