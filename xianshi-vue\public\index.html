<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>闲时有伴管理系统</title>
    <meta name="description" content="闲时有伴后台管理系统">
    <style>
      #loading-mask {
        position: fixed;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background: #fff;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #F8D010;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #303030;
        font-size: 16px;
        font-family: Arial, sans-serif;
      }
      
      /* 全局样式修复侧边栏下划线问题 */
      .sidebar-container .el-menu,
      .sidebar-container .el-menu-item,
      .sidebar-container .el-submenu__title,
      .sidebar-container .el-menu--inline {
        border: none !important;
        border-bottom: none !important;
      }
      
      /* 特别处理带有data-v属性的span元素 */
      .sidebar-container span,
      .sidebar-container span[slot="title"],
      .sidebar-container .el-submenu__title span,
      .sidebar-container .el-menu-item span,
      .sidebar-container span[data-v-10973580],
      .sidebar-container [class*="data-v-"] span,
      span[data-v-10973580] {
        border-bottom: none !important;
        text-decoration: none !important;
        border: none !important;
        box-shadow: none !important;
      }
      
      /* 处理所有带有data-v-属性的span元素 */
      [class*="data-v-"] span {
        border-bottom: none !important;
        text-decoration: none !important;
        border: none !important;
        box-shadow: none !important;
      }
      
      /* 处理伪元素 */
      .sidebar-container .el-menu-item::after,
      .sidebar-container .el-menu-item::before,
      .sidebar-container .el-submenu__title::after,
      .sidebar-container .el-submenu__title::before {
        display: none !important;
      }
      
      /* 控制台菜单项特别处理 */
      .el-menu-item span:contains("控制台"),
      .el-menu-item span:contains("订单管理"),
      .el-menu-item span:contains("陪玩类型"),
      .el-menu-item span:contains("系统配置") {
        border-bottom: none !important;
        text-decoration: none !important;
        border: none !important;
        box-shadow: none !important;
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>很抱歉，闲时有伴管理系统需要启用JavaScript才能正常工作。</strong>
    </noscript>
    <div id="app">
      <div id="loading-mask">
        <div class="loading-spinner"></div>
        <div class="loading-text">系统加载中...</div>
      </div>
    </div>
    <!-- built files will be auto injected -->
    <script>
      // 页面加载完成后执行
      window.onload = function() {
        // 延迟执行，确保Vue组件已经渲染
        setTimeout(function() {
          // 查找所有侧边栏中的span元素
          var spans = document.querySelectorAll('.sidebar-container span');
          if (spans) {
            spans.forEach(function(span) {
              // 直接设置内联样式
              span.style.borderBottom = 'none';
              span.style.textDecoration = 'none';
              span.style.border = 'none';
              span.style.boxShadow = 'none';
            });
          }
          
          // 特别处理带有data-v-10973580属性的span
          var dataVSpans = document.querySelectorAll('span[data-v-10973580]');
          if (dataVSpans) {
            dataVSpans.forEach(function(span) {
              span.style.borderBottom = 'none';
              span.style.textDecoration = 'none';
              span.style.border = 'none';
              span.style.boxShadow = 'none';
            });
          }
        }, 1000);
      };
    </script>
  </body>
</html> 