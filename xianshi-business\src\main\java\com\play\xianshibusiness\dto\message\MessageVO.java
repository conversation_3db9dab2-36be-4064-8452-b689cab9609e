package com.play.xianshibusiness.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息视图对象
 */
@Data
@ApiModel(value = "消息视图对象")
public class MessageVO {
    
    @ApiModelProperty(value = "消息ID")
    private String id;
    
    @ApiModelProperty(value = "接收者ID")
    private String receiverId;
    
    @ApiModelProperty(value = "接收者昵称")
    private String receiverName;
    
    @ApiModelProperty(value = "接收者头像")
    private String receiverAvatar;
    
    @ApiModelProperty(value = "发送者ID")
    private String senderId;
    
    @ApiModelProperty(value = "发送者昵称")
    private String senderName;
    
    @ApiModelProperty(value = "发送者头像")
    private String senderAvatar;
    
    @ApiModelProperty(value = "消息类型：1-系统通知，2-订单通知，3-活动通知")
    private Integer messageType;
    
    @ApiModelProperty(value = "消息类型描述")
    private String messageTypeDesc;
    
    @ApiModelProperty(value = "消息内容")
    private String content;
    
    @ApiModelProperty(value = "是否已读：true-已读，false-未读")
    private Boolean isRead;
    
    @ApiModelProperty(value = "阅读时间")
    private LocalDateTime readTime;
    
    @ApiModelProperty(value = "关联ID")
    private String relatedId;
    
    @ApiModelProperty(value = "关联类型：1-订单，2-活动，3-系统")
    private Integer relatedType;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
} 