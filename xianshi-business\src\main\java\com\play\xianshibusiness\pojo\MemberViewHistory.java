package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 会员浏览历史实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_member_view_history")
@ApiModel("会员浏览历史")
public class MemberViewHistory extends BaseObjPo {
    
    @ApiModelProperty("会员ID")
    private String memberId;
    
    @ApiModelProperty("目标会员ID（被浏览的会员）")
    private String targetMemberId;
    
    @ApiModelProperty("浏览次数")
    private Integer viewCount;
    
    @ApiModelProperty("首次浏览时间")
    private LocalDateTime firstViewTime;
    
    @ApiModelProperty("最近浏览时间")
    private LocalDateTime lastViewTime;
} 