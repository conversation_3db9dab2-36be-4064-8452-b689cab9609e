package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.activity.ActivityTypeDTO;
import com.play.xianshibusiness.dto.activity.ActivityTypeTreeVO;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.BOrderTypeMapper;
import com.play.xianshibusiness.pojo.BOrderType;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * (BOrderType)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:13
 */
@Service
public class BOrderTypeService {

    @Resource
    private BOrderTypeMapper orderTypeMapper;

    /**
     * 获取所有陪玩类型
     * @return 陪玩类型列表
     */
    public List<BOrderType> getAllTypes() {
        return orderTypeMapper.selectList(
                new LambdaQueryWrapper<BOrderType>()
                        .eq(BOrderType::getDeleted, false)
                        .eq(BOrderType::getAvailable, true)
                        .orderByAsc(BOrderType::getComparable)
        );
    }

    /**
     * 获取所有父级陪玩类型
     * @return 父级陪玩类型列表
     */
    public List<BOrderType> getAllParentTypes() {
        return orderTypeMapper.selectList(
                new LambdaQueryWrapper<BOrderType>()
                        .eq(BOrderType::getDeleted, false)
                        .eq(BOrderType::getAvailable, true)
                        .isNull(BOrderType::getParentId)
                        .orderByAsc(BOrderType::getComparable)
        );
    }

    /**
     * 根据父级ID获取子类型
     * @param parentId 父级ID
     * @return 子类型列表
     */
    public List<BOrderType> getChildrenByParentId(String parentId) {
        return orderTypeMapper.selectList(
                new LambdaQueryWrapper<BOrderType>()
                        .eq(BOrderType::getDeleted, false)
                        .eq(BOrderType::getAvailable, true)
                        .eq(BOrderType::getParentId, parentId)
                        .orderByAsc(BOrderType::getComparable)
        );
    }

    /**
     * 获取陪玩类型详情
     * @param id 类型ID
     * @return 类型详情
     */
    public BOrderType getTypeDetail(String id) {
        BOrderType orderType = orderTypeMapper.selectById(id);
        if (orderType == null || orderType.getDeleted() || !orderType.getAvailable()) {
            throw new GlobalException(400, "陪玩类型不存在");
        }
        return orderType;
    }

    /**
     * 创建陪玩类型
     * @param dto 类型DTO
     * @param memberId 会员ID
     * @return 类型ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String createType(ActivityTypeDTO dto, String memberId) {
        // 校验编码是否重复
        BOrderType existType = orderTypeMapper.selectOne(
                new LambdaQueryWrapper<BOrderType>()
                        .eq(BOrderType::getCode, dto.getCode())
                        .eq(BOrderType::getDeleted, false)
        );
        
        if (existType != null) {
            throw new GlobalException(400, "编码已存在");
        }
        
        // 校验父级类型是否存在
        if (!StringUtils.isEmpty(dto.getParentId())) {
            BOrderType parentType = orderTypeMapper.selectById(dto.getParentId());
            if (parentType == null || parentType.getDeleted() || !parentType.getAvailable()) {
                throw new GlobalException(400, "父级类型不存在");
            }
        }
        
        // 创建类型
        BOrderType orderType = new BOrderType();
        BeanUtils.copyProperties(dto, orderType);
        orderType.setId(UUID.randomUUID().toString());
        orderType.setCreateTime(LocalDateTime.now());
        orderType.setUpdateTime(LocalDateTime.now());
        orderType.setDeleted(false);
        orderType.setAvailable(true);
        
        orderTypeMapper.insert(orderType);
        
        return orderType.getId();
    }

    /**
     * 更新陪玩类型
     * @param id 类型ID
     * @param dto 类型DTO
     * @param memberId 会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateType(String id, ActivityTypeDTO dto, String memberId) {
        // 校验类型是否存在
        BOrderType orderType = orderTypeMapper.selectById(id);
        if (orderType == null || orderType.getDeleted()) {
            throw new GlobalException(400, "陪玩类型不存在");
        }
        
        // 校验编码是否重复
        BOrderType existType = orderTypeMapper.selectOne(
                new LambdaQueryWrapper<BOrderType>()
                        .eq(BOrderType::getCode, dto.getCode())
                        .ne(BOrderType::getId, id)
                        .eq(BOrderType::getDeleted, false)
        );
        
        if (existType != null) {
            throw new GlobalException(400, "编码已存在");
        }
        
        // 校验父级类型是否存在
        if (!StringUtils.isEmpty(dto.getParentId())) {
            BOrderType parentType = orderTypeMapper.selectById(dto.getParentId());
            if (parentType == null || parentType.getDeleted() || !parentType.getAvailable()) {
                throw new GlobalException(400, "父级类型不存在");
            }
            
            // 校验父级类型不能是自己
            if (id.equals(dto.getParentId())) {
                throw new GlobalException(400, "父级类型不能是自己");
            }
            
            // 校验父级类型不能是自己的子类型
            List<BOrderType> children = getChildrenByParentId(id);
            if (children.stream().anyMatch(child -> child.getId().equals(dto.getParentId()))) {
                throw new GlobalException(400, "父级类型不能是自己的子类型");
            }
        }
        
        // 更新类型
        BeanUtils.copyProperties(dto, orderType);
        orderType.setUpdateTime(LocalDateTime.now());
        
        orderTypeMapper.updateById(orderType);
        
        return true;
    }

    /**
     * 删除陪玩类型
     * @param id 类型ID
     * @param memberId 会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteType(String id, String memberId) {
        // 校验类型是否存在
        BOrderType orderType = orderTypeMapper.selectById(id);
        if (orderType == null || orderType.getDeleted()) {
            throw new GlobalException(400, "陪玩类型不存在");
        }
        
        // 校验是否有子类型
        int childCount = orderTypeMapper.selectCount(
                new LambdaQueryWrapper<BOrderType>()
                        .eq(BOrderType::getParentId, id)
                        .eq(BOrderType::getDeleted, false)
        ).intValue();
        
        if (childCount > 0) {
            throw new GlobalException(400, "请先删除子类型");
        }
        
        // 逻辑删除
        orderType.setDeleted(true);
        orderType.setUpdateTime(LocalDateTime.now());
        
        orderTypeMapper.updateById(orderType);
        
        return true;
    }

    /**
     * 分页查询陪玩类型
     * @param pageNum 页码
     * @param pageSize 每页条数
     * @param parentId 父级ID
     * @param name 类型名称
     * @return 分页结果
     */
    public Page<BOrderType> pageTypes(Integer pageNum, Integer pageSize, String parentId, String name) {
        LambdaQueryWrapper<BOrderType> queryWrapper = new LambdaQueryWrapper<BOrderType>()
                .eq(BOrderType::getDeleted, false);
        
        if (!StringUtils.isEmpty(parentId)) {
            queryWrapper.eq(BOrderType::getParentId, parentId);
        }
        
        if (!StringUtils.isEmpty(name)) {
            queryWrapper.like(BOrderType::getName, name);
        }
        
        queryWrapper.orderByAsc(BOrderType::getComparable);
        
        Page<BOrderType> page = new Page<>(pageNum, pageSize);
        return orderTypeMapper.selectPage(page, queryWrapper);
    }
    
    /**
     * 获取陪玩类型树形结构
     * @return 树形结构数据
     */
    public List<ActivityTypeTreeVO> getTypeTree() {
        // 1.获取所有可用的陪玩类型
        List<BOrderType> allTypes = getAllTypes();
        if (allTypes.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 2.转换为VO对象
        List<ActivityTypeTreeVO> voList = allTypes.stream()
                .map(ActivityTypeTreeVO::fromBOrderType)
                .collect(Collectors.toList());
        
        // 3.构建父子关系
        Map<String, List<ActivityTypeTreeVO>> parentChildMap = voList.stream()
                .filter(vo -> vo.getParentId() != null)
                .collect(Collectors.groupingBy(ActivityTypeTreeVO::getParentId));
        
        // 4.设置子节点
        voList.forEach(vo -> {
            List<ActivityTypeTreeVO> children = parentChildMap.get(vo.getId());
            if (children != null) {
                vo.setChildren(children);
            }
        });
        
        // 5.只返回顶级节点
        return voList.stream()
                .filter(vo -> vo.getParentId() == null)
                .collect(Collectors.toList());
    }
}

