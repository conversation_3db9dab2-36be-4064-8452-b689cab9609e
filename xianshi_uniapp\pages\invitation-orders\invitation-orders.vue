<template>
  <view class="container">
    <view class="filter-tabs">
      <view 
        v-for="(item, index) in statusList" 
        :key="index"
        :class="['tab', { active: currentStatus === item.value }]"
        @tap="switchStatus(item.value)"
      >
        {{ item.label }}
      </view>
    </view>
    
    <view class="order-list" v-if="orders.length > 0">
      <view class="order-item" v-for="(order, index) in orders" :key="index" @tap="viewOrderDetail(order.id)">
        <view class="order-header">
          <view class="order-title">
            <text class="order-project">{{ order.projectName }}</text>
            <text class="order-status" :class="'status-' + order.status">{{ getStatusText(order.status) }}</text>
          </view>
          <text class="order-time">{{ order.createTime }}</text>
        </view>
        
        <view class="order-info">
          <view class="info-row">
            <text class="info-label">邀约方：</text>
            <text class="info-value">{{ order.inviterName }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">服务时间：</text>
            <text class="info-value">{{ order.serviceTime }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">服务地点：</text>
            <text class="info-value">{{ order.location }}</text>
          </view>
        </view>
        
        <view class="order-footer">
          <view class="price-info">
            <text class="price">¥{{ order.price }}</text>
            <text class="price-unit">/小时</text>
          </view>
          
          <view class="action-buttons" v-if="order.status === 'pending'">
            <button class="btn btn-reject" @tap.stop="rejectOrder(order.id)">拒绝</button>
            <button class="btn btn-accept" @tap.stop="acceptOrder(order.id)">接受</button>
          </view>
        </view>
      </view>
      
      <!-- 加载更多状态 -->
      <view class="load-more" v-if="!loading && hasMore">
        <text class="load-more-text">上拉加载更多</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!loading && !hasMore">
        <text class="no-more-text">没有更多数据了</text>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-state" v-if="loading">
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-if="!loading && orders.length === 0">
      <image class="empty-icon" src="/static/images/empty-orders.png"></image>
      <text class="empty-text">还没有邀约订单~</text>
    </view>
  </view>
</template>

<script>
import { getCurrentMemberInfo } from '../../api/index.js';

export default {
  data() {
    return {
      statusList: [], // 动态状态列表
      currentStatus: '', // 当前选中的状态
      orders: [],
      loading: false,
      currentPage: 1,
      totalPages: 1,
      hasMore: false
    };
  },
  async onLoad() {
    await this.getUserRole();
    this.setStatusListByRole();
    this.loadOrders();
  },
  async onShow() {
    // 页面显示时重新获取用户角色和设置状态列表（用户角色可能发生变化）
    await this.getUserRole();
    this.setStatusListByRole();
    this.refreshOrders();
  },
  onPullDownRefresh() {
    // 下拉刷新
    this.refreshOrders();
  },
  onReachBottom() {
    // 上拉加载更多
    this.loadMoreOrders();
  },
  methods: {
    // 获取用户角色
    async getUserRole() {
      try {
        // 调用统一的API获取最新用户信息
        const res = await getCurrentMemberInfo();
        if (res && res.code === 200 && res.data) {
          const memberInfo = res.data;
          
          // 获取角色信息
          const currentRoleId = memberInfo.currentRoleId;
          const hasExpertRole = memberInfo.hasExpertRole;
          
          // 根据角色ID设置角色名称
          let roleName = '普通用户';
          if (currentRoleId === '2' || hasExpertRole) {
            roleName = '闲时达人';
          } else if (currentRoleId === '1') {
            roleName = '普通用户';
          }
          
          this.userRole = roleName;
          
          // 更新本地存储和全局数据
          uni.setStorageSync('userRole', roleName);
          uni.setStorageSync('currentRoleId', currentRoleId);
          uni.setStorageSync('hasExpertRole', hasExpertRole);
          
          const app = getApp();
          app.globalData.userRole = roleName;
          app.globalData.currentRoleId = currentRoleId;
          app.globalData.hasExpertRole = hasExpertRole;
          
          console.log('通过API获取用户角色:', this.userRole, 'currentRoleId:', currentRoleId, 'hasExpertRole:', hasExpertRole);
        } else {
          // API调用失败时，尝试从本地存储获取
          this.loadRoleFromStorage();
        }
      } catch (error) {
        console.error('API获取用户角色失败:', error);
        // API调用失败时，尝试从本地存储获取
        this.loadRoleFromStorage();
      }
    },
    
    // 从本地存储加载角色信息
    loadRoleFromStorage() {
      try {
        // 优先从本地存储获取
        const userRole = uni.getStorageSync('userRole');
        if (userRole) {
          this.userRole = userRole;
          console.log('从本地存储获取用户角色:', this.userRole);
          return;
        }
        
        // 从userInfo获取
        const userInfo = uni.getStorageSync('userInfo');
        if (userInfo) {
          const userInfoObj = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo;
          if (userInfoObj.currentRoleName) {
            this.userRole = userInfoObj.currentRoleName;
          } else if (userInfoObj.currentRoleId === '2' || userInfoObj.roleType === 2) {
            this.userRole = '闲时达人';
          } else {
            this.userRole = '普通用户';
          }
        }
        
        // 从全局数据获取
        if (!this.userRole) {
          const app = getApp();
          if (app.globalData && app.globalData.userRole) {
            this.userRole = app.globalData.userRole;
          }
        }
        
        // 默认值
        if (!this.userRole) {
          this.userRole = '普通用户';
        }
        
        console.log('从本地存储获取用户角色:', this.userRole);
      } catch (error) {
        console.error('从本地存储获取用户角色失败:', error);
        this.userRole = '普通用户';
      }
    },
    
    // 根据用户角色设置状态列表
    setStatusListByRole() {
      // 获取当前角色信息
      const currentRoleId = uni.getStorageSync('currentRoleId') || '';
      const hasExpertRole = uni.getStorageSync('hasExpertRole') || false;
      const userRole = this.userRole || uni.getStorageSync('userRole') || '普通用户';
      
      console.log('设置邀约订单状态列表，当前角色ID:', currentRoleId, 'hasExpertRole:', hasExpertRole, 'userRole:', userRole);
      
      // 检查是否为达人角色
      const isTalent = currentRoleId === '2' || hasExpertRole || userRole === '闲时达人';
      
      if (isTalent) {
        // 达人用户：显示邀约相关状态
        this.statusList = [
          { label: '全部', value: '' },
          { label: '待处理', value: '0' }, // 待处理的邀约
          { label: '已接受', value: '1' }, // 已接受的邀约
          { label: '已拒绝', value: '2' }, // 已拒绝的邀约
          { label: '进行中', value: '3' }, // 进行中的服务
          { label: '已完成', value: '4' }  // 已完成的服务
        ];
      } else {
        // 普通用户：显示发出的邀约状态
        this.statusList = [
          { label: '全部', value: '' },
          { label: '待回复', value: '0' }, // 等待达人回复
          { label: '已接受', value: '1' }, // 达人已接受
          { label: '已拒绝', value: '2' }, // 达人已拒绝
          { label: '进行中', value: '3' }, // 服务进行中
          { label: '已完成', value: '4' }  // 服务已完成
        ];
      }
      
      // 设置默认选中状态
      if (!this.currentStatus) {
        this.currentStatus = '';
      }
      
      console.log('邀约订单状态列表设置完成:', this.statusList);
    },
    
    // 切换状态筛选
    switchStatus(status) {
      if (this.currentStatus === status) return;
      
      this.currentStatus = status;
      this.refreshOrders();
    },
    
    // 加载订单列表
    async loadOrders() {
      if (this.loading) return;
      
      this.loading = true;
      try {
        const app = getApp().globalData;
        
        // 构建请求参数
        const params = {
          page: 1,
          size: 20,
          orderType: 'APPOINTMENT' // 只查询邀约类型的订单
        };
        
        // 如果有状态筛选，添加状态参数（转换为整数）
        if (this.currentStatus && this.currentStatus !== '' && this.currentStatus !== 'all') {
          params.status = parseInt(this.currentStatus);
        }
        
        console.log('加载邀约订单列表，请求参数:', params);
        
        let res = await this.$requestHttp.get(app.commonApi.getRoleOrderList, {
          data: params
        });
        
        console.log('邀约订单列表API响应:', res);
        
        if (res.code === 200 && res.data) {
          const { records, total, pages, current } = res.data;
          this.orders = this.processOrderData(records || []);
          this.currentPage = current || 1;
          this.totalPages = pages || 1;
          this.hasMore = this.currentPage < this.totalPages;
          
          console.log('处理后的邀约订单数据:', this.orders);
        } else {
          console.error('获取邀约订单列表失败:', res.msg || '未知错误');
          uni.showToast({
            title: res.msg || '获取订单失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载邀约订单列表异常:', error);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },
    
    // 加载更多订单
    async loadMoreOrders() {
      if (this.loading || !this.hasMore) return;
      
      this.loading = true;
      try {
        const app = getApp().globalData;
        const nextPage = this.currentPage + 1;
        
        // 构建请求参数
        const params = {
          page: nextPage,
          size: 20,
          orderType: 'APPOINTMENT' // 只查询邀约类型的订单
        };
        
        // 如果有状态筛选，添加状态参数（转换为整数）
        if (this.currentStatus && this.currentStatus !== '' && this.currentStatus !== 'all') {
          params.status = parseInt(this.currentStatus);
        }
        
        let res = await this.$requestHttp.get(app.commonApi.getRoleOrderList, {
          data: params
        });
        
        if (res.code === 200 && res.data) {
          const { records, pages, current } = res.data;
          const newOrders = this.processOrderData(records || []);
          this.orders = [...this.orders, ...newOrders];
          this.currentPage = current || nextPage;
          this.hasMore = this.currentPage < pages;
        }
      } catch (error) {
        console.error('加载更多邀约订单异常:', error);
      } finally {
        this.loading = false;
      }
    },
    
    // 刷新订单列表
    async refreshOrders() {
      this.currentPage = 1;
      this.hasMore = true;
      this.orders = [];
      await this.loadOrders();
      // 停止下拉刷新
      uni.stopPullDownRefresh();
    },
    
    // 处理订单数据
    processOrderData(orders) {
      return orders.map(order => {
        // 根据后端OrderStatusEnum映射状态
        const statusCode = this.mapBackendStatus(order.status);
        const statusText = this.getStatusText(statusCode);
        const statusClass = statusCode === 'pending' ? 'status-pending' : 
                           statusCode === 'accepted' ? 'status-accepted' : 
                           statusCode === 'rejected' ? 'status-rejected' : 
                           statusCode === 'completed' ? 'status-completed' : 'status-cancelled';
        
        // 根据状态决定是否显示操作按钮
        const showActions = statusCode === 'pending' && this.userRole === 'talent';
        
        return {
          ...order,
          // 基础字段映射（来自OrderVO）
          id: order.orderId,
          orderId: order.orderId,
          projectName: order.title || '邀约订单',
          inviterName: order.memberName || order.memberNickname || '未知用户',
          serviceTime: this.formatServiceTime(order.startTime, order.endTime),
          location: order.location || order.address || '未指定地点',
          price: order.hourlyRate || 0,
          createTime: this.formatTime(order.createTime),
          status: statusCode,
          statusText: statusText,
          statusClass: statusClass,
          showActions: showActions
        };
      });
    },
    
    // 映射后端状态到前端状态
    mapBackendStatus(backendStatus) {
      // 后端OrderStatusEnum: 0-待审核，1-报名中，2-待选择达人，3-进行中，4-已完成，5-已取消
      const statusMap = {
        0: 'pending',    // 待审核 -> 待处理
        1: 'pending',    // 报名中 -> 待处理
        2: 'pending',    // 待选择达人 -> 待处理
        3: 'accepted',   // 进行中 -> 已接受
        4: 'completed',  // 已完成 -> 已完成
        5: 'cancelled'   // 已取消 -> 已取消
      };
      
      return statusMap[backendStatus] || 'pending';
    },
    
    // 格式化服务时间
    formatServiceTime(startTime, endTime) {
      if (!startTime) return '未指定时间';
      
      const start = new Date(startTime);
      const startStr = start.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
      
      if (endTime) {
        const end = new Date(endTime);
        const endStr = end.toLocaleString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        });
        return `${startStr}-${endStr}`;
      }
      
      return startStr;
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      
      const date = new Date(timeStr);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    getStatusText(status) {
      const statusMap = {
        'pending': '待处理',
        'accepted': '已接受',
        'rejected': '已拒绝',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    
    viewOrderDetail(orderId) {
      // 跳转到订单详情页面
      uni.navigateTo({
        url: `/pages/order-detail/order-detail?orderId=${orderId}`
      });
    },
    
    acceptOrder(orderId) {
      uni.showModal({
        title: '确认接受',
        content: '确定要接受这个邀约订单吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const response = await this.$api.signUp({ orderId });
              if (response.code === 200) {
                uni.showToast({
                  title: '已接受订单',
                  icon: 'success'
                });
                // 刷新订单列表
                this.refreshOrders();
              } else {
                uni.showToast({
                  title: response.message || '接受订单失败',
                  icon: 'none'
                });
              }
            } catch (error) {
              console.error('接受订单失败:', error);
              uni.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    
    rejectOrder(orderId) {
      uni.showModal({
        title: '确认拒绝',
        content: '确定要拒绝这个邀约订单吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const response = await this.$api.cancelSignUp({ orderId });
              if (response.code === 200) {
                uni.showToast({
                  title: '已拒绝订单',
                  icon: 'success'
                });
                // 刷新订单列表
                this.refreshOrders();
              } else {
                uni.showToast({
                  title: response.message || '拒绝订单失败',
                  icon: 'none'
                });
              }
            } catch (error) {
              console.error('拒绝订单失败:', error);
              uni.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}

.filter-tabs {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tab {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 16rpx 0;
  position: relative;
}

.tab.active {
  color: #ff8c00;
  font-weight: bold;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff8c00;
  border-radius: 2rpx;
}

.order-list {
  padding: 20rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.order-title {
  display: flex;
  align-items: center;
}

.order-project {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.order-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status-pending {
  color: #ff8c00;
  background-color: rgba(255, 140, 0, 0.1);
}

.status-accepted {
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
}

.status-rejected {
  color: #999;
  background-color: rgba(153, 153, 153, 0.1);
}

.status-completed {
  color: #2979ff;
  background-color: rgba(41, 121, 255, 0.1);
}

.status-cancelled {
  color: #fa5151;
  background-color: rgba(250, 81, 81, 0.1);
}

.status {
   font-size: 24rpx;
   padding: 8rpx 16rpx;
   border-radius: 20rpx;
   background-color: #f0f0f0;
   color: #666;
 }
 
 .status-pending {
   background-color: #fff3cd;
   color: #856404;
 }
 
 .status-accepted {
   background-color: #d4edda;
   color: #155724;
 }
 
 .status-rejected {
   background-color: #f8d7da;
   color: #721c24;
 }
 
 .status-completed {
   background-color: #d1ecf1;
   color: #0c5460;
 }
 
 .status-cancelled {
   background-color: #e2e3e5;
   color: #383d41;
 }

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-info {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  margin-bottom: 8rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 36rpx;
  color: #ff8c00;
  font-weight: bold;
}

.price-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.action-buttons {
  display: flex;
}

.btn {
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-left: 16rpx;
}

.btn-reject {
  color: #666;
  background-color: #f5f5f5;
  border: 1rpx solid #ddd;
}

.btn-accept {
  color: #ffffff;
  background-color: #ff8c00;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #ccc;
}
</style>