<template>
<view class="container">
  <!-- 状态筛选栏 -->
  <view class="status-filter">
    <scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
      <view class="filter-item" 
            :class="{ active: currentStatus === '' }"
            @tap="filterByStatus('')">
        全部
      </view>
      <view class="filter-item" 
            v-for="status in statusList" 
            :key="status.code"
            :class="{ active: currentStatus === status.code }"
            @tap="filterByStatus(status.code)">
        {{ status.desc }}
      </view>
    </scroll-view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-tip" v-if="loading && orders.length === 0">
    <text>加载中...</text>
  </view>
  
  <!-- 空状态提示 -->
  <view class="empty-tip" v-if="!loading && filteredOrders.length === 0">
    <image src="/static/images/empty-orders.png" mode="aspectFit"></image>
    <text>{{ currentStatus ? '该状态下暂无订单' : '暂无订单' }}</text>
  </view>
  
  <!-- 订单列表 -->
  <view class="order-item" :class="order.statusClass" v-for="order in filteredOrders" :key="order.id" @tap="goToOrderDetail(order.id)">
    <view class="order-content">
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-status" :class="order.statusClass">{{order.statusText}}</view>
        <view class="order-date">{{formatTime(order.createTime)}}</view>
      </view>
      
      <!-- 订单主体 -->
      <view class="order-body">
        <view class="order-title">{{order.title}}</view>
        
        <!-- 发布者信息 -->
        <view class="publisher-info" v-if="order.memberName">
          <image class="publisher-avatar" :src="order.memberAvatar || '/static/default-avatar.png'" mode="aspectFill"></image>
          <text class="label">发布者:</text>
          <text class="value">{{order.memberName}}</text>
        </view>
        
        <!-- 选中达人信息 -->
        <view class="selected-info" v-if="order.enrollStatus === 'selected'">
          <view class="expert-info">
            <text class="label">选中达人:</text>
            <text class="value self-selected">您已被选中</text>
          </view>
        </view>
        
        <!-- 服务信息 -->
        <view class="service-info">
          <view class="service-type">{{order.serviceType}}</view>
          <view class="order-price">¥{{order.price || order.goldCost || order.totalFee || 0}}</view>
        </view>
        
        <!-- 订单描述 -->
        <view class="order-description" v-if="order.description">
          {{order.description}}
        </view>
        
        <!-- 地点信息 -->
        <view class="location-row">
          <image class="icon" src="/static/images/location.png"></image>
          <text class="location-text">{{order.location}}</text>
        </view>
        
        <!-- 时间信息 -->
        <view class="time-row">
          <image class="icon" src="/static/images/time.png"></image>
          <text class="time-text">{{order.date}} {{order.time}}</text>
        </view>
        
        <!-- 报名统计信息 -->
        <view class="enroll-stats" v-if="order.enrollCount > 0 || (order.enrollList && order.enrollList.length > 0)">
          <text class="stats-text">已有 {{order.enrollCount || (order.enrollList ? order.enrollList.length : 0)}} 人报名</text>
        </view>
      </view>
      
      <!-- 订单底部 -->
      <view class="order-footer">
        <!-- 状态提示 -->
        <view class="status-tip" v-if="order.enrollStatus === 'enrolled'">
          等待雇主选择达人
        </view>
        
        <!-- 按钮区域 -->
        <view class="button-group">
          <!-- 报名按钮 -->
          <button 
            class="enroll-button" 
            v-if="order.canEnroll"
            @tap.stop="enrollOrder(order.id)">
            立即报名
          </button>
          
          <!-- 取消报名按钮 -->
          <button 
            class="cancel-button" 
            v-if="order.canCancel"
            @tap.stop="cancelEnroll(order.id)">
            取消报名
          </button>
          
          <!-- 确认出发按钮 -->
          <button 
            class="confirm-button" 
            v-if="order.canConfirmDeparture"
            @tap.stop="confirmDeparture" 
            :data-id="order.id">
            确认出发
          </button>
          
          <!-- 已报名状态 -->
          <view class="enrolled-status" v-if="order.enrollStatus === 'enrolled' && !order.canCancel">
            <text>已报名</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 加载更多提示 -->
  <view class="load-more" v-if="loading && filteredOrders.length > 0">
    <text>加载中...</text>
  </view>
  
  <!-- 没有更多数据提示 -->
  <view class="no-more" v-if="!hasMore && filteredOrders.length > 0">
    <text>没有更多数据了</text>
  </view>
</view> 
</template>

<script>
export default {
	data() {
		return {
			orders: [], // 订单列表
			loading: false, // 加载状态
			isExpert: false, // 是否为达人身份
			canOperate: false, // 是否可以操作
			currentPage: 1, // 当前页码
			totalPages: 1, // 总页数
			hasMore: true, // 是否还有更多数据
			userInfo: null, // 用户信息
			currentStatus: '', // 当前筛选状态
			statusList: [], // 状态列表，根据用户角色动态设置
			// 普通用户状态列表（自己发布的订单）
			userStatusList: [
				{ code: 'DRAFT', desc: '待发布' },
				{ code: 'AUDITING', desc: '待审核' },
				{ code: 'PUBLISHED', desc: '发布中' },
				{ code: 'WAITING_SELECT', desc: '待选择达人' },
				{ code: 'PROCESSING', desc: '进行中' },
				{ code: 'COMPLETED', desc: '已完成' },
				{ code: 'CANCELLED', desc: '已取消' }
			],
			// 达人状态列表（已报名或可报名的订单）
			talentStatusList: [
				{ code: 'PUBLISHED', desc: '可报名' },
				{ code: 'WAITING_SELECT', desc: '等待选择' },
				{ code: 'PROCESSING', desc: '进行中' },
				{ code: 'COMPLETED', desc: '已完成' },
				{ code: 'CANCELLED', desc: '已取消' }
			]
		}
	},
	onLoad(options) {
		console.log('页面参数:', options);
		this.isExpert = options.isExpert === 'true';
		this.canOperate = options.canOperate === 'true';
		
		// 获取用户信息
		this.getUserInfo();
		
		// 根据用户角色设置状态列表
		this.setStatusListByRole();
		
		// 加载订单列表
		this.loadOrders();
	},
	onShow() {
		// 页面显示时刷新数据
		this.getUserInfo();
		this.setStatusListByRole();
		this.loadOrders();
	},
	onReachBottom() {
		// 触底加载更多
		if (this.hasMore && !this.loading) {
			this.loadMoreOrders();
		}
	},
	onPullDownRefresh() {
		// 下拉刷新
		this.refreshOrders();
	},
	computed: {
		// 根据状态筛选订单
		filteredOrders() {
			if (!this.currentStatus) {
				return this.orders;
			}
			return this.orders.filter(order => order.status === this.currentStatus);
		}
	},
	methods: {
		// 获取用户信息
		getUserInfo() {
			try {
				this.userInfo = uni.getStorageSync('userInfo') || {};
				console.log('用户信息:', this.userInfo);
			} catch (e) {
				console.error('获取用户信息失败:', e);
			}
		},
		
		// 根据用户角色设置状态列表
		setStatusListByRole() {
			// 检查用户是否为达人角色
			const isTalent = this.userInfo && (
				this.userInfo.currentRoleId === '2' || // 当前角色是达人
				this.userInfo.roleType === 2 || // 角色类型是达人
				this.userInfo.isTalent === true || // 标记为达人
				this.isExpert === true // 页面参数标记为达人
			);
			
			if (isTalent) {
				// 达人角色，显示达人相关状态
				this.statusList = this.talentStatusList;
				console.log('设置达人状态列表:', this.statusList);
			} else {
				// 普通用户，显示用户发布订单的所有状态
				this.statusList = this.userStatusList;
				console.log('设置用户状态列表:', this.statusList);
			}
		},
		
		// 加载订单列表
		async loadOrders() {
			if (this.loading) return;
			
			this.loading = true;
			try {
				const app = getApp().globalData;
				
				// 构建请求参数
				const params = {
					page: 1,
					size: 20
				};
				
				// 如果有状态筛选，添加状态参数（转换为后端状态码）
				if (this.currentStatus && this.currentStatus !== '' && this.currentStatus !== 'all') {
					const backendStatus = this.mapFrontendStatusToBackend(this.currentStatus);
					if (backendStatus !== undefined) {
						params.status = backendStatus;
					}
				}
				
				// 调用根据角色获取订单列表的API
				let res = await this.$requestHttp.get(app.commonApi.getRoleOrderList, {
					data: params
				});
				
				console.log('订单列表API响应:', res);
				
				if (res.code === 200 && res.data) {
					const { records, total, pages, current } = res.data;
					this.orders = this.processOrderData(records || []);
					this.currentPage = current || 1;
					this.totalPages = pages || 1;
					this.hasMore = this.currentPage < this.totalPages;
					
					console.log('处理后的订单数据:', this.orders);
				} else {
					console.error('获取订单列表失败:', res.msg || '未知错误');
					uni.showToast({
						title: res.msg || '获取订单失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载订单列表异常:', error);
				uni.showToast({
					title: '网络异常，请稍后重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
				uni.stopPullDownRefresh();
			}
		},
		
		// 加载更多订单
		async loadMoreOrders() {
			if (this.loading || !this.hasMore) return;
			
			this.loading = true;
			try {
				const app = getApp().globalData;
				const nextPage = this.currentPage + 1;
				
				// 构建请求参数
				const params = {
					page: nextPage,
					size: 20
				};
				
				// 如果有状态筛选，添加状态参数（转换为后端状态码）
				if (this.currentStatus && this.currentStatus !== '' && this.currentStatus !== 'all') {
					const backendStatus = this.mapFrontendStatusToBackend(this.currentStatus);
					if (backendStatus !== undefined) {
						params.status = backendStatus;
					}
				}
				
				let res = await this.$requestHttp.get(app.commonApi.getRoleOrderList, {
					data: params
				});
				
				if (res.code === 200 && res.data) {
					const { records, pages, current } = res.data;
					const newOrders = this.processOrderData(records || []);
					this.orders = [...this.orders, ...newOrders];
					this.currentPage = current || nextPage;
					this.hasMore = this.currentPage < pages;
				}
			} catch (error) {
				console.error('加载更多订单异常:', error);
			} finally {
				this.loading = false;
			}
		},
		
		// 刷新订单列表
		refreshOrders() {
			this.currentPage = 1;
			this.hasMore = true;
			this.loadOrders();
		},
		
		// 处理订单数据
		processOrderData(orders) {
			return orders.map(order => {
				// 根据后端OrderStatusEnum映射状态
				const statusCode = this.mapBackendStatus(order.status);
				
				return {
					...order,
					// 基础字段映射（来自OrderVO）
					id: order.orderId,
					orderId: order.orderId,
					title: order.title || '订单标题',
					typeName: order.typeName,
					typeId: order.typeId,
					memberId: order.memberId,
					memberName: order.memberName || order.memberNickname || '未知用户',
					memberAvatar: order.memberAvatar,
					duration: order.duration || 0,
					hourlyRate: order.hourlyRate || 0,
					totalFee: order.totalFee || 0,
					sexRequire: order.sexRequire,
					personCount: order.personCount || 1,
					location: order.location || order.address || '未指定地点',
					address: order.address,
					enrollCount: order.enrollCount || 0,
					startTime: order.startTime,
					endTime: order.endTime,
					createTime: order.createTime,
					isInstant: order.isInstant || false,
					
					// 详情字段映射（来自OrderDetailVO）
					description: order.description || '',
					longitude: order.longitude,
					latitude: order.latitude,
					auditResult: order.auditResult,
					auditComment: order.auditComment,
					auditTime: order.auditTime,
					auditorId: order.auditorId,
					auditorName: order.auditorName,
					cancelReason: order.cancelReason,
					cancelTime: order.cancelTime,
					enrollList: order.enrollList || [],
					selectedList: order.selectedList || [],
					appliedExperts: order.appliedExperts || order.enrollList || [],
					goldCost: order.goldCost || 0,
					updateTime: order.updateTime,
					memberNickname: order.memberNickname,
					activityTypeName: order.activityTypeName,
					enrollDetails: order.enrollDetails || [],
					
					// 前端处理字段
					price: order.goldCost || order.totalFee || 0,
					time: this.formatTime(order.startTime),
					date: this.formatDate(order.startTime),
					serviceType: order.activityTypeName || order.typeName || '服务类型',
					status: statusCode,
					statusText: this.getStatusText(statusCode),
					statusClass: this.getStatusClass(statusCode),
					canEnroll: this.canEnrollOrder(order, statusCode),
					canCancel: this.canCancelEnroll(order, statusCode),
					canConfirmDeparture: this.canConfirmDeparture(order, statusCode),
					enrollStatus: this.getEnrollStatus(order)
				};
			});
		},
		
		// 映射后端状态到前端状态
		mapBackendStatus(backendStatus) {
			// 后端OrderStatusEnum: 0-DRAFT, 1-AUDITING, 2-PUBLISHED, 3-WAITING_SELECT, 4-PROCESSING, 5-COMPLETED, 6-CANCELED
			const statusMap = {
				0: 'DRAFT',
				1: 'AUDITING', 
				2: 'PUBLISHED',
				3: 'WAITING_SELECT',
				4: 'PROCESSING',
				5: 'COMPLETED',
				6: 'CANCELLED'
			};
			return statusMap[backendStatus] || 'UNKNOWN';
		},
		
		// 映射前端状态到后端状态码
		mapFrontendStatusToBackend(frontendStatus) {
			// 前端状态字符串到后端状态码的映射
			const statusMap = {
				'DRAFT': 0,
				'AUDITING': 1,
				'PUBLISHED': 2,
				'WAITING_SELECT': 3,
				'PROCESSING': 4,
				'COMPLETED': 5,
				'CANCELLED': 6
			};
			return statusMap[frontendStatus];
		},
		
		// 格式化地址
		formatLocation(order) {
			// 优先显示地址，其次显示经纬度
			if (order.address) {
				return order.address;
			}
			if (order.location) {
				return order.location;
			}
			if (order.longitude && order.latitude) {
				return `${order.longitude}, ${order.latitude}`;
			}
			return '未指定地点';
		},
		
		// 格式化日期
		formatDate(timeStr) {
			if (!timeStr) return '未指定日期';
			try {
				// 处理LocalDateTime格式的日期字符串
				const date = new Date(timeStr);
				if (isNaN(date.getTime())) {
					return '未指定日期';
				}
				return date.toLocaleDateString('zh-CN');
			} catch (e) {
				console.error('日期格式化错误:', e);
				return '未指定日期';
			}
		},
		
		// 获取报名状态
		getEnrollStatus(order) {
			if (!this.userInfo || !this.userInfo.memberId) {
				return 'not_enrolled';
			}
			
			const memberId = this.userInfo.memberId;
			
			// 检查是否在选中列表中
			if (order.selectedList && Array.isArray(order.selectedList)) {
				const isSelected = order.selectedList.some(item => {
					return item && (item.memberId === memberId || item.id === memberId);
				});
				if (isSelected) return 'selected';
			}
			
			// 检查是否在报名列表中（包括enrollList和enrollDetails）
			if (order.enrollList && Array.isArray(order.enrollList)) {
				const isEnrolled = order.enrollList.some(item => {
					return item && (item.memberId === memberId || item.id === memberId);
				});
				if (isEnrolled) return 'enrolled';
			}
			
			// 检查enrollDetails
			if (order.enrollDetails && Array.isArray(order.enrollDetails)) {
				const isEnrolled = order.enrollDetails.some(item => {
					return item && (item.memberId === memberId || item.id === memberId);
				});
				if (isEnrolled) return 'enrolled';
			}
			
			return 'not_enrolled';
		},
		
		// 状态筛选
		filterByStatus(status) {
			this.currentStatus = status;
			// 重新加载订单列表
			this.refreshOrders();
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'DRAFT': '待发布',
				'AUDITING': '待审核',
				'PUBLISHED': '发布中',
				'WAITING_SELECT': '待选择达人',
				'PROCESSING': '进行中',
				'COMPLETED': '已完成',
				'CANCELLED': '已取消'
			};
			return statusMap[status] || '未知状态';
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			const classMap = {
				'DRAFT': '',
				'AUDITING': '',
				'PUBLISHED': '',
				'WAITING_SELECT': '',
				'PROCESSING': 'selected',
				'COMPLETED': 'selected',
				'CANCELLED': 'failed'
			};
			return classMap[status] || '';
		},
		
		// 判断是否可以报名
		canEnrollOrder(order, status) {
			return (status === 'PUBLISHED' || status === 'WAITING_SELECT') && order.enrollStatus === 'not_enrolled';
		},
		
		// 判断是否可以取消报名
		canCancelEnroll(order, status) {
			return order.enrollStatus === 'enrolled' && (status === 'PUBLISHED' || status === 'WAITING_SELECT');
		},
		
		// 判断是否可以确认出发
		canConfirmDeparture(order, status) {
			return status === 'PROCESSING' && order.enrollStatus === 'selected';
		},
		
		// 报名订单
		async enrollOrder(orderId) {
			try {
				uni.showLoading({
					title: '报名中...',
					mask: true
				});
				
				const app = getApp().globalData;
				let res = await this.$requestHttp.post(app.commonApi.signUp(orderId), {
					data: {}
				});
				
				uni.hideLoading();
				
				if (res.code === 200) {
					uni.showToast({
						title: '报名成功',
						icon: 'success'
					});
					
					// 刷新订单列表
					this.loadOrders();
				} else {
					uni.showToast({
						title: res.msg || '报名失败',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('报名订单异常:', error);
				uni.showToast({
					title: '网络异常，请稍后重试',
					icon: 'none'
				});
			}
		},
		
		// 取消报名
		async cancelEnroll(orderId) {
			try {
				uni.showModal({
					title: '确认取消',
					content: '确定要取消报名吗？',
					success: async (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: '取消中...',
								mask: true
							});
							
							const app = getApp().globalData;
							let apiRes = await this.$requestHttp.post(app.commonApi.cancelSignUp(orderId), {
								data: {}
							});
							
							uni.hideLoading();
							
							if (apiRes.code === 200) {
								uni.showToast({
									title: '取消成功',
									icon: 'success'
								});
								
								// 刷新订单列表
								this.loadOrders();
							} else {
								uni.showToast({
									title: apiRes.msg || '取消失败',
									icon: 'none'
								});
							}
						}
					}
				});
			} catch (error) {
				uni.hideLoading();
				console.error('取消报名异常:', error);
				uni.showToast({
					title: '网络异常，请稍后重试',
					icon: 'none'
				});
			}
		},
		
		// 确认出发
		confirmDeparture(e) {
			const orderId = e.currentTarget.dataset.id;
			console.log('确认出发，订单ID:', orderId);
			
			uni.showModal({
				title: '确认出发',
				content: '确认已出发前往服务地点？',
				success: (res) => {
					if (res.confirm) {
						// 这里可以调用确认出发的API
						uni.showToast({
							title: '已确认出发',
							icon: 'success'
						});
						
						// 刷新订单列表
						this.loadOrders();
					}
				}
			});
		},
		
		// 点击订单项
		goToOrderDetail(orderId) {
			uni.navigateTo({
				url: `/pages/order-detail/order-detail?orderId=${orderId}`
			});
		},
		
		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '未指定时间';
			try {
				// 处理LocalDateTime格式的时间字符串
				const date = new Date(timeStr);
				if (isNaN(date.getTime())) {
					return '未指定时间';
				}
				return date.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				});
			} catch (e) {
				console.error('时间格式化错误:', e);
				return timeStr;
			}
		}
	},
	

}
</script>

<style scoped>
/* 订单列表页面样式 */
.container {
  padding: 0;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 状态筛选栏样式 */
.status-filter {
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  transition: all 0.3s;
}

.filter-item.active {
  background-color: #1890ff;
  color: #fff;
}

.filter-item:first-child {
  margin-left: 20rpx;
}

.filter-item:last-child {
  margin-right: 20rpx;
}

/* 订单列表区域 */
.order-item {
  margin: 20rpx;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 200rpx;
  color: #999;
}

.empty-tip image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
}

.order-content {
  padding: 24rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-status {
  font-size: 26rpx;
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  background-color: #e6f7ff;
  color: #1890ff;
}

.order-status.selected {
  background-color: #f6ffed;
  color: #52c41a;
  font-weight: bold;
}

.order-status.failed {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.order-date {
  font-size: 24rpx;
  color: #999;
}

.order-body {
  margin-bottom: 20rpx;
}

.order-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #333;
}

.publisher-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.publisher-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.publisher-info .label {
  color: #666;
  margin-right: 10rpx;
}

.publisher-info .value {
  color: #333;
  font-weight: 500;
}

.order-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  background-color: #f9f9f9;
  padding: 12rpx;
  border-radius: 8rpx;
}

.enroll-stats {
  margin-top: 12rpx;
  padding: 8rpx 12rpx;
  background-color: #e6f7ff;
  border-radius: 6rpx;
  border-left: 4rpx solid #1890ff;
}

.stats-text {
  font-size: 24rpx;
  color: #1890ff;
}

.selected-info {
  margin: 16rpx 0;
  padding: 12rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  border-left: 6rpx solid #52c41a;
}

.expert-info {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.expert-info .label {
  color: #666;
  margin-right: 10rpx;
}

.expert-info .value {
  color: #333;
  font-weight: 500;
}

.expert-info .self-selected {
  color: #52c41a;
  font-weight: bold;
}

.service-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-type {
  font-size: 28rpx;
  color: #666;
}

.order-price {
  font-size: 32rpx;
  color: #f5222d;
  font-weight: bold;
}

.location-row, .time-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.location-text, .time-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.order-footer {
  margin-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.status-tip {
  font-size: 24rpx;
  color: #faad14;
  margin-bottom: 10rpx;
}

.fail-reason {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-bottom: 16rpx;
  background-color: #fff2f0;
  padding: 10rpx;
  border-radius: 6rpx;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
}

.enroll-button {
  background-color: #52c41a;
  color: white;
  font-size: 28rpx;
  padding: 0 30rpx;
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 32rpx;
  border: none;
}

.cancel-button {
  background-color: #ff4d4f;
  color: white;
  font-size: 28rpx;
  padding: 0 30rpx;
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 32rpx;
  border: none;
}

.confirm-button {
  background-color: #1890ff;
  color: white;
  font-size: 28rpx;
  padding: 0 30rpx;
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 32rpx;
  border: none;
}

.enrolled-status {
  font-size: 26rpx;
  color: #faad14;
  padding: 10rpx 20rpx;
  background-color: #fffbe6;
  border-radius: 16rpx;
}

.loading-tip, .load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: #ccc;
  font-size: 24rpx;
}

/* 特殊状态下订单的整体样式 */
.order-item.in-progress {
  border-left: 8rpx solid #1890ff;
}

.order-item.completed {
  border-left: 8rpx solid #52c41a;
}

.order-item.cancelled {
  border-left: 8rpx solid #d9d9d9;
  opacity: 0.8;
} 
</style>
