package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * (BOrderComment)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("b_order_comment")
@ApiModel("订单评价表")
public class BOrderCommentPo extends BaseObjPo {

    //订单ID
    @ApiModelProperty(value = "订单ID")
    private String orderId;
    @ApiModelProperty(value = "会员ID")
    //会员ID
    private String memberId;
    //评价勾选内容
    @ApiModelProperty(value = "评价勾选内容")
    private String commentJson;
    //评价内容
    @ApiModelProperty(value = "评价内容")
    private String comment;
    //评价星级
    @ApiModelProperty(value = "评价星级")
    private String commentStarts;

}

