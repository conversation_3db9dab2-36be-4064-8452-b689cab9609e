<template>
  <div class="navbar clay-card">
    <div class="navbar-left">
      <div class="hamburger-container" @click="toggleSideBar">
        <i :class="{'el-icon-s-fold': sidebar.opened, 'el-icon-s-unfold': !sidebar.opened}"></i>
      </div>
    </div>
    
    <!-- 历史菜单标签 -->
    <div class="history-tags" v-if="visitedViews.length > 0">
      <el-scrollbar ref="scrollPane" class="tags-scrollbar" wrap-class="tags-scrollbar-wrapper">
        <div class="tags-container">
          <span 
            v-for="(tag, index) in visibleTags" 
            :key="tag.path" 
            :class="{'tag-item': true, 'active': isActive(tag)}"
            @click="goToView(tag)">
            <i :class="getTagIcon(tag)"></i>
            <span class="tag-title">{{ tag.title }}</span>
            <i class="el-icon-close" @click.stop="closeTag(tag)"></i>
          </span>
          <el-dropdown v-if="extraTags.length > 0" trigger="click">
            <span class="tag-item more-tags">
              <i class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown" class="more-tags-dropdown clay-dropdown">
              <el-dropdown-item 
                v-for="tag in extraTags" 
                :key="tag.path"
                @click.native="goToView(tag)">
                <i :class="getTagIcon(tag)"></i>
                {{ tag.title }}
                <i class="el-icon-close" @click.stop="closeTag(tag)"></i>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-scrollbar>
    </div>
    
    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar || require('@/assets/images/default-avatar.png')" class="user-avatar" :key="avatarKey">
          <span class="user-name">{{ name || '管理员' }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown clay-dropdown">
          <el-dropdown-item>
            <span @click="toProfile">个人资料</span>
          </el-dropdown-item>
          <el-dropdown-item divided>
            <span @click="logout">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      
      <!-- 设置按钮 - 移到管理员右边 -->
      <div class="setting-btn" @click="openSettings">
        <i class="el-icon-setting"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'Navbar',
  data() {
    return {
      visitedViews: [],
      avatarKey: 0
    };
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'name'
    ]),
    visibleTags() {
      return this.visitedViews.slice(0, 7);
    },
    extraTags() {
      return this.visitedViews.slice(7);
    }
  },
  watch: {
    $route(route) {
      this.addVisitedView(route);
    }
  },
  mounted() {
    if (this.$route) {
      this.addVisitedView(this.$route);
    }
    
    // 监听头像更新事件
    this.$root.$on('avatar-updated', (newAvatar) => {
      console.log('Navbar收到头像更新事件:', newAvatar);
      // 强制重新渲染组件
      this.$forceUpdate();
      this.avatarKey++;
    });
  },
  beforeDestroy() {
    // 移除事件监听
    this.$root.$off('avatar-updated');
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar');
    },
    async logout() {
      await this.$store.dispatch('user/logout');
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
    toProfile() {
      this.$router.push('/profile');
    },
    openSettings() {
      // 触发侧边栏中的设置抽屉
      this.$root.$emit('open-settings-drawer');
    },
    addVisitedView(route) {
      if (!route.meta || route.meta.hidden) return;
      
      const { name, path, meta } = route;
      const title = meta.title || name;
      const icon = meta.icon || 'el-icon-document';
      
      // 检查是否已存在
      const isExist = this.visitedViews.some(v => v.path === path);
      if (!isExist) {
        this.visitedViews.push({
          path,
          title,
          icon
        });
        
        // 最多保留8个历史记录
        if (this.visitedViews.length > 8) {
          this.visitedViews.shift();
        }
      }
    },
    isActive(tag) {
      return tag.path === this.$route.path;
    },
    getTagIcon(tag) {
      return tag.icon || 'el-icon-document';
    },
    goToView(tag) {
      if (this.$route.path !== tag.path) {
        this.$router.push(tag.path);
      }
    },
    closeTag(tag) {
      const index = this.visitedViews.findIndex(v => v.path === tag.path);
      if (index > -1) {
        this.visitedViews.splice(index, 1);
      }
      
      // 如果关闭的是当前页，则跳转到最后一个标签
      if (this.isActive(tag) && this.visitedViews.length) {
        this.$router.push(this.visitedViews[this.visitedViews.length - 1].path);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .tags-scrollbar-wrapper {
  margin-bottom: 0 !important;
  margin-right: 0 !important;
  overflow-x: auto;
  &::-webkit-scrollbar {
    height: 6px;
    background: transparent;
  }
  &:hover::-webkit-scrollbar {
    background: transparent; // 修改为透明背景
  }
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }
}
::v-deep .el-scrollbar__view {
  height: 100%;
  display: flex;
  align-items: center;
}
::v-deep .el-scrollbar__bar {
  display: none !important;
}
.navbar {
  height: 70px;
  overflow: hidden;
  position: relative;
  background-color: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  border-radius: 16px;
  margin: 15px;
  width: auto;
  box-sizing: border-box;
  
  &.fixed-header {
    position: fixed;
    top: 15px;
    left: 265px;
    right: 15px;
    width: calc(100% - 280px);
    z-index: 999;
    
    .sidebar-collapsed & {
      left: 79px;
      width: calc(100% - 94px);
    }
  }
  
  .navbar-left {
    display: flex;
    align-items: center;
    
    .hamburger-container {
      line-height: 70px;
      height: 100%;
      width: 60px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 16px 0 0 16px;
      
      &:hover {
        background: rgba(248, 208, 16, 0.1);
      }
      
      i {
        font-size: 20px;
        color: #303030;
      }
    }
  }
  
  .history-tags {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    
    .tags-scrollbar {
      flex: 1;
      height: 100%;
      
      .tags-scrollbar-wrapper {
        overflow-x: auto;
        
        &::-webkit-scrollbar {
          display: none;
        }
      }
    }
    
    .tags-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      white-space: nowrap;
      padding: 0 10px;
      
      .tag-item {
        display: inline-flex;
        align-items: center;
        height: 36px;
        padding: 0 12px;
        margin-right: 10px;
        border-radius: 8px;
        background-color: #f8f8f8;
        color: #606266;
        font-size: 15px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background-color: rgba(248, 208, 16, 0.1);
          transform: translateY(-2px);
        }
        
        &.active {
          background-color: #F8D010;
          color: #303030;
          font-weight: 500;
        }
        
        i {
          margin-right: 5px;
          font-size: 16px;
        }
        
        .tag-title {
          margin: 0 5px;
        }
        
        .el-icon-close {
          margin-left: 5px;
          border-radius: 50%;
          font-size: 12px;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &:hover {
            background-color: rgba(0, 0, 0, 0.1);
          }
        }
      }
      .more-tags {
        padding: 0 8px;
        
        i {
          margin-right: 0;
        }
      }
    }
  }
  
  .right-menu {
    display: flex;
    align-items: center;
    padding-right: 24px;
    
    .setting-btn {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 10px;
      margin-right: 10px;
      transition: all 0.3s;
      
      &:hover {
        background-color: rgba(248, 208, 16, 0.1);
        transform: rotate(30deg);
      }
      
      i {
        font-size: 20px;
        color: #909090;
      }
    }
    
    .avatar-container {
      margin-right: 10px;
      
      .avatar-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 12px;
        transition: all 0.3s;
        
        &:hover {
          background-color: rgba(248, 208, 16, 0.1);
        }
        
        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 12px;
          object-fit: cover;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .user-name {
          margin: 0 10px;
          font-size: 15px;
          font-weight: 500;
          color: #303030;
        }
        
        .el-icon-caret-bottom {
          font-size: 12px;
          color: #909090;
          transition: transform 0.3s;
        }
        
        &:hover .el-icon-caret-bottom {
          transform: rotate(180deg);
        }
      }
    }
  }
}

.clay-dropdown {
  ::v-deep .el-dropdown-menu {
    border-radius: 12px;
    padding: 6px;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
    border: none;
    
    .el-dropdown-menu__item {
      padding: 10px 16px;
      font-size: 14px;
      border-radius: 8px;
      margin: 4px 0;
      color: #303030;
      
      &:hover, &:focus {
        background-color: rgba(248, 208, 16, 0.1);
        color: #F8D010;
      }
      
      &.is-disabled {
        color: #c0c4cc;
      }
    }
    
    .el-dropdown-menu__item--divided {
      margin-top: 6px;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
  }
  
  &.more-tags-dropdown {
    min-width: 160px;
  }
}
</style>