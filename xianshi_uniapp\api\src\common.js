import { getCurrentMemberInfo } from "..";

export default {
	// 发送短线验证码 get
	getLoginRole: '/app/member/mobile/captcha',
	// 登录 post
	login: '/app/member/login',
	// 获取已发布的订单
	getOrderLIst: '/api/order/page',
	// 获取单个订单详情
	getOrderDetail: (orderId) => `/api/order/detail/${orderId}`,
	// 创建订单
	createOrder: '/api/order/create',
	// 获取陪玩类型
	getPlayType: '/api/activity-type/tree',
	//提交审核
	submitAudit: (orderId) => `/api/order/submit/${orderId}`,

	// 获取个人资料完整度 get
	getMemberCompletion: '/app/member/profile/completion',
	// 更新会员资料
	updateMemberInfo: '/app/member/profile',
	// 获取当前会员信息 get
	getCurrentMemberInfo: '/app/member',


	// 达人报名订单
	signUp: (orderId) => `/api/order/enroll-detail/enroll/${orderId}`,
	// 取消报名
	cancelSignUp: (orderId) => `/api/order/enroll-detail/cancel/${orderId}`,
	// 根据当前用户角色获取订单列表
	getRoleOrderList: '/api/order/my-orders',
	// 选择达人
	selectTalents: (orderId) => `/api/order/select-talents/${orderId}`,


	// 获取会员订单发布的列表
	getUserOrderList: '/api/member/order/publish-orders',
	// 申请达人认证
	signExpertAuth: '/app/member/talent/apply',


	// OSS文件上传接口
	ossUploadFile: '/api/oss/upload',
	ossUploadImage: '/api/oss/upload/image',
	ossDeleteFile: '/api/oss/delete'
}
