package com.play.xianshibusiness.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 消息广播DTO
 */
@Data
@ApiModel(value = "消息广播DTO")
public class MessageBroadcastDTO {
    
    @NotBlank(message = "消息内容不能为空")
    @ApiModelProperty(value = "消息内容", required = true)
    private String content;
    
    @NotNull(message = "消息类型不能为空")
    @ApiModelProperty(value = "消息类型：1-系统通知，2-订单通知，3-活动通知", required = true)
    private Integer messageType;
    
    @ApiModelProperty(value = "接收者类型：1-所有用户，2-指定用户组，3-指定用户")
    private Integer receiverType = 1;
    
    @ApiModelProperty(value = "指定会员ID列表")
    private List<String> memberIds;
    
    @ApiModelProperty(value = "关联ID")
    private String relatedId;
    
    @ApiModelProperty(value = "关联类型：1-订单，2-活动，3-系统")
    private Integer relatedType = 3;
    
    @ApiModelProperty(value = "操作人ID")
    private String operatorId;
} 