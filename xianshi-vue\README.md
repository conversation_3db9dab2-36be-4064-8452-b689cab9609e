# 闲时有伴管理系统

## 项目介绍

闲时有伴后台管理系统是为闲时有伴APP项目开发的管理端，主要用于管理线下陪玩服务的订单、用户等信息。

## 技术栈

- Vue 2.x
- Vuex
- Vue Router
- Element UI
- Axios
- Echarts

## 项目结构

```
xianshi-vue/
├── public/                  # 静态资源
├── src/
│   ├── api/                 # API接口
│   ├── assets/              # 静态资源
│   ├── components/          # 公共组件
│   ├── layout/              # 布局组件
│   ├── router/              # 路由配置
│   ├── store/               # Vuex状态管理
│   ├── utils/               # 工具函数
│   ├── views/               # 页面
│   ├── App.vue              # 根组件
│   ├── main.js              # 入口文件
│   └── permission.js        # 权限控制
├── .gitignore               # Git忽略文件
├── package.json             # 项目依赖
└── vue.config.js            # Vue配置文件
```

## 功能模块

- 登录/登出
- 首页/控制台
- 订单管理
  - 订单列表
  - 订单详情
  - 订单审核
  - 订单取消

## 环境变量配置（新增说明）

- 开发环境：在根目录新建 .env.development 文件
- 生产环境：在根目录新建 .env.production 文件
- 示例内容：
```
VUE_APP_BASE_API=/admin
```

## 项目整理说明（2024年优化）

- 删除了冗余的 vue.config.local.js、env-config.js、start-local.js
- 统一用 .env.development、.env.production 管理环境变量
- package.json 脚本精简，仅保留 serve、build、lint
- .idea 目录已忽略
- 详细中文备注见各配置文件

## 支持的浏览器

- Chrome
- Firefox
- Safari
- Edge

## 项目说明

本项目采用组件化、模块化的开发方式，遵循Vue的开发规范和最佳实践。项目中使用了Element UI组件库，结合了自定义的主题样式。

项目配色方案采用：
- 主色调：#F8D010 (黄金色)
- 辅助色：#F8E068 (淡黄色)
- 中性色：#909090 (灰色)
- 深色：#303030 (深灰色)

设计风格现代、简洁，符合年轻人的审美，提供了良好的响应式体验。 