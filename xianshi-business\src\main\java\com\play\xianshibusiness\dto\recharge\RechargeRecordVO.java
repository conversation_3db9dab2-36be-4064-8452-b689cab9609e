package com.play.xianshibusiness.dto.recharge;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值记录VO
 */
@Data
@ApiModel("充值记录VO")
public class RechargeRecordVO {

    @ApiModelProperty(value = "充值记录ID")
    private String id;
    
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    
    @ApiModelProperty(value = "充值金额(元)")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "充值金币数量")
    private BigDecimal goldAmount;
    
    @ApiModelProperty(value = "充值方式")
    private String payType;
    
    @ApiModelProperty(value = "充值方式描述")
    private String payTypeDesc;
    
    @ApiModelProperty(value = "充值状态")
    private String status;
    
    @ApiModelProperty(value = "充值状态描述")
    private String statusDesc;
    
    @ApiModelProperty(value = "充值套餐名称")
    private String packageName;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "支付时间")
    private LocalDateTime payTime;
    
    @ApiModelProperty(value = "备注")
    private String remark;
} 