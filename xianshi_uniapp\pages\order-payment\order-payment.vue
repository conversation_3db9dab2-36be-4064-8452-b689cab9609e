<template>
	<view class="container">
		<!-- 订单信息 -->
		<view class="order-info">
			<!-- 基本信息 -->
			<view class="info-item">
				<view class="info-label">基本信息</view>
				<view class="info-content">
					<view class="info-row" v-if="orderInfo.expertName">
						<text class="info-key">约单对象：</text>
						<text class="info-value highlight">{{ orderInfo.expertName || '未指定' }}</text>
					</view>
					<view class="info-row">
						<text class="info-key">活动地点：</text>
						<view class="info-value">
							<view>{{ orderInfo.location || '未指定地点' }}</view>
							<view class="detail-text">{{ orderInfo.addressDetail || '' }}</view>
						</view>
					</view>
					<view class="info-row">
						<text class="info-key">活动时间：</text>
						<text class="info-value">{{ orderInfo.date || '未指定日期' }} {{ orderInfo.time || '' }}</text>
					</view>
					<view class="info-row">
						<text class="info-key">活动时长：</text>
						<text class="info-value">{{ orderInfo.duration || 0 }}</text>
					</view>
					<view class="info-row" v-if="!orderInfo.expertName">
						<text class="info-key">性别需求：</text>
						<text class="info-value">{{ orderInfo.gender === 'male' ? '男' : (orderInfo.gender === 'female' ? '女' :
							'不限')}}</text>
					</view>
					<view class="info-row" v-if="!orderInfo.expertName">
						<text class="info-key">参与人数：</text>
						<text class="info-value">{{ orderInfo.people || 1 }}人</text>
					</view>
					<view class="info-row">
						<text class="info-key">服务项目：</text>
						<text class="info-value">{{ serviceDisplay || '未指定服务' }}</text>
					</view>
				</view>
			</view>

			<!-- 需求描述 -->
			<view class="info-item">
				<view class="info-label">需求描述</view>
				<view class="info-content description">{{ orderInfo.description || '无' }}</view>
			</view>
		</view>

		<!-- 服务费用预算 -->
		<view class="fee-section">
			<view class="section-divider">服务费用预算</view>
			<view class="fee-item preview">
				<text>小时费用</text>
				<text class="fee">¥{{ orderInfo.hourlyRate || 0 }}/每小时 </text>
			</view>
			<view class="fee-item preview">
				<text>服务总预算</text>
				<text class="fee">¥{{ serviceFee || 0 }}</text>
			</view>
			<view class="fee-item-tip">
				<text class="fee-tip">*服务费用将在服务人员与您见面并确认开始服务前由您直接支付给服务人员</text>
			</view>
		</view>

		<!-- 金币支付部分 -->
		<view class="coin-section">
			<view class="coin-header">
				<view class="coin-title">发布订单</view>
				<view class="coin-balance" @tap="navigateToCoinCenter">
					<text>剩余: {{ coinBalance }}金币</text>
					<text class="coin-recharge">充值 ></text>
				</view>
			</view>
			<view class="coin-row">
				<view class="coin-label">所需金币</view>
				<view class="coin-value">{{ coinFee }}<text class="coin-unit">金币</text></view>
			</view>
			<view class="coin-tip">
				<text>*未匹配成功服务人员时自动返还金币；成功匹配后不予退还</text>
			</view>
		</view>

		<!-- 底部支付按钮 -->
		<view class="bottom-button">
			<button class="pay-btn" @tap="handlePayment">{{ orderInfo.expertName ? '确认约单' : '确认发布' }}</button>
		</view>
	</view>
</template>

<script>
import { getGoldBalance, getAllOrderTypes, getOrderTypeDetail } from '@/api/index.js';

export default {
	data() {
		return {
			orderInfo: {},
			serviceDisplay: '',   // 服务项目显示名称
			serviceFee: 0,        // 服务费预算
			coinFee: 0,           // 所需金币数量
			coinBalance: 0,       // 用户金币余额
			apiParams: null,      // 存储API请求参数
			orderTypes: [],       // 订单类型列表
			currentOrderType: null // 当前订单类型
		}
	},
	/**
 * 生命周期函数--监听页面加载
 */
	onLoad(options) {
		// 添加日志，帮助调试数据传递
		console.log('订单支付页面加载，options:', options);

		// 处理从URL参数传递的数据
		if (options.orderData && options.createParams) {
			try {
				const orderInfo = JSON.parse(decodeURIComponent(options.orderData));
				const createParams = JSON.parse(decodeURIComponent(options.createParams));
				
				console.log('从URL参数接收到订单数据:', orderInfo);
				console.log('从URL参数接收到创建参数:', createParams);
				
				// 保存API请求参数，用于后续创建订单
				this.apiParams = createParams;
				
				// 处理服务项目名称显示
				let serviceDisplay = '';
				if (orderInfo.service && orderInfo.serviceSubType) {
					serviceDisplay = `${orderInfo.service} > ${orderInfo.serviceSubType}`;
				} else if (orderInfo.service) {
					serviceDisplay = orderInfo.service;
				} else if (orderInfo.serviceSubType) {
					serviceDisplay = orderInfo.serviceSubType;
				} else {
					serviceDisplay = '未指定服务';
				}
				
				// 确保订单数据中的所有字段都有值，防止空值导致的显示问题
				const safeOrderInfo = {
					...orderInfo,
					location: orderInfo.location || '未指定地点',
					addressDetail: orderInfo.addressDetail || '',
					date: orderInfo.date || '未指定日期',
					time: orderInfo.time || '未指定时间',
					duration: orderInfo.duration || '0小时',
					gender: orderInfo.gender || 'any',
					people: orderInfo.people || 1,
					hourlyRate: orderInfo.hourlyRate || 0,
					description: orderInfo.description || '无'
				};
				
				this.orderInfo = safeOrderInfo;
				this.serviceDisplay = serviceDisplay;
				
				console.log('已设置订单数据到页面:', this.orderInfo);
				this.calculateTotalAmount();
		} catch (err) {
			console.error('解析URL参数数据时出错:', err);
			console.error('错误详情:', err.message);
			console.error('orderData:', options.orderData);
			console.error('createParams:', options.createParams);
			
			// 提供更详细的错误信息
			let errorMessage = '订单数据解析失败';
			if (err.message) {
				errorMessage += ': ' + err.message;
			}
			
			uni.showToast({
				title: errorMessage,
				icon: 'none',
				duration: 3000
			});
			
			// 设置默认数据，避免页面完全无法使用
			this.orderInfo = {
				location: '未指定地点',
				addressDetail: '',
				date: '未指定日期',
				time: '未指定时间',
				duration: '0小时',
				gender: 'any',
				people: 1,
				hourlyRate: 0,
				description: '无'
			};
			this.serviceDisplay = '数据解析失败';
		}
		} else {
			// 兼容原有的eventChannel方式
			try {
				const eventChannel = this.getOpenerEventChannel();
				if (eventChannel) {
					eventChannel.on('acceptOrderData', (data) => {
						console.log('接收到订单数据:', data);

						if (!data || !data.orderInfo) {
							console.error('接收到的订单数据为空或无效');
							uni.showToast({
								title: '订单数据加载失败',
								icon: 'none',
								duration: 2000
							});
							return;
						}

						const orderInfo = data.orderInfo;
						// 保存API请求参数，用于后续创建订单
						this.apiParams = data.apiParams;

						// 处理服务项目名称显示
						let serviceDisplay = '';
						if (orderInfo.service && orderInfo.serviceSubType) {
							serviceDisplay = `${orderInfo.service} > ${orderInfo.serviceSubType}`;
						} else if (orderInfo.service) {
							serviceDisplay = orderInfo.service;
						} else if (orderInfo.serviceSubType) {
							serviceDisplay = orderInfo.serviceSubType;
						} else {
							serviceDisplay = '未指定服务';
						}

						// 确保订单数据中的所有字段都有值，防止空值导致的显示问题
						const safeOrderInfo = {
							...orderInfo,
							location: orderInfo.location || '未指定地点',
							addressDetail: orderInfo.addressDetail || '',
							date: orderInfo.date || '未指定日期',
							time: orderInfo.time || '未指定时间',
							duration: orderInfo.duration || '0小时',
							gender: orderInfo.gender || 'any',
							people: orderInfo.people || 1,
							hourlyRate: orderInfo.hourlyRate || 0,
							description: orderInfo.description || '无'
						};

						this.orderInfo = safeOrderInfo;
						this.serviceDisplay = serviceDisplay;

						console.log('已设置订单数据到页面:', this.orderInfo);
						this.calculateTotalAmount();
					});
				} else {
					console.log('没有找到eventChannel，可能是直接访问页面');
				}
			} catch (err) {
				console.error('处理eventChannel时出错:', err);
				// 不显示错误提示，因为这可能是正常的直接访问页面
			}
		}

		// 确保全局数据存在
		const app = getApp();
		if (!app.globalData) {
			app.globalData = {};
		}
		if (!app.globalData.currentUserId) {
			app.globalData.currentUserId = 'user_123'; // 默认用户ID
		}

		// 获取用户金币余额和订单类型信息
		this.loadGoldBalance();
		this.loadOrderTypes();
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},

		navigateToCoinCenter() {
			// 保存当前页面，以便从充值页面返回后刷新金币余额
			const currentPage = this;

			// 监听页面显示事件，从充值页面返回时更新金币余额
			this.onShowCallback = () => {
				// 重新获取最新金币余额
				const latestCoinBalance = uni.getStorageSync('coinBalance') || 0;
				currentPage.coinBalance = latestCoinBalance;
			};

			// 注册页面显示回调
			this.onShow = this.onShowCallback;

			// 跳转到充值页面
			uni.navigateTo({
				url: '/pages/coin-center/coin-center'
			});
		},

		// 页面显示时触发
		onShow() {
			// 更新金币余额
			this.loadGoldBalance();
		},

		/**
		 * 获取用户金币余额
		 */
		async loadGoldBalance() {
			try {
				const res = await getGoldBalance();
				if (res.code === 200) {
					this.coinBalance = res.data || 0;
					console.log('获取金币余额成功:', this.coinBalance);
				} else {
					console.error('获取金币余额失败:', res.msg);
					// 使用缓存值作为备用
					this.coinBalance = uni.getStorageSync('coinBalance') || 0;
				}
			} catch (error) {
				console.error('获取金币余额异常:', error);
				// 使用缓存值作为备用
				this.coinBalance = uni.getStorageSync('coinBalance') || 0;
			}
		},

		/**
		 * 获取订单类型列表
		 */
		async loadOrderTypes() {
			try {
				const res = await getAllOrderTypes();
				if (res.code === 200) {
					this.orderTypes = res.data || [];
					console.log('获取订单类型成功:', this.orderTypes);
					// 根据当前订单的服务类型匹配所需金币
					this.matchOrderTypeAndSetCoinFee();
				} else {
					console.error('获取订单类型失败:', res.msg);
					// 使用默认值
					this.coinFee = 2;
				}
			} catch (error) {
				console.error('获取订单类型异常:', error);
				// 使用默认值
				this.coinFee = 2;
			}
		},

		/**
		 * 根据订单服务类型匹配所需金币
		 */
		matchOrderTypeAndSetCoinFee() {
			if (!this.orderTypes || this.orderTypes.length === 0) {
				this.coinFee = 2; // 默认值
				return;
			}

			// 优先使用activityTypeId精确匹配
			if (this.orderInfo.activityTypeId) {
				this.getOrderTypeByIdAndSetCoinFee(this.orderInfo.activityTypeId);
				return;
			}

			// 根据订单的服务类型查找对应的订单类型
			const serviceType = this.orderInfo.service || this.serviceDisplay;
			const matchedType = this.orderTypes.find(type => 
				type.name === serviceType || 
				type.name.includes(serviceType) ||
				serviceType.includes(type.name)
			);

			if (matchedType) {
				this.currentOrderType = matchedType;
				this.coinFee = matchedType.needGold || 2;
				console.log('匹配到订单类型:', matchedType.name, '所需金币:', this.coinFee);
			} else {
				// 如果没有匹配到，使用第一个订单类型或默认值
				if (this.orderTypes.length > 0) {
					this.currentOrderType = this.orderTypes[0];
					this.coinFee = this.orderTypes[0].needGold || 2;
					console.log('未匹配到具体类型，使用默认类型:', this.orderTypes[0].name, '所需金币:', this.coinFee);
				} else {
					this.coinFee = 2;
					console.log('未找到订单类型，使用默认金币数量:', this.coinFee);
				}
			}
		},

		/**
		 * 根据订单类型ID获取详细信息并设置金币费用
		 */
		async getOrderTypeByIdAndSetCoinFee(typeId) {
			try {
				const res = await getOrderTypeDetail(typeId);
				if (res.code === 200 && res.data) {
					this.currentOrderType = res.data;
					this.coinFee = res.data.needGold || 2;
					console.log('根据ID获取订单类型成功:', res.data.name, '所需金币:', this.coinFee);
				} else {
					console.error('根据ID获取订单类型失败:', res.msg);
					// 回退到名称匹配
					this.fallbackToNameMatch();
				}
			} catch (error) {
				console.error('根据ID获取订单类型异常:', error);
				// 回退到名称匹配
				this.fallbackToNameMatch();
			}
		},

		/**
		 * 回退到名称匹配
		 */
		fallbackToNameMatch() {
			const serviceType = this.orderInfo.service || this.serviceDisplay;
			const matchedType = this.orderTypes.find(type => 
				type.name === serviceType || 
				type.name.includes(serviceType) ||
				serviceType.includes(type.name)
			);

			if (matchedType) {
				this.currentOrderType = matchedType;
				this.coinFee = matchedType.needGold || 2;
				console.log('回退匹配到订单类型:', matchedType.name, '所需金币:', this.coinFee);
			} else {
				this.coinFee = 2;
				console.log('回退匹配失败，使用默认金币数量:', this.coinFee);
			}
		},

		async handlePayment() {
			// 验证必要参数
			if (!this.apiParams) {
				uni.showToast({
					title: "订单参数缺失，请重新填写",
					icon: "none",
					duration: 2000
				});
				return;
			}

			// 重新获取最新金币余额
			await this.loadGoldBalance();

			// 检查金币余额是否足够
			if (this.coinBalance < this.coinFee) {
				uni.showModal({
					title: '金币不足',
					content: `发布订单需要${this.coinFee}金币，您当前余额为${this.coinBalance}金币，是否前往充值？`,
					confirmText: '去充值',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.navigateToCoinCenter();
						}
					}
				});
				return;
			}

			// 显示加载提示
			uni.showLoading({
				title: '创建订单中...',
				mask: true
			});

			console.log("创建订单参数:", this.apiParams);
			
			// 调用API创建订单
			const app = getApp().globalData;
			this.$requestHttp.post(app.commonApi.createOrder, {
				data: this.apiParams
			}).then(res => {
				if (res.code === 200) {
					// 创建成功，获取订单ID
					console.log("订单创建成功:", res.data);
					
					// 从响应中获取订单ID
					const orderId = res.data?.id || res.data?.orderId || res.message;
					
					if (!orderId) {
						uni.hideLoading();
						uni.showToast({
							title: "订单创建失败：未获取到订单ID",
							icon: "none",
							duration: 2000
						});
						return;
					}
					
					// 提交审核
					uni.showLoading({
						title: '提交审核中...',
						mask: true
					});
					
					return this.$requestHttp.post(app.commonApi.submitAudit(orderId), {
						data: {}
					});
				} else {
					// 创建失败
					uni.hideLoading();
					console.error("订单创建失败:", res);
					uni.showToast({
						title: res.message || "订单创建失败，请重试",
						icon: "none",
						duration: 2000
					});
					throw new Error(res.message || "订单创建失败");
				}
			}).then(auditRes => {
				uni.hideLoading();
				
				if (auditRes && auditRes.code === 200) {
					// 审核提交成功
					console.log("审核提交成功:", auditRes.data);
					
					uni.showToast({
						title: "发布成功",
						icon: "success",
						duration: 2000
					});

					// 延迟跳转到首页
					setTimeout(() => {
						uni.reLaunch({
							url: "/pages/home/<USER>",
							fail: (err) => {
								console.error("跳转首页失败:", err);
								uni.switchTab({
									url: "/pages/home/<USER>"
								});
							}
						});
					}, 2000);
				} else {
					// 审核提交失败
					console.error("审核提交失败:", auditRes);
					uni.showToast({
						title: auditRes?.message || "提交审核失败，请重试",
						icon: "none",
						duration: 2000
					});
				}
			}).catch(error => {
				uni.hideLoading();
				console.error("订单处理失败:", error);
				
				uni.showToast({
					title: "网络错误，请检查网络连接后重试",
					icon: "none",
					duration: 3000
				});
			});
		},

		// 计算订单总金额
		calculateTotalAmount() {
			try {
				if (!this.orderInfo) {
					console.error('订单信息不存在，无法计算总金额');
					return;
				}

				// 使用Number强制转换，并提供默认值为0
				const hourlyRate = Number(this.orderInfo.hourlyRate || 0);
				
				// 提取时长中的小时数
				let duration = 0;
				if (typeof this.orderInfo.duration === 'string') {
					// 处理格式如"3小时"或"3小时30分钟"
					const durationStr = this.orderInfo.duration;
					const hourMatch = durationStr.match(/(\d+)小时/);
					const minuteMatch = durationStr.match(/(\d+)分钟/);
					
					if (hourMatch) {
						duration += parseInt(hourMatch[1]);
					}
					if (minuteMatch) {
						duration += parseInt(minuteMatch[1]) / 60;
					}
				} else {
					duration = Number(this.orderInfo.duration || 0);
				}

				if (isNaN(hourlyRate) || isNaN(duration)) {
					console.error('小时费用或时长格式错误:', {
						hourlyRate: this.orderInfo.hourlyRate,
						duration: this.orderInfo.duration
					});

					// 设置默认值
					this.serviceFee = 0;
					this.totalAmount = 0;
					return;
				}

				// 计算服务费用总预算（小时费用 × 时长）
				const totalAmount = hourlyRate * duration;

				console.log('计算总金额:', { hourlyRate, duration, totalAmount });

				this.serviceFee = totalAmount.toFixed(2);
				this.totalAmount = totalAmount.toFixed(2);
			} catch (err) {
				console.error('计算总金额时出错:', err);
				this.serviceFee = 0;
				this.totalAmount = 0;
			}
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background-color: #f7f7f7;
	padding-bottom: 120rpx;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 88rpx;
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 30rpx;
	border-bottom: 1rpx solid #eee;
	z-index: 100;
}

.nav-title {
	font-size: 32rpx;
	font-weight: 500;
}

.order-info {
	/* margin-top: 108rpx; */
	padding: 30rpx;
}

.info-item {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
}

.info-label {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 12rpx;
}

.info-content {
	font-size: 30rpx;
	color: #333;
}

.location-area {
	font-size: 28rpx;
	margin-bottom: 8rpx;
}

.location-detail {
	font-size: 26rpx;
	color: #666;
}

.duration {
	color: #666;
	margin-left: 20rpx;
}

.description {
	font-size: 28rpx;
	line-height: 1.6;
	color: #333;
	padding: 16rpx;
	background: #f8f8f8;
	border-radius: 8rpx;
}

.fee-section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin: 30rpx;
}

.section-divider {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin: 20rpx 0 10rpx 0;
	border-left: 8rpx solid #ff8c00;
	padding-left: 16rpx;
}

.fee-item {
	display: flex;
	justify-content: space-between;
	padding: 20rpx 0;
	font-size: 28rpx;
	color: #666;
}

.fee-item.preview {
	color: #888;
	border-bottom: 1px dashed #eee;
}

.fee-item.real {
	color: #333;
	font-weight: 500;
}

.fee-item-tip {
	padding: 10rpx 0;
}

.fee-tip {
	font-size: 24rpx;
	color: #999;
	font-style: italic;
}

.fee {
	color: #333;
}

.fee-total {
	display: flex;
	justify-content: space-between;
	padding: 30rpx 0 10rpx;
	border-top: 1px solid #eee;
	margin-top: 20rpx;
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
}

.total-amount {
	color: #f60;
	font-size: 36rpx;
}

.bottom-button {
	padding: 30rpx;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.pay-btn {
	background: #ff8c00;
	color: #fff;
	border-radius: 50rpx;
	font-size: 32rpx;
	height: 88rpx;
	line-height: 88rpx;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
}

.info-row {
	margin-bottom: 16rpx;
	display: flex;
	align-items: flex-start;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-key {
	color: #666;
	font-size: 28rpx;
	width: 140rpx;
	flex-shrink: 0;
}

.info-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.info-value.highlight {
	color: #FF8C00;
	font-weight: 500;
}

.detail-text {
	font-size: 24rpx;
	color: #666;
	margin-top: 4rpx;
}

.fee-item.real.preview {
	color: #333;
	font-weight: 500;
	border-bottom: 1px dashed #eee;
}

/* 金币支付部分 */
.coin-section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.coin-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.coin-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.coin-balance {
	font-size: 28rpx;
	color: #ff8c00;
	font-weight: 500;
	display: flex;
	align-items: center;
}

.coin-recharge {
	margin-left: 12rpx;
	font-size: 24rpx;
	color: #fff;
	background-color: #ff8c00;
	padding: 4rpx 12rpx;
	border-radius: 24rpx;
}

.coin-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1px dashed #eee;
}

.coin-label {
	font-size: 28rpx;
	color: #333;
}

.coin-value {
	font-size: 40rpx;
	color: #ff8c00;
	font-weight: bold;
}

.coin-unit {
	font-size: 28rpx;
	margin-left: 4rpx;
	font-weight: normal;
}

.coin-tip {
	padding: 16rpx 0;
	font-size: 24rpx;
	color: #999;
	line-height: 1.5;
}
</style>
