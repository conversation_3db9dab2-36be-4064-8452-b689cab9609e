package com.play.xianshibusiness.dto.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 数据字典类型DTO
 */
@Data
@ApiModel(value = "数据字典类型DTO")
public class DataDictTypeDTO {
    
    @NotBlank(message = "类型编码不能为空")
    @ApiModelProperty(value = "类型编码", required = true)
    private String typeCode;
    
    @NotBlank(message = "类型名称不能为空")
    @ApiModelProperty(value = "类型名称", required = true)
    private String typeName;
    
    @ApiModelProperty(value = "类型描述")
    private String description;
    
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled = true;
    
    @ApiModelProperty(value = "排序")
    private Integer sort = 0;
} 