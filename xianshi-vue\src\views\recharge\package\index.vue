<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>充值套餐管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAdd">新增套餐</el-button>
      </div>
      
      <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.native.prevent>
        <el-form-item label="套餐名称">
          <el-input v-model="queryParams.name" placeholder="请输入套餐名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table
        v-loading="loading"
        :data="packageList"
        style="width: 100%"
        border
      >
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column prop="name" label="套餐名称" min-width="120" />
        <el-table-column prop="amount" label="价格(元)" min-width="80" />
        <el-table-column prop="baseGold" label="基础金币" min-width="80" />
        <el-table-column prop="bonusGold" label="赠送金币" min-width="80" />
        <el-table-column prop="totalGold" label="总金币" min-width="80" />
        <el-table-column prop="discount" label="折扣" min-width="80">
          <template slot-scope="scope">
            {{ scope.row.discount ? scope.row.discount + '折' : '无折扣' }}
          </template>
        </el-table-column>
        <el-table-column prop="recommended" label="是否推荐" min-width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.recommended ? 'success' : 'info'">
              {{ scope.row.recommended ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" min-width="60" />
        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    
    <!-- 添加或修改套餐对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="套餐名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入套餐名称" />
        </el-form-item>
        <el-form-item label="价格(元)" prop="amount">
          <el-input-number v-model="form.amount" :precision="2" :min="0" :step="10" />
        </el-form-item>
        <el-form-item label="基础金币" prop="baseGold">
          <el-input-number v-model="form.baseGold" :precision="0" :min="0" :step="100" @change="calculateTotalGold" />
        </el-form-item>
        <el-form-item label="赠送金币" prop="bonusGold">
          <el-input-number v-model="form.bonusGold" :precision="0" :min="0" :step="10" @change="calculateTotalGold" />
        </el-form-item>
        <el-form-item label="总金币" prop="totalGold">
          <el-input-number v-model="form.totalGold" :precision="0" :min="0" disabled />
        </el-form-item>
        <el-form-item label="折扣" prop="discount">
          <el-input-number v-model="form.discount" :precision="1" :min="0" :max="10" :step="0.1" />
          <span class="form-tip">0表示无折扣，例如：8.5表示8.5折</span>
        </el-form-item>
        <el-form-item label="是否推荐" prop="recommended">
          <el-switch v-model="form.recommended" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :precision="0" :min="0" />
          <span class="form-tip">数字越小排序越靠前</span>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="form.icon" placeholder="请输入图标URL" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pagePackages, createPackage, updatePackage, deletePackage, getPackageDetail } from '@/api/recharge';
import Pagination from '@/components/Pagination';

export default {
  name: 'RechargePackage',
  components: { Pagination },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 套餐表格数据
      packageList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        name: undefined,
        amount: 0,
        baseGold: 0,
        bonusGold: 0,
        totalGold: 0,
        discount: 0,
        description: undefined,
        icon: undefined,
        recommended: false,
        sortOrder: 0
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '套餐名称不能为空', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ],
        baseGold: [
          { required: true, message: '基础金币不能为空', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询套餐列表 */
    getList() {
      this.loading = true;
      pagePackages(this.queryParams).then(response => {
        this.packageList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: undefined
      };
      this.handleQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加充值套餐';
    },
    /** 编辑按钮操作 */
    handleEdit(row) {
      this.reset();
      const id = row.id;
      getPackageDetail(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = '修改充值套餐';
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id) {
            updatePackage(this.form.id, this.form).then(response => {
              this.$message.success('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            createPackage(this.form).then(response => {
              this.$message.success('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除该充值套餐?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return deletePackage(row.id);
      }).then(() => {
        this.$message.success('删除成功');
        this.getList();
      }).catch(() => {});
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        amount: 0,
        baseGold: 0,
        bonusGold: 0,
        totalGold: 0,
        discount: 0,
        description: undefined,
        icon: undefined,
        recommended: false,
        sortOrder: 0
      };
      this.resetForm('form');
    },
    // 计算总金币
    calculateTotalGold() {
      this.form.totalGold = this.form.baseGold + this.form.bonusGold;
    }
  }
};
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}
</style> 