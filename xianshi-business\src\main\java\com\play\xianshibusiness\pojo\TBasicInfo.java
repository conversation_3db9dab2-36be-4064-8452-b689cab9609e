package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统基础信息实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_basic_info")
@ApiModel("系统基础信息表")
public class TBasicInfo extends BaseObjPo {

    @ApiModelProperty("系统名称")
    private String systemName;
    
    @ApiModelProperty("系统Logo")
    private String systemLogo;
    
    @ApiModelProperty("系统描述")
    private String systemDesc;
    
    @ApiModelProperty("版本号")
    private String version;
    
    @ApiModelProperty("公司名称")
    private String companyName;
    
    @ApiModelProperty("公司地址")
    private String companyAddress;
    
    @ApiModelProperty("联系电话")
    private String contactPhone;
    
    @ApiModelProperty("联系邮箱")
    private String contactEmail;
    
    @ApiModelProperty("备案号")
    private String icp;
    
    @ApiModelProperty("版权信息")
    private String copyright;
    
    @ApiModelProperty("隐私政策")
    private String privacyPolicy;
    
    @ApiModelProperty("用户协议")
    private String userAgreement;
}

