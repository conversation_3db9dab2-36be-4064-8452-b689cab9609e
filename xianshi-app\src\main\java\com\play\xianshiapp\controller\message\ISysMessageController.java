package com.play.xianshiapp.controller.message;

import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.pojo.ISysMessage;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.ISysMessageService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import com.play.xianshibusiness.dto.message.MessageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * (ISysMessage)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:15
 */
@RestController
@RequestMapping("/app/iSysMessage")
@Api(tags = "系统消息接口")
public class ISysMessageController {
    /**
     * 服务对象
     */
    @Resource
    private ISysMessageService iSysMessageService;


    @ApiOperation(value = "获取-系统消息")
    @GetMapping("/message")
    public Result<List<ISysMessage>> fetchSysMessage() {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(iSysMessageService.fetchSysMessage(memberId));
    }

    @ApiOperation(value = "获取-订单消息")
    @GetMapping("/order/message")
    public Result<List<ISysMessage>> fetchOrderMessage() {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(iSysMessageService.fetchOrderMessage(memberId));
    }

    @ApiOperation(value = "获取未读消息数量")
    @GetMapping("/unread/count")
    public Result<Integer> getUnreadCount() {
        String memberId = PrincipalUtil.getMemberId();
        Integer count = iSysMessageService.getUnreadCount(memberId);
        return ResultUtils.success(count);
    }
    
    @ApiOperation(value = "获取指定消息详情")
    @GetMapping("/detail/{messageId}")
    public Result<ISysMessage> getMessageDetail(@PathVariable("messageId") String messageId) {
        // 注意：服务器上的接口不支持传入会员ID参数
        // 如需标记消息为已读功能，请考虑更新服务接口
        ISysMessage message = iSysMessageService.fetchMessage(messageId);
        return ResultUtils.success(message);
    }
}

