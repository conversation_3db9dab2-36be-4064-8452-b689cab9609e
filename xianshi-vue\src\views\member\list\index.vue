<template>
  <div class="app-container clay-container">
    <div class="filter-container clay-card">
      <el-form :inline="true" :model="listQuery" class="filter-form">
        <el-form-item label="会员昵称">
          <el-input v-model="listQuery.nickname" placeholder="请输入会员昵称" clearable class="clay-input" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="listQuery.phone" placeholder="请输入手机号" clearable class="clay-input" />
        </el-form-item>
        <el-form-item label="会员状态">
          <el-select v-model="listQuery.status" placeholder="请选择状态" clearable class="clay-select">
            <el-option :value="true" label="正常" />
            <el-option :value="false" label="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" class="clay-button" style="background-color: #409EFF; color: #fff; border-color: #409EFF;">查询</el-button>
          <el-button @click="resetQuery" class="clay-button clay-button-secondary" style="background-color: #f5f7fa; color: #606266; border-color: #dcdfe6;">重置</el-button>
          <el-button type="success" @click="showCreateDialog" class="clay-button" style="background-color: #67c23a; color: #fff; border-color: #67c23a;">新增会员</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      class="clay-table"
    >
      <el-table-column label="会员ID" align="center" min-width="120">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="头像" align="center" width="80">
        <template slot-scope="{row}">
          <el-avatar :src="row.avatar" :size="40">
            <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
          </el-avatar>
        </template>
      </el-table-column>
      
      <el-table-column label="昵称" min-width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.nickname }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="手机号" min-width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.tel }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="当前角色" min-width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.currentRoleId === 'TALENT' ? 'success' : 'primary'" class="clay-tag">
            {{ row.currentRoleId === 'TALENT' ? '达人' : '普通用户' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="拥有角色" min-width="150" align="center">
        <template slot-scope="{row}">
          <div v-if="row.roles && row.roles.length > 0">
            <el-tag 
              v-for="role in row.roles" 
              :key="role.code" 
              :type="role.code === 'TALENT' ? 'success' : 'primary'" 
              class="role-tag"
              size="mini"
            >
              {{ role.name }}
            </el-tag>
          </div>
          <span v-else>无</span>
        </template>
      </el-table-column>
      
      <el-table-column label="达人申请状态" min-width="120" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getApplyStatusType(row.applyStatus)" class="clay-tag">
            {{ getApplyStatusText(row.applyStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="金币余额" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.gold || 0 }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" min-width="80" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.available ? 'success' : 'danger'" class="clay-tag">
            {{ row.available ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="注册时间" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createTime }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" min-width="220" class-name="fixed-width">
        <template slot-scope="{row}">
          <el-button 
            type="info" 
            size="mini" 
            @click="handleDetail(row)" 
            style="background-color: #909399; color: #fff; border-color: #909399;"
          >查看</el-button>
          
          <el-button 
            type="primary" 
            size="mini" 
            @click="handleOrders(row)" 
            style="background-color: #409EFF; color: #fff; border-color: #409EFF;"
          >订单</el-button>
          
          <el-button 
            type="success" 
            size="mini" 
            v-if="!row.available" 
            @click="handleStatus(row, true)"
            style="background-color: #67c23a; color: #fff; border-color: #67c23a;"
          >启用</el-button>
          
          <el-button 
            type="danger" 
            size="mini" 
            v-if="row.available" 
            @click="handleStatus(row, false)"
            style="background-color: #f56c6c; color: #fff; border-color: #f56c6c;"
          >禁用</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
      class="clay-pagination"
    />
    
    <!-- 禁用/启用对话框 -->
    <el-dialog :title="statusForm.status ? '启用会员' : '禁用会员'" :visible.sync="statusDialogVisible" width="500px" class="clay-dialog">
      <el-form ref="statusForm" :model="statusForm" label-width="100px">
        <el-form-item label="操作原因" prop="reason" :rules="{ required: !statusForm.status, message: '请填写禁用原因', trigger: 'blur' }">
          <el-input 
            v-model="statusForm.reason" 
            type="textarea" 
            :placeholder="statusForm.status ? '启用原因（选填）' : '请填写禁用原因'"
            :rows="4"
            class="clay-textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="statusDialogVisible = false" class="clay-button clay-button-secondary" style="background-color: #f5f7fa; color: #606266; border-color: #dcdfe6;">取消</el-button>
        <el-button type="primary" @click="submitStatus" class="clay-button" style="background-color: #409EFF; color: #fff; border-color: #409EFF;">确认</el-button>
      </div>
    </el-dialog>
    
    <!-- 创建会员对话框 -->
    <el-dialog title="创建会员" :visible.sync="createDialogVisible" width="600px" class="clay-dialog">
      <el-form ref="createForm" :model="createForm" :rules="createRules" label-width="100px">
        <el-form-item label="手机号" prop="tel">
          <el-input v-model="createForm.tel" placeholder="请输入手机号" class="clay-input" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="createForm.nickname" placeholder="请输入昵称（选填）" class="clay-input" />
        </el-form-item>
        <el-form-item label="性别">
          <el-select v-model="createForm.gender" placeholder="请选择性别（选填）" class="clay-select">
            <el-option :value="1" label="男" />
            <el-option :value="2" label="女" />
          </el-select>
        </el-form-item>
        <el-form-item label="年龄">
          <el-input-number v-model="createForm.age" :min="1" :max="120" placeholder="请输入年龄（选填）" class="clay-input" />
        </el-form-item>
        <el-form-item label="金币余额">
          <el-input-number v-model="createForm.gold" :min="0" :precision="2" :step="100" placeholder="请输入金币余额（选填）" class="clay-input" />
        </el-form-item>
        <el-form-item label="当前角色">
          <el-select v-model="createForm.currentRoleId" placeholder="请选择当前角色（选填）" class="clay-select">
            <el-option value="1" label="普通用户" />
            <el-option value="2" label="达人" />
          </el-select>
        </el-form-item>
        <el-form-item label="拥有角色">
          <el-checkbox-group v-model="createForm.roleIds">
            <el-checkbox label="1">普通用户</el-checkbox>
            <el-checkbox label="2">达人</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="createDialogVisible = false" class="clay-button clay-button-secondary" style="background-color: #f5f7fa; color: #606266; border-color: #dcdfe6;">取消</el-button>
        <el-button type="primary" @click="submitCreate" class="clay-button" style="background-color: #409EFF; color: #fff; border-color: #409EFF;">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMemberList, updateMemberStatus, createMember } from '@/api/member';
import Pagination from '@/components/Pagination';

export default {
  name: 'MemberList',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        nickname: '',
        phone: '',
        status: ''
      },
      statusDialogVisible: false,
      statusForm: {
        memberId: '',
        status: true,
        reason: ''
      },
      createDialogVisible: false,
      createForm: {
        tel: '',
        nickname: '',
        gender: '',
        age: '',
        gold: '',
        currentRoleId: '',
        roleIds: []
      },
      createRules: {
        tel: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        nickname: [{ max: 20, message: '昵称长度不能超过20个字符', trigger: 'blur' }],
        gender: [{ required: true, message: '请选择性别', trigger: 'blur' }],
        age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
        gold: [{ required: true, message: '请输入金币余额', trigger: 'blur' }],
        currentRoleId: [{ required: true, message: '请选择当前角色', trigger: 'blur' }],
        roleIds: [{ required: true, message: '请选择拥有角色', trigger: 'blur' }]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      getMemberList(this.listQuery).then(response => {
        this.list = response.data.records || [];
        this.total = response.data.total || 0;
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      });
    },
    handleSearch() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        nickname: '',
        phone: '',
        status: ''
      };
      this.getList();
    },
    handleDetail(row) {
      this.$router.push({ path: `/member/detail/${row.id}` });
    },
    handleOrders(row) {
      this.$router.push({ path: `/member/detail/${row.id}`, query: { tab: 'orders' } });
    },
    handleStatus(row, status) {
      this.statusForm = {
        memberId: row.id,
        status: status,
        reason: ''
      };
      this.statusDialogVisible = true;
    },
    submitStatus() {
      this.$refs.statusForm.validate(valid => {
        if (valid) {
          updateMemberStatus(this.statusForm.memberId, this.statusForm.status).then(response => {
            this.$message({
              type: 'success',
              message: this.statusForm.status ? '启用会员成功' : '禁用会员成功'
            });
            this.statusDialogVisible = false;
            this.getList();
          });
        }
      });
    },
    getApplyStatusType(status) {
      const statusMap = {
        0: 'info',    // 未申请
        1: 'warning', // 待审核
        2: 'success', // 已通过
        3: 'danger'   // 已拒绝
      };
      return statusMap[status] || 'info';
    },
    getApplyStatusText(status) {
      const statusMap = {
        0: '未申请',
        1: '待审核',
        2: '已通过',
        3: '已拒绝'
      };
      return statusMap[status] || '未知';
    },
    showCreateDialog() {
      this.createDialogVisible = true;
    },
    submitCreate() {
      this.$refs.createForm.validate(valid => {
        if (valid) {
          createMember(this.createForm).then(response => {
            this.$message({
              type: 'success',
              message: '创建会员成功'
            });
            this.createDialogVisible = false;
            this.getList();
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.clay-container {
  background-color: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
}

.clay-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.clay-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  
  ::v-deep .el-table__header-wrapper {
    th {
      background-color: #f7f9fc;
      color: #606266;
      font-weight: 600;
    }
  }
  
  ::v-deep .el-table__body-wrapper {
    td {
      padding: 16px 0;
    }
  }
}

.clay-input, .clay-select, .clay-date-picker {
  ::v-deep .el-input__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    transition: all 0.3s;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-textarea {
  ::v-deep .el-textarea__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-tag {
  border-radius: 16px;
  padding: 2px 10px;
  font-weight: 500;
}

.role-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.clay-dialog {
  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
    
    .el-dialog__header {
      background-color: #f7f9fc;
      padding: 16px 20px;
      
      .el-dialog__title {
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}

.clay-pagination {
  margin-top: 24px;
  text-align: right;
  
  ::v-deep .el-pagination {
    padding: 10px 0;
    
    button, li {
      background-color: #f9fafc;
      border-radius: 6px;
      margin: 0 3px;
      
      &.active {
        background-color: #409eff;
        color: white;
      }
      
      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}
</style> 