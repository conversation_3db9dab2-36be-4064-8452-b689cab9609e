//package com.play.xianshiapp.controller.sysBasic;
//
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.extension.api.ApiController;
//import com.baomidou.mybatisplus.extension.api.R;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.play.xianshibusiness.pojo.TBasicInfo;
//import com.play.xianshibusiness.service.TBasicInfoService;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import java.io.Serializable;
//import java.util.List;
//
///**
// * (TBasicInfo)表控制层
// *
// * <AUTHOR>
// * @since 2025-06-03 22:00:14
// */
//@RestController
//@RequestMapping("/app/basicInfo")
//public class TBasicInfoController  {
//
//    @Resource
//    private TBasicInfoService tBasicInfoService;
//
//}
//
