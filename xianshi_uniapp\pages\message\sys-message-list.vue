<template>
  <view class="container">
    <view v-if="type === 'order'">
      <view v-if="orderMessages.length === 0" class="no-message">
        <text>暂无订单消息</text>
      </view>
      <view v-else class="message-list">
        <view class="message-item" v-for="msg in orderMessages" :key="msg.id" @click="goDetail(msg)">
          <view class="red-dot" v-if="!msg.hasRead"></view>
          <view class="message-icon">
            <text class="fas fa-file-alt"></text>
          </view>
          <view class="message-content">
            <view class="message-title">{{ msg.title }}</view>
            <view class="message-desc">{{ msg.content }}</view>
            <view class="message-row">
              <view class="message-time">{{ msg.startTime ? (msg.startTime.slice ? msg.startTime.slice(0, 16).replace('T', ' ') : msg.startTime) : '' }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-else>
      <view v-if="sysMessages.length === 0" class="no-message">
        <text>暂无系统消息</text>
      </view>
      <view v-else class="message-list">
        <view class="message-item" v-for="msg in sysMessages" :key="msg.id" @click="goDetail(msg)">
          <view class="red-dot" v-if="!msg.hasRead"></view>
          <view class="message-icon">
            <text class="fas fa-bell"></text>
          </view>
          <view class="message-content">
            <view class="message-title">{{ msg.title }}</view>
            <view class="message-desc">{{ msg.content }}</view>
            <view class="message-row">
              <view class="message-time">{{ msg.startTime ? (msg.startTime.slice ? msg.startTime.slice(0, 16).replace('T', ' ') : msg.startTime) : '' }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getSysMessages, getOrderMessages } from '../../api/index.js';
export default {
  data() {
    return {
      sysMessages: [],
      orderMessages: [],
      type: 'sys' // 默认系统消息
    }
  },
  onLoad(options) {
    if (options && options.type) {
      this.type = options.type;
    }
    this.loadMessages();
  },
  methods: {
    async loadMessages() {
      if (this.type === 'order') {
        try {
          const res = await getOrderMessages();
          if ((res.code === 200 || res.status === 0) && Array.isArray(res.data)) {
            this.orderMessages = res.data;
          }
        } catch (e) {
          console.error('获取订单消息失败', e);
        }
      } else {
        try {
          const res = await getSysMessages();
          if ((res.code === 200 || res.status === 0) && Array.isArray(res.data)) {
            this.sysMessages = res.data;
          }
        } catch (e) {
          console.error('获取系统消息失败', e);
        }
      }
    },
    goDetail(msg) {
      uni.navigateTo({ url: `/pages/message/sys-message-detail?id=${msg.id}` });
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 40rpx;
}
.nav-bar {
  height: 88rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  border-bottom: 1rpx solid #eee;
}
.message-list {
  background: #fff;
  margin: 24rpx 0;
}
.message-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.message-item:last-child {
  border-bottom: none;
}
.message-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f0f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  font-size: 32rpx;
  margin-right: 24rpx;
}
.message-content {
  flex: 1;
}
.message-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.message-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.message-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.message-time {
  font-size: 24rpx;
  color: #999;
}
.red-dot {
  width: 16rpx;
  height: 16rpx;
  background: #ff3b30;
  border-radius: 50%;
  border: 2rpx solid #fff;
}
.no-message {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}
</style> 