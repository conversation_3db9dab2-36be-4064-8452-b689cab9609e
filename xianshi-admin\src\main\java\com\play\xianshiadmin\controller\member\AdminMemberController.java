package com.play.xianshiadmin.controller.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.dto.member.AdminCreateMemberDTO;
import com.play.xianshibusiness.dto.member.CMemberDto;
import com.play.xianshibusiness.dto.member.TalentApplyDTO;
import com.play.xianshibusiness.dto.order.OrderVO;
import com.play.xianshibusiness.enums.OrderStatusEnum;
import com.play.xianshibusiness.pojo.CMemberGoldRecord;
import com.play.xianshibusiness.pojo.CRole;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.BOrderService;
import com.play.xianshibusiness.service.CMemberGoldRecordService;
import com.play.xianshibusiness.service.CMemberRoleService;
import com.play.xianshibusiness.service.CMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 后台会员管理控制器
 */
@RestController
@RequestMapping("/admin/member")
@Api(tags = "后台会员管理-API")
@RequireAdmin(checkRole = true, roles = {"ADMIN", "MANAGER"})
public class AdminMemberController {
    
    @Resource
    private CMemberService cMemberService;
    
    @Resource
    private BOrderService bOrderService;
    
    @Resource
    private CMemberGoldRecordService memberGoldRecordService;
    
    @Resource
    private CMemberRoleService cMemberRoleService;
    
    /**
     * 分页查询会员列表
     */
    @ApiOperation("分页查询会员列表")
    @GetMapping("/list")
    public Result<Page<CMemberDto>> listMembers(
            @ApiParam(value = "页码", defaultValue = "1") 
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", defaultValue = "10") 
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "会员昵称（模糊查询）") 
            @RequestParam(value = "nickname", required = false) String nickname,
            @ApiParam(value = "手机号") 
            @RequestParam(value = "phone", required = false) String phone,
            @ApiParam(value = "会员状态") 
            @RequestParam(value = "status", required = false) Boolean status) {
        
        Page<CMemberDto> page = cMemberService.pageMembers(pageNum, pageSize, nickname, phone, status);
        
        // 为每个会员添加角色信息
        for (CMemberDto member : page.getRecords()) {
            List<CRole> roles = cMemberRoleService.getMemberRoles(member.getId());
            member.setRoles(roles);
        }
        
        return ResultUtils.success(page);
    }
    
    /**
     * 获取会员详情
     */
    @ApiOperation("获取会员详情")
    @GetMapping("/{memberId}")
    public Result<CMemberDto> getMemberDetail(
            @ApiParam(value = "会员ID", required = true) 
            @PathVariable("memberId") String memberId) {
        
        CMemberDto memberDto = cMemberService.getMemberInfo(memberId, true, true);
        
        // 获取会员拥有的所有角色
        List<CRole> roles = cMemberRoleService.getMemberRoles(memberId);
        memberDto.setRoles(roles);
        
        return ResultUtils.success(memberDto);
    }
    
    /**
     * 禁用/启用会员
     */
    @ApiOperation("禁用/启用会员")
    @PostMapping("/status")
    public Result<Boolean> updateMemberStatus(
            @ApiParam(value = "会员ID", required = true) 
            @RequestParam("memberId") String memberId,
            @ApiParam(value = "启用状态", required = true) 
            @RequestParam("status") Boolean status) {
        
        return ResultUtils.success(cMemberService.updateMemberStatus(memberId, status));
    }
    
    /**
     * 查询达人认证申请列表
     */
    @ApiOperation("查询达人认证申请列表")
    @GetMapping("/talent/apply/list")
    public Result<List<TalentApplyDTO>> listTalentApplies(
            @ApiParam(value = "审核状态（0-待审核 1-已通过 2-已拒绝）") 
            @RequestParam(value = "status", required = false) Integer status) {
        
        return ResultUtils.success(cMemberService.listTalentApplies(status));
    }
    
    /**
     * 审核达人认证申请
     */
    @ApiOperation("审核达人认证申请")
    @PostMapping("/talent/apply/audit")
    public Result<Boolean> auditTalentApply(
            @ApiParam(value = "会员ID", required = true) 
            @RequestParam("memberId") String memberId,
            @ApiParam(value = "审核结果（1-通过 2-拒绝）", required = true) 
            @RequestParam("result") Integer result,
            @ApiParam(value = "审核意见") 
            @RequestParam(value = "comment", required = false) String comment) {
        
        return ResultUtils.success(cMemberService.auditTalentApply(memberId, result, comment));
    }
    
    /**
     * 获取会员订单列表
     */
    @ApiOperation("获取会员订单列表")
    @GetMapping("/{memberId}/orders")
    public Result<Page<OrderVO>> getMemberOrders(
            @ApiParam(value = "会员ID", required = true) 
            @PathVariable("memberId") String memberId,
            @ApiParam(value = "页码", defaultValue = "1") 
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", defaultValue = "10") 
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "订单状态，0-草稿，1-审核中，2-已发布，3-待选择，4-进行中，5-已完成，6-已取消") 
            @RequestParam(value = "status", required = false) Integer statusCode) {
        
        // 构建查询条件
        com.play.xianshibusiness.dto.order.OrderQueryDTO queryDTO = new com.play.xianshibusiness.dto.order.OrderQueryDTO();
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(pageSize);
        queryDTO.setMemberId(memberId);
        queryDTO.setStatus(statusCode);
        
        return ResultUtils.success(bOrderService.adminPageOrders(queryDTO));
    }
    
    /**
     * 获取会员金币记录
     */
    @ApiOperation("获取会员金币记录")
    @GetMapping("/{memberId}/gold/records")
    public Result<Page<CMemberGoldRecord>> getMemberGoldRecords(
            @ApiParam(value = "会员ID", required = true) 
            @PathVariable("memberId") String memberId,
            @ApiParam(value = "页码", defaultValue = "1") 
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", defaultValue = "10") 
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        
        return ResultUtils.success(memberGoldRecordService.getMemberGoldRecords(memberId, pageNum, pageSize));
    }
    
    /**
     * 创建会员
     */
    @ApiOperation("创建会员")
    @PostMapping("/create")
    public Result<CMemberDto> createMember(
            @ApiParam(value = "会员信息", required = true) 
            @RequestBody @Valid AdminCreateMemberDTO dto) {
        
        return ResultUtils.success(cMemberService.createMember(dto));
    }
} 