package com.play.xianshibusiness.enums;

import lombok.Getter;

/**
 * 聊天消息类型枚举
 */
@Getter
public enum ChatMessageType {

    /**
     * 聊天消息
     */
    CHAT("CHAT"),
    
    /**
     * 系统消息
     */
    SYSTEM("SYSTEM"),
    
    /**
     * 连接消息
     */
    CONNECT("CONNECT"),
    
    /**
     * 心跳消息
     */
    PING("PING"),
    
    /**
     * 群组消息
     */
    GROUP("GROUP");
    
    private final String type;
    
    ChatMessageType(String type) {
        this.type = type;
    }
} 