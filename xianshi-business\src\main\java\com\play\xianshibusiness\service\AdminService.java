package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.dto.admin.AdminLoginRequest;
import com.play.xianshibusiness.dto.admin.AdminLoginResponse;
import com.play.xianshibusiness.dto.admin.AdminVerifyCodeResponse;
import com.play.xianshibusiness.enums.ResultCode;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.AdminMapper;
import com.play.xianshibusiness.pojo.Admin;
import com.play.xianshibusiness.utils.JwtSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 后台管理员服务
 */
@Service
@Slf4j
public class AdminService {
    
    @Resource
    private AdminMapper adminMapper;
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    @Resource
    private BCryptPasswordEncoder passwordEncoder;
    
    // Redis key 前缀，用于存储验证码
    private static final String VERIFY_CODE_PREFIX = "admin:verify_code:";
    
    // 验证码有效期（分钟）
    private static final int VERIFY_CODE_EXPIRE_MINUTES = 5;
    
    // 登录错误次数前缀
    private static final String LOGIN_FAIL_COUNT_PREFIX = "admin:login_fail:";
    
    // 错误锁定时间（分钟）
    private static final int LOGIN_LOCK_MINUTES = 30;
    
    // 最大错误次数
    private static final int MAX_FAIL_COUNT = 5;
    
    // 是否启用验证码(默认启用)
    @Value("${app.admin.verify-code-enabled:true}")
    private boolean verifyCodeEnabled;
    
    // 默认头像
    @Value("${app.admin.default-avatar:/static/images/admin-avatar.png}")
    private String defaultAvatar;
    
    /**
     * 管理员登录
     * @param request 登录请求
     * @return 登录响应
     */
    @Transactional(rollbackFor = Exception.class)
    public AdminLoginResponse adminLogin(AdminLoginRequest request) {
        // 校验账号是否被锁定
        checkLoginLock(request.getUsername());
        
        // 校验验证码
        if (verifyCodeEnabled && "PASSWORD".equals(request.getLoginType())) {
            verifyLoginCode(request.getVerifyKey(), request.getVerifyCode());
        }
        
        // 根据登录方式获取管理员
        Admin admin;
        if ("PASSWORD".equals(request.getLoginType())) {
            // 账号密码登录
            admin = getAdminByUsernameAndPassword(request.getUsername(), request.getPassword());
        } else if ("SMS".equals(request.getLoginType())) {
            // 手机号验证码登录
            // 验证手机验证码
            // TODO: 验证码验证
            admin = getAdminByPhone(request.getPhone());
        } else {
            throw new GlobalException(ResultCode.InValid_Param, "不支持的登录方式");
        }
        
        // 更新最后登录时间
        admin.setLastLoginTime(LocalDateTime.now());
        adminMapper.updateById(admin);
        
        // 清除登录失败记录
        clearLoginFailCount(request.getUsername());
        
        // 构建登录响应
        AdminLoginResponse response = new AdminLoginResponse();
        response.setToken(JwtSignUtils.createJWtTokenByAdminInfo(admin));
        response.setAdminId(admin.getId());
        response.setUsername(admin.getUsername());
        response.setNickname(admin.getNickname());
        response.setAvatar(admin.getAvatar());
        
        // 设置角色和权限
        List<String> roles = Arrays.asList(admin.getRoles().split(","));
        response.setRoles(roles);
        
        // 实际项目中应该从权限表查询权限
        List<String> permissions = new ArrayList<>();
        if (roles.contains("ADMIN")) {
            permissions.add("system:user:view");
            permissions.add("system:user:edit");
        }
        response.setPermissions(permissions);
        
        return response;
    }
    
    /**
     * 生成验证码
     * @return 验证码响应
     */
    public AdminVerifyCodeResponse generateVerifyCode() {
        // 生成4位随机验证码
        String verifyCode = generateRandomCode(4);
        
        // 生成验证码键
        String verifyKey = UUID.randomUUID().toString();
        
        // 存入Redis
        String codeKey = VERIFY_CODE_PREFIX + verifyKey;
        stringRedisTemplate.opsForValue().set(codeKey, verifyCode, VERIFY_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        // 生成图片
        String imageBase64 = generateCaptchaImage(verifyCode);
        
        // 构建响应
        AdminVerifyCodeResponse response = new AdminVerifyCodeResponse();
        response.setImageBase64(imageBase64);
        response.setVerifyKey(verifyKey);
        response.setExpireIn(VERIFY_CODE_EXPIRE_MINUTES * 60);
        
        return response;
    }
    
    /**
     * 生成验证码图片并返回Base64编码
     * @param code 验证码
     * @return Base64编码的图片
     */
    private String generateCaptchaImage(String code) {
        int width = 100;
        int height = 40;
        
        // 创建BufferedImage对象
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 设置背景色
        g.setColor(new Color(240, 240, 245));
        g.fillRect(0, 0, width, height);
        
        // 添加干扰线
        Random random = new Random();
        for (int i = 0; i < 8; i++) {
            int x1 = random.nextInt(width);
            int y1 = random.nextInt(height);
            int x2 = random.nextInt(width);
            int y2 = random.nextInt(height);
            g.setColor(getRandomColor());
            g.drawLine(x1, y1, x2, y2);
        }
        
        // 添加噪点
        for (int i = 0; i < 50; i++) {
            int x = random.nextInt(width);
            int y = random.nextInt(height);
            g.setColor(getRandomColor());
            g.drawOval(x, y, 1, 1);
        }
        
        // 设置字体
        g.setFont(new Font("Arial", Font.BOLD, 24));
        
        // 设置字体偏移和旋转
        for (int i = 0; i < code.length(); i++) {
            g.setColor(getRandomDarkColor());
            
            // 添加轻微旋转
            double degree = (random.nextInt(60) - 30) * Math.PI / 180;
            g.rotate(degree, 20 + i * 20, 25);
            
            // 绘制字符
            g.drawString(String.valueOf(code.charAt(i)), 15 + i * 20, 28);
            
            // 恢复旋转
            g.rotate(-degree, 20 + i * 20, 25);
        }
        
        // 释放资源
        g.dispose();
        
        // 转换为Base64编码
        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            ImageIO.write(image, "png", os);
            return Base64.getEncoder().encodeToString(os.toByteArray());
        } catch (IOException e) {
            log.error("生成验证码图片失败", e);
            return "";
        }
    }
    
    /**
     * 获取随机颜色
     */
    private Color getRandomColor() {
        Random random = new Random();
        return new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256));
    }
    
    /**
     * 获取随机深色（用于文字，确保与背景有足够对比度）
     */
    private Color getRandomDarkColor() {
        Random random = new Random();
        int r = random.nextInt(100);
        int g = random.nextInt(100);
        int b = random.nextInt(100);
        return new Color(r, g, b);
    }
    
    /**
     * 获取管理员信息
     * @param adminId 管理员ID
     * @return 管理员信息
     */
    public Object getAdminInfo(String adminId) {
        Admin admin = getAdminPo(adminId);
        
        Map<String, Object> map = new HashMap<>();
        map.put("adminId", admin.getId());
        map.put("username", admin.getUsername());
        map.put("nickname", admin.getNickname());
        map.put("avatar", admin.getAvatar());
        map.put("roles", admin.getRoles().split(","));
        map.put("phone", admin.getPhone());
        
        return map;
    }
    
    /**
     * 管理员退出登录
     * @param adminId 管理员ID
     * @return 是否成功
     */
    public boolean adminLogout(String adminId) {
        // 获取管理员对象
        Admin admin = getAdminPo(adminId);
        
        // 实际项目中可能需要清除缓存、令牌等，这里简化处理
        
        return true;
    }
    
    /**
     * 根据用户名和密码获取管理员
     * @param username 用户名
     * @param password 密码
     * @return 管理员对象
     */
    private Admin getAdminByUsernameAndPassword(String username, String password) {
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Admin::getUsername, username)
                   .eq(Admin::getDeleted, false);
        
        Admin admin = adminMapper.selectOne(queryWrapper);
        if (admin == null) {
            // 登录失败，记录错误次数
            increaseLoginFailCount(username);
            throw new GlobalException(ResultCode.InValid_Param, "用户名或密码错误");
        }
        
        // 校验账号状态
        if (!admin.getAvailable()) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "账号已被禁用");
        }
        
        // 校验密码
        if (!passwordEncoder.matches(password, admin.getPassword())) {
            // 登录失败，记录错误次数
            increaseLoginFailCount(username);
            throw new GlobalException(ResultCode.InValid_Param, "用户名或密码错误");
        }
        
        return admin;
    }
    
    /**
     * 根据手机号获取管理员
     * @param phone 手机号
     * @return 管理员对象
     */
    private Admin getAdminByPhone(String phone) {
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Admin::getPhone, phone)
                   .eq(Admin::getDeleted, false);
        
        Admin admin = adminMapper.selectOne(queryWrapper);
        if (admin == null) {
            throw new GlobalException(ResultCode.InValid_Param, "手机号未注册");
        }
        
        // 校验账号状态
        if (!admin.getAvailable()) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "账号已被禁用");
        }
        
        return admin;
    }
    
    /**
     * 获取管理员实体
     * @param adminId 管理员ID
     * @return 管理员实体
     */
    public Admin getAdminPo(String adminId) {
        Admin admin = adminMapper.selectById(adminId);
        if (admin == null || admin.getDeleted()) {
            throw new GlobalException(ResultCode.InValid_Param, "管理员不存在");
        }
        
        if (!admin.getAvailable()) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "账号已被禁用");
        }
        
        return admin;
    }
    
    /**
     * 校验登录验证码
     * @param verifyKey 验证码键
     * @param verifyCode 验证码
     */
    private void verifyLoginCode(String verifyKey, String verifyCode) {
        if (verifyKey == null || verifyCode == null) {
            throw new GlobalException(ResultCode.InValid_Param, "验证码不能为空");
        }
        
        String key = VERIFY_CODE_PREFIX + verifyKey;
        String savedCode = stringRedisTemplate.opsForValue().get(key);
        
        if (savedCode == null || !savedCode.equalsIgnoreCase(verifyCode)) {
            throw new GlobalException(ResultCode.InValid_Param, "验证码错误或已过期");
        }
        
        // 验证成功后删除验证码，防止重复使用
        stringRedisTemplate.delete(key);
    }
    
    /**
     * 检查账号是否被锁定
     * @param username 用户名
     */
    private void checkLoginLock(String username) {
        String key = LOGIN_FAIL_COUNT_PREFIX + username;
        String countStr = stringRedisTemplate.opsForValue().get(key);
        
        if (countStr != null) {
            int count = Integer.parseInt(countStr);
            if (count >= MAX_FAIL_COUNT) {
                // 获取锁定剩余时间
                Long ttl = stringRedisTemplate.getExpire(key, TimeUnit.MINUTES);
                throw new GlobalException(ResultCode.PROGRAM_ERROR, "账号已被锁定，请在" + ttl + "分钟后重试");
            }
        }
    }
    
    /**
     * 增加登录失败次数
     * @param username 用户名
     */
    private void increaseLoginFailCount(String username) {
        String key = LOGIN_FAIL_COUNT_PREFIX + username;
        String countStr = stringRedisTemplate.opsForValue().get(key);
        
        int count = 1;
        if (countStr != null) {
            count = Integer.parseInt(countStr) + 1;
        }
        
        // 存入Redis
        stringRedisTemplate.opsForValue().set(key, String.valueOf(count), LOGIN_LOCK_MINUTES, TimeUnit.MINUTES);
        
        // 如果达到最大失败次数，提示用户
        if (count >= MAX_FAIL_COUNT) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "登录失败次数过多，账号已被锁定" + LOGIN_LOCK_MINUTES + "分钟");
        } else {
            log.warn("用户名: {} 登录失败，已失败 {} 次，最多失败 {} 次", username, count, MAX_FAIL_COUNT);
        }
    }
    
    /**
     * 清除登录失败次数
     * @param username 用户名
     */
    private void clearLoginFailCount(String username) {
        String key = LOGIN_FAIL_COUNT_PREFIX + username;
        stringRedisTemplate.delete(key);
    }
    
    /**
     * 生成指定长度的随机字符验证码
     * @param length 验证码长度
     * @return 随机验证码
     */
    private String generateRandomCode(int length) {
        String chars = "0123456789ABCDEFGHJKMNPQRSTUVWXYZ";
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }
} 