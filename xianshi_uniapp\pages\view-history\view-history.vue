<template>
  <view class="view-history-page">
    
    <!-- 浏览历史列表 -->
    <view class="history-list" v-if="viewHistoryList.length > 0">
      <view class="history-item" v-for="(item, index) in viewHistoryList" :key="item.id">
                 <!-- 头像区域 -->
         <image 
           class="avatar" 
           :src="item.targetMemberAvatar || '/static/images/default-avatar.png'" 
           mode="aspectFill" 
           @tap="goToUserProfile(item)"
         ></image>
         
         <!-- 用户信息区域 -->
         <view class="user-info" @tap="goToUserProfile(item)">
           <view class="name-row">
             <text class="username">{{ item.targetMemberNickname || '未知用户' }}</text>
           </view>
           
           <view class="user-details">
             <text>用户ID: {{ item.targetMemberId }}</text>
           </view>
          
          <view class="view-info">
            <text class="view-time">{{ formatViewTime(item.lastViewTime) }}</text>
            <text class="view-count" v-if="item.viewCount > 1">浏览{{ item.viewCount }}次</text>
          </view>
        </view>

        <!-- 删除按钮 -->
        <view class="delete-btn" @tap.stop="deleteHistoryItem(item, index)">
          <text class="delete-icon">×</text>
        </view>
      </view>
    </view>
    <view class="header-actions" v-if="viewHistoryList.length > 0">
      <text class="clear-btn" @tap="showClearConfirm">清空</text>
    </view>
    <!-- 空状态 -->
    <view class="empty-state" v-else-if="!isLoading">
      <image class="empty-icon" src="/static/images/default-avatar.png" mode="aspectFit"></image>
      <text class="empty-text">暂无浏览历史</text>

    </view>
    
    <!-- 加载状态 -->
    <view class="loading-more" v-if="isLoading && page > 1">
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" v-if="!hasMore && viewHistoryList.length > 0">
      <text>— 没有更多浏览记录了 —</text>
    </view>
  </view>
</template>

<script>
import { getMemberViewHistory, deleteViewHistory, clearAllViewHistory } from '@/api/index.js';

export default {
  data() {
    return {
      viewHistoryList: [],
      page: 1,
      size: 10,
      hasMore: true,
      isLoading: false,
      userId: '' // 用户ID
    };
  },
  
  async onLoad(options) {
    if (options.userId) {
      this.userId = options.userId;
    } else {
      // 如果没有传入userId，则从API获取当前登录用户的ID
      await this.getCurrentUserId();
    }
    this.loadViewHistory();
  },
  
  onPullDownRefresh() {
    this.page = 1;
    this.viewHistoryList = [];
    this.hasMore = true;
    this.loadViewHistory().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.page++;
      this.loadViewHistory();
    }
  },
  
  methods: {
    // 获取当前用户ID
    async getCurrentUserId() {
      try {
        const app = getApp().globalData;
        const res = await this.$requestHttp.get(app.commonApi.getCurrentMemberInfo, {});
        
        if (res.code === 200 && res.data) {
          this.userId = res.data.sysId || res.data.id || '';
        } else {
          console.error('获取用户信息失败:', res.message);
          uni.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取用户信息异常:', error);
        uni.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    },

         // 加载浏览历史
     async loadViewHistory() {
       if (this.isLoading || !this.hasMore) return;
       
       this.isLoading = true;
       
       try {
         console.log('开始加载浏览历史，页码:', this.page);
         const res = await getMemberViewHistory({ 
           pageNum: this.page, 
           pageSize: this.size 
         });
         
         console.log('浏览历史API响应:', res);
         
         if (res.code === 200) {
           if (res.data && res.data.records && res.data.records.length > 0) {
             console.log('获取到浏览历史记录:', res.data.records.length, '条');
             this.viewHistoryList = [...this.viewHistoryList, ...res.data.records];
             this.hasMore = res.data.current < res.data.pages;
           } else {
             console.log('没有更多浏览历史记录');
             this.hasMore = false;
           }
         } else {
           console.error('获取浏览历史失败:', res);
           uni.showToast({
             title: res.message || '获取浏览历史失败',
             icon: 'none'
           });
         }
       } catch (error) {
         console.error('获取浏览历史出错:', error);
         uni.showToast({
           title: '网络异常，请稍后重试',
           icon: 'none'
         });
       } finally {
         this.isLoading = false;
       }
     },
    
    // 删除单条浏览记录
    async deleteHistoryItem(item, index) {
      try {
        uni.showModal({
          title: '确认删除',
          content: '确定要删除这条浏览记录吗？',
          success: async (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: '删除中...'
              });
              
                             const deleteRes = await deleteViewHistory(item.id);
               
               if (deleteRes.code === 200) {
                 // 从列表中移除
                 this.viewHistoryList.splice(index, 1);
                 
                 uni.showToast({
                   title: '删除成功',
                   icon: 'success'
                 });
               } else {
                 uni.showToast({
                   title: deleteRes.message || '删除失败',
                   icon: 'none'
                 });
               }
              
              uni.hideLoading();
            }
          }
        });
      } catch (error) {
        console.error('删除浏览记录失败:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
        uni.hideLoading();
      }
    },
    
    // 显示清空确认
    showClearConfirm() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有浏览历史吗？此操作不可恢复。',
        success: async (res) => {
          if (res.confirm) {
            await this.clearAllHistory();
          }
        }
      });
    },
    
    // 清空所有浏览历史
    async clearAllHistory() {
      try {
        uni.showLoading({
          title: '清空中...'
        });
        
                 const res = await clearAllViewHistory();
         
         if (res.code === 200) {
           this.viewHistoryList = [];
           this.hasMore = false;
           
           uni.showToast({
             title: '清空成功',
             icon: 'success'
           });
         } else {
           uni.showToast({
             title: res.message || '清空失败',
             icon: 'none'
           });
         }
      } catch (error) {
        console.error('清空浏览历史失败:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
         // 跳转到用户个人主页
     goToUserProfile(item) {
       if (!item.targetMemberId) {
         uni.showToast({
           title: '用户信息不存在',
           icon: 'none'
         });
         return;
       }
       
       // 直接跳转到用户主页，通过ID获取详细信息
       uni.navigateTo({
         url: `/pages/profile/preview?id=${item.targetMemberId}&name=${item.targetMemberNickname || '用户'}`
       });
     },
    
    
    
         // 格式化浏览时间
     formatViewTime(timestamp) {
       if (!timestamp) return '';
       
       // 处理后端返回的LocalDateTime格式
       let viewTime;
       if (typeof timestamp === 'string') {
         // 如果是字符串格式，直接解析
         viewTime = new Date(timestamp).getTime();
       } else {
         // 如果已经是时间戳
         viewTime = timestamp;
       }
       
       const now = new Date().getTime();
       const diffMinutes = Math.floor((now - viewTime) / (60 * 1000));
       
       if (diffMinutes < 1) {
         return '刚刚';
       } else if (diffMinutes < 60) {
         return `${diffMinutes}分钟前`;
       } else if (diffMinutes < 24 * 60) {
         const hours = Math.floor(diffMinutes / 60);
         return `${hours}小时前`;
       } else {
         const days = Math.floor(diffMinutes / (24 * 60));
         return `${days}天前`;
       }
     }
  }
};
</script>

<style scoped>
.view-history-page {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 0 30rpx;
}

.back-btn {
  position: fixed;
  top: 60rpx;
  left: 30rpx;
  z-index: 100;
}

.iconfont {
  font-family: "iconfont";
  font-size: 40rpx;
  color: #333;
}

.header {
  padding: 120rpx 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

.header-actions {
  position: absolute;
  right: 20px;
}

.clear-btn {
  font-size: 28rpx;
  color: #ff6b6b;
  padding: 8rpx 16rpx;
}

.history-list {
  padding-bottom: 30rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.user-info {
  flex: 1;
  overflow: hidden;
}

.name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.username {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-right: 10rpx;
}

.gender-icon {
  font-size: 28rpx;
  color: #ff6b9a;
}

.gender-icon:last-child {
  color: #6b9aff;
}

.user-details {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.view-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.view-time {
  font-size: 22rpx;
  color: #999;
}

.view-count {
  font-size: 22rpx;
  color: #ff8c00;
  background-color: rgba(255, 140, 0, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.delete-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.delete-icon {
  font-size: 32rpx;
  color: #999;
  font-weight: bold;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.find-experts-btn {
  width: 240rpx;
  height: 70rpx;
  border-radius: 35rpx;
  background-color: #ff8c00;
  display: flex;
  align-items: center;
  justify-content: center;
}

.find-experts-btn text {
  color: #fff;
  font-size: 28rpx;
}

.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-more text, .no-more text {
  font-size: 24rpx;
  color: #999;
}
</style>