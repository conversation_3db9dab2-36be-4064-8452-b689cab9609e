<template>
  <view class="container">
    <!-- 性别选择 -->
    <view class="gender-select">
      <view
        :class="['gender-item', {active: gender === 'male'}]"
        @tap="switchGender"
        data-gender="male"
        >男生风格</view
      >
      <view
        :class="['gender-item', {active: gender === 'female'}]"
        @tap="switchGender"
        data-gender="female"
        >女生风格</view
      >
    </view>

    <!-- 风格网格 -->
    <view class="style-grid">
      <view
        v-for="item in currentStyles"
        :key="item.name"
        :class="['style-item', {selected: selectedStyles[item.name]}]"
        @tap="toggleStyle"
        :data-style="item.name"
      >
        <text>{{ item.name }}</text>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-btn-wrap">
      <button class="save-btn" @tap="saveStyles">保存</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
    gender: "female",
    selectedStyles: {},
    styles: {
      female: [
        { name: "萝莉" },
        { name: "御姐" },
        { name: "大长腿" },
        { name: "街头风" },
        { name: "文艺范" },
        { name: "复古风" },
        { name: "运动休闲" },
        { name: "轻熟女" },
        { name: "时尚达人" },
        { name: "甜美可爱" },
        { name: "优雅气质" },
        { name: "清新自然" },
      ],
      male: [
        { name: "阳光帅气" },
        { name: "成熟稳重" },
        { name: "运动型男" },
        { name: "文艺青年" },
        { name: "商务精英" },
        { name: "潮流时尚" },
        { name: "硬朗" },
        { name: "绅士" },
        { name: "街头" },
        { name: "休闲" },
        { name: "学院风" },
        { name: "型男" },
      ],
    },
    currentStyles: [],
      selectedStylesList: [],
    };
  },

  onLoad(options) {
    uni.setNavigationBarTitle({
      title: "编辑形象风格",
    });
    console.log("options", options);

    // 初始化风格列表
    this.currentStyles = this.styles[this.gender];

    // 如果有已选择的风格，初始化选中状态
    if (options.styles) {
      const styles = JSON.parse(decodeURIComponent(options.styles));
      const selectedStyles = {};

      styles.forEach((style) => {
        selectedStyles[style] = true;
      });

      this.selectedStyles = selectedStyles;
    }
  },
  methods: {
    switchGender(e) {
      const gender = e.currentTarget.dataset.gender;
      this.gender = gender;
      this.currentStyles = this.styles[gender];
      this.selectedStyles = {}; // 切换性别时清空已选风格
    },

    toggleStyle(e) {
      const style = e.currentTarget.dataset.style;
      const selectedStyles = { ...this.selectedStyles };

      if (selectedStyles[style]) {
        delete selectedStyles[style];
      } else {
        selectedStyles[style] = true;
      }

      this.selectedStyles = selectedStyles;
    },

    async saveStyles() {
      try {
        uni.showLoading({ title: '保存中...' });
        
        // 获取选中的风格列表
        const selectedStylesList = Object.keys(this.selectedStyles);
        
        // 调用API保存风格
        const app = getApp().globalData;
        const res = await this.$requestHttp.put(app.commonApi.updateMemberInfo, {
          data: {
            style: selectedStylesList.join(',')
          }
        });
        
        if (res && res.code === 200) {
          // 保存到当前页面数据中
          this.selectedStylesList = selectedStylesList;

          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];
          prevPage.styles = selectedStylesList;

          // 更新本地存储
          const profileData = uni.getStorageSync("profileData") || {};
          profileData.styles = selectedStylesList;
          uni.setStorageSync("profileData", profileData);

          // 触发上一页面的完整度更新
          if (prevPage.updateCompletion) {
            prevPage.updateCompletion();
          }
          
          uni.hideLoading();
          uni.showToast({ title: '保存成功', icon: 'success' });
          
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(res?.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存风格失败:', error);
        uni.hideLoading();
        uni.showToast({ title: error.message || '保存失败，请重试', icon: 'none' });
      }
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.gender-select {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.gender-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.gender-item.active {
  background: #fff5e6;
  color: #ff8c00;
  font-weight: 500;
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16rpx;
  margin-bottom: 40rpx;
}

.style-item {
  aspect-ratio: 1;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  padding: 8rpx;
  text-align: center;
  word-break: break-all;
}

.style-item.selected {
  background: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  font-weight: 500;
}

.save-btn-wrap {
  margin-top: auto;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
