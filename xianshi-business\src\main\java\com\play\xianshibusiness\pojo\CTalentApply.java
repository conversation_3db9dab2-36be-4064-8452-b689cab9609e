package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 达人申请实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_talent_apply")
@ApiModel("达人申请表")
public class CTalentApply extends BaseObjPo {

    @ApiModelProperty("会员ID")
    private String memberId;
    
    @ApiModelProperty("申请状态：0-待审核，1-已通过，2-已拒绝")
    private Integer status;
    
    @ApiModelProperty("自我介绍")
    private String introduction;
    
    @ApiModelProperty("技能特长")
    private String skills;
    
    @ApiModelProperty("照片列表")
    private String photos;
    
    @ApiModelProperty("视频")
    private String video;
    
    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;
    
    @ApiModelProperty("审核人ID")
    private String auditorId;
    
    @ApiModelProperty("审核意见")
    private String auditComment;
} 