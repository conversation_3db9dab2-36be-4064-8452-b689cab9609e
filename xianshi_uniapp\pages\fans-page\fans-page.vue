<script>
import { getMemberInfo ,getFansList, followM<PERSON>ber, unfollowMember } from '@/api/index.js';

export default {
  data() {
    return {
      fans: [],
      page: 1,
      size: 10,
      hasMore: true,
      isLoading: false,
      userId: '' // 用户ID
    };
  },
  
  onLoad(options) {
    if (options.userId) {
      this.userId = options.userId;
    } else {
      // 如果没有传入userId，则使用当前登录用户的ID
      const userInfo = uni.getStorageSync('userInfo');
      this.userId = userInfo?.sysId || '';
    }
    this.loadFans();
  },
  
  onPullDownRefresh() {
    this.page = 1;
    this.fans = [];
    this.hasMore = true;
    this.loadFans().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.page++;
      this.loadFans();
    }
  },
  
  methods: {
    // 加载粉丝列表
    async loadFans() {
      if (this.isLoading || !this.hasMore) return;
      
      this.isLoading = true;
      
      try {
        const res = await getFansList({ 
          page: this.page, 
          size: this.size 
        });
        
        if (res.code === 200) {
          if (res.data && res.data.length > 0) {
            this.fans = [...this.fans, ...res.data];
          } else {
            this.hasMore = false;
          }
        } else {
          uni.showToast({
            title: res.message || '获取粉丝列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取粉丝列表出错:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 关注/取消关注
    async handleFollow(fan, index) {
      try {
        uni.showLoading({
          title: fan.following ? '取消关注中...' : '关注中...'
        });
        
        const apiCall = fan.following 
          ? unfollowMember(fan.id)
          : followMember(fan.id);
        
        const res = await apiCall;
        
        if (res.code === 200) {
          // 更新关注状态
          this.$set(this.fans[index], 'following', !fan.following);
          
          uni.showToast({
            title: fan.following ? '已取消关注' : '已关注',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('关注操作失败:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 打开用户个人主页

    async goToUserProfile(userId) {
      try {
        const res = await getMemberInfo(userId, {withExtra: true, withPrivacy: false});
        console.log("=========",res.data)
        if (res.code === 200) {
          uni.navigateTo({
            url:
                "/pages/profile/preview?id=" + res.data.id + "&name=" + res.data.nickname,
            fail: (err) => {
              console.log("跳转失败", err);
            },
          });
        }else {
          uni.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      }catch (e){
        console.log(e);
      }

    }
   ,
    
    // 格式化用户详细信息
    formatUserDetails(fan) {
      let details = [];
      
      if (fan.age) {
        details.push(`${fan.age}岁`);
      }
      
      if (fan.height) {
        details.push(`${fan.height}`);
      }
      
      if (fan.weight) {
        details.push(`${fan.weight}`);
      }
      
      return details.join(' · ');
    }
  }
};
</script>

<template>
  <view class="fans-page">

    
    <view class="fans-list" v-if="fans.length > 0">
      <view class="fan-item" v-for="(fan, index) in fans" :key="fan.id">
        <!-- 头像区域 -->
        <image class="avatar" :src="fan.avatar || '/static/images/default-avatar.png'" mode="aspectFill" @tap="goToUserProfile(fan.id)"></image>
        
        <!-- 用户信息区域 -->
        <view class="user-info" @tap="goToUserProfile(fan.id)">
          <view class="name-row">
            <text class="username">{{ fan.nickname }}</text>
            <text class="gender-icon" v-if="fan.gender !== undefined">{{ fan.gender === 1 ? '♂' : '♀' }}</text>
          </view>
          
          <view class="user-details">
            <text>{{ formatUserDetails(fan) || '未知' }}</text>
          </view>
        </view>
        
        <!-- 关注按钮 -->
        <view class="follow-btn" :class="{ 'followed': fan.following }" @tap.stop="handleFollow(fan, index)">
          <text>{{ fan.following ? '已关注' : '关注' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else-if="!isLoading">
      <image class="empty-icon" src="/static/images/default-avatar.png" mode="aspectFit"></image>
      <text class="empty-text">暂无粉丝</text>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-more" v-if="isLoading && page > 1">
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" v-if="!hasMore && fans.length > 0">
      <text>— 没有更多粉丝了 —</text>
    </view>
  </view>
</template>

<style scoped>
.fans-page {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 0 30rpx;
}

.back-btn {
  position: fixed;
  top: 60rpx;
  left: 30rpx;
  z-index: 100;
}

.iconfont {
  font-family: "iconfont";
  font-size: 40rpx;
  color: #333;
}

.header {
  padding: 120rpx 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.fans-list {
  padding-bottom: 30rpx;
}

.fan-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.user-info {
  flex: 1;
  overflow: hidden;
}

.name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.username {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-right: 10rpx;
}

.gender-icon {
  font-size: 28rpx;
  color: #ff6b9a;
}

.gender-icon:last-child {
  color: #6b9aff;
}

.user-details {
  font-size: 24rpx;
  color: #999;
}

.follow-btn {
  min-width: 120rpx;
  height: 54rpx;
  border-radius: 27rpx;
  background-color: #ff8c00;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.follow-btn text {
  color: #fff;
  font-size: 24rpx;
}

.followed {
  background-color: #f2f2f2;
  border: 1px solid #e0e0e0;
}

.followed text {
  color: #666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-more text, .no-more text {
  font-size: 24rpx;
  color: #999;
}
</style>