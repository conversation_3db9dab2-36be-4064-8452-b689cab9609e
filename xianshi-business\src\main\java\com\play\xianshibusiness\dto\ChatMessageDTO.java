package com.play.xianshibusiness.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天消息数据传输对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("聊天消息请求对象")
public class ChatMessageDTO {

    @ApiModelProperty("接收者ID")
    private String targetId;
    
    @ApiModelProperty("消息内容（JSON格式）")
    private String content;
} 