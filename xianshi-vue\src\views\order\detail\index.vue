<template>
  <div v-loading="loading" class="app-container clay-container">
    <div class="detail-header clay-card">
      <div class="header-left">
        <h2 class="header-title">订单详情</h2>
        <span class="order-id">订单ID: {{ orderDetail.orderId }}</span>
      </div>
      <div class="header-right">
        <el-button icon="el-icon-back" @click="goBack" class="clay-button clay-button-secondary">返回</el-button>
        
        <el-button 
          v-if="orderDetail.status === 1" 
          type="success" 
          @click="handleAudit(true)"
          class="clay-button clay-button-success"
        >
          审核通过
        </el-button>
        
        <el-button 
          v-if="orderDetail.status === 1" 
          type="danger" 
          @click="handleAudit(false)"
          class="clay-button clay-button-danger"
        >
          审核拒绝
        </el-button>
        
        <el-button 
          v-if="[1, 2, 3].includes(orderDetail.status)" 
          type="warning" 
          @click="handleCancel"
          class="clay-button clay-button-warning"
        >
          取消订单
        </el-button>
      </div>
    </div>
    
    <el-card shadow="hover" class="detail-card clay-card">
      <div slot="header" class="card-header">
        <span>订单基本信息</span>
        <el-tag :type="getOrderStatusType(orderDetail.status)" class="clay-tag">{{ orderDetail.statusDesc }}</el-tag>
      </div>
      <el-descriptions :column="3" border class="clay-descriptions">
        <el-descriptions-item label="陪玩项目">{{ orderDetail.typeName }}</el-descriptions-item>
        <el-descriptions-item label="发单用户">{{ orderDetail.memberName }}</el-descriptions-item>
        <el-descriptions-item label="订单标题">{{ orderDetail.title }}</el-descriptions-item>
        <el-descriptions-item label="小时费用">{{ orderDetail.hourlyRate }} 元/小时</el-descriptions-item>
        <el-descriptions-item label="陪玩时长">{{ orderDetail.duration }} 分钟</el-descriptions-item>
        <el-descriptions-item label="参与人数">{{ orderDetail.personCount }} 人</el-descriptions-item>
        <el-descriptions-item label="性别要求">{{ getSexRequireText(orderDetail.sexRequire) }}</el-descriptions-item>
        <el-descriptions-item label="订单金币">{{ orderDetail.goldCost }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ orderDetail.createTime }}</el-descriptions-item>
        <el-descriptions-item :span="3" label="地点">{{ orderDetail.address }}</el-descriptions-item>
        <el-descriptions-item :span="3" label="时间安排">
          {{ orderDetail.startTime }} 至 {{ orderDetail.endTime }}
        </el-descriptions-item>
        <el-descriptions-item :span="3" label="订单描述">{{ orderDetail.description }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <el-card v-if="orderDetail.auditTime" shadow="hover" class="detail-card clay-card">
      <div slot="header" class="card-header">
        <span>审核信息</span>
      </div>
      <el-descriptions :column="2" border class="clay-descriptions">
        <el-descriptions-item label="审核人">{{ orderDetail.auditorName || orderDetail.auditorId || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="审核时间">{{ orderDetail.auditTime }}</el-descriptions-item>
        <el-descriptions-item label="审核结果">{{ orderDetail.auditResult === 1 ? '通过' : '拒绝' }}</el-descriptions-item>
        <el-descriptions-item :span="2" label="审核意见">{{ orderDetail.auditComment || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <el-card v-if="orderDetail.enrollDetails && orderDetail.enrollDetails.length > 0" shadow="hover" class="detail-card clay-card">
      <div slot="header" class="card-header">
        <span>达人报名信息</span>
      </div>
      <el-table :data="orderDetail.enrollDetails" border style="width: 100%" class="clay-table">
        <el-table-column prop="memberId" label="达人ID" width="180" />
        <el-table-column prop="memberNickname" label="达人昵称" width="150" />
        <el-table-column prop="createTime" label="报名时间" width="180" />
        <el-table-column label="状态" width="120">
          <template slot-scope="{row}">
            <el-tag :type="getEnrollStatusType(row.status)" class="clay-tag">{{ getEnrollStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="arrivalTime" label="到达时间" width="180" />
        <el-table-column prop="serviceEndTime" label="服务结束时间" width="180" />
        <el-table-column label="操作" width="220" align="center">
          <template slot-scope="{row}">
            <el-button 
              v-if="row.status === 0 && orderDetail.status === 2" 
              size="mini" 
              type="primary" 
              @click="selectTalent(row)"
              class="clay-button clay-button-sm"
            >选择</el-button>
            
            <el-button 
              v-if="row.status === 1" 
              size="mini" 
              type="primary" 
              @click="updateDetailStatus(row, 2)"
              class="clay-button clay-button-sm"
            >已出发</el-button>
            
            <el-button 
              v-if="row.status === 2" 
              size="mini" 
              type="primary" 
              @click="updateDetailStatus(row, 3)"
              class="clay-button clay-button-sm"
            >已到达</el-button>
            
            <el-button 
              v-if="row.status === 3" 
              size="mini" 
              type="primary" 
              @click="updateDetailStatus(row, 4)"
              class="clay-button clay-button-sm"
            >服务中</el-button>
            
            <el-button 
              v-if="row.status === 4" 
              size="mini" 
              type="success" 
              @click="updateDetailStatus(row, 5)"
              class="clay-button clay-button-sm"
            >服务完成</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 子订单状态流转记录区域 -->
    <el-card v-if="orderDetail.enrollDetails && orderDetail.enrollDetails.length > 0" shadow="hover" class="detail-card clay-card">
      <div slot="header" class="card-header">
        <span>子订单状态流转记录</span>
      </div>
      <el-tabs type="border-card" class="clay-tabs">
        <el-tab-pane v-for="(detail, index) in orderDetail.enrollDetails" :key="index" :label="detail.memberNickname || '达人'+index">
          <div class="sub-order-info">
            <div class="sub-order-header">
              <span class="sub-order-title">达人：{{ detail.memberNickname }}</span>
              <el-tag :type="getEnrollStatusType(detail.status)" class="clay-tag">{{ getEnrollStatusText(detail.status) }}</el-tag>
            </div>
            
            <el-steps :active="getSubOrderActiveStep(detail)" :align-center="true" finish-status="success">
              <el-step title="已报名" :description="formatTime(detail.createTime)"></el-step>
              <el-step title="被选中" :description="formatTime(detail.selectTime)"></el-step>
              <el-step title="已出发" :description="formatTime(detail.departureTime)"></el-step>
              <el-step title="已到达" :description="formatTime(detail.arrivalTime)"></el-step>
              <el-step title="服务中" :description="formatTime(detail.serviceStartTime)"></el-step>
              <el-step title="服务完成" :description="formatTime(detail.serviceEndTime)"></el-step>
            </el-steps>
            
            <div v-if="detail.status >= 4" class="sub-order-fee">
              <el-descriptions :column="2" border class="clay-descriptions">
                <el-descriptions-item label="服务时长">{{ calculateServiceDuration(detail) }} 分钟</el-descriptions-item>
                <el-descriptions-item label="订单金币">{{ calculateOrderGold(detail) }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <el-card v-if="statusRecords && statusRecords.length > 0" shadow="hover" class="detail-card clay-card">
      <div slot="header" class="card-header">
        <span>订单状态流转记录</span>
      </div>
      <el-timeline>
        <el-timeline-item
          v-for="(record, index) in statusRecords"
          :key="index"
          :timestamp="record.createTime"
          :type="getStatusTimelineType(record)"
        >
          {{ record.description || getStatusDescription(record) }}
        </el-timeline-item>
      </el-timeline>
    </el-card>
    
    <el-card v-if="comments && comments.length > 0" shadow="hover" class="detail-card clay-card">
      <div slot="header" class="card-header">
        <span>评价信息</span>
      </div>
      <div class="evaluation-list">
        <div v-for="(item, index) in comments" :key="index" class="evaluation-item">
          <div class="evaluation-header">
            <div class="user-info">
              <el-avatar :size="40" :src="item.memberAvatar || 'https://via.placeholder.com/40'" class="clay-avatar"></el-avatar>
              <span class="user-name">{{ item.memberName }}</span>
            </div>
            <div class="evaluation-rating">
              <el-rate v-model="item.score" disabled show-score text-color="#ff9900" />
            </div>
          </div>
          <div class="evaluation-tags">
            <el-tag 
              v-for="(tag, idx) in item.tags" 
              :key="idx" 
              size="small" 
              class="tag-item clay-tag"
            >{{ tag }}</el-tag>
          </div>
          <div class="evaluation-content">{{ item.content }}</div>
          <div class="evaluation-time">{{ item.createTime }}</div>
        </div>
      </div>
    </el-card>
    
    <!-- 审核对话框 -->
    <el-dialog :title="auditForm.pass ? '审核通过' : '审核拒绝'" :visible.sync="auditDialogVisible" width="500px" class="clay-dialog">
      <el-form ref="auditForm" :model="auditForm" label-width="100px">
        <el-form-item label="审核意见" prop="reason" :rules="{ required: !auditForm.pass, message: '请填写拒绝原因', trigger: 'blur' }">
          <el-input 
            v-model="auditForm.reason" 
            type="textarea" 
            :placeholder="auditForm.pass ? '审核意见（选填）' : '请填写拒绝原因'"
            :rows="4"
            class="clay-textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false" class="clay-button clay-button-secondary">取消</el-button>
        <el-button type="primary" @click="submitAudit" class="clay-button">确认</el-button>
      </div>
    </el-dialog>
    
    <!-- 取消订单对话框 -->
    <el-dialog title="取消订单" :visible.sync="cancelDialogVisible" width="500px" class="clay-dialog">
      <el-form ref="cancelForm" :model="cancelForm" label-width="100px">
        <el-form-item label="取消原因" prop="reason" :rules="{ required: true, message: '请填写取消原因', trigger: 'blur' }">
          <el-input 
            v-model="cancelForm.reason" 
            type="textarea" 
            placeholder="请填写取消原因"
            :rows="4"
            class="clay-textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialogVisible = false" class="clay-button clay-button-secondary">取消</el-button>
        <el-button type="primary" @click="submitCancel" class="clay-button">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOrderDetail, auditOrder, cancelOrder, getOrderComments, getOrderStatusRecords, updateOrderDetailStatus, selectTalents } from '@/api/order';

export default {
  name: 'OrderDetail',
  data() {
    return {
      loading: true,
      orderDetail: {},
      comments: [],
      statusRecords: [],
      auditDialogVisible: false,
      auditForm: {
        pass: true,
        reason: ''
      },
      cancelDialogVisible: false,
      cancelForm: {
        reason: ''
      }
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.loading = true;
      const orderId = this.$route.params.id;
      
      // 获取订单详情
      getOrderDetail(orderId).then(response => {
        this.orderDetail = response.data || {};
        this.loading = false;
        
        // 获取订单评价
        this.getOrderComments(orderId);
        
        // 获取订单状态流转记录
        this.getStatusRecords(orderId);
      }).catch(() => {
        this.loading = false;
      });
    },
    
    getOrderComments(orderId) {
      getOrderComments(orderId, 1, 10).then(response => {
        this.comments = response.data.records || [];
      });
    },
    
    // 获取订单状态流转记录
    getStatusRecords(orderId) {
      getOrderStatusRecords(orderId).then(response => {
        this.statusRecords = response.data || [];
      });
    },
    
    goBack() {
      this.$router.push('/activity/order');
    },
    
    handleAudit(pass) {
      this.auditForm = {
        pass,
        reason: ''
      };
      this.auditDialogVisible = true;
    },
    
    submitAudit() {
      this.$refs.auditForm.validate(valid => {
        if (valid) {
          const data = {
            orderId: this.orderDetail.orderId,
            approved: this.auditForm.pass,
            remark: this.auditForm.reason || ''
          };
          
          auditOrder(data).then(() => {
            this.$message({
              type: 'success',
              message: this.auditForm.pass ? '审核通过成功' : '审核拒绝成功'
            });
            this.auditDialogVisible = false;
            this.getDetail();
          });
        }
      });
    },
    
    handleCancel() {
      this.cancelForm = {
        reason: ''
      };
      this.cancelDialogVisible = true;
    },
    
    submitCancel() {
      this.$refs.cancelForm.validate(valid => {
        if (valid) {
          cancelOrder(this.orderDetail.orderId, this.cancelForm.reason).then(() => {
            this.$message({
              type: 'success',
              message: '取消订单成功'
            });
            this.cancelDialogVisible = false;
            this.getDetail();
          });
        }
      });
    },
    
    selectTalent(talent) {
      this.$confirm(`确定选择达人 ${talent.memberNickname || '未知'} 吗?`, '选择达人', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用选择达人API
        const data = {
          orderId: this.orderDetail.orderId,
          memberIds: [talent.memberId]
        };
        
        selectTalents(data).then(() => {
          this.$message({
            type: 'success',
            message: '选择达人成功'
          });
          this.getDetail();
        }).catch(error => {
          this.$message.error(error.message || '选择达人失败');
        });
      }).catch(() => {});
    },
    
    getOrderStatusType(status) {
      const statusMap = {
        0: 'warning',
        1: 'primary',
        2: 'success',
        3: 'info',
        4: 'success',
        5: 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    getEnrollStatusType(status) {
      // 处理枚举类型
      if (status && typeof status === 'object' && status.code) {
        const codeMap = {
          'ENROLLED': 'info',
          'SELECTED': 'success',
          'DEPARTED': 'primary',
          'ARRIVED': 'success',
          'SERVICING': 'success',
          'SERVICE_COMPLETED': 'success',
          'EVALUATED': 'success',
          'CANCELED': 'danger'
        };
        return codeMap[status.code] || 'info';
      }
      
      // 处理数字类型
      const statusMap = {
        0: 'info',
        1: 'success',
        2: 'primary',
        3: 'success',
        4: 'success',
        5: 'success',
        6: 'success',
        7: 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    getEnrollStatusText(status) {
      // 处理枚举类型
      if (status && typeof status === 'object' && status.code) {
        const codeMap = {
          'ENROLLED': '已报名',
          'SELECTED': '已选中',
          'DEPARTED': '已出发',
          'ARRIVED': '已到达',
          'SERVICING': '服务中',
          'SERVICE_COMPLETED': '服务完成',
          'EVALUATED': '已评价',
          'CANCELED': '已取消'
        };
        return codeMap[status.code] || '未知状态';
      }
      
      // 处理数字类型
      const statusMap = {
        0: '已报名',
        1: '已选中',
        2: '已出发',
        3: '已到达',
        4: '服务中',
        5: '服务完成',
        6: '已评价',
        7: '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    
    getSexRequireText(sexRequire) {
      const sexMap = {
        0: '不限',
        1: '男',
        2: '女'
      };
      return sexMap[sexRequire] || '不限';
    },
    
    updateDetailStatus(row, status) {
      const data = {
        detailId: row.id,
        status: status
      };
      
      updateOrderDetailStatus(data).then(() => {
        this.$message({
          type: 'success',
          message: `子订单状态更新成功，新状态：${this.getEnrollStatusText(status)}`
        });
        this.getDetail();
      });
    },
    
    getStatusTimelineType(record) {
      if (record.type === 'Order') {
        return 'primary';
      } else {
        return 'success';
      }
    },
    
    getStatusDescription(record) {
      if (record.description) {
        return record.description;
      }
      
      if (record.type === 'Order') {
        const statusMap = {
          0: '待发布',
          1: '待审核',
          2: '发布中',
          3: '待选择达人',
          4: '进行中',
          5: '已完成',
          6: '已取消'
        };
        return `订单状态变更为：${statusMap[record.status] || '未知状态'}`;
      } else {
        return '子订单状态变更';
      }
    },
    
    getSubOrderActiveStep(detail) {
      // 如果是枚举类型
      if (detail.status && typeof detail.status === 'object' && detail.status.code) {
        const codeMap = {
          'ENROLLED': 0,
          'SELECTED': 1,
          'DEPARTED': 2,
          'ARRIVED': 3,
          'SERVICING': 4,
          'SERVICE_COMPLETED': 5,
          'EVALUATED': 6
        };
        return codeMap[detail.status.code] || 0;
      }
      
      // 如果是数字类型
      const statusMap = {
        0: 0,
        1: 1,
        2: 2,
        3: 3,
        4: 4,
        5: 5,
        6: 6
      };
      return statusMap[detail.status] || 0;
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleString();
    },
    
    calculateServiceDuration(detail) {
      if (!detail.serviceStartTime || !detail.serviceEndTime) {
        return 0;
      }
      const startTime = new Date(detail.serviceStartTime).getTime();
      const endTime = new Date(detail.serviceEndTime).getTime();
      return Math.floor((endTime - startTime) / 1000 / 60);
    },
    
    calculateOrderGold(detail) {
      // 如果服务时长或小时费用为空，返回0
      if (!detail.serviceStartTime || !detail.serviceEndTime || !this.orderDetail.hourlyRate) {
        return 0;
      }
      
      // 计算服务时长（小时）
      const durationMinutes = this.calculateServiceDuration(detail);
      const durationHours = durationMinutes / 60;
      
      // 计算订单金币 = 小时费用 × 时长
      return Math.round(this.orderDetail.hourlyRate * durationHours);
    }
  }
};
</script>

<style lang="scss" scoped>
.clay-container {
  background-color: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
}

.clay-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  margin-bottom: 24px;
  
  .header-left {
    .header-title {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 22px;
      font-weight: 600;
    }
    
    .order-id {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .header-right {
    display: flex;
    gap: 12px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.clay-descriptions {
  ::v-deep .el-descriptions-item__label {
    background-color: #f7f9fc;
    color: #606266;
    font-weight: 500;
  }
  
  ::v-deep .el-descriptions-item__content {
    padding: 12px 16px;
  }
}

.clay-table {
  border-radius: 8px;
  overflow: hidden;
  
  ::v-deep .el-table__header-wrapper {
    th {
      background-color: #f7f9fc;
      color: #606266;
      font-weight: 600;
    }
  }
  
  ::v-deep .el-table__body-wrapper {
    td {
      padding: 16px 0;
    }
  }
}

.evaluation-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.evaluation-item {
  padding: 16px;
  background-color: #fafbfc;
  border-radius: 8px;
  
  .evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .clay-avatar {
        border: 2px solid #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      .user-name {
        font-weight: 500;
        color: #303133;
      }
    }
  }
  
  .evaluation-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .evaluation-content {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 12px;
  }
  
  .evaluation-time {
    color: #909399;
    font-size: 12px;
  }
}

.clay-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  &.clay-button-sm {
    padding: 7px 12px;
  }
  
  &.clay-button-secondary {
    background-color: #f5f7fa;
    color: #606266;
    border-color: #dcdfe6;
    
    &:hover {
      background-color: #e9ecf2;
    }
  }
  
  &.clay-button-success {
    background-color: #67c23a;
    border-color: #67c23a;
  }
  
  &.clay-button-danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
  }
  
  &.clay-button-warning {
    background-color: #e6a23c;
    border-color: #e6a23c;
  }
}

.clay-textarea {
  ::v-deep .el-textarea__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-tag {
  border-radius: 16px;
  padding: 2px 10px;
  font-weight: 500;
}

.clay-dialog {
  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
    
    .el-dialog__header {
      background-color: #f7f9fc;
      padding: 16px 20px;
      
      .el-dialog__title {
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}

.clay-tabs {
  ::v-deep .el-tabs__header {
    margin-bottom: 0;
  }
  
  ::v-deep .el-tabs__content {
    padding: 16px;
  }
}

.sub-order-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sub-order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  .sub-order-title {
    font-weight: 600;
    color: #303133;
  }
}

.clay-tabs {
  ::v-deep .el-tabs__header {
    margin-bottom: 0;
  }
  
  ::v-deep .el-tabs__content {
    padding: 16px;
  }
}
</style> 