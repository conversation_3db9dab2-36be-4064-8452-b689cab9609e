package com.play.xianshibusiness.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * OSS文件上传服务接口
 */
public interface OssService {
    
    /**
     * 上传文件到OSS
     * 
     * @param file 要上传的文件
     * @param folder 存储文件夹路径，如 "images/avatars/"
     * @return 文件访问URL
     */
    String uploadFile(MultipartFile file, String folder);
    
    /**
     * 上传图片文件到OSS
     * 
     * @param file 要上传的图片文件
     * @param folder 存储文件夹路径，如 "images/"
     * @return 图片访问URL
     */
    String uploadImage(MultipartFile file, String folder);
    
    /**
     * 删除OSS中的文件
     * 
     * @param fileUrl 文件URL
     * @return 是否删除成功
     */
    boolean deleteFile(String fileUrl);
    
    /**
     * 生成文件存储路径
     * 
     * @param originalFilename 原始文件名
     * @param folder 存储文件夹
     * @return 完整的存储路径
     */
    String generateFilePath(String originalFilename, String folder);
    
    /**
     * 生成OSS STS临时访问凭证
     * 
     * @return 包含临时访问凭证的Map，包括accessKeyId、accessKeySecret、securityToken、expiration等
     */
    Map<String, String> generateStsCredentials();
} 