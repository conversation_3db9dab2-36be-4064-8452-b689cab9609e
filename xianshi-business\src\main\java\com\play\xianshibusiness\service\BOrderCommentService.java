package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.play.xianshibusiness.dto.order.OrderCommentDTO;
import com.play.xianshibusiness.enums.OrderEnrollDetailStatusEnum;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.BOrderCommentMapper;
import com.play.xianshibusiness.mapper.BOrderEnrollDetailMapper;
import com.play.xianshibusiness.mapper.BOrderMapper;
import com.play.xianshibusiness.mapper.CMemberMapper;
import com.play.xianshibusiness.pojo.BOrder;
import com.play.xianshibusiness.pojo.BOrderCommentPo;
import com.play.xianshibusiness.pojo.BOrderEnrollDetail;
import com.play.xianshibusiness.pojo.CMember;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 订单评价服务
 */
@Service
public class BOrderCommentService extends ServiceImpl<BOrderCommentMapper, BOrderCommentPo> {
    
    @Resource
    private BOrderCommentMapper orderCommentMapper;
    
    @Resource
    private BOrderMapper orderMapper;
    
    @Resource
    private BOrderEnrollDetailMapper orderEnrollDetailMapper;
    
    @Resource
    private CMemberMapper memberMapper;
    
    /**
     * 创建订单评价
     * @param dto 评价DTO
     * @param memberId 会员ID
     * @param orderId 订单ID
     * @return 评价ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String createComment(OrderCommentDTO dto, String memberId, String orderId) {
        // 1.校验订单是否存在
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }
        
        // 2.校验子订单是否存在
        BOrderEnrollDetail detail = orderEnrollDetailMapper.selectById(dto.getEnrollDetailId());
        if (detail == null || detail.getDeleted()) {
            throw new GlobalException(400, "订单报名记录不存在");
        }
        
        // 3.检查子订单是否属于该订单
        if (!detail.getOrderId().equals(orderId)) {
            throw new GlobalException(400, "订单报名记录与订单不匹配");
        }
        
        // 4.校验是否重复评价
        BOrderCommentPo existComment = orderCommentMapper.selectOne(
                new LambdaQueryWrapper<BOrderCommentPo>()
                .eq(BOrderCommentPo::getOrderId, orderId)
                .eq(BOrderCommentPo::getMemberId, memberId)
                .eq(BOrderCommentPo::getDeleted, false)
        );
        
        if (existComment != null) {
            throw new GlobalException(400, "您已评价过该订单");
        }
        
        // 5.创建评价
        BOrderCommentPo comment = new BOrderCommentPo();
        comment.setId(UUID.randomUUID().toString());
        comment.setOrderId(orderId);
        comment.setMemberId(memberId);
        comment.setCommentStarts(dto.getStars().toString());
        comment.setComment(dto.getComment());
        
        // 处理评价标签
        if (dto.getTagList() != null && !dto.getTagList().isEmpty()) {
            comment.setCommentJson(String.join(",", dto.getTagList()));
        }
        
        comment.setCreateTime(LocalDateTime.now());
        comment.setUpdateTime(LocalDateTime.now());
        comment.setDeleted(false);
        comment.setAvailable(true);
        
        orderCommentMapper.insert(comment);
        
        return comment.getId();
    }
    
    /**
     * 获取订单评价列表
     * @param orderId 订单ID
     * @return 订单评价列表
     */
    public List<BOrderCommentPo> getCommentsByOrderId(String orderId) {
        // 1.校验订单是否存在
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            return Collections.emptyList();
        }
        
        // 2.查询评价列表
        return orderCommentMapper.selectList(
                new LambdaQueryWrapper<BOrderCommentPo>()
                .eq(BOrderCommentPo::getOrderId, orderId)
                .eq(BOrderCommentPo::getDeleted, false)
                .orderByDesc(BOrderCommentPo::getCreateTime)
        );
    }

    /**
     * 获取订单评价
     *
     * @param orderId 订单ID
     * @return 评价列表
     */
    public List<Map<String, Object>> getOrderComments(String orderId) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }
        
        // 2.获取订单下的所有子订单
        List<BOrderEnrollDetail> details = orderEnrollDetailMapper.selectList(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                .eq(BOrderEnrollDetail::getOrderId, orderId)
                .eq(BOrderEnrollDetail::getDeleted, false)
                .eq(BOrderEnrollDetail::getStatus, OrderEnrollDetailStatusEnum.EVALUATED)
        );
        
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        
        // 3.获取子订单的评价
        List<Map<String, Object>> result = new ArrayList<>();
        for (BOrderEnrollDetail detail : details) {
            // 获取评价
            BOrderCommentPo comment = getOne(
                    new LambdaQueryWrapper<BOrderCommentPo>()
                    .eq(BOrderCommentPo::getOrderId, orderId)
                    .eq(BOrderCommentPo::getMemberId, detail.getMemberId())
                    .eq(BOrderCommentPo::getDeleted, false)
            );
            
            if (comment != null) {
                // 获取达人信息
                CMember member = memberMapper.selectById(detail.getMemberId());
                
                Map<String, Object> commentMap = new HashMap<>();
                commentMap.put("commentId", comment.getId());
                commentMap.put("orderId", orderId);
                commentMap.put("orderDetailId", detail.getId());
                commentMap.put("memberId", detail.getMemberId());
                commentMap.put("memberNickname", member != null ? member.getNickname() : "");
                commentMap.put("memberAvatar", member != null ? member.getAvatar() : "");
                commentMap.put("stars", comment.getCommentStarts());
                commentMap.put("comment", comment.getComment());
                
                // 处理标签列表
                List<String> tagList = new ArrayList<>();
                if (StringUtils.hasText(comment.getCommentJson())) {
                    tagList = Arrays.asList(comment.getCommentJson().split(","));
                }
                commentMap.put("tagList", tagList);
                
                commentMap.put("createTime", comment.getCreateTime());
                
                result.add(commentMap);
            }
        }
        
        return result;
    }
}

