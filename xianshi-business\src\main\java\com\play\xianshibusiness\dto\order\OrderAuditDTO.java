package com.play.xianshibusiness.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 订单审核DTO
 */
@Data
@ApiModel("订单审核DTO")
public class OrderAuditDTO {

    @ApiModelProperty(value = "订单ID", required = true)
    @NotBlank(message = "订单ID不能为空")
    private String orderId;
    
    @ApiModelProperty(value = "是否通过", required = true)
    @NotNull(message = "审核结果不能为空")
    private Boolean passed;
    
    @ApiModelProperty(value = "审核备注")
    private String remark;
}