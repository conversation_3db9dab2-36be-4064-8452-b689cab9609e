package com.play.xianshibusiness.dto.member;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.play.xianshibusiness.annotation.LoginRequestValid;
import com.play.xianshibusiness.enums.LoginMethod;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@LoginRequestValid
public class LoginRequest {

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "验证码")
    private String code;

    @ApiModelProperty(value = "登录方式")
    @JsonProperty("loginMethod") // 支持前端发送 loginMethod 字段
    private String loginMethod;

    // 获取登录方式枚举
    public LoginMethod getMethod() {
        if (loginMethod == null) {
            return null;
        }

        // 将字符串转换为枚举
        switch (loginMethod.toUpperCase()) {
            case "SMS":
            case "PHONE_CODE":
                return LoginMethod.PHONE_CODE;
            case "WECHAT":
            case "ONE_CLICK":
            case "CURRENT_PHONE":
                return LoginMethod.CURRENT_PHONE;
            default:
                return null;
        }
    }

}
