<template>
  <view class="container">
    <!-- 加载中状态 -->
    <block v-if="loading">
      <view class="loading-container">
        <view class="loading">
          <view class="loading-icon"></view>
          <text>加载中...</text>
        </view>
      </view>
    </block>

    <!-- 错误状态 -->
    <block v-else-if="error">
      <view class="error-container">
        <icon type="warn" size="64" color="#ff4d4f"></icon>
        <view class="error-text">{{ errorMsg || "订单加载失败" }}</view>
        <view class="button-group">
          <button class="refresh-button" @tap="loadOrderDetail">重试</button>
          <button class="back-button" @tap="navigateBack">返回</button>
        </view>
      </view>
    </block>

    <block v-else>
      <!-- 订单信息卡片 -->
      <view class="order-info-card">
        <view class="card-header">
          <view class="card-title">订单信息</view>
        </view>
        <view class="card-body">
          <view class="info-item">
            <text class="label">订单标题：</text>
            <text class="value">{{ order.title }}</text>
          </view>
          <view class="info-item">
            <text class="label">时间：</text>
            <text class="value">{{ order.timeDisplay }}</text>
          </view>
          <view class="info-item">
            <text class="label">地点：</text>
            <text class="value">{{ order.location || "线上" }}</text>
          </view>
          <view class="info-item">
            <text class="label">价格：</text>
            <text class="value">¥{{ order.price }}</text>
          </view>
          <view class="info-item">
            <text class="label">描述：</text>
            <text class="value description">{{
              order.description || "暂无描述"
            }}</text>
          </view>
        </view>
      </view>

      <!-- 已申请状态 -->
      <block v-if="hasApplied">
        <view class="apply-status">
          <icon type="success" size="64" color="#07c160"></icon>
          <view class="success-text">已成功申请</view>
          <view class="status-tip">请等待用户选择</view>
          <button class="back-to-list" @tap="navigateBack">返回列表</button>
        </view>
      </block>

      <!-- 申请表单 -->
      <block v-else>
        <view class="apply-form">
          <view class="form-title">申请该订单</view>
          <view class="form-item">
            <text class="form-label">备注信息（选填）</text>
            <textarea
              class="remark-input"
              placeholder="可以补充您的专业背景、相关经验等信息，增加被选中的几率"
              :maxlength="remarkMaxLength"
              @input="onRemarkInput"
              :value="remark"
            ></textarea>
            <text class="char-count"
              >{{ remark.length }}/{{ remarkMaxLength }}</text
            >
          </view>
          <view class="button-group">
            <button class="cancel-button" @tap="navigateBack">取消</button>
            <button
              class="submit-button"
              :class="{ disabled: submitting }"
              :disabled="submitting"
              @tap="submitApplication"
            >
              提交申请
            </button>
          </view>
        </view>
      </block>
    </block>
  </view>
</template>

<script>
// 引入订单状态模块
import { OrderState } from '../../utils/order-state';
import { formatDateTime } from '../../utils/time-formatter';
import utils from '../../utils/util.js';
import safe from '../../utils/safe.js';

export default {
  data() {
    return {
      orderId: "",
      order: null,
      loading: true,
      error: false,
      errorMsg: "",
      hasApplied: false,
      remark: "",
      remarkMaxLength: 200,
      submitting: false,
    };
  },
  onLoad(options) {
    if (!options.id) {
      this.loading = false;
      this.error = true;
      this.errorMsg = "订单ID无效，请返回重试";
      return;
    }

    this.orderId = options.id;
    this.loadOrderDetail();
    this.checkIfApplied();
  },
  methods: {
    /**
     * 加载订单详情
     */
    loadOrderDetail() {
      const { orderId } = this;
      console.log("正在加载订单详情，订单ID:", orderId);

      // 先检查是否有缓存
      const cachedOrders = uni.getStorageSync("orders") || [];
      const cachedOrder = cachedOrders.find((order) => order.id === orderId);

      if (cachedOrder) {
        console.log("从本地缓存orders中找到订单");
        this.processOrderData(cachedOrder);
        return;
      }

      // 如果缓存中没有，尝试从globalData获取
      const app = getApp();
      const globalOrders = app.globalData.orders || [];
      const globalOrder = globalOrders.find((order) => order.id === orderId);

      if (globalOrder) {
        console.log("从globalData.orders中找到订单");
        this.processOrderData(globalOrder);
        return;
      }

      // 尝试从publishedOrders获取
      const publishedOrders = app.globalData.publishedOrders || [];
      const publishedOrder = publishedOrders.find(
        (order) => order.id === orderId || order.orderId === orderId
      );

      if (publishedOrder) {
        console.log("从globalData.publishedOrders中找到订单");
        this.processOrderData(publishedOrder);
        return;
      }

      // 尝试从publishedOrders缓存中获取
      try {
        const publishedOrdersStr = uni.getStorageSync("publishedOrders");
        if (publishedOrdersStr) {
          const publishedOrdersCache = JSON.parse(publishedOrdersStr);
          const publishedOrderCache = publishedOrdersCache.find(
            (order) => order.id === orderId || order.orderId === orderId
          );

          if (publishedOrderCache) {
            console.log("从本地缓存publishedOrders中找到订单");
            this.processOrderData(publishedOrderCache);
            return;
          }
        }
      } catch (e) {
        console.error("解析publishedOrders缓存失败:", e);
      }

      // 尝试从003账号专用存储中获取
      try {
        const publishedOrders003Str = uni.getStorageSync("publishedOrders_003");
        if (publishedOrders003Str) {
          const publishedOrders003 = JSON.parse(publishedOrders003Str);
          const publishedOrder003 = publishedOrders003.find(
            (order) => order.id === orderId || order.orderId === orderId
          );

          if (publishedOrder003) {
            console.log("从本地缓存publishedOrders_003中找到订单");
            this.processOrderData(publishedOrder003);
            return;
          }
        }
      } catch (e) {
        console.error("解析publishedOrders_003缓存失败:", e);
      }

      // 如果所有缓存都没有找到，则显示错误
      this.loading = false;
      this.error = true;
      this.errorMsg = "未找到订单信息，请返回重试";
    },

    /**
     * 处理订单数据
     */
    processOrderData(orderData) {
      if (!orderData) {
        this.loading = false;
        this.error = true;
        this.errorMsg = "订单数据无效";
        return;
      }

      // 处理时间显示
      const timeDisplay = formatDateTime(orderData.time);
      
      // 更新数据
      this.order = {
        ...orderData,
        timeDisplay
      };
      
      this.loading = false;
    },

    /**
     * 检查是否已申请
     */
    checkIfApplied() {
      const { orderId } = this;
      const appliedOrders = uni.getStorageSync("appliedOrders") || [];
      this.hasApplied = appliedOrders.some(order => order.id === orderId);
    },

    /**
     * 备注输入处理
     */
    onRemarkInput(e) {
      this.remark = e.detail.value;
    },

    /**
     * 提交申请
     */
    submitApplication() {
      if (this.submitting) return;
      
      this.submitting = true;
      
      // 获取当前用户信息
      const userInfo = uni.getStorageSync("userInfo");
      if (!userInfo) {
        uni.showToast({
          title: "请先登录",
          icon: "none"
        });
        this.submitting = false;
        return;
      }

      // 构建申请数据
      const application = {
        orderId: this.orderId,
        userId: userInfo.id,
        userName: userInfo.name,
        userAvatar: userInfo.avatar,
        remark: this.remark,
        applyTime: new Date().getTime()
      };

      // 获取已申请订单列表
      let appliedOrders = [];
      try {
        const appliedOrdersStr = uni.getStorageSync("appliedOrders");
        if (appliedOrdersStr) {
          appliedOrders = JSON.parse(appliedOrdersStr);
        }
      } catch (e) {
        console.error("解析已申请订单列表失败:", e);
      }

      // 检查是否重复申请
      if (appliedOrders.some(order => order.id === this.orderId)) {
        uni.showToast({
          title: "您已申请过该订单",
          icon: "none"
        });
        this.submitting = false;
        return;
      }

      // 添加新申请
      appliedOrders.push(application);

      // 保存更新后的申请列表
      try {
        uni.setStorageSync("appliedOrders", JSON.stringify(appliedOrders));
        
        // 更新状态
        this.hasApplied = true;
        
        uni.showToast({
          title: "申请成功",
          icon: "success"
        });
      } catch (e) {
        console.error("保存申请失败:", e);
        uni.showToast({
          title: "申请失败，请重试",
          icon: "none"
        });
      }

      this.submitting = false;
    },

    /**
     * 返回上一页
     */
    navigateBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #e6e6e6;
  border-top: 6rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading text {
  font-size: 28rpx;
  color: #888;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.error-text {
  margin: 20rpx 0 40rpx;
  font-size: 30rpx;
  color: #666;
}

.refresh-button,
.back-button {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin: 10rpx;
}

.refresh-button {
  background-color: #fff;
  color: #07c160;
  border: 1rpx solid #07c160;
}

.back-button {
  background-color: #f2f2f2;
  color: #666;
}

/* 订单信息卡片 */
.order-info-card {
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.card-body {
  padding: 20rpx 30rpx;
}

.info-item {
  display: flex;
  padding: 16rpx 0;
  font-size: 28rpx;
}

.label {
  width: 160rpx;
  color: #888;
  flex-shrink: 0;
}

.value {
  flex: 1;
  color: #333;
}

.value.description {
  display: block;
  margin-top: 10rpx;
  white-space: pre-wrap;
  word-break: break-all;
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 6rpx;
}

/* 申请状态 */
.apply-status {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 60rpx 0;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.success-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 0 16rpx;
}

.status-tip {
  font-size: 28rpx;
  color: #888;
  margin-bottom: 40rpx;
}

.back-to-list {
  width: 60%;
  background-color: #07c160;
  color: #fff;
  font-size: 30rpx;
  margin-top: 20rpx;
}

/* 申请表单 */
.apply-form {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
  color: #333;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.remark-input {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 6rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.char-count {
  position: absolute;
  bottom: 10rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.cancel-button,
.submit-button {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
}

.cancel-button {
  background-color: #f2f2f2;
  color: #666;
}

.submit-button {
  background-color: #07c160;
  color: #fff;
}

.submit-button.disabled {
  opacity: 0.6;
}
</style>
