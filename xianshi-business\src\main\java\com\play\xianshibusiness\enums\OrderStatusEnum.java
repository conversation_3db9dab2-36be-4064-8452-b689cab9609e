package com.play.xianshibusiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
    
    DRAFT("DRAFT", "待发布"), 
    AUDITING("AUDITING", "待审核"), 
    PUBLISHED("PUBLISHED", "发布中"), 
    WAITING_SELECT("WAITING_SELECT", "待选择达人"), 
    PROCESSING("PROCESSING", "进行中"),
    COMPLETED("COMPLETED", "已完成"), 
    CANCELED("CANCELED", "已取消");

    private final String code;
    private final String desc;
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果找不到返回null
     */
    public static OrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        switch (code) {
            case 0:
                return DRAFT;
            case 1:
                return AUDITING;
            case 2:
                return PUBLISHED;
            case 3:
                return WAITING_SELECT;
            case 4:
                return PROCESSING;
            case 5:
                return COMPLETED;
            case 6:
                return CANCELED;
            default:
                return null;
        }
    }
} 