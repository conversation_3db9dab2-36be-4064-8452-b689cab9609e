<template>
  <div class="app-container clay-container">
    <!-- 搜索区域 -->
    <div class="filter-container clay-card">
      <el-form :inline="true" :model="listQuery" class="filter-form">
        <el-form-item label="套餐名称">
          <el-input v-model="listQuery.name" placeholder="请输入套餐名称" clearable class="clay-input" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" class="clay-button">查询</el-button>
          <el-button @click="resetQuery" class="clay-button clay-button-secondary">重置</el-button>
          <el-button type="success" @click="handleCreate" class="clay-button clay-button-success">新增套餐</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格区域 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      class="clay-table"
    >
      <el-table-column label="套餐名称" align="center" min-width="120">
        <template slot-scope="{row}">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="套餐金额" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.amount }} 元</span>
        </template>
      </el-table-column>
      
      <el-table-column label="基础金币" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.baseGold }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="赠送金币" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.bonusGold }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="总金币" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.totalGold }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="折扣" min-width="80" align="center">
        <template slot-scope="{row}">
          <span>{{ row.discount ? row.discount + ' 折' : '无折扣' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="是否推荐" min-width="80" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.isRecommend ? 'success' : 'info'" class="clay-tag">
            {{ row.isRecommend ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="排序" min-width="80" align="center">
        <template slot-scope="{row}">
          <span>{{ row.sort }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createTime }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" min-width="200" class-name="fixed-width">
        <template slot-scope="{row}">
          <el-button 
            type="primary" 
            size="mini" 
            @click="handleUpdate(row)" 
            class="clay-button clay-button-sm"
          >编辑</el-button>
          
          <el-button 
            type="danger" 
            size="mini" 
            @click="handleDelete(row)" 
            class="clay-button clay-button-sm clay-button-danger"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
      class="clay-pagination"
    />
    
    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '新增充值套餐' : '编辑充值套餐'" :visible.sync="dialogVisible" width="600px" class="clay-dialog">
      <el-form ref="dataForm" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="套餐名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入套餐名称" class="clay-input" />
        </el-form-item>
        
        <el-form-item label="套餐金额" prop="amount">
          <el-input-number v-model="formData.amount" :precision="2" :step="10" :min="0" class="clay-input-number" />
          <span class="unit-text">元</span>
        </el-form-item>
        
        <el-form-item label="基础金币" prop="baseGold">
          <el-input-number v-model="formData.baseGold" :precision="0" :step="100" :min="0" class="clay-input-number" />
        </el-form-item>
        
        <el-form-item label="赠送金币" prop="bonusGold">
          <el-input-number v-model="formData.bonusGold" :precision="0" :step="10" :min="0" class="clay-input-number" />
        </el-form-item>
        
        <el-form-item label="折扣" prop="discount">
          <el-input-number v-model="formData.discount" :precision="1" :step="0.1" :min="0" :max="10" class="clay-input-number" />
          <span class="unit-text">折</span>
        </el-form-item>
        
        <el-form-item label="套餐描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入套餐描述" class="clay-textarea" />
        </el-form-item>
        
        <el-form-item label="套餐图标" prop="icon">
          <el-input v-model="formData.icon" placeholder="请输入套餐图标URL" class="clay-input" />
        </el-form-item>
        
        <el-form-item label="是否推荐" prop="isRecommend">
          <el-switch v-model="formData.isRecommend" class="clay-switch" />
        </el-form-item>
        
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :precision="0" :step="1" :min="0" class="clay-input-number" />
          <span class="unit-text">数值越小越靠前</span>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" class="clay-button clay-button-secondary">取消</el-button>
        <el-button type="primary" @click="submitForm" class="clay-button">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pagePackages, createPackage, updatePackage, deletePackage } from '@/api/recharge';
import Pagination from '@/components/Pagination';

export default {
  name: 'RechargePackage',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        name: ''
      },
      dialogVisible: false,
      dialogStatus: 'create',
      formData: {
        name: '',
        amount: 0,
        baseGold: 0,
        bonusGold: 0,
        totalGold: 0,
        discount: 10,
        description: '',
        icon: '',
        isRecommend: false,
        sort: 0
      },
      rules: {
        name: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
        amount: [{ required: true, message: '请输入套餐金额', trigger: 'blur' }],
        baseGold: [{ required: true, message: '请输入基础金币数量', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入排序值', trigger: 'blur' }]
      }
    };
  },
  watch: {
    'formData.amount': function(val) {
      this.updateTotalGold();
    },
    'formData.baseGold': function(val) {
      this.updateTotalGold();
    },
    'formData.bonusGold': function(val) {
      this.updateTotalGold();
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      pagePackages(this.listQuery).then(response => {
        this.list = response.data.records || [];
        this.total = response.data.total || 0;
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      });
    },
    handleSearch() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        name: ''
      };
      this.getList();
    },
    resetForm() {
      this.formData = {
        name: '',
        amount: 0,
        baseGold: 0,
        bonusGold: 0,
        totalGold: 0,
        discount: 10,
        description: '',
        icon: '',
        isRecommend: false,
        sort: 0
      };
    },
    handleCreate() {
      this.dialogStatus = 'create';
      this.resetForm();
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleUpdate(row) {
      this.dialogStatus = 'update';
      this.formData = Object.assign({}, row);
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    handleDelete(row) {
      this.$confirm('确认删除该充值套餐吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePackage(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.getList();
        });
      }).catch(() => {});
    },
    updateTotalGold() {
      const baseGold = this.formData.baseGold || 0;
      const bonusGold = this.formData.bonusGold || 0;
      this.formData.totalGold = baseGold + bonusGold;
    },
    submitForm() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.updateTotalGold();
          const formData = Object.assign({}, this.formData);
          
          if (this.dialogStatus === 'create') {
            createPackage(formData).then(() => {
              this.dialogVisible = false;
              this.$message({
                type: 'success',
                message: '创建成功!'
              });
              this.getList();
            });
          } else {
            updatePackage(formData.id, formData).then(() => {
              this.dialogVisible = false;
              this.$message({
                type: 'success',
                message: '更新成功!'
              });
              this.getList();
            });
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.clay-container {
  background-color: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
}

.clay-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.clay-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  
  ::v-deep .el-table__header-wrapper {
    th {
      background-color: #f7f9fc;
      color: #606266;
      font-weight: 600;
    }
  }
  
  ::v-deep .el-table__body-wrapper {
    td {
      padding: 16px 0;
    }
  }
}

.clay-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  &.clay-button-sm {
    padding: 7px 12px;
  }
  
  &.clay-button-secondary {
    background-color: #f5f7fa;
    color: #606266;
    border-color: #dcdfe6;
    
    &:hover {
      background-color: #e9ecf2;
    }
  }
  
  &.clay-button-success {
    background-color: #67c23a;
    border-color: #67c23a;
    color: #fff;
    
    &:hover {
      background-color: #85ce61;
      border-color: #85ce61;
    }
  }
  
  &.clay-button-danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #fff;
    
    &:hover {
      background-color: #f78989;
      border-color: #f78989;
    }
  }
}

.clay-input, .clay-textarea, .clay-input-number {
  width: 100%;
  
  ::v-deep .el-input__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    transition: all 0.3s;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-input-number {
  width: 200px;
}

.clay-textarea {
  ::v-deep .el-textarea__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-pagination {
  margin-top: 24px;
  text-align: right;
  
  ::v-deep .el-pagination {
    padding: 10px 0;
    
    button, li {
      background-color: #f9fafc;
      border-radius: 6px;
      margin: 0 3px;
      
      &.active {
        background-color: #409eff;
        color: white;
      }
      
      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}

.clay-tag {
  border-radius: 16px;
  padding: 2px 10px;
  font-weight: 500;
}

.clay-dialog {
  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
    
    .el-dialog__header {
      background-color: #f7f9fc;
      padding: 16px 20px;
      
      .el-dialog__title {
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}

.unit-text {
  margin-left: 10px;
  color: #909399;
}
</style> 