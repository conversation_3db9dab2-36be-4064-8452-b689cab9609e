package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.enums.OrderDataBaseType;
import com.play.xianshibusiness.enums.OrderStatusEnum;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * (BOrderStauts)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("b_order_status")
@ApiModel("订单变更表")
public class BOrderStatus extends BaseObjPo {

    //变更内容类型【枚举】 订单或是子订单
    @ApiModelProperty(value = "变更内容类型【枚举】 订单或是子订单")
    private OrderDataBaseType type;
    //业务ID 订单或子订单ID
    @ApiModelProperty(value = "业务ID 订单或子订单ID")
    private String bussinessId;
    @ApiModelProperty(value = "变更描述")
    private String description;
    @ApiModelProperty(value = "状态值")
    private Integer status;

}

