/* eslint-disable */
'use strict'
const path = require('path')

// 通过环境变量区分开发和生产
const isDev = process.env.NODE_ENV === 'development'
const BASE_API = process.env.VUE_APP_BASE_API || '/admin'
const port = isDev ? 8085 : 8080
const host = isDev ? 'localhost' : '**************'

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  publicPath: '/', // 公共路径
  outputDir: 'dist', // 构建输出目录
  assetsDir: 'static', // 静态资源目录
  lintOnSave: false, // 保存时不自动lint
  productionSourceMap: false, // 生产环境不生成source map
  devServer: {
    port: port, // 端口
    open: true, // 启动后自动打开浏览器
    host: host, // 主机名
    disableHostCheck: true, // 禁用主机检查
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      // 代理API请求到后端
      [BASE_API]: {
        //线上环境
        // target: `http://**************:9999`,
        //本地环境
        target: `http://127.0.0.1:9999/admin`,
        changeOrigin: true,
        pathRewrite: {
          ['^' + BASE_API]: ''
        }
      }
    }
  },
  configureWebpack: {
    // 配置别名，方便引入
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  },
  chainWebpack(config) {
    // 设置页面标题
    config.plugin('html').tap(args => {
      args[0].title = '闲时有伴管理系统'
      return args
    })
  }
} 