package com.play.xianshiadmin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.dto.order.*;
import com.play.xianshibusiness.pojo.BOrderStatus;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.BOrderCommentService;
import com.play.xianshibusiness.service.BOrderEnrollDetailService;
import com.play.xianshibusiness.service.BOrderService;
import com.play.xianshibusiness.service.BOrderStatusService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 管理端订单控制器
 */
@RestController
@RequestMapping("/admin/order")
@Api(tags = "管理端订单API")
public class OrderAdminController {

    @Resource
    private BOrderService orderService;
    
    @Resource
    private BOrderCommentService orderCommentService;
    
    @Resource
    private BOrderStatusService orderStatusService;
    
    @Resource
    private BOrderEnrollDetailService orderEnrollDetailService;
    
    /**
     * 分页查询订单列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询订单列表")
    @RequireAdmin
    public Result<Page<OrderVO>> pageOrders(OrderQueryDTO queryDTO) {
        return ResultUtils.success(orderService.adminPageOrders(queryDTO));
    }
    
    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderId}")
    @ApiOperation(value = "获取订单详情")
    @RequireAdmin
    public Result<OrderDetailVO> getOrderDetail(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId) {
        return ResultUtils.success(orderService.adminGetOrderDetail(orderId));
    }
    
    /**
     * 审核订单
     */
    @PostMapping("/audit")
    @ApiOperation(value = "审核订单")
    @RequireAdmin
    public Result<Boolean> auditOrder(@RequestBody @Valid AuditOrderDTO auditDTO) {
        // 设置操作人ID为当前管理员
        String adminId = PrincipalUtil.getMemberIdOrNull();
        if (adminId != null) {
            auditDTO.setAuditorId(adminId);
        }
        
        return ResultUtils.success(orderService.auditOrder(auditDTO));
    }
    
    /**
     * 取消订单
     */
    @PostMapping("/cancel/{orderId}")
    @ApiOperation(value = "取消订单")
    @RequireAdmin
    public Result<Boolean> cancelOrder(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId,
            @ApiParam(value = "取消原因") @RequestParam(required = false) String reason) {
        return ResultUtils.success(orderService.adminCancelOrder(orderId, reason));
    }
    
    /**
     * 获取订单统计数据
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取订单统计数据")
    @RequireAdmin
    public Result<Map<String, Object>> getOrderStatistics() {
        System.out.println("接收到获取订单统计数据请求");
        Map<String, Object> statistics = orderService.getOrderStatistics();
        
        // 检查关键数据是否存在
        if (statistics != null) {
            System.out.println("订单总数: " + statistics.get("totalOrders") + ", 类型: " + 
                    (statistics.get("totalOrders") != null ? statistics.get("totalOrders").getClass().getName() : "null"));
            System.out.println("进行中订单: " + statistics.get("processingOrders") + ", 类型: " + 
                    (statistics.get("processingOrders") != null ? statistics.get("processingOrders").getClass().getName() : "null"));
            System.out.println("用户数量: " + statistics.get("totalUsers") + ", 类型: " + 
                    (statistics.get("totalUsers") != null ? statistics.get("totalUsers").getClass().getName() : "null"));
            System.out.println("已完成订单: " + statistics.get("completedOrders") + ", 类型: " + 
                    (statistics.get("completedOrders") != null ? statistics.get("completedOrders").getClass().getName() : "null"));
        }
        
        System.out.println("返回订单统计数据: " + statistics);
        return ResultUtils.success(statistics);
    }
    
    /**
     * 根据时间范围获取订单统计数据
     */
    @GetMapping("/statistics/by-date")
    @ApiOperation(value = "根据时间范围获取订单统计数据")
    @RequireAdmin
    public Result<Map<String, Object>> getOrderStatisticsByDateRange(
            @ApiParam(value = "开始日期 yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期 yyyy-MM-dd") @RequestParam(required = false) String endDate) {
        System.out.println("接收到获取订单统计数据请求，开始日期: " + startDate + ", 结束日期: " + endDate);
        Map<String, Object> statistics = orderService.getOrderStatisticsByDateRange(startDate, endDate);
        
        // 检查关键数据是否存在
        if (statistics != null) {
            System.out.println("订单总数: " + statistics.get("totalOrders") + ", 类型: " + 
                    (statistics.get("totalOrders") != null ? statistics.get("totalOrders").getClass().getName() : "null"));
            System.out.println("进行中订单: " + statistics.get("processingOrders") + ", 类型: " + 
                    (statistics.get("processingOrders") != null ? statistics.get("processingOrders").getClass().getName() : "null"));
            System.out.println("用户数量: " + statistics.get("totalUsers") + ", 类型: " + 
                    (statistics.get("totalUsers") != null ? statistics.get("totalUsers").getClass().getName() : "null"));
            System.out.println("已完成订单: " + statistics.get("completedOrders") + ", 类型: " + 
                    (statistics.get("completedOrders") != null ? statistics.get("completedOrders").getClass().getName() : "null"));
        }
        
        System.out.println("返回订单统计数据: " + statistics);
        return ResultUtils.success(statistics);
    }
    
    /**
     * 获取订单趋势数据
     */
    @GetMapping("/trend")
    @ApiOperation(value = "获取订单趋势数据")
    @RequireAdmin
    public Result<Map<String, Object>> getOrderTrendData(
            @ApiParam(value = "周期类型：week-本周, month-本月, year-本年, custom-自定义", defaultValue = "month") 
            @RequestParam(defaultValue = "month") String period,
            @ApiParam(value = "开始日期 yyyy-MM-dd（仅当period为custom时有效）") 
            @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期 yyyy-MM-dd（仅当period为custom时有效）") 
            @RequestParam(required = false) String endDate) {
        System.out.println("接收到获取订单趋势数据请求，周期: " + period + ", 开始日期: " + startDate + ", 结束日期: " + endDate);
        Map<String, Object> trendData = orderService.getOrderTrendData(period, startDate, endDate);
        
        // 检查返回的数据结构
        if (trendData != null) {
            List<String> dateLabels = (List<String>) trendData.get("dateLabels");
            List<Long> orderCounts = (List<Long>) trendData.get("orderCounts");
            String groupBy = (String) trendData.get("groupBy");
            
            System.out.println("订单趋势数据: dateLabels=" + 
                    (dateLabels != null ? dateLabels.size() : "null") + 
                    ", orderCounts=" + 
                    (orderCounts != null ? orderCounts.size() : "null") + 
                    ", groupBy=" + groupBy);
            
            if (dateLabels != null && dateLabels.size() > 0) {
                System.out.println("日期标签示例: " + dateLabels.get(0) + 
                        (dateLabels.size() > 1 ? ", " + dateLabels.get(1) : ""));
            }
            
            if (orderCounts != null && orderCounts.size() > 0) {
                System.out.println("订单数量示例: " + orderCounts.get(0) + 
                        (orderCounts.size() > 1 ? ", " + orderCounts.get(1) : ""));
            }
        } else {
            System.out.println("订单趋势数据为空");
        }
        
        return ResultUtils.success(trendData);
    }
    
    /**
     * 获取订单类型分布数据
     */
    @GetMapping("/type-distribution")
    @ApiOperation(value = "获取订单类型分布数据")
    @RequireAdmin
    public Result<List<Map<String, Object>>> getOrderTypeDistribution(
            @ApiParam(value = "开始日期 yyyy-MM-dd") 
            @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期 yyyy-MM-dd") 
            @RequestParam(required = false) String endDate) {
        
        // 转换日期参数
        java.time.LocalDateTime startDateTime = null;
        java.time.LocalDateTime endDateTime = null;
        
        if (startDate != null && !startDate.isEmpty()) {
            startDateTime = java.time.LocalDate.parse(startDate).atStartOfDay();
        }
        
        if (endDate != null && !endDate.isEmpty()) {
            endDateTime = java.time.LocalDate.parse(endDate).plusDays(1).atStartOfDay();
        }
        
        return ResultUtils.success(orderService.getOrderTypeDistribution(startDateTime, endDateTime));
    }
    
    /**
     * 获取订单状态流转记录
     */
    @GetMapping("/status-records/{orderId}")
    @ApiOperation(value = "获取订单状态流转记录")
    @RequireAdmin
    public Result<List<BOrderStatus>> getOrderStatusRecords(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId) {
        return ResultUtils.success(orderStatusService.getOrderStatusRecords(orderId));
    }
    
    /**
     * 更新子订单状态
     */
    @PostMapping("/update-detail-status")
    @ApiOperation(value = "更新子订单状态")
    @RequireAdmin
    public Result<Boolean> updateOrderDetailStatus(@RequestBody @Valid OrderDetailStatusUpdateDTO dto) {
        String adminId = PrincipalUtil.getMemberIdOrNull();
        return ResultUtils.success(orderEnrollDetailService.adminUpdateOrderDetailStatus(dto, adminId));
    }
    
    /**
     * 获取订单评价
     */
    @GetMapping("/comments/{orderId}")
    @ApiOperation(value = "获取订单评价")
    @RequireAdmin
    public Result<Page<Object>> getOrderComments(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId,
            @ApiParam(value = "页码") @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页条数") @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        return ResultUtils.success(orderService.adminGetOrderComments(orderId, pageNum, pageSize));
    }

} 