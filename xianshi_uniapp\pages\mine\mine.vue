<template>
  <view class="container">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-info">
        <image
          class="avatar"
          :src="userInfo.avatar || '/static/images/default-avatar.png'"
        />
        <view class="user-details">
          <view class="nickname">{{ userInfo.nickname || "闲时会员792" }}</view>
          <view class="user-id">ID: {{ formatUserId(userInfo.sysId) }}</view>
          <view class="role-badges">
            <text class="role-badge normal" v-if="userInfo.currentRoleId === '1'">{{ currentRoleName }}</text>
            <text class="role-badge expert" v-if="userInfo.currentRoleId === '2'">{{ currentRoleName }}</text>
            <text class="role-badge guild" v-if="userInfo.currentRoleId === '3'">{{ currentRoleName }}</text>
          </view>
        </view>
        <view class="edit-btn" @tap="handleHomepage">
          编辑主页
        </view>
      </view>

          <!-- 数据统计 -->
    <view class="stats-row">
      <view class="stat-item" @tap="goToFansPage">
        <text class="stat-num">{{ userInfo.fansCount || 0 }}</text>
        <text class="stat-text">粉丝</text>
      </view>
      <view class="stat-item" @tap="goToFollowingPage">
        <text class="stat-num">{{ userInfo.followCount || 0 }}</text>
        <text class="stat-text">关注</text>
      </view>
      <view class="stat-item" @tap="goToViewHistory">
        <text class="stat-num">{{ recondsM || 0 }}</text>
        <text class="stat-text">浏览</text>
      </view>
    </view>

    </view>

        <view class="vip-banner" @tap="handleVip">
          <view class="vip-left">
            <text class="vip-crown">👑</text>
            <view class="vip-text">
              <text class="vip-title">闲时VIP</text>
              <text class="vip-subtitle">开通会员享更多权益</text>
            </view>
          </view>
          <view class="vip-action">立即开通</view>
        </view>
    <!-- 快捷功能图标 -->
    <view class="quick-functions">
      <view class="function-item" @tap="goToCoinCenter">
        <view class="function-icon coin-icon">
          <i class="iconfont icon-tubiao_jinbi"></i>
        </view>
        <text class="function-text">金币</text>
      </view>
      <view class="function-item" @tap="goToFavorites">
        <view class="function-icon favorite-icon">
          <i class="iconfont icon-shoucang1"></i>
        </view>
        <text class="function-text">收藏</text>
      </view>
      <view class="function-item" @tap="goToSignIn">
        <view class="function-icon signin-icon">
          <i class="iconfont icon-qiandao"></i>
        </view>
        <text class="function-text">签到</text>
      </view>

    </view>


    <!-- 功能列表 -->
    <view class="function-list">
      <!-- 用户订单管理 -->
      <view class="list-section">
        <view class="list-item" @tap="goToPublishOrders">
          <view class="item-icon">📄</view>
          <text class="item-text">发布订单</text>
          <view class="item-arrow">></view>
        </view>
        <view class="list-item" @tap="goToAppointmentOrders">
          <view class="item-icon">📋</view>
          <text class="item-text">约单订单</text>
          <view class="item-arrow">></view>
        </view>
        <view class="list-item">
          <view class="item-icon">💬</view>
          <text class="item-text">订单进程</text>
          <view class="item-arrow">></view>
        </view>
      </view>

      <!-- 达人订单管理 -->
      <view class="list-section" v-if="userInfo.currentRoleId === '2'">
        <view class="list-item" @tap="goToExpertOrders">
          <view class="item-icon">🎯</view>
          <text class="item-text">报名订单</text>
          <view class="item-arrow">></view>
        </view>
        <view class="list-item" @tap="goToInvitationOrders">
          <view class="item-icon">💌</view>
          <text class="item-text">邀约订单</text>
          <view class="item-arrow">></view>
        </view>
        <view class="list-item" @tap="goToOrderSettings">
          <view class="item-icon">⚙️</view>
          <text class="item-text">接单设置</text>
          <view class="item-arrow">></view>
          <text class="item-badge warning" v-if="!hasOrderItems && !hasCitySettings">未配置</text>
        </view>
      </view>

      <!-- 公会管理 -->
      <view class="list-section">
        <view class="list-item" @tap="goToGuildMembers">
          <view class="item-icon">👥</view>
          <text class="item-text">公会员工</text>
          <view class="item-arrow">></view>
        </view>
        <view class="list-item" @tap="goToGuildOrders">
          <view class="item-icon">📋</view>
          <text class="item-text">公会订单</text>
          <view class="item-arrow">></view>
        </view>
        <view class="list-item" @tap="goToGuildFinance">
          <view class="item-icon">💰</view>
          <text class="item-text">公会资产</text>
          <view class="item-arrow">></view>
          <text class="item-badge warning" v-if="!hasOrderItems && !hasCitySettings">未配置</text>
        </view>
        <view class="list-item" @tap="goToGuildSettings">
          <view class="item-icon">⚙️</view>
          <text class="item-text">公会设置</text>
          <view class="item-arrow">></view>
          <text class="item-badge warning" v-if="!hasOrderItems && !hasCitySettings">未配置</text>
        </view>
      </view>

      <!-- 申请认证 -->
      <view class="list-section">
        <view class="list-item" @tap="goToApplyCertification">
          <view class="item-icon">🎖️</view>
          <text class="item-text">达人认证</text>
          <view class="item-arrow">></view>
          <text class="item-badge" v-if="!isRealNameVerified">实名未完成</text>
        </view>
        <view class="list-item" @tap="goToApplyCertification">
          <view class="item-icon">🏢</view>
          <text class="item-text">公会认证</text>
          <view class="item-arrow">></view>
          <text class="item-badge" v-if="!isRealNameVerified">实名未完成</text>
        </view>
      </view>

      <!-- 平台服务 -->
      <view class="list-section">
        <view class="list-item" @tap="goToHelpCenter">
          <view class="item-icon">❓</view>
          <text class="item-text">帮助中心</text>
          <view class="item-arrow">></view>
        </view>
        <view class="list-item" @tap="goToAboutUs">
          <view class="item-icon">ℹ️</view>
          <text class="item-text">关于我们</text>
          <view class="item-arrow">></view>
        </view>
      </view>
    </view>
  </view>
  <Tabbar currentPath="/pages/mine/mine"></Tabbar>
</template>

<script>
import { getMemberViewHistory,getCurrentMemberInfo } from '@/api/index.js';

export default {
  data() {
    return {
      userInfo: {
        avatar: '',
        nickname: '',
        sysId: '',
        currentRoleId: '', // 当前角色ID，默认为普通用户
        roles: [], // 默认拥有普通用户角色，可包含多个角色ID
        popularity: 0,
        fansCount: 0,
        followCount: 0,
        viewCount: 0,
        guildInfo: null, // 公会信息
        applyStatus: '0', // 添加申请状态字段，默认为未申请
        realNameStatus: 0, // 实名认证状态：0-未认证，1-已认证
      },
      recondsM:0,
      hasOrderItems: false,
      hasCitySettings: false,
      notices: [],
      cities: [],
      userId: "未知ID",
    };
  },
  computed: {
    // 判断用户是否拥有达人角色权限
    isExpert() {
      return this.userInfo.roles && this.userInfo.roles.includes('2');
    },
    // 判断用户是否拥有公会角色权限
    isGuild() {
      return this.userInfo.roles && this.userInfo.roles.includes('3');
    },
    // 是否拥有达人身份（用于显示标签）
    hasExpertRole() {
      return this.userInfo.roles && this.userInfo.roles.includes('2');
    },
    // 是否拥有公会身份（用于显示标签）
    hasGuildRole() {
      return this.userInfo.roles && this.userInfo.roles.includes('3');
    },
    // 获取当前角色显示名称
    currentRoleName() {
      switch(this.userInfo.currentRoleId) {
        case '1': return '普通用户';
        case '2': return '闲时达人';
        case '3': return '闲时公会';
        default: return '游客用户';
      }
    },
    // 判断当前是否为达人角色
    isCurrentExpert() {
      return this.userInfo.currentRoleId === '2';
    },
    // 判断当前是否为公会角色
    isCurrentGuild() {
      return this.userInfo.currentRoleId === '3';
    },
    // 判断是否已完成实名认证
    isRealNameVerified() {
      return this.userInfo.realNameStatus === 1;
    }
  },
  onLoad() {
    // 更新tabbar激活状态
    uni.$emit('updateTabbar', '/pages/mine/mine');
  },
  onShow() {
    // 确保每次显示页面时tabbar状态正确
    uni.$emit('updateTabbar', '/pages/mine/mine');
    this.clearUserDataCache();
    this.fetchUserInfo();
  },
  methods: {

   async fetchUserInfo() {
      getCurrentMemberInfo({ withExtra: true, withPrivacy: true }).then(res => {
        if (res && res.data) {
          const data = res.data;
          console.log("获取用户信息", data);
          this.userInfo.avatar = data.avatar || '/static/images/default-avatar.png';
          this.userInfo.nickname = data.nickname || '未登录';
          this.userInfo.sysId = data.sysId || '';
          
          // 设置当前角色ID
          this.userInfo.currentRoleId = data.currentRoleId;
          
          // 设置用户拥有的角色（从后端返回的roles数组中提取角色ID）
          if (data.roles && Array.isArray(data.roles)) {
            this.userInfo.roles = data.roles.map(role => role.id || role.code);
          } else {
            this.userInfo.roles = [];
          }
          
          // 根据角色设置公会信息（仅当用户有公会角色时）
          if (this.userInfo.roles.includes('3')) {
            this.userInfo.guildInfo = {
              name: '闲时娱乐公会',
              id: 'G001',
              memberCount: 24,
              orderCount: 156
            };
          } else {
            this.userInfo.guildInfo = null;
          }
          
          // 保存申请状态和实名认证状态
          this.userInfo.applyStatus = data.applyStatus || '0';
          this.userInfo.realNameStatus = data.realNameStatus || 0;
          this.userInfo.popularity = data.popularity || 0;
          this.userInfo.fansCount = data.fansCount || 0;
          this.userInfo.followCount = data.followCount || 0;
          this.userInfo.viewCount = data.viewCount || 0;
          this.userInfo.myViewCount = data.myViewCount || 0;
          console.log("获取到的用户信息 - myViewCount:", this.userInfo.myViewCount);
          
          // 保存到本地存储
          uni.setStorageSync('userInfo', this.userInfo);
        }
      }).catch(error => {
        console.error('获取用户信息失败:', error);
        // API调用失败时，不从本地存储获取，避免显示上一个用户的信息
        uni.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      });
      //===========================
      try {

        const res = await getMemberViewHistory({
          pageNum: this.page,
          pageSize: this.size
        });

        console.log('浏览历史API响应:', res);

        if (res.code === 200) {
          if (res.data && res.data.records && res.data.records.length > 0) {
            console.log('获取到浏览历史记录:', res.data.records.length, '条');
            this.recondsM = res.data.records.length;
          }
        }
      } catch (e){}
     console.log(e)
    },
    goToCoinCenter(){
      // 跳转到金币中心页面
      uni.navigateTo({
        url: '/pages/coin-center/coin-center'
      });
    },

    goToFavorites(){
      // 跳转到收藏页面
      uni.showToast({
        title: "收藏功能开发中",
        icon: "none"
      });
    },

    goToSignIn(){
      // 跳转到签到页面
      uni.showToast({
        title: "签到成功",
        icon: "none"
      });
    },


    handleHomepage() {
      uni.showLoading({
        title: "加载中",
        mask: true,
      });

      setTimeout(() => {
        uni.hideLoading();
        uni.navigateTo({
          url: "/pages/personal-homepage/personal-homepage",
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面加载失败",
              icon: "none",
            });
          },
        });
      }, 100);
    },

    handleVip() {
      uni.navigateTo({
        url: "/pages/VIP-page/vip-page",
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },

    handleNotice() {
      uni.showLoading({
        title: "加载中",
        mask: true,
      });

      setTimeout(() => {
        uni.hideLoading();
        uni.navigateTo({
          url: "/pages/notice-manage/notice-manage",
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面加载失败",
              icon: "none",
            });
          },
        });
      }, 100);
    },

    // 前往接单设置页面
    goToOrderSettings() {
      uni.showLoading({
        title: "加载中",
        mask: true,
      });

      setTimeout(() => {
        uni.hideLoading();
        uni.navigateTo({
          url: "/pages/order-settings/order-settings",
          success: () => {
            // 跳转成功后的回调
            if (!this.hasOrderItems && !this.hasCitySettings) {
              setTimeout(() => {
                uni.showToast({
                  title: "请配置接单设置",
                  icon: "none",
                });
              }, 500);
            }
          },
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面加载失败",
              icon: "none",
            });
          },
        });
      }, 100);
    },
    
    // 获取设置状态的显示文本
    getSettingsStatus() {
      let status = '';
      if (this.hasOrderItems) {
        status += `已选${this.notices.length}项`;
      }
      if (this.hasCitySettings) {
        if (status) {
          status += '，';
        }
        status += this.cities[0];
      }
      return status;
    },
    
    // 前往邀约订单页面
    goToInvitationOrders() {

      
      uni.showLoading({
        title: "加载中",
        mask: true,
      });

      setTimeout(() => {
        uni.hideLoading();
        uni.navigateTo({
          url: "/pages/invitation-orders/invitation-orders",
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面加载失败",
              icon: "none",
            });
          },
        });
      }, 100);
    },

    // 导航到申请认证页面
    goToApplyCertification() {
      // 检查是否已经是达人身份
      if (this.userInfo.currentRoleId === '2') {
        uni.showToast({
          title: "您已经是闲时达人了",
          icon: "success",
          duration: 2000
        });
        return;
      }
      
      // 检查实名认证状态
      if (this.isRealNameVerified) {
        // 已完成实名认证，直接跳转到达人申请页面
        uni.navigateTo({
          url: "/pages/apply-expert/apply-expert",
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面加载失败",
              icon: "none",
            });
          },
        });
      } else {
        // 未完成实名认证，跳转到认证选择页面
        uni.navigateTo({
          url: "/pages/apply-certification/apply-certification",
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面加载失败",
              icon: "none",
            });
          },
        });
      }
    },

    // 前往帮助中心
    goToHelpCenter() {
      uni.navigateTo({
        url: "/pages/help-center/help-center",
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },
    
    // 前往关于我们页面
    goToAboutUs() {
      uni.navigateTo({
        url: "/pages/about-us/about-us",
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },

    // handleCitySettings方法已合并到goToOrderSettings方法中



    // 跳转到发布订单页面
    goToPublishOrders() {
      uni.showLoading({
        title: "加载中",
        mask: true,
      });

      // 延迟执行导航
      setTimeout(() => {
        uni.hideLoading();
        uni.navigateTo({
          url: "/pages/publish-orders/publish-orders",
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面加载失败",
              icon: "none",
            });
          },
        });
      }, 100);
    },

    // 跳转到达人订单页面
    goToExpertOrders() {
      uni.showLoading({
        title: "加载中",
        mask: true,
      });

      // 延迟执行导航
      setTimeout(() => {
        uni.hideLoading();
        
        // 导航到订单页面，但传递不同的参数
        uni.navigateTo({
          url: `/pages/export-orders/export-orders?canOperate=${this.isExpert}`,
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面加载失败",
              icon: "none",
            });
          },
        });
        

      }, 100);
    },

    // 跳转到约单订单页面
    goToAppointmentOrders() {
      uni.showLoading({
        title: "加载中",
        mask: true,
      });

      // 延迟执行导航
      setTimeout(() => {
        uni.hideLoading();
        uni.navigateTo({
          url: "/pages/publish-orders/publish-orders2",
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面加载失败",
              icon: "none",
            });
          },
        });
      }, 100);
    },
    
    formatUserId(id) {
      if (!id) return "0000000";
      
      // 如果ID已经是纯数字且长度为7，则直接返回
      if (/^\d{7}$/.test(id)) {
        return id;
      }
      
      // 如果ID是纯数字但长度不是7
      if (/^\d+$/.test(id)) {
        // 如果长度小于7，在前面补0
        if (id.length < 7) {
          return id.padStart(7, '0');
        }
        // 如果长度大于7，取后7位
        return id.substring(id.length - 7);
      }
      
      // 如果ID不是纯数字，生成一个基于ID的哈希值
      let hash = 0;
      for (let i = 0; i < id.length; i++) {
        hash = ((hash << 5) - hash) + id.charCodeAt(i);
        hash |= 0; // 转换为32位整数
      }
      
      // 取绝对值并截取后7位，不足7位前面补0
      const numStr = Math.abs(hash).toString();
      if (numStr.length < 7) {
        return numStr.padStart(7, '0');
      }
      return numStr.substring(numStr.length - 7);
    },
    
    // 公会相关方法
    
    goToGuildMembers() {
      uni.navigateTo({
        url: "/pages/guild-members/guild-members",
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },
    
    goToGuildOrders() {
      uni.navigateTo({
        url: "/pages/guild-orders/guild-orders",
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },
    
    goToGuildFinance() {
      uni.navigateTo({
        url: "/pages/guild-finance/guild-finance",
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },
    
    goToGuildSettings() {
      uni.navigateTo({
        url: "/pages/guild-settings/guild-settings",
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },

    // 角色切换器方法已移除

    goToFansPage() {
      uni.navigateTo({
        url: `/pages/fans-page/fans-page?userId=${this.userInfo.sysId}`,
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },

    goToFollowingPage() {
      uni.navigateTo({
        url: `/pages/following-page/following-page?userId=${this.userInfo.sysId}`,
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },

    goToViewHistory() {
      uni.navigateTo({
        url: `/pages/view-history/view-history?userId=${this.userInfo.sysId}`,
        fail: (err) => {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "页面加载失败",
            icon: "none",
          });
        },
      });
    },

    // 清除用户数据缓存
    clearUserDataCache() {
      // 重置用户信息
      this.userInfo = {
        avatar: '/static/images/default-avatar.png',
        nickname: '未登录',
        sysId: '',
        currentRoleId: '1',
        roles: ['1'],
        guildInfo: null,
        applyStatus: '0',
        realNameStatus: 0,
        popularity: 0,
        fansCount: 0,
        followCount: 0,
        viewCount: 0,
        myViewCount: 0
      };
      
      // 清除本地存储的用户信息
      uni.removeStorageSync('userInfo');
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 120rpx;
}

/* 用户信息区域 */
.user-section {
  padding: 40rpx 30rpx;
  background: #fff;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 32rpx;
  font-weight: 600;
  color: #000;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.role-badges {
  display: flex;
  gap: 8rpx;
}

.role-badge {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.role-badge.normal {
  background: #f5f5f5;
  color: #666;
}

.role-badge.expert {
  background: #ffd700;
  color: #000;
}

.role-badge.guild {
  background: #6366f1;
  color: #fff;
}

.role-badge.secondary {
  opacity: 0.7;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
}

.edit-btn {
  padding: 8rpx 20rpx;
  background: #ffd700;
  color: #000;
  font-size: 24rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

/* 数据统计 */
.stats-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.stat-num {
  font-size: 32rpx;
  font-weight: 700;
  color: #000;
}

.stat-text {
  font-size: 24rpx;
  color: #666;
}

/* 快捷功能图标 */
.quick-functions {
  display: flex;
  justify-content: space-around;
  padding: 15rpx 30rpx;
  background: #fff;
  margin-bottom: 15rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.function-item:active {
  transform: scale(0.95);
  background: rgba(255, 215, 0, 0.1);
}

.function-icon {
  font-size: 40rpx;
  margin-bottom: 4rpx;
}


.function-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* VIP会员卡片 */
.vip-banner {
  margin: 0 30rpx 20rpx;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.2);
}

.vip-left {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.vip-crown {
  font-size: 36rpx;
}

.vip-text {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.vip-title {
  font-size: 28rpx;
  color: #000;
  font-weight: 600;
}

.vip-subtitle {
  font-size: 22rpx;
  color: #666;
}

.vip-action {
  padding: 8rpx 20rpx;
  background: #000;
  color: #ffd700;
  font-size: 24rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

/* 快捷功能区 */
.quick-menu {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  margin: 0 30rpx 40rpx;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.menu-icon {
  font-size: 40rpx;
  color: #ffd700;
}

.menu-text {
  font-size: 24rpx;
  color: #666;
}

/* 功能列表 */
.function-list {
  background: #fff;
  padding: 0 30rpx;
}

.list-section {
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background: #fff;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
  transition: all 0.2s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background: #f8f8f8;
}

.item-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 40rpx;
  text-align: center;
}

.item-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.item-arrow {
  font-size: 24rpx;
  color: #ccc;
  margin-left: 10rpx;
}

.item-badge {
  font-size: 20rpx;
  color: #ffd700;
  background: rgba(255, 215, 0, 0.15);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 10rpx;
}

.item-badge.warning {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.15);
}

/* 动画效果 */
.user-section,
.vip-banner,
.list-section {
  transition: all 0.2s ease;
}

.vip-banner:active,
.menu-item:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.edit-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 状态样式 */
.item-badge.success {
  color: #4ade80;
  background: rgba(74, 222, 128, 0.15);
}

.item-badge.pending {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.15);
}

.item-badge.error {
  color: #f87171;
  background: rgba(248, 113, 113, 0.15);
}

/* 列表项悬停效果 */
.list-item:hover {
  background: #f8f8f8;
}

.menu-item:active .menu-icon {
  transform: scale(1.1);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
</style>
