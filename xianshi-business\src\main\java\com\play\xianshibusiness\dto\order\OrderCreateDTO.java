package com.play.xianshibusiness.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.play.xianshibusiness.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 创建订单DTO
 */
@Data
@ApiModel("创建订单DTO")
public class OrderCreateDTO {

    @ApiModelProperty(value = "活动开始时间", required = true)
    @NotNull(message = "活动开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "活动结束时间", required = true)
    @NotNull(message = "活动结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "性别要求：0-不限，1-男，2-女", required = true)
    @NotBlank(message = "性别要求不能为空")
    private String sexRequire;

    @ApiModelProperty(value = "活动地点文本", required = true)
    @NotBlank(message = "地点不能为空")
    private String addressText;

    @ApiModelProperty(value = "地点JSON（包含经纬度等信息）", required = true)
    @NotBlank(message = "地点JSON不能为空")
    private String addressJson;

    @ApiModelProperty(value = "参与人数", required = true)
    @NotNull(message = "参与人数不能为空")
    @Min(value = 1, message = "参与人数必须大于0")
    private Long personCount;

    @ApiModelProperty(value = "需求描述")
    private String description;

    @ApiModelProperty(value = "小时费用", required = true)
    @NotNull(message = "小时费用不能为空")
    @Min(value = 0, message = "小时费用不能为负数")
    private BigDecimal hourlyRate;
    
    @ApiModelProperty(value = "活动类型ID", required = true)
    @NotBlank(message = "活动类型不能为空")
    private String activityTypeId;
    
    @ApiModelProperty(value = "订单标题")
    private String title;
    
    @ApiModelProperty(value = "经度")
    private String longitude;
    
    @ApiModelProperty(value = "纬度")
    private String latitude;
    
    @ApiModelProperty(value = "详细地址")
    private String address;
    
    @ApiModelProperty(value = "订单类型：DISPATCH-派单模式，APPOINTMENT-约单模式")
    private OrderTypeEnum orderType;
    
    @ApiModelProperty(value = "目标达人ID（约单模式时使用，派单模式时为空）")
    private String targetId;
}