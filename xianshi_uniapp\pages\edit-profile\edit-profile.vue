<template>
  <view class="container">
    <view class="form-group">
      <!-- 头像修改 -->
      <view class="form-item avatar-item" @tap="chooseAvatar">
        <text class="label">头像</text>
        <view class="avatar-container">
          <image class="avatar-preview" :src="avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
          <text class="change-text">点击更换</text>
        </view>
      </view>

      <!-- 性别选择 -->
      <view class="form-item" @tap="showPicker('gender')">
        <text class="label">性别</text>
        <view class="picker-value">
          <text>{{ genderText || "请选择性别" }}</text>
          <text class="arrow">></text>
        </view>
      </view>

      <view class="form-item">
        <text class="label">年龄</text>
        <view class="input-container">
          <input type="number" v-model="age" class="uni-input" placeholder="请输入年龄"/>
        </view>
      </view>

      <view class="form-item">
        <text class="label">身高</text>
        <view class="input-container">
          <input
              type="digit"
              v-model="height"
              class="uni-input"
              placeholder="请输入身高(cm)"
              @blur="validateHeight"
          />
        </view>
      </view>
      <view class="form-item" v-if="heightError">
        <text class="error-text">{{ heightError }}</text>
      </view>

      <view class="form-item">
        <text class="label">体重</text>
        <view class="input-container">
          <input
              type="digit"
              v-model="weight"
              class="uni-input"
              placeholder="请输入体重(kg)"
              @blur="validateWeight"
          />
        </view>
      </view>
      <view class="form-item" v-if="weightError">
        <text class="error-text">{{ weightError }}</text>

      </view>

      <view class="form-item">
        <text class="label">城市</text>
        <view class="input-container">
          <text class="label" >{{city}}</text>
        </view>
        <view class="Map-icon"@tap="localMap()">
        <image :src="'/static/images/icon/locaMap.svg'"></image>
      </view>

      </view>
      <view class="form-item" v-if="weightError">
        <text class="error-text">{{ weightError }}</text>
      </view>

      <view class="form-item" @tap="showPicker('constellation')">
        <text class="label">星座</text>
        <view class="picker-value">
          <text>{{ constellation || "请选择星座" }}</text>
          <text class="arrow">></text>
        </view>
      </view>
    </view>

    <button class="save-btn" @tap="saveProfile">保存</button>

    <!-- 数字选择器 -->
    <view
        :class="['picker-container', {'show': showPicker_data && currentPickerType !== 'constellation'}]"
    >
      <view class="picker-header">
        <text class="picker-title">
          {{
            currentPickerType === "age"
                ? "选择年龄"
                : currentPickerType === "height"
                    ? "选择身高"
                    : currentPickerType === "weight"
                        ? "选择体重"
                        : ""
          }}
        </text>
        <text class="picker-confirm" @tap="onConfirm">确定</text>
      </view>
      <picker-view
          class="number-picker"
          indicator-class="picker-indicator"
          :value="pickerValue"
          @change="onPickerChange"
          v-if="showPicker_data && currentPickerType !== 'constellation'"
      >
        <picker-view-column>
          <view
              class="picker-item"
              v-for="item in pickerRange"
              :key="item"
              @tap="onPickerItemTap"
              :data-value="item"
          >
            {{ item }}{{ pickerUnit }}
          </view>
        </picker-view-column>
      </picker-view>
    </view>

    <!-- 星座选择器 -->
    <view
        :class="['picker-container', {'show': showPicker_data && currentPickerType === 'constellation'}]"
    >
      <view class="picker-header">
        <text class="picker-title">选择星座</text>
        <text class="picker-confirm" @tap="onConfirm">确定</text>
      </view>
      <picker-view
          class="constellation-picker"
          indicator-class="picker-indicator"
          :value="[constellationList.indexOf(constellation)]"
          @change="onPickerConfirm"
          v-if="showPicker_data && currentPickerType === 'constellation'"
      >
        <picker-view-column>
          <view
              class="picker-item"
              v-for="item in constellationList"
              :key="item"
              @tap="onPickerItemTap"
              :data-value="item"
          >
            {{ item }}
          </view>
        </picker-view-column>
      </picker-view>
    </view>

    <!-- 性别选择器 -->
    <view
        :class="['picker-container', {'show': showPicker_data && currentPickerType === 'gender'}]"
    >
      <view class="picker-header">
        <text class="picker-title">选择性别</text>
        <text class="picker-confirm" @tap="onConfirm">确定</text>
      </view>
      <picker-view
          class="gender-picker"
          indicator-class="picker-indicator"
          :value="[genderList.findIndex(item => item.value === gender)]"
          @change="onGenderPickerChange"
          v-if="showPicker_data && currentPickerType === 'gender'"
      >
        <picker-view-column>
          <view
              class="picker-item"
              v-for="item in genderList"
              :key="item.value"
              @tap="onGenderPickerItemTap"
              :data-value="item.value"
          >
            {{ item.label }}
          </view>
        </picker-view-column>
      </picker-view>
    </view>

    <view
        class="picker-mask"
        @tap="hidePicker"
        v-if="showPicker_data"
    ></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      avatar: "",
      gender: null,
      genderText: "",
      genderList: [
        {value: 1, label: "男"},
        {value: 2, label: "女"}
      ],
      age: "",
      height: "",
      weight: "",
      city: "",
      heightError: "",
      weightError: "",
      constellation: "",
      constellationList: [
        "白羊座",
        "金牛座",
        "双子座",
        "巨蟹座",
        "狮子座",
        "处女座",
        "天秤座",
        "天蝎座",
        "射手座",
        "摩羯座",
        "水瓶座",
        "双鱼座",
      ],
      showPicker_data: false,
      currentPickerType: "",
      pickerRange: [],
      pickerValue: [0],
      pickerUnit: "",
      tempValue: null,
    };
  },
  onLoad(options) {
    if (options) {
      this.avatar = options.avatar || "";
      this.gender = options.gender !== undefined ? Number(options.gender) : null;
      this.genderText = this.gender !== null ? (this.gender === 1 ? "男" : "女") : "";
      this.age = options.age || "";
      this.height = options.height || "";
      this.weight = options.weight || "";
      this.city = options.city || "" ;
      this.constellation = options.constellation || "";
    }
  },
  methods: {

    localMap(){

    },
    showPicker(type) {
      let range = [];
      let value = [0];
      let unit = "";

      switch (type) {
        case "age":
          range = Array.from({length: 83}, (_, i) => i + 18); // 18-100岁
          value = [
            range.indexOf(Number(this.age)) !== -1
                ? range.indexOf(Number(this.age))
                : range.indexOf(25),
          ];
          break;
        case "height":
          range = Array.from({length: 81}, (_, i) => i + 140); // 140-220cm
          value = [
            range.indexOf(Number(this.height)) !== -1
                ? range.indexOf(Number(this.height))
                : range.indexOf(170),
          ];
          break;
        case "weight":
          range = Array.from({length: 121}, (_, i) => i + 30); // 30-150kg
          value = [
            range.indexOf(Number(this.weight)) !== -1
                ? range.indexOf(Number(this.weight))
                : range.indexOf(60),
          ];
          unit = "kg";
          break;
        case "constellation":
          value = [
            this.constellation
                ? this.constellationList.indexOf(this.constellation)
                : 0,
          ];
          break;
        case "gender":
          value = [
            this.gender !== null
                ? this.genderList.findIndex(item => item.value === this.gender)
                : 0,
          ];
          break;
      }

      this.showPicker_data = true;
      this.currentPickerType = type;
      this.pickerRange = range;
      this.pickerValue = value;
      this.pickerUnit = unit;
      this.tempValue =
          type === "constellation"
              ? this.constellation || this.constellationList[0]
              : type === "gender"
                  ? this.gender !== null ? this.gender : 1
                  : type === "age"
                      ? Number(this.age) || 25
                      : type === "height"
                          ? Number(this.height) || 170
                          : Number(this.weight) || 60;
    },

    hidePicker() {
      this.showPicker_data = false;
      this.tempValue = null;
    },

    onPickerChange(e) {
      const value =
          this.currentPickerType === "constellation"
              ? this.constellationList[e.detail.value[0]]
              : this.pickerRange[e.detail.value[0]];
      this.tempValue = value;
    },

    onPickerConfirm(e) {
      this.tempValue = this.constellationList[e.detail.value[0]];
    },

    onPickerItemTap(e) {
      const value = e.currentTarget.dataset.value;
      this.tempValue = value;
      this.onConfirm();
    },

    onConfirm() {
      if (this.tempValue !== null) {
        switch (this.currentPickerType) {
          case "age":
            this.age = this.tempValue;
            break;
          case "height":
            this.height = this.tempValue;
            break;
          case "weight":
            this.weight = this.tempValue;
            break;
          case "constellation":
            this.constellation = this.tempValue;
            break;
          case "gender":
            this.gender = this.tempValue;
            this.genderText = this.gender === 1 ? "男" : "女";
            break;
        }
      }
      this.hidePicker();
    },

    validateHeight() {
      this.heightError = "";
      if (this.height) {
        const heightNum = Number(this.height);
        if (isNaN(heightNum)) {
          this.heightError = "身高必须为数字";
        } else if (heightNum < 140 || heightNum > 220) {
          this.heightError = "身高应在140cm到220cm之间";
        }
      }
    },

    validateWeight() {
      this.weightError = "";
      if (this.weight) {
        const weightNum = Number(this.weight);
        if (isNaN(weightNum)) {
          this.weightError = "体重必须为数字";
        } else if (weightNum < 30 || weightNum > 150) {
          this.weightError = "体重应在30kg到150kg之间";
        }
      }
    },

    // 性别选择器变化处理
    onGenderPickerChange(e) {
      const index = e.detail.value[0];
      this.tempValue = this.genderList[index].value;
    },

    // 性别选择器项目点击处理
    onGenderPickerItemTap(e) {
      const value = Number(e.currentTarget.dataset.value);
      this.tempValue = value;
      this.onConfirm();
    },

    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0];

          // 显示上传中提示
          uni.showLoading({
            title: '上传中...'
          });

          try {
            // 立即上传到OSS
            const ossUrl = await this.uploadAvatar(tempFilePath);
            this.avatar = ossUrl; // 使用OSS地址

            uni.hideLoading();
            uni.showToast({
              title: '头像上传成功',
              icon: 'success'
            });
          } catch (error) {
            uni.hideLoading();
            // 上传失败时仍然显示本地图片作为预览
            this.avatar = tempFilePath;
            console.error('头像上传失败:', error);
          }
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },

    // 上传头像到服务器
    async uploadAvatar(filePath) {
      try {
        const app = getApp().globalData;
        const uploadResult = await uni.uploadFile({
          url: app.baseUrl + app.commonApi.ossUploadImage,
          filePath: filePath,
          name: 'file',
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          },
          formData: {
            folder: 'member/avatars' // 指定头像存储文件夹
          }
        });

        console.log('上传结果:', uploadResult);
        const response = JSON.parse(uploadResult.data);
        if (response.code === 200) {
          return response.message; // 返回上传后的图片URL
        } else {
          throw new Error(response.message || '上传失败');
        }
      } catch (error) {
        console.error('头像上传失败:', error);
        uni.showToast({
          title: '头像上传失败',
          icon: 'none'
        });
        throw error;
      }
    },

    async saveProfile() {
      this.validateHeight();
      this.validateWeight();

      if (this.heightError || this.weightError) {
        uni.showToast({
          title: "请检查输入信息是否正确",
          icon: "none"
        });
        return;
      }

      uni.showLoading({
        title: '保存中...'
      });

      try {
        const {gender, age, height, weight, constellation} = this;
        const updateData = {
          avatar: this.avatar, // 头像已经在选择时上传到OSS
          gender,
          age: age ? parseInt(age) : null,
          height: height ? height.toString() : null,
          weight: weight ? weight.toString() : null,
          constellation
        };

        // 调用更新会员资料API
        const app = getApp().globalData;
        console.log('准备更新会员资料:', updateData);
        const res = await this.$requestHttp.put(app.commonApi.updateMemberInfo, {
          data: updateData
        });

        if (res.code === 200) {
          // 更新成功，更新本地数据
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];

          const updatedProfile = {
            avatar: this.avatar,
            gender,
            age,
            height,
            weight,
            constellation,
            introduction: (prevPage && prevPage.profileInfo && prevPage.profileInfo.introduction) ? prevPage.profileInfo.introduction : "",
          };

          if (prevPage) {
            prevPage.profileInfo = updatedProfile;
          }

          const profileData = uni.getStorageSync("profileData") || {};
          profileData.profileInfo = {
            ...profileData.profileInfo,
            ...updatedProfile,
          };
          uni.setStorageSync("profileData", profileData);

          if (prevPage && prevPage.updateCompletion) {
            prevPage.updateCompletion();
          }

          uni.hideLoading();
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });

          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(res.message || '保存失败');
        }
      } catch (error) {
        console.error('保存资料失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: error.message || '保存失败，请重试',
          icon: 'none'
        });
      }
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #f7f7f7;
  padding: 24rpx;
}

.form-group {
  background: #fff;
  border-radius: 16rpx;
  padding: 0 24rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 120rpx;
  font-size: 28rpx;
  color: #333;
}

.input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.picker-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arrow {
  color: #999;
  font-size: 24rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-top: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.picker-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  transform: translateY(100%);
  transition: transform 0.3s;
  z-index: 1001;
}

.picker-container.show {
  transform: translateY(0);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.picker-confirm {
  font-size: 32rpx;
  color: #ff8c00;
}

.number-picker,
.constellation-picker {
  height: 400rpx;
}

.picker-item {
  /* line-height: 88rpx; */
  text-align: center;
  font-size: 32rpx;
}

.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.input-container {
  flex: 1;
}

.uni-input {
  width: 100%;
  height: 60rpx;
  font-size: 28rpx;
}

.error-text {
  color: #ff4d4f;
  font-size: 24rpx;
  padding-left: 120rpx;
  width: 100%;
}

/* 头像相关样式 */
.avatar-item {
  align-items: center;
}

.avatar-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.avatar-preview {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  border: 2rpx solid #f0f0f0;
}

.change-text {
  font-size: 28rpx;
  color: #ff8c00;
}

/* 性别选择器样式 */
.gender-picker {
  height: 400rpx;
}
.Map-icon {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.Map-icon image {
  width: 100%;
  height: 100%;
}
</style>

