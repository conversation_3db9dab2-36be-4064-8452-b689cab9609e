package com.play.xianshibusiness.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**
 * WebSocket聊天消息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage implements Serializable {
    
    /**
     * 消息ID
     */
    private String id;
    
    /**
     * 消息类型：CHAT(聊天消息)、SYSTEM(系统消息)、CONNECT(连接消息)、PING(心跳)
     */
    private String type;
    
    /**
     * 发送者ID
     */
    private String fromUserId;
    
    /**
     * 发送者昵称
     */
    private String fromUserName;
    
    /**
     * 接收者ID：如果是单聊信息，则是用户ID；如果是群聊，则是群组ID
     */
    private String toUserId;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 扩展字段，用于传输额外信息
     */
    private Object extras;
    
    /**
     * 发送时间
     */
    private Date timestamp;
    
    /**
     * 消息状态：SENT(已发送)、DELIVERED(已送达)、READ(已读)
     */
    private String status;
} 