/* eslint-disable */
// 黏土风格样式定义
// 特点：柔和圆润的边角、微妙的阴影、柔和的颜色、轻微的纹理

// Clay风格设计系统
import './clay-style.scss';

// 设置CSS变量
const setClayVariables = () => {
  document.documentElement.style.setProperty('--primary-color', '#F8D010');
  document.documentElement.style.setProperty('--primary-light', '#F8E068');
  document.documentElement.style.setProperty('--dark-color', '#303030');
  document.documentElement.style.setProperty('--gray-color', '#909090');
  document.documentElement.style.setProperty('--light-gray', '#f8f8f8');
  document.documentElement.style.setProperty('--text-primary', '#303030');
  document.documentElement.style.setProperty('--text-secondary', '#909090');
  document.documentElement.style.setProperty('--border-radius', '12px');
  document.documentElement.style.setProperty('--box-shadow', '0 8px 16px rgba(0, 0, 0, 0.1)');
  document.documentElement.style.setProperty('--transition', 'all 0.3s ease');
};

// 添加Clay风格卡片效果
const addClayEffects = () => {
  // 为所有带clay-card类的元素添加悬停效果
  const clayCards = document.querySelectorAll('.clay-card');
  clayCards.forEach(card => {
    card.addEventListener('mouseenter', (e) => {
      card.style.transform = 'translateY(-5px) scale(1.02)';
      card.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.15)';
    });
    
    card.addEventListener('mouseleave', (e) => {
      card.style.transform = '';
      card.style.boxShadow = '';
    });
  });
  
  // 为所有带clay-button类的按钮添加点击效果
  const clayButtons = document.querySelectorAll('.clay-button');
  clayButtons.forEach(button => {
    button.addEventListener('mousedown', (e) => {
      button.style.transform = 'scale(0.95)';
    });
    
    button.addEventListener('mouseup', (e) => {
      button.style.transform = 'scale(1)';
    });
    
    button.addEventListener('mouseleave', (e) => {
      button.style.transform = 'scale(1)';
    });
  });
  
  // 为所有输入框添加焦点效果
  const inputs = document.querySelectorAll('input, textarea, .el-input__inner');
  inputs.forEach(input => {
    input.addEventListener('focus', (e) => {
      const parent = input.closest('.el-input') || input.parentNode;
      if (parent) {
        parent.style.transform = 'scale(1.02)';
        parent.style.boxShadow = '0 0 0 3px rgba(248, 208, 16, 0.1)';
      }
    });
    
    input.addEventListener('blur', (e) => {
      const parent = input.closest('.el-input') || input.parentNode;
      if (parent) {
        parent.style.transform = '';
        parent.style.boxShadow = '';
      }
    });
  });
};

// 初始化Clay风格
const initClayStyle = () => {
  setClayVariables();
  
  // 在DOM加载完成后添加交互效果
  document.addEventListener('DOMContentLoaded', () => {
    addClayEffects();
    
    // 监听DOM变化，为新添加的元素也添加效果
    const observer = new MutationObserver((mutations) => {
      addClayEffects();
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  });
  
  // 添加自定义指令
  if (window.Vue) {
    // clay-hover指令 - 添加悬停效果
    window.Vue.directive('clay-hover', {
      bind(el, binding) {
        el.addEventListener('mouseenter', () => {
          const scale = binding.value && binding.value.scale ? binding.value.scale : 1.05;
          const y = binding.value && binding.value.y ? binding.value.y : -5;
          el.style.transform = `translateY(${y}px) scale(${scale})`;
          el.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.15)';
        });
        
        el.addEventListener('mouseleave', () => {
          el.style.transform = '';
          el.style.boxShadow = '';
        });
      }
    });
    
    // clay-press指令 - 添加按压效果
    window.Vue.directive('clay-press', {
      bind(el) {
        el.addEventListener('mousedown', () => {
          el.style.transform = 'scale(0.95)';
        });
        
        el.addEventListener('mouseup', () => {
          el.style.transform = 'scale(1)';
        });
        
        el.addEventListener('mouseleave', () => {
          el.style.transform = 'scale(1)';
        });
      }
    });
  }
};

export default initClayStyle; 