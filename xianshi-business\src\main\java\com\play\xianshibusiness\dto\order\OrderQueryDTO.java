package com.play.xianshibusiness.dto.order;

import com.play.xianshibusiness.dto.PageQueryParam;
import com.play.xianshibusiness.enums.OrderStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单查询参数DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "订单查询参数DTO")
public class OrderQueryDTO extends PageQueryParam {
    
    @ApiModelProperty(value = "订单ID")
    private String orderId;
    
    @ApiModelProperty(value = "用户ID")
    private String memberId;
    
    @ApiModelProperty(value = "用户昵称(模糊查询)")
    private String memberName;
    
    @ApiModelProperty(value = "订单状态")
    private OrderStatusEnum status;
    
    @ApiModelProperty(value = "陪玩类型ID")
    private String typeId;
    
    @ApiModelProperty(value = "创建开始时间 yyyy-MM-dd HH:mm:ss")
    private String createTimeStart;
    
    @ApiModelProperty(value = "创建结束时间 yyyy-MM-dd HH:mm:ss")
    private String createTimeEnd;
    
    @ApiModelProperty(value = "排序字段")
    private String orderBy;
    
    @ApiModelProperty(value = "排序方式：asc-升序，desc-降序")
    private String orderDirection = "desc";
    
    @ApiModelProperty(value = "只查询我的报名")
    private Boolean myEnrollment;
    
    @ApiModelProperty(value = "状态列表")
    private List<String> statusList;
    
    @ApiModelProperty(value = "活动类型ID")
    private String activityTypeId;
    
    @ApiModelProperty(value = "开始时间开始")
    private String startTimeBegin;
    
    @ApiModelProperty(value = "开始时间结束")
    private String startTimeEnd;
    
    /**
     * 重载setStatus方法，接受Integer参数
     * 用于兼容Jenkins服务器上的代码
     * 
     * @param statusCode 状态码
     */
    public void setStatus(Integer statusCode) {
        if (statusCode != null) {
            switch (statusCode) {
                case 0:
                    this.status = OrderStatusEnum.DRAFT;
                    break;
                case 1:
                    this.status = OrderStatusEnum.AUDITING;
                    break;
                case 2:
                    this.status = OrderStatusEnum.PUBLISHED;
                    break;
                case 3:
                    this.status = OrderStatusEnum.WAITING_SELECT;
                    break;
                case 4:
                    this.status = OrderStatusEnum.PROCESSING;
                    break;
                case 5:
                    this.status = OrderStatusEnum.COMPLETED;
                    break;
                case 6:
                    this.status = OrderStatusEnum.CANCELED;
                    break;
                default:
                    this.status = null;
            }
        } else {
            this.status = null;
        }
    }
} 