package com.play.xianshibusiness.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.List;

/**
 * 后台创建会员DTO
 */
@Data
@ApiModel("后台创建会员DTO")
public class AdminCreateMemberDTO {
    
    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String tel;
    
    @ApiModelProperty(value = "昵称")
    private String nickname;
    
    @ApiModelProperty(value = "头像")
    private String avatar;
    
    @ApiModelProperty(value = "性别：1-男，2-女")
    private Integer gender;
    
    @ApiModelProperty(value = "年龄")
    private Integer age;
    
    @ApiModelProperty(value = "星座")
    private String constellation;
    
    @ApiModelProperty(value = "身高")
    private String height;
    
    @ApiModelProperty(value = "简介")
    private String context;
    
    @ApiModelProperty(value = "金币余额")
    private BigDecimal gold;
    
    @ApiModelProperty(value = "当前角色ID")
    private String currentRoleId;
    
    @ApiModelProperty(value = "角色ID列表")
    private List<String> roleIds;
} 