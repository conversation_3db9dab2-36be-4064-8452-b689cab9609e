package com.play.xianshiadmin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.dto.member.MemberSimpleVO;
import com.play.xianshibusiness.dto.recharge.AdminRechargeDTO;
import com.play.xianshibusiness.dto.recharge.RechargePackageVO;
import com.play.xianshibusiness.dto.recharge.RechargeQueryDTO;
import com.play.xianshibusiness.dto.recharge.RechargeRecordVO;
import com.play.xianshibusiness.pojo.CRechargePackage;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.CMemberRechargeService;
import com.play.xianshibusiness.service.CMemberService;
import com.play.xianshibusiness.service.CRechargePackageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 管理端充值控制器
 */
@RestController
@RequestMapping("/admin/recharge")
@Api(tags = "管理端充值API")
public class RechargeAdminController {

    @Resource
    private CRechargePackageService rechargePackageService;
    
    @Resource
    private CMemberRechargeService memberRechargeService;
    
    @Resource
    private CMemberService memberService;

    @GetMapping("/packages")
    @ApiOperation(value = "获取所有充值套餐")
    @RequireAdmin
    public Result<List<RechargePackageVO>> getAllPackages() {
        return ResultUtils.success(rechargePackageService.getAllPackages());
    }
    
    @GetMapping("/package/{id}")
    @ApiOperation(value = "获取充值套餐详情")
    @RequireAdmin
    public Result<RechargePackageVO> getPackageDetail(
            @ApiParam(value = "套餐ID", required = true) @PathVariable String id) {
        return ResultUtils.success(rechargePackageService.getPackageDetail(id));
    }
    
    @PostMapping("/package")
    @ApiOperation(value = "创建充值套餐")
    @RequireAdmin
    public Result<String> createPackage(@RequestBody @Valid CRechargePackage rechargePackage) {
        return ResultUtils.success(rechargePackageService.createPackage(rechargePackage));
    }
    
    @PutMapping("/package/{id}")
    @ApiOperation(value = "更新充值套餐")
    @RequireAdmin
    public Result<Boolean> updatePackage(
            @ApiParam(value = "套餐ID", required = true) @PathVariable String id,
            @RequestBody @Valid CRechargePackage rechargePackage) {
        return ResultUtils.success(rechargePackageService.updatePackage(id, rechargePackage));
    }
    
    @DeleteMapping("/package/{id}")
    @ApiOperation(value = "删除充值套餐")
    @RequireAdmin
    public Result<Boolean> deletePackage(
            @ApiParam(value = "套餐ID", required = true) @PathVariable String id) {
        return ResultUtils.success(rechargePackageService.deletePackage(id));
    }
    
    @GetMapping("/package/page")
    @ApiOperation(value = "分页查询充值套餐")
    @RequireAdmin
    public Result<Page<RechargePackageVO>> pagePackages(
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页条数", defaultValue = "10") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "套餐名称") @RequestParam(required = false) String name) {
        return ResultUtils.success(rechargePackageService.pagePackages(pageNum, pageSize, name));
    }
    
    @GetMapping("/records")
    @ApiOperation(value = "获取充值记录")
    @RequireAdmin
    public Result<Page<RechargeRecordVO>> getRechargeRecords(RechargeQueryDTO dto) {
        return ResultUtils.success(memberRechargeService.adminPageRechargeRecords(dto));
    }
    
    @GetMapping("/record/{id}")
    @ApiOperation(value = "获取充值记录详情")
    @RequireAdmin
    public Result<RechargeRecordVO> getRechargeDetail(
            @ApiParam(value = "充值记录ID", required = true) @PathVariable String id) {
        return ResultUtils.success(memberRechargeService.adminGetRechargeDetail(id));
    }
    
    @PostMapping("/member")
    @ApiOperation(value = "管理员给会员充值")
    @RequireAdmin
    public Result<Boolean> adminRecharge(@RequestBody @Valid AdminRechargeDTO dto) {
        return ResultUtils.success(memberRechargeService.adminRecharge(dto));
    }
    
    @GetMapping("/member/search")
    @ApiOperation(value = "搜索会员")
    @RequireAdmin
    public Result<List<MemberSimpleVO>> searchMembers(
            @ApiParam(value = "搜索关键词", required = true) @RequestParam String keyword) {
        return ResultUtils.success(memberService.searchMembers(keyword));
    }
} 