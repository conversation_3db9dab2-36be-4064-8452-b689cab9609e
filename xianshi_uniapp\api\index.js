import qs from "qs";
import { isEmpty, encryptAES, genSign } from "../utils/func";

export const url_all = {
	// "DEV": "http://121.43.188.218:9019",
	// "PORD": "http://121.43.188.218:9019"
	"DEV": "http://127.0.0.1:9019",
	"PORD": "http://127.0.0.1:9019"
}

// const BaseUrl = url_all['DEV#1'];

let requestHttp = new Proxy({
	BaseUrlKey: 'DEV',  // 默认使用的 key
	get BaseUrl() {
		return url_all[this.BaseUrlKey];
	},
	params: {
		header: {
			'Content-Type': 'application/json'
		}
	},
	commonFun: function () {
		try {
			if (uni.getStorageSync('token')) {
				this.params.header = {
					'Content-Type': 'application/json',
					// token: uni.getStorageSync('token'),
					Authorization: uni.getStorageSync('token')
				}
			}
			return new Promise((resolve, reject) => {
				console.log("=====>", this.params);
				uni.request({
					...this.params,
					success: (response) => {
						console.log("response", response);
						if (response.statusCode == 200) {
							if (response.data.status == -1) {
								uni.showToast({
									title: response.data.msg || '请求失败，请重试',
									icon: 'none',
									duration: 2000
								})
								reject(response.data)
							}
							console.log(`%c[=====RESPONSE_start=====]`, "color: red; font-bold: 700; font-size: 18px;",);
							console.log(this.params.url)
							console.log(response.data)
							console.log(`%c[=====RESPONSE_end=====]`, "color: red; font-bold: 700; font-size: 18px;");
							return resolve(response.data);
						}
						/**
						 * 其他状态码则做弹出框处理
						 * 400: 错误码，提示错误信息
						 * 500: 错误码，服务器报错
						 */
						if (response.statusCode == 401 || response.statusCode == 403) {
							uni.removeStorageSync('token');
							// 触发登录弹窗
							uni.$emit('trigger-login')
							// 提示
							let errmsg = response.data.errmsg || '登录已过期，请重新登录'
							uni.showToast({
								title: errmsg,
								icon: 'none',
								duration: 2000,
							})
							return reject(response)
						}

						uni.showToast({
							title: response.data.msg || '网络请求失败，请重试',
							icon: 'none',
							duration: 2000
						})
						reject(response.data)
					},
					fail: (error) => {
						console.log("dDDD", error);
						reject(error);
					}
				})
			})
		} catch (error) {
			console.log("error", error);
		}
	},
}, {
	// 拦截对象属性的读取
	get(target, key) {
		console.log("target: ", target, "key: ", key);
		const config = {
			get: "GET",
			post: "POST",
			delete: "DELETE",
			put: "PUT"
		}
		if (config[key]) {
			return (url, data = null) => {
				console.log("data", data);
				let c_params = '';
				const { custom_params = {}, BaseUri = "", ...rest } = data;
				console.log("target", target, custom_params);
				if (!isEmpty(custom_params)) c_params = `?${qs.stringify(custom_params)}`

				target['params'] = {
					...target['params'],
					method: config[key],
					url: `${BaseUri ? BaseUri : target.BaseUrl}${url}${c_params}`,
					...rest
				}
				console.log("target2", target);
				console.log(process.env.NODE_ENV, process.env.UNI_PLATFORM, 'target[params]', target['params']);
				return target.commonFun();
			};
		}
		return target[key] || 'error';
	},
	// 拦截对象设置属性
	set(target, key, value) {
		if (key === 'header') {
			return target[key] = value;
		} else {
			return target[key];
		}
	},
	// 拦截delete
	deleteProperty(target, key) {
		if (key.indexOf('_') > -1) {
			delete target[key];
			return true;
		} else {
			return target[key]
		}
	},
	//把原始对象里面的key值全部拿出来再过滤
	ownKeys(target) {
		return Object.keys(target).filter(vl => vl != 'vl') //过滤vl这个属性
	}
});

/**
 * 获取达人列表
 * @param {Object} params {pageNum, pageSize}
 * @returns Promise
 */
export function getTalentList(params = {}) {
	return requestHttp.get('/app/member/talent/list', {
		data: params
	});
}

/**
 * 获取当前登录会员信息（含基础信息、粉丝、关注等）
 * @param {Object} params {withExtra, withPrivacy}
 * @returns Promise
 */
export function getCurrentMemberInfo(params = { withExtra: true, withPrivacy: true }) {
	return requestHttp.get('/app/member', {
		data: params
	});
}

/**
 * 获取指定会员信息（含基础信息、粉丝、关注等）
 * @param {String} memberId 会员ID
 * @param {Object} params {withExtra, withPrivacy}
 * @returns Promise
 */
export function getMemberInfo(memberId, params = { withExtra: true, withPrivacy: false }) {
	return requestHttp.get(`/app/profile/${memberId}`, {
		data: params
	});
}

/**
 * 关注会员
 * @param {String} targetId 目标会员ID
 * @returns Promise
 */
export function followMember(targetId) {
	return requestHttp.post(`/app/follow/${targetId}`, {});
}

/**
 * 取消关注会员
 * @param {String} targetId 目标会员ID
 * @returns Promise
 */
export function unfollowMember(targetId) {
	return requestHttp.delete(`/app/follow/${targetId}`, {});
}

/**
 * 检查是否关注
 * @param {String} targetId 目标会员ID
 * @returns Promise
 */
export function isFollowing(targetId) {
	return requestHttp.get(`/app/follow/check/${targetId}`, {});
}

/**
 * 获取粉丝列表
 * @param {Object} params {page, size}
 * @returns Promise
 */
export function getFansList(params = { page: 1, size: 10 }) {
	return requestHttp.get('/app/follow/fans', {
		data: params
	});
}

/**
 * 获取关注列表
 * @param {Object} params {page, size}
 * @returns Promise
 */
export function getFollowingList(params = { page: 1, size: 10 }) {
	return requestHttp.get('/app/follow/list', {
		data: params
	});
}

/**
 * 获取系统消息列表
 * @returns Promise
 */
export function getSysMessages() {
	return requestHttp.get('/app/iSysMessage/message', {});
}

/**
 * 获取订单消息列表
 * @returns Promise
 */
export function getOrderMessages() {
	return requestHttp.get('/app/iSysMessage/order/message', {});
}

/**
 * 获取订单消息详情
 * @param {String} messageId 消息ID
 * @returns Promise
 */
export function getOrderMessageDetail(messageId) {
	return requestHttp.get(`/app/iSysMessage/detail/${messageId}`, {});
}

/**
 * 获取系统消息详情（与订单消息详情接口一致）
 * @param {String} messageId 消息ID
 * @returns Promise
 */
export function getSysMessageDetail(messageId) {
	return requestHttp.get(`/app/iSysMessage/detail/${messageId}`, {});
}

/**
 * 获取会员浏览历史列表
 * @param {Object} params {pageNum, pageSize}
 * @returns Promise
 */
export function getMemberViewHistory(params = { pageNum: 1, pageSize: 10 }) {
	return requestHttp.get('/app/history/list', params);
}

/**
 * 删除单条浏览历史记录
 * @param {String} historyId 浏览历史记录ID
 * @returns Promise
 */
export function deleteViewHistory(historyId) {
	return requestHttp.post(`/app/history/delete/${historyId}`, {});
}

/**
 * 清空所有浏览历史记录
 * @returns Promise
 */
export function clearAllViewHistory() {
	return requestHttp.post('/app/history/clear', {});
}

/**
 * 查看会员资料并记录浏览历史
 * @param {String} memberId 被查看的会员ID
 * @returns Promise
 */
export function viewMemberProfile(memberId) {
	return requestHttp.get(`/app/profile/view/${memberId}`, {});
}

/**
 * 发送聊天消息
 * @param {Object} messageData {targetId, content}
 * @returns Promise
 */
export function sendChatMessage(messageData) {
	return requestHttp.post('/app/iMemberChat/send', {
		data: messageData
	});
}

/**
 * 获取聊天历史记录
 * @param {String} targetId 聊天对象ID
 * @returns Promise
 */
export function getChatHistory(targetId) {
	return requestHttp.get(`/app/iMemberChat/history/${targetId}`, {});
}

/**
 * 获取聊天列表
 * @returns Promise
 */
export function getChatList() {
	return requestHttp.get('/app/iMemberChat/list', {});
}



/**
 * 删除聊天记录
 * @param {String} targetId 聊天对象ID
 * @returns Promise
 */
export function deleteChatHistory(targetId) {
	return requestHttp.delete(`/app/iMemberChat/delete/${targetId}`, {});
}

/**
 * 检查用户是否在线
 * @param {String} userId 用户ID
 * @returns Promise
 */
export function checkUserOnline(userId) {
	return requestHttp.get(`/app/iMemberChat/online/${userId}`, {});
}

/**
 * 获取聊天未读消息数
 * @returns Promise
 */
export function getChatUnreadCount() {
	return requestHttp.get('/app/iMemberChat/unread/count', {});
}

/**
 * 在线用户数量
 * @returns Promise
 */
export function getOnlineUserCount() {
	return requestHttp.get('/app/iMemberChat/online/count', {});
}

// ==================== 订单相关API ====================

/**
 * 获取订单列表（分页）
 * @param {Object} params {pageNum, pageSize, status, type}
 * @returns Promise
 */
export function getOrderList(params = {}) {
	return requestHttp.get('/api/order/page', {
		data: params
	});
}

/**
 * 获取订单详情
 * @param {String} orderId 订单ID
 * @returns Promise
 */
export function getOrderDetail(orderId) {
	return requestHttp.get(`/api/order/detail/${orderId}`, {});
}

/**
 * 创建订单
 * @param {Object} orderData 订单数据
 * @returns Promise
 */
export function createOrder(orderData) {
	return requestHttp.post('/api/order/create', {
		data: orderData
	});
}

/**
 * 提交订单审核
 * @param {String} orderId 订单ID
 * @returns Promise
 */
export function submitOrderForAudit(orderId) {
	return requestHttp.post(`/api/order/submit/${orderId}`, {});
}

/**
 * 选择达人
 * @param {String} orderId 订单ID
 * @param {Array} memberIds 达人ID数组
 * @returns Promise
 */
export function selectTalents(orderId, memberIds) {
	return requestHttp.post(`/api/order/select-talents/${orderId}`, {
		data: memberIds
	});
}

/**
 * 根据用户角色获取订单列表
 * @param {Object} params {pageNum, pageSize, status}
 * @returns Promise
 */
export function getMyOrders(params = {}) {
	return requestHttp.get('/api/order/my-orders', {
		data: params
	});
}

/**
 * 取消订单
 * @param {String} orderId 订单ID
 * @param {String} reason 取消原因
 * @returns Promise
 */
export function cancelOrder(orderId, reason = '') {
	return requestHttp.post(`/api/order/cancel/${orderId}`, {
		data: { reason }
	});
}

/**
 * 达人报名订单
 * @param {String} orderId 订单ID
 * @returns Promise
 */
export function enrollOrder(orderId) {
	return requestHttp.post(`/api/order/enroll-detail/enroll/${orderId}`, {});
}

/**
 * 达人取消报名
 * @param {String} orderId 订单ID
 * @returns Promise
 */
export function cancelEnrollment(orderId) {
	return requestHttp.post(`/api/order/enroll-detail/cancel/${orderId}`, {});
}

/**
 * 更新子订单状态
 * @param {Object} updateData {id, status, ...}
 * @returns Promise
 */
export function updateOrderEnrollDetailStatus(updateData) {
	return requestHttp.post('/api/order/enroll-detail/update', {
		data: updateData
	});
}

/**
 * 服务完成
 * @param {String} enrollDetailId 子订单ID
 * @returns Promise
 */
export function completeService(enrollDetailId) {
	return requestHttp.post(`/api/order/enroll-detail/complete-service/${enrollDetailId}`, {});
}

/**
 * 提交订单评价
 * @param {Object} commentData 评价数据
 * @returns Promise
 */
export function submitOrderComment(commentData) {
	return requestHttp.post('/api/order/enroll-detail/comment', {
		data: commentData
	});
}

/**
 * 获取订单评价列表
 * @param {String} orderId 订单ID
 * @returns Promise
 */
export function getOrderComments(orderId) {
	return requestHttp.get(`/api/order/comments/${orderId}`, {});
}

/**
 * 获取订单报名详情列表
 * @param {String} orderId 订单ID
 * @returns Promise
 */
export function getOrderEnrollDetails(orderId) {
	return requestHttp.get(`/api/order/enroll-details/${orderId}`, {});
}

/**
 * 获取子订单详情
 * @param {String} enrollDetailId 子订单ID
 * @returns Promise
 */
export function getOrderEnrollDetail(enrollDetailId) {
	return requestHttp.get(`/api/order/enroll-detail/${enrollDetailId}`, {});
}

/**
 * 获取用户订单统计数据
 * @returns Promise
 */
export function getOrderStatistics() {
	return requestHttp.get('/api/order/statistics', {});
}

// ==================== 金币相关API ====================

/**
 * 获取用户金币余额
 * @returns Promise
 */
export function getGoldBalance() {
	return requestHttp.get('/api/member/gold/balance', {});
}

/**
 * 获取金币交易记录（分页）
 * @param {Object} params {pageNum, pageSize, type, startTime, endTime}
 * @returns Promise
 */
export function getGoldRecords(params = {}) {
	return requestHttp.get('/api/member/gold/records', {
		data: params
	});
}

/**
 * 获取金币记录详情
 * @param {String} recordId 记录ID
 * @returns Promise
 */
export function getGoldRecordDetail(recordId) {
	return requestHttp.get(`/api/member/gold/record/${recordId}`, {});
}

// ==================== 订单类型相关API ====================

/**
 * 获取所有订单类型
 * @returns Promise
 */
export function getAllOrderTypes() {
	return requestHttp.get('/api/activity-type/list', {});
}

/**
 * 获取订单类型详情
 * @param {String} typeId 类型ID
 * @returns Promise
 */
export function getOrderTypeDetail(typeId) {
	return requestHttp.get(`/api/activity-type/detail/${typeId}`, {});
}

/**
 * 获取父级订单类型
 * @returns Promise
 */
export function getParentOrderTypes() {
	return requestHttp.get('/api/activity-type/parents', {});
}

/**
 * 根据父级ID获取子级订单类型
 * @param {String} parentId 父级ID
 * @returns Promise
 */
export function getChildOrderTypes(parentId) {
	return requestHttp.get(`/api/activity-type/children/${parentId}`, {});
}

export default requestHttp;