<template>
  <div :class="{'sidebar-container': true, 'is-collapsed': !sidebar.opened}">
    <!-- 顶部Logo区域 -->
    <div class="logo-container">
      <router-link to="/" class="logo-link">
        <h1 class="logo-text" :class="{'full-logo': sidebar.opened, 'mini-logo': !sidebar.opened}">
          {{ logoText }}
        </h1>
      </router-link>
      <div class="drawer-toggle" @click="toggleDrawer" v-if="sidebar.opened">
        <i class="el-icon-setting"></i>
      </div>
    </div>
    
    <!-- 菜单区域 -->
    <div class="menu-container">
      <div class="menu-wrapper">
        <el-menu
          :default-active="activeMenu"
          :collapse="!sidebar.opened"
          :background-color="variables.menuBg"
          :text-color="variables.menuText"
          :active-text-color="variables.menuActiveText"
          :unique-opened="true"
          :collapse-transition="false"
          mode="vertical"
          class="custom-menu"
        >
          <!-- 渲染所有菜单项 -->
          <menu-item-component 
            v-for="route in routes" 
            :key="route.path" 
            :item="route" 
            :base-path="route.path" 
          />
        </el-menu>
      </div>
    </div>
    
    <!-- 菜单抽屉设置 -->
    <el-drawer
      title="菜单设置"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="300px"
      class="menu-drawer"
      :modal="true"
      :append-to-body="true"
      :with-header="true"
      :before-close="closeDrawer"
    >
      <div class="drawer-content">
        <div class="drawer-section">
          <h3 class="drawer-title">布局设置</h3>
          <div class="drawer-item">
            <span>固定顶部</span>
            <el-switch v-model="fixedHeader" @change="toggleFixedHeader"></el-switch>
          </div>
          <div class="drawer-item">
            <span>显示标签栏</span>
            <el-switch v-model="showTags" @change="toggleShowTags"></el-switch>
          </div>
          <div class="drawer-item">
            <span>收起侧边栏</span>
            <el-switch v-model="sidebarCollapsed" @change="toggleCollapse"></el-switch>
          </div>
        </div>
        
        <div class="drawer-section">
          <h3 class="drawer-title">主题设置</h3>
          <div class="drawer-item">
            <span>主题色</span>
            <div class="color-picker">
              <div 
                v-for="(color, index) in themeColors" 
                :key="index" 
                :style="{ backgroundColor: color }" 
                class="color-item"
                :class="{ 'active': currentTheme === color }"
                @click="setTheme(color)">
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import path from 'path';

// 菜单项组件
const MenuItemComponent = {
  name: 'MenuItemComponent',
  props: {
    item: {
      type: Object,
      required: true
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  render(h) {
    // 如果路由被隐藏，则不渲染
    if (this.item.hidden) {
      return null;
    }
    
    // 判断是否为简单路由（没有子路由或只有一个子路由）
    if (!this.item.children || (this.item.children.length === 1 && !this.item.alwaysShow)) {
      const route = this.item.children ? this.item.children[0] : this.item;
      
      // 如果没有元数据，则不渲染
      if (!route.meta) {
        return null;
      }
      
      // 计算实际路径
      const fullPath = this.resolvePath(route.path);
      
      // 渲染单个菜单项
      return h('router-link', {
        props: {
          to: fullPath
        },
        class: 'menu-link'
      }, [
        h('el-menu-item', {
          props: {
            index: fullPath
          },
          class: 'custom-menu-item'
        }, [
          h('div', { class: 'menu-item-content' }, [
            route.meta.icon ? h('i', { class: route.meta.icon }) : null,
            h('div', { class: 'menu-title' }, route.meta.title)
          ])
        ])
      ]);
    } else {
      // 渲染子菜单
      const title = h('template', { slot: 'title' }, [
        h('div', { class: 'menu-item-content' }, [
          this.item.meta && this.item.meta.icon ? h('i', { class: this.item.meta.icon }) : null,
          h('div', { class: 'menu-title' }, this.item.meta ? this.item.meta.title : '')
        ])
      ]);
      
      // 过滤出可见的子路由
      const children = this.item.children.filter(child => !child.hidden).map(child => {
        // 检查是否是子菜单项
        if (!child.children) {
          // 计算子菜单项的完整路径
          const childFullPath = this.resolvePath(child.path);
          console.log('子菜单路径:', childFullPath);
          
          // 子菜单项 - 添加子菜单特有的类名
          return h('router-link', {
            props: {
              to: childFullPath
            },
            class: 'menu-link',
            key: child.path
          }, [
            h('el-menu-item', {
              props: {
                index: childFullPath
              },
              class: 'custom-menu-item submenu-item' // 添加submenu-item类
            }, [
              h('div', { class: 'menu-item-content submenu-content' }, [ // 添加submenu-content类
                child.meta && child.meta.icon ? h('i', { class: child.meta.icon }) : null,
                h('div', { class: 'menu-title' }, child.meta ? child.meta.title : '')
              ])
            ])
          ]);
        } else {
          // 递归渲染子菜单
          return h(MenuItemComponent, {
            props: {
              item: child,
              basePath: this.resolvePath(child.path)
            },
            key: child.path
          });
        }
      });
      
      return h('el-submenu', {
        props: {
          index: this.resolvePath(this.item.path)
        },
        class: 'custom-submenu'
      }, [title, ...children]);
    }
  },
  methods: {
    resolvePath(routePath) {
      // 如果是完整路径，直接返回
      if (routePath.startsWith('/')) {
        return routePath;
      }
      
      // 如果基础路径是外部链接，直接返回
      if (/^(https?:|mailto:|tel:)/.test(this.basePath)) {
        return this.basePath;
      }
      
      // 处理相对路径
      let resolvedPath;
      if (this.basePath.endsWith('/')) {
        resolvedPath = this.basePath + routePath;
      } else {
        // 使用path.join而不是path.resolve，避免路径解析问题
        resolvedPath = this.basePath + '/' + routePath;
      }
      
      // 移除多余的斜杠
      resolvedPath = resolvedPath.replace(/\/+/g, '/');
      
      console.log('基础路径:', this.basePath, '路由路径:', routePath, '解析后:', resolvedPath);
      
      return resolvedPath;
    }
  }
};

export default {
  name: 'Sidebar',
  components: {
    MenuItemComponent
  },
  data() {
    return {
      drawerVisible: false,
      fixedHeader: true,
      showTags: true,
      sidebarCollapsed: false,
      currentTheme: '#F8D010',
      themeColors: ['#F8D010', '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
    };
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'permission_routes'
    ]),
    routes() {
      return this.permission_routes;
    },
    activeMenu() {
      const { meta, path } = this.$route;
      // 如果设置了activeMenu属性则优先使用
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    variables() {
      return {
        menuBg: '#ffffff',
        menuText: '#303030',
        menuActiveText: this.currentTheme
      };
    },
    logoText() {
      return this.sidebar.opened ? '闲时有伴' : '闲时';
    }
  },
  mounted() {
    // 初始化设置
    this.loadSettings();
    
    // 监听打开设置抽屉的事件
    this.$root.$on('open-settings-drawer', () => {
      this.drawerVisible = true;
    });
  },
  beforeDestroy() {
    // 移除事件监听
    this.$root.$off('open-settings-drawer');
  },
  methods: {
    toggleDrawer() {
      this.drawerVisible = !this.drawerVisible;
    },
    
    toggleCollapse(value) {
      // 折叠菜单栏
      this.sidebarCollapsed = value !== undefined ? value : !this.sidebarCollapsed;
      this.$store.dispatch('app/toggleSideBar', !this.sidebarCollapsed);
      this.saveSettings();
    },
    
    toggleFixedHeader() {
      // 应用固定顶部设置
      const navbar = document.querySelector('.navbar');
      const appMain = document.querySelector('.app-main');
      
      if (navbar && appMain) {
        if (this.fixedHeader) {
          navbar.classList.add('fixed-header');
          appMain.style.paddingTop = '85px'; // 导航栏高度 + 上下margin
        } else {
          navbar.classList.remove('fixed-header');
          appMain.style.paddingTop = '0';
        }
      }
      
      this.saveSettings();
    },
    
    toggleShowTags() {
      // 应用显示标签栏设置
      const historyTags = document.querySelector('.history-tags');
      if (historyTags) {
        historyTags.style.display = this.showTags ? 'flex' : 'none';
      }
      this.saveSettings();
    },
    
    setTheme(color) {
      this.currentTheme = color;
      // 应用主题色
      document.documentElement.style.setProperty('--primary-color', color);
      document.documentElement.style.setProperty('--primary-light', this.lightenColor(color, 20));
      
      this.saveSettings();
    },
    
    lightenColor(hex, percent) {
      // 简单的颜色变亮函数
      const num = parseInt(hex.replace('#', ''), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) + amt;
      const G = (num >> 8 & 0x00FF) + amt;
      const B = (num & 0x0000FF) + amt;
      return '#' + (
        0x1000000 +
        (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
        (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
        (B < 255 ? (B < 1 ? 0 : B) : 255)
      ).toString(16).slice(1);
    },
    
    saveSettings() {
      // 保存设置到localStorage
      const settings = {
        sidebarCollapsed: this.sidebarCollapsed,
        fixedHeader: this.fixedHeader,
        showTags: this.showTags,
        currentTheme: this.currentTheme
      };
      localStorage.setItem('layoutSettings', JSON.stringify(settings));
    },
    
    loadSettings() {
      // 从localStorage加载设置
      const settingsStr = localStorage.getItem('layoutSettings');
      if (settingsStr) {
        try {
          const settings = JSON.parse(settingsStr);
          this.sidebarCollapsed = settings.sidebarCollapsed !== undefined ? settings.sidebarCollapsed : false;
          this.fixedHeader = settings.fixedHeader !== undefined ? settings.fixedHeader : true;
          this.showTags = settings.showTags !== undefined ? settings.showTags : true;
          this.currentTheme = settings.currentTheme || '#F8D010';
          
          // 应用设置
          this.toggleCollapse(this.sidebarCollapsed);
          this.toggleFixedHeader();
          this.toggleShowTags();
          this.setTheme(this.currentTheme);
        } catch (e) {
          console.error('加载设置失败', e);
        }
      }
    },
    
    closeDrawer() {
      this.drawerVisible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.sidebar-container {
  transition: width 0.3s;
  width: 240px;
  height: 100%;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  
  .logo-container {
    height: 70px;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
    margin: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .logo-link {
      height: 100%;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      text-decoration: none;
      
      .logo-text {
        color: #F8D010 !important; // 强制使用黄色
        margin: 0;
        white-space: nowrap;
      }
      
      .full-logo {
        font-size: 22px;
        font-weight: 600;
      }
      
      .mini-logo {
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .drawer-toggle {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 10px;
      transition: all 0.3s;
      
      &:hover {
        background-color: rgba(248, 208, 16, 0.1);
        transform: rotate(30deg);
      }
      
      i {
        font-size: 20px;
        color: #909090;
      }
    }
  }
  
  .menu-container {
    height: calc(100% - 70px);
    overflow: visible;
  }
  
  .menu-wrapper {
    height: 100%;
  }
  
  .custom-menu {
    border: none;
    height: 100%;
    width: 100% !important;
    padding: 16px;
    
    // 添加子菜单样式
    ::v-deep .el-submenu .el-menu {
      background-color: transparent;
      padding-left: 10px;
    }
    
    ::v-deep .el-submenu .el-menu-item {
      min-width: auto;
      height: 40px;
      line-height: 40px;
      padding-left: 45px !important;
      margin: 4px 0;
      border-radius: 8px;
      font-size: 14px;
      background-color: rgba(248, 248, 248, 0.5);
      
      &:hover {
        background-color: rgba(248, 208, 16, 0.08) !important;
      }
      
      &.is-active {
        background-color: rgba(248, 208, 16, 0.6) !important;
        box-shadow: 0 1px 4px rgba(248, 208, 16, 0.2) !important;
        transform: translateX(3px) !important;
      }
      
      i {
        font-size: 16px;
        margin-right: 8px;
      }
      
      .menu-title {
        font-size: 14px;
        font-weight: normal;
      }
    }
  }
  
  &.is-collapsed {
    width: 64px;
    
    .logo-container {
      margin: 0 !important;
      justify-content: center !important;
      
      .logo-link {
        justify-content: center !important;
        width: 100% !important;
        padding: 0 !important;
      }
      
      .drawer-toggle {
        display: none !important;
      }
    }
    
    .custom-menu {
      padding: 16px 0;
    }
    
    // 添加收缩状态下隐藏文字的样式
    .menu-title {
      display: none !important;
    }
    
    // 调整图标在收缩状态下的样式
    .menu-item-content {
      justify-content: center;
      width: 100%;
      
      i {
        margin: 0 auto;
        font-size: 20px;
      }
    }
    
    // 调整菜单项在收缩状态下的样式
    .custom-menu-item {
      padding: 0 !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    // 确保所有图标居中显示
    .el-menu-item, .el-submenu__title {
      padding: 0 !important;
      text-align: center;
    }
    
    // 特别处理中间三个图标
    .el-menu-item i, .el-submenu__title i {
      margin: 0 auto !important;
      padding: 0 !important;
    }
  }
}

.menu-drawer {
  ::v-deep .el-drawer__header {
    padding: 20px;
    margin-bottom: 0;
    color: #303030;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  ::v-deep .el-drawer__body {
    padding: 0;
  }
  
  .drawer-content {
    padding: 20px;
    
    .drawer-section {
      margin-bottom: 30px;
      
      .drawer-title {
        font-size: 16px;
        color: #303030;
        margin: 0 0 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      }
      
      .drawer-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        span {
          color: #606266;
          font-size: 14px;
        }
        
        .color-picker {
          display: flex;
          flex-wrap: wrap;
          
          .color-item {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            margin-right: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            
            &:hover {
              transform: scale(1.1);
            }
            
            &.active::after {
              content: '✓';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: white;
              font-size: 14px;
              text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
            }
          }
        }
      }
    }
  }
}
</style>

<style>
/* 全局样式，确保没有下划线 */
.sidebar-container .el-menu,
.sidebar-container .el-menu-item,
.sidebar-container .el-submenu__title,
.sidebar-container .el-menu--inline {
  border: none !important;
  border-bottom: none !important;
}

.sidebar-container span,
.sidebar-container .el-menu span,
.sidebar-container .el-submenu__title span,
.sidebar-container .el-menu-item span {
  border-bottom: none !important;
  text-decoration: none !important;
  border: none !important;
  box-shadow: none !important;
}

.menu-title {
  border-bottom: none !important;
  text-decoration: none !important;
  border: none !important;
  box-shadow: none !important;
}

.menu-link {
  text-decoration: none !important;
}

.custom-menu-item {
  height: 50px !important;
  line-height: 50px !important;
  padding: 0 20px !important;
  margin: 6px 0 !important;
  border-radius: 12px !important;
  transition: all 0.3s !important;
  border: none !important;
}

.custom-menu-item:hover {
  background-color: rgba(248, 208, 16, 0.1) !important;
  transform: translateX(5px) !important;
}

.custom-menu-item.is-active {
  background-color: var(--primary-color, #F8D010) !important;
  color: #303030 !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 8px rgba(248, 208, 16, 0.3) !important;
  transform: translateX(5px) scale(1.03) !important;
  width: calc(100% - 10px) !important; /* 确保不会溢出 */
  max-width: 100% !important;
  overflow: hidden !important;
}

/* 调整菜单标题布局 */
.el-submenu__title {
  position: relative !important; /* 恢复相对定位 */
  padding-right: 20px !important; /* 减少右侧padding */
}

/* 调整箭头位置到红框位置 */
.el-submenu__icon-arrow {
  position: absolute !important; /* 使用绝对定位 */
  right: 70px !important; /* 距离右边缘70px */
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  transition: transform 0.3s !important; /* 添加过渡效果 */
}

/* 确保箭头旋转正常 */
.el-submenu.is-opened > .el-submenu__title .el-submenu__icon-arrow {
  transform: translateY(-50%) rotateZ(180deg) !important; /* 保持Y轴位置并旋转 */
}

/* 自定义菜单样式 */
.custom-submenu .el-submenu__title {
  height: 50px !important;
  line-height: 50px !important;
  padding: 0 20px !important;
  margin: 6px 0 !important;
  border-radius: 12px !important;
  border: none !important;
}

.custom-submenu .el-submenu__title:hover {
  background-color: rgba(248, 208, 16, 0.1) !important;
  transform: translateX(5px) !important;
}

.custom-submenu.is-active .el-submenu__title {
  color: var(--primary-color, #F8D010) !important;
}

.custom-submenu.is-active .el-submenu__title i {
  color: var(--primary-color, #F8D010) !important;
}

/* 收缩状态下的菜单项样式 */
.sidebar-container.is-collapsed .custom-menu-item {
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 50px !important;
}

.sidebar-container.is-collapsed .menu-item-content {
  width: 100% !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
}

.sidebar-container.is-collapsed .menu-item-content i {
  margin: 0 !important;
  font-size: 20px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* 确保所有图标在收缩状态下居中显示 */
.sidebar-container.is-collapsed .el-menu-item,
.sidebar-container.is-collapsed .el-submenu__title {
  padding: 0 !important;
  text-align: center !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.sidebar-container.is-collapsed .el-menu-item i,
.sidebar-container.is-collapsed .el-submenu__title i {
  margin: 0 !important;
  padding: 0 !important;
  position: absolute !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
}

.menu-item-content {
  display: flex;
  align-items: center;
  
  i {
    font-size: 18px;
    margin-right: 8px;
  }
  
  .menu-title {
    font-size: 14px;
    font-weight: normal;
  }
}

.custom-menu-item.is-active .menu-item-content i,
.custom-menu-item.is-active .menu-item-content .menu-title {
  color: #303030 !important;
}

/* 确保侧边栏折叠时不显示文字 */
.el-menu--collapse .menu-title {
  display: none !important;
}

.el-menu--collapse .menu-item-content {
  justify-content: center !important;
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

.el-menu--collapse .menu-item-content i {
  margin: 0 !important;
  font-size: 20px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
}

.el-menu--collapse .custom-menu-item {
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 50px !important;
}

.el-menu--collapse .el-menu-item,
.el-menu--collapse .el-submenu__title {
  padding: 0 !important;
  text-align: center !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.el-menu--collapse .el-menu-item i,
.el-menu--collapse .el-submenu__title i {
  margin: 0 !important;
  padding: 0 !important;
  position: absolute !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* 覆盖子菜单项样式 */
.submenu-item {
  height: 40px !important; /* 比父菜单项小一些 */
  line-height: 40px !important;
  padding-left: 45px !important; /* 增加左侧缩进 */
  margin: 4px 0 !important; /* 减小上下间距 */
  border-radius: 8px !important; /* 圆角更小 */
  font-size: 14px !important; /* 字体更小 */
  background-color: rgba(248, 248, 248, 0.5) !important; /* 浅灰色背景 */
  width: calc(100% - 10px) !important; /* 确保不会溢出 */
  max-width: 100% !important;
  overflow: hidden !important;
  transform: none !important; /* 防止transform导致溢出 */
}

.submenu-item:hover {
  background-color: rgba(248, 208, 16, 0.08) !important; /* 更浅的悬停背景色 */
  transform: translateX(3px) !important; /* 减小悬停时的位移 */
}

.submenu-item.is-active {
  transform: translateX(3px) !important; /* 减小选中时的位移和缩放 */
  width: calc(100% - 10px) !important;
  max-width: 100% !important;
  overflow: hidden !important;
}

.submenu-content i {
  font-size: 16px !important; /* 图标更小 */
  margin-right: 8px !important;
}

.submenu-content .menu-title {
  font-size: 14px !important;
  font-weight: normal !important;
}
</style> 