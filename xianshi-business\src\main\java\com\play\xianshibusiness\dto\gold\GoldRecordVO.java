package com.play.xianshibusiness.dto.gold;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 金币记录VO
 */
@Data
@ApiModel(value = "金币记录VO")
public class GoldRecordVO {
    
    @ApiModelProperty(value = "记录ID")
    private String id;
    
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    
    @ApiModelProperty(value = "会员昵称")
    private String memberName;
    
    @ApiModelProperty(value = "会员头像")
    private String memberAvatar;
    
    @ApiModelProperty(value = "操作类型：1-充值，2-消费，3-系统调整-增加，4-系统调整-减少，5-任务奖励")
    private Integer operationType;
    
    @ApiModelProperty(value = "操作类型描述")
    private String operationTypeDesc;
    
    @ApiModelProperty(value = "变更金币数量")
    private Integer amount;
    
    @ApiModelProperty(value = "变更前金币余额")
    private Integer beforeBalance;
    
    @ApiModelProperty(value = "变更后金币余额")
    private Integer afterBalance;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "关联订单ID")
    private String orderId;
    
    @ApiModelProperty(value = "关联订单编号")
    private String orderNo;
    
    @ApiModelProperty(value = "操作人ID")
    private String operatorId;
    
    @ApiModelProperty(value = "操作人名称")
    private String operatorName;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
} 