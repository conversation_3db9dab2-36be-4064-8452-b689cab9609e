<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>会员充值</span>
      </div>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="会员" prop="memberId">
          <el-input
            v-model="memberKeyword"
            placeholder="请输入会员昵称或手机号搜索"
            @input="searchMember"
            clearable
          >
            <template slot="append">
              <el-button icon="el-icon-search" @click="searchMember"></el-button>
            </template>
          </el-input>
          
          <div v-if="memberList.length > 0" class="member-list">
            <div
              v-for="item in memberList"
              :key="item.id"
              class="member-item"
              @click="selectMember(item)"
            >
              <el-avatar :size="30" :src="item.avatar"></el-avatar>
              <div class="member-info">
                <div class="member-name">{{ item.nickname }}</div>
                <div class="member-phone">{{ item.phone }}</div>
              </div>
              <div class="member-gold">
                <i class="el-icon-coin"></i>
                {{ item.goldBalance }}
              </div>
            </div>
          </div>
          
          <div v-if="selectedMember" class="selected-member">
            <el-tag type="success" closable @close="clearSelectedMember">
              <el-avatar :size="20" :src="selectedMember.avatar"></el-avatar>
              {{ selectedMember.nickname }} ({{ selectedMember.phone }})
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="充值金额" prop="amount">
          <el-input-number v-model="form.amount" :min="1" :precision="0" @change="calculateGold"></el-input-number>
          <span class="form-tip">单位：元</span>
        </el-form-item>
        
        <el-form-item label="充值金币" prop="goldAmount">
          <el-input-number v-model="form.goldAmount" :min="1" :precision="0"></el-input-number>
          <span class="form-tip">单位：金币</span>
        </el-form-item>
        
        <el-form-item label="充值备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入充值备注"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">确认充值</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { adminRecharge, searchMembers } from '@/api/recharge';
import { debounce } from 'lodash';

export default {
  name: 'RechargeMember',
  data() {
    return {
      // 表单参数
      form: {
        memberId: '',
        amount: 100,
        goldAmount: 100,
        remark: ''
      },
      // 表单校验
      rules: {
        memberId: [
          { required: true, message: '请选择会员', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入充值金额', trigger: 'blur' }
        ],
        goldAmount: [
          { required: true, message: '请输入充值金币', trigger: 'blur' }
        ]
      },
      // 会员搜索关键词
      memberKeyword: '',
      // 会员列表
      memberList: [],
      // 已选择的会员
      selectedMember: null,
      // 提交状态
      loading: false
    };
  },
  created() {
    // 使用debounce防抖，避免频繁请求
    this.debouncedSearch = debounce(this.fetchMembers, 500);
  },
  methods: {
    // 搜索会员
    searchMember() {
      if (this.memberKeyword.length >= 2) {
        this.debouncedSearch();
      } else {
        this.memberList = [];
      }
    },
    // 获取会员列表
    fetchMembers() {
      searchMembers(this.memberKeyword).then(response => {
        this.memberList = response.data;
      });
    },
    // 选择会员
    selectMember(member) {
      this.selectedMember = member;
      this.form.memberId = member.id;
      this.memberList = [];
      this.memberKeyword = '';
    },
    // 清除已选择的会员
    clearSelectedMember() {
      this.selectedMember = null;
      this.form.memberId = '';
    },
    // 根据充值金额计算金币
    calculateGold() {
      // 默认1元=1金币，可根据实际业务逻辑调整
      this.form.goldAmount = this.form.amount;
    },
    // 提交表单
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.loading = true;
          adminRecharge(this.form).then(response => {
            this.$message.success('充值成功');
            this.resetForm();
            this.loading = false;
          }).catch(() => {
            this.loading = false;
          });
        }
      });
    },
    // 重置表单
    resetForm() {
      this.form = {
        memberId: '',
        amount: 100,
        goldAmount: 100,
        remark: ''
      };
      this.selectedMember = null;
      this.memberKeyword = '';
      this.memberList = [];
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    }
  }
};
</script>

<style scoped>
.member-list {
  position: absolute;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
  margin-top: 5px;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #ebeef5;
}

.member-item:hover {
  background-color: #f5f7fa;
}

.member-info {
  flex: 1;
  margin-left: 10px;
}

.member-name {
  font-weight: bold;
}

.member-phone {
  font-size: 12px;
  color: #909399;
}

.member-gold {
  color: #e6a23c;
  font-weight: bold;
}

.selected-member {
  margin-top: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}
</style> 