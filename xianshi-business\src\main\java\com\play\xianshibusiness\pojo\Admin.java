package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 系统管理员实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_admin")
@ApiModel("系统管理员")
public class Admin extends BaseObjPo {
    
    @ApiModelProperty("用户名")
    private String username;
    
    @ApiModelProperty("密码")
    private String password;
    
    @ApiModelProperty("昵称")
    private String nickname;
    
    @ApiModelProperty("手机号")
    private String phone;
    
    @ApiModelProperty("邮箱")
    private String email;
    
    @ApiModelProperty("头像")
    private String avatar;
    
    @ApiModelProperty("角色（逗号分隔的角色编码）")
    private String roles;
    
    @ApiModelProperty("最后登录时间")
    private LocalDateTime lastLoginTime;
    
    @ApiModelProperty("备注")
    private String remark;
} 