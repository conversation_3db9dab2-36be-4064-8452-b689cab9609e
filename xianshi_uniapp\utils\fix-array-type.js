// 修复存储格式问题

// 初始化函数
export const fixStorageFormat = (app, forceReset = false) => {
  console.log('开始修复数据格式问题...');
  
  // 如果强制重置，直接初始化示例订单
  if (forceReset) {
    console.log('强制重置订单数据');
    uni.setStorageSync('publishedOrders', '[]');
    app.globalData.publishedOrders = [];
    app.initSampleOrders();
    return true;
  }
  
  // 清除存储中可能存在的非数组数据
  let publishedOrders = uni.getStorageSync('publishedOrders');
  
  // 如果不是字符串或者为空，直接初始化为空数组
  if (typeof publishedOrders !== 'string' || !publishedOrders) {
    console.log('订单数据不是字符串或为空，初始化为空数组');
    uni.setStorageSync('publishedOrders', '[]');
    app.globalData.publishedOrders = [];
    return false;
  }
  
  // 尝试解析数据
  try {
    const parsedOrders = JSON.parse(publishedOrders);
    
    // 确保是数组
    if (!Array.isArray(parsedOrders)) {
      console.log('解析后的订单不是数组，初始化为空数组');
      uni.setStorageSync('publishedOrders', '[]');
      app.globalData.publishedOrders = [];
      return false;
    } else {
      console.log('订单数据格式正确');
      app.globalData.publishedOrders = parsedOrders;
      return true;
    }
  } catch (e) {
    console.error('解析订单数据失败', e);
    uni.setStorageSync('publishedOrders', '[]');
    app.globalData.publishedOrders = [];
    return false;
  }
}