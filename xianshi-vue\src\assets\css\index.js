/* eslint-disable */
import applyClayStyle from './clay-style';

// 创建一个CSS样式对象并应用到DOM
const styles = {
  // 项目主题色变量
  ':root': {
    '--primary-color': '#F8D010',      // 主题色 
    '--primary-light': '#F8E068',      // 亮色
    '--gray-color': '#909090',         // 灰色
    '--dark-color': '#303030',         // 深色
    '--white-color': '#ffffff',        // 白色
    '--background-color': '#f5f5f5',   // 背景色
    '--success-color': '#67C23A',      // 成功色
    '--warning-color': '#E6A23C',      // 警告色
    '--danger-color': '#F56C6C',       // 危险色
    '--info-color': '#909090',         // 信息色
    '--border-color': '#EBEEF5',       // 边框色
    '--text-primary': '#303030',       // 主要文字
    '--text-regular': '#606266',       // 常规文字
    '--text-secondary': '#909090',     // 次要文字
  },
  
  // 全局样式
  'body': {
    'height': '100%',
    'margin': '0',
    'padding': '0',
    '-moz-osx-font-smoothing': 'grayscale',
    '-webkit-font-smoothing': 'antialiased',
    'text-rendering': 'optimizeLegibility',
    'font-family': '"Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif',
    'background-color': 'var(--background-color)'
  },
  
  'html': {
    'height': '100%',
    'box-sizing': 'border-box'
  },
  
  // 修改Element UI主题色
  '.el-button--primary': {
    'background-color': 'var(--primary-color)',
    'border-color': 'var(--primary-color)',
    'color': 'var(--dark-color)'
  },
  
  '.el-button--primary:hover, .el-button--primary:focus': {
    'background-color': 'var(--primary-light)',
    'border-color': 'var(--primary-light)',
    'color': 'var(--dark-color)'
  },
  
  '.el-radio__input.is-checked .el-radio__inner': {
    'border-color': 'var(--primary-color)',
    'background': 'var(--primary-color)'
  },
  
  '.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner': {
    'background-color': 'var(--primary-color)',
    'border-color': 'var(--primary-color)'
  },
  
  '.el-switch.is-checked .el-switch__core': {
    'border-color': 'var(--primary-color)',
    'background-color': 'var(--primary-color)'
  },
  
  '.el-menu--horizontal>.el-menu-item.is-active': {
    'border-bottom': '2px solid var(--primary-color)',
    'color': 'var(--primary-color)'
  },
  
  '.el-pagination.is-background .el-pager li:not(.disabled).active': {
    'background-color': 'var(--primary-color)',
    'color': 'var(--dark-color)'
  },
  
  // 布局相关
  '.app-container': {
    'padding': '20px'
  },
  
  '.flex-center': {
    'display': 'flex',
    'align-items': 'center',
    'justify-content': 'center'
  },
  
  '.flex-between': {
    'display': 'flex',
    'align-items': 'center',
    'justify-content': 'space-between'
  },
  
  // 间距类
  '.mt-10': {
    'margin-top': '10px'
  },
  
  '.mb-10': {
    'margin-bottom': '10px'
  },
  
  '.ml-10': {
    'margin-left': '10px'
  },
  
  '.mr-10': {
    'margin-right': '10px'
  },
  
  '.p-20': {
    'padding': '20px'
  },
  
  // 定义卡片样式
  '.custom-card': {
    'border-radius': '8px',
    'box-shadow': '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
    'background-color': 'var(--white-color)',
    'overflow': 'hidden',
    'transition': 'all 0.3s'
  },
  
  '.custom-card:hover': {
    'box-shadow': '0 4px 16px 0 rgba(0, 0, 0, 0.2)'
  },
  
  // 定义按钮样式  
  '.custom-button': {
    'background-color': 'var(--primary-color)',
    'color': 'var(--dark-color)',
    'border': 'none',
    'border-radius': '4px',
    'padding': '10px 20px',
    'cursor': 'pointer',
    'transition': 'all 0.3s'
  },
  
  '.custom-button:hover': {
    'background-color': 'var(--primary-light)'
  },
  
  '.custom-button.danger': {
    'background-color': 'var(--danger-color)'
  },
  
  '.custom-button.danger:hover': {
    'opacity': '0.9'
  },
  
  // 侧边栏菜单样式修复
  '.sidebar-container .el-menu': {
    'border': 'none !important',
    'border-bottom': 'none !important'
  },
  
  '.sidebar-container .el-menu-item, .sidebar-container .el-submenu__title, .sidebar-container .el-menu--inline': {
    'border': 'none !important',
    'border-bottom': 'none !important',
    'box-shadow': 'none !important'
  },
  
  '.sidebar-container .el-menu li, .sidebar-container .el-menu-item, .sidebar-container .el-submenu .el-menu-item': {
    'border': 'none !important',
    'border-bottom': 'none !important',
    'box-shadow': 'none !important'
  },
  
  // 特别处理带有data-v属性的span元素
  '.sidebar-container span[slot="title"], .sidebar-container .el-submenu__title span, .sidebar-container .el-menu-item span': {
    'border-bottom': 'none !important',
    'text-decoration': 'none !important',
    'border': 'none !important',
    'box-shadow': 'none !important'
  },
  
  '.sidebar-container span[data-v-10973580]': {
    'border-bottom': 'none !important',
    'text-decoration': 'none !important',
    'border': 'none !important',
    'box-shadow': 'none !important'
  },
  
  // 处理所有带有data-v-属性的span元素
  '.sidebar-container [class*="data-v-"] span': {
    'border-bottom': 'none !important',
    'text-decoration': 'none !important',
    'border': 'none !important',
    'box-shadow': 'none !important'
  },
  
  // 处理伪元素
  '.sidebar-container .el-menu-item::after, .sidebar-container .el-menu-item::before': {
    'display': 'none !important'
  },
  
  '.sidebar-container .el-submenu__title::after, .sidebar-container .el-submenu__title::before': {
    'display': 'none !important'
  }
};

// 将样式对象应用到DOM
function applyStyles() {
  const styleEl = document.createElement('style');
  styleEl.type = 'text/css';
  let cssText = '';
  
  for (const selector in styles) {
    cssText += `${selector} {\n`;
    for (const property in styles[selector]) {
      cssText += `  ${property}: ${styles[selector][property]};\n`;
    }
    cssText += '}\n\n';
  }
  
  styleEl.textContent = cssText;
  document.head.appendChild(styleEl);
  
  // 应用黏土风格
  applyClayStyle();
  
  // 添加DOM加载完成后的处理
  window.addEventListener('DOMContentLoaded', () => {
    fixMenuItemStyles();
  });
  
  // 延迟处理动态加载的菜单
  setTimeout(() => {
    fixMenuItemStyles();
  }, 1000);
}

// 修复菜单项样式
function fixMenuItemStyles() {
  // 查找所有菜单项中的span元素并移除下划线
  const menuSpans = document.querySelectorAll('.sidebar-container span[slot="title"], .sidebar-container .el-menu span');
  if (menuSpans) {
    menuSpans.forEach(span => {
      span.style.borderBottom = 'none';
      span.style.textDecoration = 'none';
      span.style.border = 'none';
      span.style.boxShadow = 'none';
    });
  }
  
  // 特别处理带有data-v属性的span
  const dataVSpans = document.querySelectorAll('.sidebar-container span[data-v-10973580]');
  if (dataVSpans) {
    dataVSpans.forEach(span => {
      span.style.borderBottom = 'none';
      span.style.textDecoration = 'none';
      span.style.border = 'none';
      span.style.boxShadow = 'none';
    });
  }
  
  // 处理所有可能的data-v-*属性
  const allDataVSpans = document.querySelectorAll('.sidebar-container [data-v-]');
  if (allDataVSpans) {
    allDataVSpans.forEach(el => {
      const spans = el.querySelectorAll('span');
      spans.forEach(span => {
        span.style.borderBottom = 'none';
        span.style.textDecoration = 'none';
        span.style.border = 'none';
        span.style.boxShadow = 'none';
      });
    });
  }
}

// 导出函数
export default applyStyles;