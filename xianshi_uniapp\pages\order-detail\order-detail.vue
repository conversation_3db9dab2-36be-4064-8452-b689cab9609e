<template>
	<view class="container">
		<!-- 加载中显示 -->
		<view class="loading-container" v-if="loading">
			<view class="loading-spinner"></view>
			<text>加载中...</text>
		</view>

		<!-- 错误信息显示 -->
		<view class="error-container" v-if="loadError">
			<view class="error-icon">!</view>
			<text class="error-text">{{ errorMsg || "加载失败" }}</text>
			<button class="retry-button" @tap="refreshOrder">重试</button>
		</view>

		<!-- 订单信息卡片 -->
		<view class="order-card" v-if="!loading && order && !loadError">
			<!-- 发布人信息 -->
			<view class="publisher-section">
				<view class="publisher-avatar">
					<image :src="order.memberAvatar || '/images/default-avatar.png'" mode="aspectFill"></image>
				</view>
				<view class="publisher-info">
					<view class="publisher-name">{{ order.memberName || "未知用户" }}</view>
					<view class="publish-time">{{ order.createTime || "" }}</view>
				</view>
				<view class="order-status-badge" v-if="order.statusDesc">{{ order.statusDesc }}</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="info-section">
				<view class="info-title">基本信息</view>
				<view class="info-content">
					<view class="info-row order-id-row">
						<text class="label">订单编号：</text>
						<text class="value order-id-value">{{ order.orderId }}</text>
						<text class="copy-btn" @tap="copyOrderId">复制</text>
					</view>
					<view class="info-row">
						<text class="label">发布时间：</text>
						<text class="value">{{
							order.createTime ||
							"未知"
						}}</text>
					</view>
					<view class="info-row">
						<text class="label">服务项目：</text>
						<view class="service-display">
							<text class="service-type">{{
								order.typeName
							}}</text>
						</view>
					</view>
					<view class="info-row">
						<text class="label">活动时间：</text>
						<view class="value">
							<text>
								{{ formatActivityTime(order.startTime, order.endTime) }}</text>
						</view>
					</view>
					<view class="info-row">
						<text class="label">活动时长：</text>
						<text class="value">{{ formatDuration(order.duration) }}</text>
					</view>
					<view class="info-row">
						<text class="label">性别要求：</text>
						<text class="value">{{ formatGender(order.sexRequire) }}</text>
					</view>
					<view class="info-row">
						<text class="label">参与人数：</text>
						<text class="value">{{ order.personCount || 1 }}人</text>
					</view>
				</view>
			</view>

			<!-- 需求描述信息 -->
			<view class="info-section description-section">
				<view class="info-title">需求描述</view>
				<view class="description-content">
					<text class="description-text">{{ order.description || "暂无需求描述" }}</text>
				</view>
			</view>

			<!-- 活动地点信息单独一个框 -->
			<view class="info-section location-section">
				<view class="info-title">活动地点</view>
				<view class="location-content">
					<view class="location-info">
						<view class="location-full">
							{{ order.address || "未指定地点" }}
						</view>
					</view>
					<view class="location-nav-icon">
						<text class="nav-icon">🧭</text>
					</view>
				</view>
			</view>

			<!-- 费用信息 -->
			<view class="info-section">
				<view class="info-title">费用信息</view>
				<view class="fee-content">
					<view class="fee-row">
						<text>活动时长</text>
						<text>{{ formatDuration(order.duration) }}</text>
					</view>
					<view class="fee-row">
						<text>参与人数</text>
						<text>{{ order.personCount || 1 }}人</text>
					</view>
					<view class="fee-row total">
						<text>小时收费</text>
						<view class="fee-detail">
							<text class="total-amount">¥{{ order.hourlyRate || 0 }}</text>
							<text class="unit">/小时</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 状态和日期 -->
			<!-- <view class="order-header">
				<view class="order-status">
					<text class="status-text">{{ order.statusText }}</text>
				</view>
				<view class="order-date">{{ order.createdAtFormatted }}</view>
			</view> -->

			<!-- 选中达人信息 -->
			<view class="selected-expert-info"
				v-if="order.selectedExpertId && (order.status === 'expertSelected' || order.status === 'orderStarted' || order.status === 'departureConfirmed' || order.status === 'arrivalConfirmed')">
				<view class="info-header">服务达人信息</view>
				<view class="expert-container">
					<image class="expert-avatar" :src="order?.selectedExpertAvatar || '/images/default-avatar.png'"></image>
					<view class="expert-details">
						<view class="expert-name">{{
							order.selectedExpertName || "未知达人"
						}}</view>
						<view class="expert-id">达人ID: {{ order.selectedExpertId }}</view>
						<view class="expert-status" v-if="order.expertStatus">
							<text>状态: {{ order.expertStatusText || "未知状态" }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 服务信息 -->
			<view class="service-info">
				<!-- 报名达人列表 - 所有角色可见 -->
				<view class="info-section applicants-section"
					v-if="order && order.status !== 'draft' && order.status !== 'pending'">
					<view class="info-title">
						<text class="title-text">报名达人</text>
						<text class="applicants-count" v-if="order && order.enrollList && Array.isArray(order.enrollList) && order.enrollList.length > 0">({{ order.enrollList.length }}人报名)</text>
						<text class="applicants-count" v-else>(0人报名)</text>
						<text class="view-all" @tap="viewAppliedExperts" v-if="order && order.enrollList && Array.isArray(order.enrollList) && order.enrollList.length > 0">查看全部</text>
					</view>
					
					<!-- 暂无达人报名提示 -->
					<view class="no-experts-tip" v-if="order && (!order.enrollList || !Array.isArray(order.enrollList) || order.enrollList.length === 0)">
						<image class="empty-icon" src="/static/images/empty-data.png" mode="aspectFit"></image>
						<text class="empty-text">暂无达人报名，请耐心等待...</text>
						<text class="empty-subtext" v-if="isCreator">系统将持续为您推荐合适的达人</text>
					</view>
					
					<!-- 报名达人头像展示区域 -->
					<view class="experts-avatars-section" v-if="order && order.enrollList && Array.isArray(order.enrollList) && order.enrollList.length > 0">
						<view class="avatars-container">
							<!-- 显示前10个达人头像 -->
							<view class="avatar-item" v-for="(item, index) in order.enrollList.slice(0, 10)" :key="item.enrollId || index">
								<image class="expert-avatar-small" :src="item?.talentAvatar || '/static/images/default-avatar.png'" mode="aspectFill"
									@tap="viewExpertProfile" :data-expert-id="item.talentId"></image>
								<view class="selected-badge" v-if="item.isSelected">✓</view>
							</view>
							<!-- 如果超过10个，显示+N -->
							<view class="more-avatar-item" v-if="order.enrollList.length > 10" @tap="viewAppliedExperts">
								<text class="more-count">+{{ order.enrollList.length - 10 }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 达人提示信息 - 只对已报名的达人显示 -->
				<view class="info-section expert-notice" v-if="userRole === '闲时达人' && isApplied">
					<view class="notice-content">
						<text class="notice-status">{{
							isSelected ? "您已被选中" : "您已报名此订单"
						}}</text>
						<text class="notice-tip" v-if="!isSelected">请等待用户选择</text>
						<text class="notice-tip" v-if="isSelected && order.status === 'expertSelected'">等待用户确认开始订单</text>
						<text class="notice-tip" v-if="isSelected && order.status === 'orderStarted'">订单进行中</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部按钮区域 -->
		<view class="bottom-buttons" >
			<!-- 订单创建者的按钮 -->
			 <!-- {{ isCreator }} -->
			 <!-- {{ userRole === '闲时达人' }} -->
			<block v-if="isCreator">
				<!-- 查看达人报名按钮 -->
			<button class="primary-btn view-experts" @tap="viewAppliedExperts"
				v-if="order && order.enrollList && Array.isArray(order.enrollList) && order.enrollList.length > 0">
				<text v-if="order && !order.viewedApplications" class="notification-dot"></text>
				查看报名达人({{ order.enrollList.length }}人)
			</button>

			<!-- 已选择达人状态 -->
			<button class="primary-btn selected-expert" @tap="viewAppliedExperts"
				v-if="order && order.selectedList && Array.isArray(order.selectedList) && order.selectedList.length > 0">
				<text class="selected-icon">✓</text>
				已选择达人
			</button>

			<!-- 开始订单按钮 -->
			<button class="primary-btn start-order" @tap="startOrder" v-if="order && order.status === 'expertSelected'">
				开始订单
			</button>

			<!-- 已完成按钮 -->
			<button class="primary-btn complete" @tap="confirmComplete"
				v-if="order && order.status === 'orderStarted' && order.expertStatus === 'serviceCompleted'">
				确认完成
			</button>

				<!-- 返回按钮 -->
				<!-- <button class="secondary-btn" bindtap="goBack">返回</button> -->
			</block>

			<!-- 达人用户的按钮 -->
			<block v-else-if="currentRoleId === '2'&& !isCreator ">
				<button class="primary-btn apply" @tap="handleApply"
				v-if="order && order.status === 2 && !isApplied">
				申请报名
			</button>

			<!-- 已报名未被选中状态 -->
			<button class="disabled-btn" disabled v-if="isApplied && !isSelected">
				已报名，等待选择
			</button>

			<!-- 被选中状态 -->
			<button class="primary-btn" v-if="order && isSelected && order.status === 'expertSelected'">
				已被选中
			</button>

			<!-- 订单进行中状态 -->
			<button class="primary-btn complete-service" @tap="completeService"
				v-if="order && isSelected && order.status === 'orderStarted' && order.expertStatus === 'arrivalConfirmed'">
				服务完成
			</button>

			<!-- 等待开始订单提示 -->
			<view class="waiting-tip bottom-tip"
				v-if="order && isSelected && order.expertStatus === 'arrivalConfirmed' && order.status !== 'orderStarted'">
				已到达，等待用户开始订单
			</view>

				<!-- 返回按钮 -->
				<button class="secondary-btn" v-if="!isApplied" @tap="goBack">返回</button>
				<button class="secondary-btn" v-if="isApplied" @tap="cancalSignUp">取消报名</button>
			</block>

			<!-- 其他用户的按钮 -->
			<block v-else>
				<!-- 查看达人报名按钮 -->
			<button class="primary-btn view-experts" @tap="viewAppliedExperts"
				v-if="order && order.enrollList && Array.isArray(order.enrollList) && order.enrollList.length > 0">
				查看报名达人({{ order.enrollList.length }}人)
			</button>
				<!-- <button class="secondary-btn" @tap="goBack">返回</button> -->
			</block>
		</view>

		<!-- 用户操作区 -->
		<view class="action-area" v-if="isOrderCreator && order">
			<!-- 已选择达人，显示对应提示和操作按钮 -->
			<block v-if="order.status === 'expertSelected'">
				<!-- 等待达人到达提示 -->
				<view class="status-message waiting-tip" v-if="!order.expertStatus || order.expertStatus === 'chosen'">
					已选择达人，等待达人出发...
				</view>

				<view class="status-message waiting-tip" v-if="order.expertStatus === 'departureConfirmed'">
					达人已出发，等待到达...
				</view>

				<!-- 达人已到达，可以开始订单 -->
				<button class="action-btn start-order" v-if="order.expertStatus === 'arrivalConfirmed'" @tap="startOrder">
					开始订单
				</button>
			</block>

			<!-- 订单进行中，等待服务完成 -->
			<block v-if="order.status === 'orderStarted'">
				<view class="status-message" v-if="order.expertStatus !== 'serviceCompleted'">
					服务进行中...
				</view>
			</block>
		</view>
	</view>
</template>

<script>
// 引入订单流程模块
import { changeOrderState } from "../../utils/order-flow";
// 引入API函数
import { getCurrentMemberInfo } from "../../api/index.js";
const app = getApp().globalData;
const appAll = getApp();
export default {
	data() {
		return {
			orderId: null,
		order: null,
		loading: true,
		loadError: false,
		errorMsg: "",
		userRole: null,
		currentRoleId: '',
		hasExpertRole: false,
		isCreator: false,
			isApplied: false,
			isSelected: false,
			showOrderFlowModal_flag: false,
			isOrderCreator: false
		};
	},
	onLoad(options) {
		// 移除清除所有达人订单存储的调用
		// this.clearExpertOrderStorage();

		// 获取全局应用实例和用户信息
		const userRole = app.userRole;

		console.log("userRole____as", userRole);
		const userId = app.currentUserId;

		console.log("订单详情页 - 用户信息:", {
			userRole: userRole,
			userId: userId,
			isVerified: app.isVerified,
			hasExpertRole: app.hasExpertRole,
		});

		// 加载最新的用户角色信息
		this.loadUserRole();

		// 检查options参数是否存在
		console.log("页面加载参数:", options);

		// 获取订单ID，支持多种参数命名
		let orderId = null;

		if (options.id) {
			// 使用id参数获取订单ID
			orderId = options.id;
		} else if (options.orderId) {
			// 兼容使用orderId参数
			orderId = options.orderId;
		} else {
			// 无效的参数
			console.error("未提供有效的订单ID参数");
			uni.showToast({
				title: "订单ID不存在",
				icon: "none",
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
			return;
		}

		// 检查订单ID是否有效
		if (!orderId || orderId === "undefined" || orderId === "null") {
			console.error("无效的订单ID参数:", orderId);
			uni.showToast({
				title: "订单ID不存在或无效",
				icon: "none",
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
			return;
		}

		console.log("解析后的订单ID:", orderId);

		// 设置页面数据
		this.orderId = orderId;
		this.userRole = userRole;

		// 加载订单详情
		this.loadOrderDetail();
	},
	
	// 页面显示时触发
	async onShow() {
		// 刷新用户信息
		await this.loadUserRole();
		
		// 检查是否需要刷新订单
		if (this.orderId && uni.getStorageSync('orderUpdated') === true) {
			console.log('检测到订单更新，正在刷新数据...');
			this.refreshOrder();
			uni.removeStorageSync('orderUpdated');
			return; // 如果有订单更新标志，直接返回，避免重复刷新
		}
		
		// 获取最新用户角色信息
		const userRole = app.userRole;
		const userId = app.currentUserId;

		console.log("订单详情页显示 - 当前用户信息:", {
			userRole: userRole,
			userId: userId,
			isVerified: app.isVerified,
		});

		// 如果角色发生变化，需要更新页面数据
		if (userRole !== this.userRole) {
			this.userRole = userRole;

			// 如果已加载订单，重新处理订单数据来更新UI
			if (this.order) {
				this.processOrder(this.order);
			}
		}

		// 在页面每次显示时刷新订单数据，获取最新的报名情况
		if (this.orderId) {
			console.log("页面显示时刷新订单数据");
			this.loadOrderDetail(this.orderId);
		}
	},
	
	// 下拉刷新
	onPullDownRefresh() {
		this.refreshOrder();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	methods: {
		/**
		 * 加载用户角色信息
		 */
		async loadUserRole() {
			try {
				// 使用统一的API函数获取用户信息
				const res = await getCurrentMemberInfo({ withExtra: true, withPrivacy: false });
				
				if (res.code === 200 && res.data) {
					const currentRoleId = res.data.currentRoleId;
					const roles = res.data.roles || [];
					
					// 设置当前角色
					this.currentRoleId = currentRoleId;
					this.userRole = this.getRoleDisplayName(currentRoleId);
					
					// 检查是否有达人权限
					this.hasExpertRole = roles.some(role => role.code === '2');
					
					// 保存到本地存储
					uni.setStorageSync('currentRoleId', currentRoleId);
					uni.setStorageSync('hasExpertRole', this.hasExpertRole);
					
					// 更新全局数据
					app.globalData.userRole = this.userRole;
					app.globalData.hasExpertRole = this.hasExpertRole;
					app.globalData.currentRoleId = currentRoleId;
					
					console.log('用户角色信息更新:', {
						currentRoleId,
						userRole: this.userRole,
						hasExpertRole: this.hasExpertRole,
						roles
					});
				}
			} catch (error) {
				console.error('获取用户角色失败:', error);
				// 降级到本地存储
				this.loadRoleFromStorage();
			}
		},

		/**
		 * 从本地存储加载角色信息
		 */
		loadRoleFromStorage() {
			const currentRoleId = uni.getStorageSync('currentRoleId') || '1';
			const hasExpertRole = uni.getStorageSync('hasExpertRole') || false;
			
			this.currentRoleId = currentRoleId;
			this.userRole = this.getRoleDisplayName(currentRoleId);
			this.hasExpertRole = hasExpertRole;
			
			console.log('从本地存储加载角色信息:', {
				currentRoleId,
				userRole: this.userRole,
				hasExpertRole
			});
		},

		/**
		 * 获取角色显示名称
		 */
		getRoleDisplayName(roleId) {
			switch(roleId) {
				case '1': return '普通用户';
				case '2': return '闲时达人';
				case '3': return '闲时公会';
				default: return '游客用户';
			}
		},

		/**
		 * 加载订单详情
		 */
		async loadOrderDetail(orderId, forceRefresh = false) {
			// 使用传入的订单ID或者实例上的订单ID
			orderId = orderId || this.orderId;
			
			console.log("正在加载订单详情，订单ID:", orderId, "强制刷新:", forceRefresh);

			// 确保进入加载状态
			this.loading = true;
			this.loadError = false;
			this.errorMsg = "";

			// 内部定义安全比较函数，替代使用safe模块
			const safeEqual = (a, b) => {
				if (a === b) return true;
				if (a === undefined || b === undefined || a === null || b === null)
					return false;
				const strA = String(a).trim();
				const strB = String(b).trim();
				return strA === strB;
			};

			if (!orderId) {
				console.error("订单ID无效");
				this.loading = false;
				this.loadError = true;
				this.errorMsg = "订单ID无效";
				return;
			}

			try {
				// 从API获取订单详情
				let res = await this.$requestHttp.get(app.commonApi.getOrderDetail(orderId), {
					data: {}
				});
				
				if (res.code === 200 && res.data) {
					// API请求成功，处理订单数据
					console.log("API获取订单详情成功:", res.data);
					
					// 检查关键数据，帮助诊断问题
					const apiData = res.data;
					
					// 兼容处理报名数据
					if (!apiData.appliedExperts && apiData.enrollList && Array.isArray(apiData.enrollList)) {
						apiData.appliedExperts = apiData.enrollList;
						console.log("从enrollList复制报名数据到appliedExperts");
					}
					
					console.log("订单API数据诊断:", {
						orderId: apiData.orderId || apiData.id,
						hasAppliedExperts: !!apiData.appliedExperts,
						appliedExpertsType: apiData.appliedExperts ? typeof apiData.appliedExperts : 'undefined',
						appliedExpertsCount: apiData.appliedExperts && Array.isArray(apiData.appliedExperts) ? apiData.appliedExperts.length : 'N/A',
						enrollCount: apiData.enrollCount,
						applicantsCount: apiData.applicantsCount,
						hasEnrolledExperts: !!apiData.enrolledExperts,
						enrolledCount: apiData.enrolledExperts && Array.isArray(apiData.enrolledExperts) ? apiData.enrolledExperts.length : 'N/A',
						// 检查enrollList字段
						hasEnrollList: !!apiData.enrollList,
						enrollListCount: apiData.enrollList && Array.isArray(apiData.enrollList) ? apiData.enrollList.length : 'N/A'
					});
					
					this.processOrder(res.data);
					return;
				} else {
					// API请求失败，显示错误
					console.error("API获取订单详情失败:", res.message || "未知错误");
					this.loading = false;
					this.loadError = true;
					this.errorMsg = res.message || "获取订单详情失败";
				}
			} catch (error) {
				console.error("获取订单详情异常:", error);
				this.loading = false;
				this.loadError = true;
				this.errorMsg = "网络异常，请稍后重试";
			}
		},



		// 安全比较函数
		safeEqual(a, b) {
			if (a === b) return true;
			if (a === undefined || b === undefined || a === null || b === null)
				return false;
			const strA = String(a).trim();
			const strB = String(b).trim();
			return strA === strB;
		},

		// 移除003账号的硬编码逻辑，直接处理订单数据
		processOrderWithMergedApplicants(order) {
			if (!order) return;
			// 直接调用原始处理方法，不再处理003账号的特殊逻辑
			this.processOrder(order);
		},

		// 处理订单信息，判断是否为创建者和是否已申请
		processOrder(order) {
			console.log("order", order, app, getApp());
			if (!order) {
				this.loading = false;
				this.loadError = true;
				this.errorMsg = "订单数据无效";
				return null;
			}

			// 复制一份订单信息，避免引用修改
			const processedOrder = JSON.parse(JSON.stringify(order));

			// 处理字段映射，确保与订单列表页字段一致
			// 处理订单编号
			if (!processedOrder.orderId && processedOrder.orderNo) {
				processedOrder.orderId = processedOrder.orderNo;
			}

			// 处理服务类型
			if (!processedOrder.type && processedOrder.activityTypeName) {
				processedOrder.type = processedOrder.activityTypeName;
			}
			if (!processedOrder.service && processedOrder.type) {
				processedOrder.service = processedOrder.type;
			}

			// 处理服务子类型
			if (!processedOrder.subType && processedOrder.serviceSubType) {
				processedOrder.subType = processedOrder.serviceSubType;
			}

			// 处理日期和时间
			if (processedOrder.startTime && processedOrder.endTime) {
				// 格式化活动时间
				const formattedTime = this.formatActivityTime(processedOrder.startTime, processedOrder.endTime);
				const timeParts = formattedTime.split(" ");

				if (formattedTime.includes(" - ")) {
					// 分离日期和时间
					if (timeParts.length >= 3) {
						processedOrder.date = timeParts[0];
						processedOrder.time = timeParts.slice(1).join(" ");
					} else {
						processedOrder.time = formattedTime;
					}
				} else {
					processedOrder.time = formattedTime;
				}

				// 计算活动时长
				const durationInfo = this.calculateDuration(processedOrder.startTime, processedOrder.endTime);
				// 保存小时和分钟信息
				processedOrder.durationHours = durationInfo?.hours;
				processedOrder.durationMinutes = durationInfo?.minutes;
				// 格式化时长文本
				if (durationInfo && durationInfo.hours !== undefined) {
					processedOrder.duration = durationInfo.hours !== undefined && durationInfo.minutes !== undefined
						? `${durationInfo.hours}小时${durationInfo.minutes > 0 ? durationInfo.minutes + '分钟' : ''}`
						: "";
				}

				// 判断订单类型（即时订单或预约订单）
				processedOrder.isInstant = this.isInstantOrder(processedOrder.startTime);
				processedOrder.orderType = processedOrder.isInstant ? "即时订单" : "预约订单";
			}

			// 处理参与人数
			if (!processedOrder.people && processedOrder.personCount) {
				processedOrder.people = processedOrder.personCount;
			}
			if (!processedOrder.participants && processedOrder.people) {
				processedOrder.participants = processedOrder.people;
			}

			// 处理小时收费
			if (!processedOrder.hourlyRate && processedOrder.price) {
				processedOrder.hourlyRate = processedOrder.price;
			}

			// 处理性别要求
			if (processedOrder.sexRequire && !processedOrder.gender) {
				processedOrder.gender = processedOrder.sexRequire;
			}

			// 性别文本转换
			let genderText = "不限";
			if (processedOrder.gender) {
				if (processedOrder.gender === "male" || processedOrder.gender === "男") {
					genderText = "男";
					processedOrder.gender = "male";
				} else if (processedOrder.gender === "female" || processedOrder.gender === "女") {
					genderText = "女";
					processedOrder.gender = "female";
				}
			}
			processedOrder.genderText = genderText;

			// 处理地址信息
			if (!processedOrder.address && (processedOrder.location || processedOrder.addressDetail)) {
				processedOrder.address = (processedOrder.location || "") + " " + (processedOrder.addressDetail || "");
			}

			// 处理发布者信息
			if (!processedOrder.publisherName && processedOrder.memberName) {
				processedOrder.publisherName = processedOrder.memberName;
			}
			if (!processedOrder.publisherAvatar && processedOrder.memberAvatar) {
				processedOrder.publisherAvatar = processedOrder.memberAvatar;
			}
			
			// 确保发布者头像有默认值
			if (!processedOrder.publisherAvatar && !processedOrder.memberAvatar) {
				processedOrder.publisherAvatar = '/images/default-avatar.png';
			}
			
			// 确保发布时间格式化
			if (processedOrder.createTime && !processedOrder.publishTime) {
				processedOrder.publishTime = this.formatTimeAgo(processedOrder.createTime);
			}

			// 处理报名人数
			if (!processedOrder.applicantsCount && processedOrder.enrollCount) {
				processedOrder.applicantsCount = processedOrder.enrollCount;
			}
			
			// 确保appliedExperts字段存在且为数组
			if (!processedOrder.appliedExperts) {
				processedOrder.appliedExperts = [];
			} else if (!Array.isArray(processedOrder.appliedExperts)) {
				processedOrder.appliedExperts = [];
				console.error("后端返回的appliedExperts不是数组:", processedOrder.appliedExperts);
			}
			
			// 检查enrollList字段（后端的主要报名字段）
			if (processedOrder.enrollList && Array.isArray(processedOrder.enrollList)) {
				// 如果有enrollList字段，使用enrollList作为报名数据
				processedOrder.appliedExperts = processedOrder.enrollList.map(enroll => ({
					id: enroll.talentId || enroll.enrollId,
					talentId: enroll.talentId,
					name: enroll.talentName || '未知达人',
					avatar: enroll.talentAvatar || '/images/default-avatar.png',
					applyTime: enroll.createTime,
					createTime: enroll.createTime,
					isSelected: enroll.isSelected || false,
					status: enroll.status || 0
				}));
				console.log("从enrollList转换报名数据", processedOrder.appliedExperts.length);
			}
			
			// 检查enrollDetails字段（后端的详细报名字段）
			else if (processedOrder.enrollDetails && Array.isArray(processedOrder.enrollDetails)) {
				processedOrder.appliedExperts = processedOrder.enrollDetails.map(detail => ({
					id: detail.memberId || detail.id,
					talentId: detail.memberId,
					name: detail.memberNickname || detail.talentName || '未知达人',
					avatar: detail.memberAvatar || detail.talentAvatar || '/images/default-avatar.png',
					applyTime: detail.createTime,
					createTime: detail.createTime,
					isSelected: detail.isSelect || false,
					status: detail.status || 0
				}));
				console.log("从enrollDetails转换报名数据", processedOrder.appliedExperts.length);
			}
			
			// 检查其他可能的报名字段
			else if (processedOrder.enrolledExperts && Array.isArray(processedOrder.enrolledExperts) && processedOrder.enrolledExperts.length > 0) {
				processedOrder.appliedExperts = processedOrder.enrolledExperts;
				console.log("使用enrolledExperts报名数据", processedOrder.appliedExperts.length);
			}
			
			// 移除占位数据生成逻辑，仅使用真实的后端数据
			// 如果后端有enrollCount但无具体报名列表，不再创建假数据
			
			// 设置报名人数
			processedOrder.applicantsCount = processedOrder.appliedExperts.length;
			
			// 如果后端有enrollCount且大于appliedExperts长度，使用enrollCount
			if (processedOrder.enrollCount && processedOrder.enrollCount > processedOrder.applicantsCount) {
				processedOrder.applicantsCount = processedOrder.enrollCount;
			}
			
			// 日志输出报名情况
			console.log("订单报名情况:", {
				orderId: processedOrder.orderId || processedOrder.id,
				appliedExpertsCount: processedOrder.appliedExperts.length,
				applicantsCount: processedOrder.applicantsCount
			});

			// 处理发布时间
			if (processedOrder.createTime) {
				processedOrder.publishTime = this.formatTimeAgo(processedOrder.createTime);
			}

			// 处理状态文本
			if (processedOrder.status !== undefined && !processedOrder.statusText) {
				processedOrder.statusText = this.getStatusText(processedOrder.status);
			}

			// 判断当前用户是否为订单创建者
			const userId = app.currentUserId;
			processedOrder.isCreator = order.memberId === userId;

			// 检查当前用户是否已申请此订单
			// 优先使用后端返回的数据判断当前用户是否已报名
			if (processedOrder.isCurrentMemberEnrolled !== undefined) {
				// 后端返回了当前用户是否已报名的标志
				processedOrder.hasApplied = processedOrder.isCurrentMemberEnrolled;
				console.log("使用后端数据判断用户是否已报名:", processedOrder.hasApplied);
			} else {
				// 后端没有返回相关标志，尝试从订单的报名列表中判断
				if (processedOrder.appliedExperts && Array.isArray(processedOrder.appliedExperts)) {
					const currentUserId = app.currentUserId;
					processedOrder.hasApplied = processedOrder.appliedExperts.some(
						expert => expert.id === currentUserId || expert.memberId === currentUserId
					);
					console.log("从报名列表判断用户是否已报名:", processedOrder.hasApplied);
				} else {
					// 如果没有报名列表数据，则回退到本地存储判断
					const expertAppliedOrdersKey = appAll.getExpertAppliedOrdersKey();
					const expertAppliedOrders = uni.getStorageSync(expertAppliedOrdersKey) || [];
					
					// 检查用户是否已申请
					processedOrder.hasApplied = expertAppliedOrders.some(
						appliedOrder => appliedOrder.id === order.id || appliedOrder.orderId === order.orderId
					);
					console.log("从本地存储判断用户是否已报名:", processedOrder.hasApplied);
				}
			}
			
			// 判断当前用户是否被选中
			const isSelected = processedOrder.selectedExpertId === app.currentUserId;

			// 如果已选择了达人，查找达人信息并添加达人名称
			if (
				processedOrder.selectedExpertId &&
				processedOrder.appliedExperts &&
				processedOrder.appliedExperts.length > 0
			) {
				const selectedExpert = processedOrder.appliedExperts.find(
					(expert) => expert.id === processedOrder.selectedExpertId
				);
				if (selectedExpert) {
					// 设置选中达人的详细信息
					processedOrder.selectedExpertName = selectedExpert.name;
					processedOrder.selectedExpertAvatar =
						selectedExpert.avatar || "/images/default-avatar.png";
					console.log("已找到选中达人：", selectedExpert.name);
				} else {
					console.log(
						"未在申请列表中找到选中达人，ID:",
						processedOrder.selectedExpertId
					);

					// 尝试从账号信息中获取达人信息
					// 已移除测试账号逻辑，选中达人信息应从API返回的数据中获取
				if (processedOrder.selectedExpertId && !processedOrder.selectedExpertName) {
					processedOrder.selectedExpertName = `达人${processedOrder.selectedExpertId}`;
					processedOrder.selectedExpertAvatar = "/images/default-avatar.png";
					console.log(
						"使用默认达人信息：",
						processedOrder.selectedExpertName
					);
				}
				}
			}
			
			/**
			 * 子订单状态：0-已报名，1-被选中，2-已出发，3-已到达，4-服务中，5-服务完成，6-已评价,
			 * 可用值:ENROLLED,SELECTED,DEPARTED,ARRIVED,SERVICING,SERVICE_COMPLETED,EVALUATED,CANCELED
			 * */
console.log("processedOrder_cc", processedOrder);
			// 更新页面数据
			this.order = processedOrder;
			this.loading = false;
			this.isCreator = processedOrder.isCreator;
			this.isApplied = processedOrder.hasApplied; // 判断是否报名
			this.isSelected = isSelected;
			
			// 如果后端返回的报名列表为空但当前用户已报名，尝试添加当前用户到报名列表
			if (this.isApplied && (!processedOrder.appliedExperts || !processedOrder.appliedExperts.length)) {
				console.log("检测到用户已报名但报名列表为空，尝试添加当前用户到报名列表");
				this.ensureCurrentUserInAppliedExperts();
			}
			
			// 保存当前订单状态到缓存，但不依赖它进行主要逻辑
			try {
				uni.setStorageSync('currentOrderDetail', processedOrder);
			} catch (e) {
				console.error('保存当前订单详情到缓存失败', e);
			}

			return processedOrder;
		},

		// 格式化活动时间
		formatActivityTime(startTime, endTime) {
      if (!startTime || !endTime) return "";
      
      try {
        const start = new Date(startTime);
        const end = new Date(endTime);
        
        // 格式化日期部分 (yyyy-MM-dd)
        const startYear = start.getFullYear();
        const startMonth = start.getMonth() + 1;
        const startDay = start.getDate();
        const startDateStr = `${startYear}-${startMonth}-${startDay}`;
        
        const endYear = end.getFullYear();
        const endMonth = end.getMonth() + 1;
        const endDay = end.getDate();
        const endDateStr = `${endYear}-${endMonth}-${endDay}`;
        
        // 格式化时间部分 (HH:mm)
        const startHours = String(start.getHours()).padStart(2, '0');
        const startMinutes = String(start.getMinutes()).padStart(2, '0');
        const startTimeStr = `${startHours}:${startMinutes}`;
        
        const endHours = String(end.getHours()).padStart(2, '0');
        const endMinutes = String(end.getMinutes()).padStart(2, '0');
        const endTimeStr = `${endHours}:${endMinutes}`;
        
        // 检查是否是同一天
        if (startYear === endYear && startMonth === endMonth && startDay === endDay) {
          // 同一天，只显示一个日期
          return `${startDateStr} ${startTimeStr} - ${endTimeStr}`;
        } else {
          // 不同天，显示完整的开始和结束时间
          return `${startDateStr} ${startTimeStr} - ${endDateStr} ${endTimeStr}`;
        }
      } catch (error) {
        console.error("格式化活动时间出错:", error);
        return startTime && endTime ? `${startTime} - ${endTime}` : "";
      }
    },

		// 计算活动时长
		calculateDuration(startTime, endTime) {
			if (!startTime || !endTime) return { hours: 0, minutes: 0 };

			try {
				const startDate = new Date(startTime);
				const endDate = new Date(endTime);

				// 计算时间差（毫秒）
				const diffMs = endDate.getTime() - startDate.getTime();

				// 转换为小时和分钟
				const hours = Math.floor(diffMs / (1000 * 60 * 60));
				const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

				return { hours, minutes };
			} catch (error) {
				console.error("计算活动时长出错:", error);
				return { hours: 0, minutes: 0 };
			}
		},

		// 判断是否为即时订单
		isInstantOrder(startTime) {
			if (!startTime) return false;

			try {
				const startDate = new Date(startTime);
				const now = new Date();

				// 如果开始时间在当前时间的2小时内，认为是即时订单
				return (startDate.getTime() - now.getTime()) < (2 * 60 * 60 * 1000);
			} catch (error) {
				console.error("判断即时订单出错:", error);
				return false;
			}
		},

		// 格式化时间为"几分钟前"、"几小时前"等
		formatTimeAgo(dateString) {
			const date = new Date(dateString);
			const now = new Date();
			const diff = Math.floor((now - date) / 1000); // 秒数

			if (diff < 60) {
				return '刚刚';
			} else if (diff < 3600) {
				return Math.floor(diff / 60) + '分钟前';
			} else if (diff < 86400) {
				return Math.floor(diff / 3600) + '小时前';
			} else {
				return Math.floor(diff / 86400) + '天前';
			}
		},

		// 获取订单状态文本
		getStatusText(status) {
			const statusMap = {
				0: '待审核',
				1: '报名中',
				2: '待选择达人',
				3: '进行中',
				4: '已完成',
				5: '已取消',
				'published': '已发布订单',
				'expertSelected': '已选择达人',
				'orderStarted': '订单已开始',
				'completed': '订单已完成',
				'evaluated': '已评价'
			};
			return statusMap[status] || '未知状态';
		},

		// 获取状态文本
		getStateText(state) {
			const stateTextMap = {
				published: "订单已发布",
				expertSelected: "已选择达人",
				orderStarted: "订单已开始",
				completed: "订单已完成",
				evaluated: "已评价",
				applied: "已报名",
				chosen: "已被选中",
				departureConfirmed: "已确认出发",
				arrivalConfirmed: "已到达目的地",
				serviceCompleted: "服务已完成",
			};

			return stateTextMap[state] || state;
		},

		// 刷新订单数据
		refreshOrder() {
			if (this.orderId) {
				this.loading = true;
				// 使用强制刷新模式，确保从后端获取最新数据
				this.loadOrderDetail(this.orderId, true);
			}
		},

		// 返回上一页
		goBack() {
			if (this.orderSuccess) {
				// 如果订单已申请成功，返回到首页
				uni.navigateBack({
					delta: 2,
				});
			} else {
				uni.navigateBack();
			}
		},

		// 取消报名
		async cancalSignUp() {
			uni.showLoading({
				title: "取消报名中...",
				mask: true,
			});
			let res = await this.$requestHttp.post(app.commonApi.cancelSignUp(this.orderId), {
				data: {}
			})
			console.log("达人报名订单是否成功", res);
			if(res.code == 200) {
				this.isApplied = false;
				// 显示成功提示
				uni.hideLoading();
				uni.showToast({
					title: "取消报名成功",
					icon: "success",
				});
				
				// 立即从后端获取最新订单数据
				this.loadOrderDetail(this.orderId, true);
				
				// 通知其他页面订单已更新
				uni.setStorageSync('orderUpdated', true);
			} else {
				uni.hideLoading();
				uni.showToast({
					title: res.message || "取消报名失败",
					icon: "error",
				});
			}
		},

		// 报名
		async handleApply() {
			console.log("申请报名 - 初始状态:", {
				orderId: this.orderId,
				orderIdType: typeof this.orderId,
				userRole: app.userRole,
				isVerified: app.isVerified,
				currentUserId: app.currentUserId,
				currentRoleId: this.currentRoleId,
				hasExpertRole: this.hasExpertRole
			});

			// 先确保获取最新的用户角色信息
			await this.loadUserRole();

			// 检查是否为达人用户或具有达人权限
			if (this.currentRoleId !== "2" && !this.hasExpertRole) {
				console.log("用户角色验证失败:", {
					currentRoleId: this.currentRoleId,
					hasExpertRole: this.hasExpertRole,
					userRole: this.userRole
				});
				uni.showModal({
					title: "提示",
					content: "需要认证为达人才能报名接单，是否去认证？",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: "/pages/mine/mine",
							});
						}
					},
				});
				return;
			}

			// 检查用户认证状态
			if (!app.isVerified) {
				uni.showModal({
					title: "提示",
					content: "需要完成实名认证才能报名接单，是否去认证？",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: "/pages/verify-identity/verify-identity",
							});
						}
					},
				});
				return;
			}

			// 检查用户是否是订单创建者，如果是则不允许报名
			if (this.isCreator) {
				uni.showToast({
					title: "不能报名自己发布的订单",
					icon: "none",
				});
				return;
			}

			console.log("Xxxx", this.orderId, app.currentUserId);

			uni.showModal({
				title: "报名确认",
				content:
					"确定报名此订单吗？报名后平台将会通知用户，申请成功后将会收到通知。",
				success: async (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: "报名中...",
							mask: true,
						});

						// 查找当前订单
						const orderId = this.orderId;

						let res = await this.$requestHttp.post(app.commonApi.signUp(orderId), {
							data: {}
						})
						console.log("达人报名订单是否成功", res);
						if(res.code == 200) {
							// 标记已报名状态
							this.isApplied = true;
							
							// 显示成功提示
							uni.hideLoading();
							uni.showToast({
								title: "报名成功",
								icon: "success",
							});
							
							// 重要：临时添加当前用户到报名列表，提高用户体验
							if (!this.order.appliedExperts) {
								this.order.appliedExperts = [];
							}
							
							// 获取当前用户信息
							const currentUserId = app.currentUserId;
							const userInfo = uni.getStorageSync('userInfo') || {};
			// 已移除测试账号逻辑，使用真实用户信息或默认值
			const userAvatar = userInfo.avatar || app.globalData.memberInfo?.avatar || '/images/default-avatar.png';
			const userNickname = userInfo.nickname || app.globalData.memberInfo?.nickname || '闲时达人';
							
							// 如果用户不在报名列表中，添加
							if (!this.order.appliedExperts.some(expert => expert.id === currentUserId || expert.memberId === currentUserId)) {
								this.order.appliedExperts.push({
									id: currentUserId,
									memberId: currentUserId,
									name: userNickname,
									avatar: userAvatar,
									applyTime: new Date().toISOString(),
									applyTimeFormatted: '刚刚'
								});
								
								console.log("临时添加当前用户到报名列表");
							}
							
							// 延迟100毫秒后从后端获取最新订单数据（给UI时间刷新）
							setTimeout(() => {
								// 立即从后端获取最新订单数据
								this.loadOrderDetail(this.orderId, true);
								
								// 通知其他页面订单已更新
								uni.setStorageSync('orderUpdated', true);
							}, 100);
						} else {
							uni.hideLoading();
							uni.showToast({
								title: res.message || "报名失败",
								icon: "error",
							});
						}

						return;
						} else {
							uni.hideLoading();
							console.error("未找到订单:", orderId);
							uni.showToast({
								title: "订单不存在",
								icon: "none",
							});
						}
					}
				});
		},

		// 查看申请的达人
		viewAppliedExperts() {
			const orderId = this.orderId;
			if (!orderId) return;

			// 如果是创建者且有选中的达人但未正式确认，需要提示用户
			if (
				this.isCreator &&
				this.order &&
				this.order.selectedExpertId &&
				this.order.status === "published"
			) {
				uni.showModal({
					title: "选人未完成",
					content:
						'您已选择达人，但尚未完成选人流程。需要前往达人列表页面点击"确认选人结束"按钮来完成选择并通知达人。',
					confirmText: "前往确认",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: `/pages/order-applicants/order-applicants?id=${orderId}&isCreator=${this.isCreator ? 1 : 0}`,
							});
						}
					},
				});
				return;
			}

			// 正常跳转到达人列表页面，传递是否为创建者的标志
			uni.navigateTo({
				url: `/pages/order-applicants/order-applicants?id=${orderId}&isCreator=${this.isCreator ? 1 : 0}`,
			});
		},

		// 选择达人
		selectExpert(e) {
			const expertId = e.currentTarget.dataset.expertId;
			const order = this.order;

			if (!order) {
				uni.showToast({
					title: "订单不存在",
					icon: "none",
				});
				return;
			}

			uni.showModal({
				title: "确认选择",
				content: "确定选择该达人接单吗？",
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: "处理中",
						});

						// 更新订单状态
						let orders = app.publishedOrders || [];
						const targetOrder = orders.find(
							(o) => o.id === order.id || o.orderId === order.orderId
						);

						if (targetOrder) {
							// 选中达人
							targetOrder.selectedExpertId = expertId;
							targetOrder.selected = true;

							// 更新状态
							targetOrder.status = "expertSelected";
							targetOrder.statusText = "已选择达人";

							// 更新进度
							targetOrder.userProgress = 2;

							// 添加日志
							const expert = targetOrder.appliedExperts.find(
								(e) => e.id === expertId
							);
							const expertName = expert ? expert.name : "达人";

							if (!targetOrder.stateLogs) targetOrder.stateLogs = [];

							// 创建新的日志条目，确保包含当前时间
							const currentTime = new Date().toISOString();
							targetOrder.stateLogs.unshift({
								state: "expertSelected",
								time: currentTime,
								formattedTime: formatDateTime(currentTime),
								role: "user",
								remark: `选择了达人${expertName}`,
							});

							// 更新全局数据和本地存储
							app.publishedOrders = orders;
							uni.setStorageSync("publishedOrders", JSON.stringify(orders));

							// 刷新当前页面
							this.loadOrderDetail(this.orderId);

							uni.hideLoading();
							uni.showToast({
								title: "已选择达人",
								icon: "success",
							});
						} else {
							uni.hideLoading();
							uni.showToast({
								title: "操作失败",
								icon: "none",
							});
						}
					}
				},
			});
		},

		// 复制订单ID
		copyOrderId() {
			if (this.order && this.order.orderId) {
				uni.setClipboardData({
					data: this.order.orderId,
					success: () => {
						uni.showToast({
							title: "订单号已复制",
							icon: "success",
						});
					},
				});
			}
		},
		
		// 格式化报名时间
		formatApplyTime(timeStr) {
			if (!timeStr) return '';
			
			try {
				const applyTime = new Date(timeStr);
				if (isNaN(applyTime.getTime())) return '';
				
				const now = new Date();
				const diffMs = now - applyTime;
				const diffSec = Math.floor(diffMs / 1000);
				
				// 2分钟内
				if (diffSec < 120) {
					return '刚刚';
				}
				
				// 1小时内
				if (diffSec < 3600) {
					return Math.floor(diffSec / 60) + '分钟前';
				}
				
				// 24小时内
				if (diffSec < 86400) {
					return Math.floor(diffSec / 3600) + '小时前';
				}
				
				// 7天内
				if (diffSec < 604800) {
					return Math.floor(diffSec / 86400) + '天前';
				}
				
				// 超过7天显示具体日期和时间
				const year = applyTime.getFullYear();
				const month = (applyTime.getMonth() + 1).toString().padStart(2, '0');
				const day = applyTime.getDate().toString().padStart(2, '0');
				const hour = applyTime.getHours().toString().padStart(2, '0');
				const minute = applyTime.getMinutes().toString().padStart(2, '0');
				
				// 如果是今年，不显示年份
				const currentYear = new Date().getFullYear();
				if (year === currentYear) {
					return `${month}-${day} ${hour}:${minute}`;
				} else {
					return `${year}-${month}-${day} ${hour}:${minute}`;
				}
			} catch (e) {
				console.error('格式化报名时间错误:', e);
				return '';
			}
		},

		// 达人确认服务完成
		completeService() {
			const orderId = this.order.id || this.order.orderId;
			const order = this.order;

			// 检查订单状态
			if (order.status !== "orderStarted") {
				uni.showToast({
					title: "等待用户开始订单",
					icon: "none",
				});
				return;
			}

			// 检查达人状态
			if (order.expertStatus !== "arrivalConfirmed") {
				uni.showToast({
					title: "请先确认到达",
					icon: "none",
				});
				return;
			}

			uni.showModal({
				title: "确认服务完成",
				content: "确认您已完成此订单的服务？",
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: "处理中",
						});

						// 获取订单列表
						let orders = app.publishedOrders || [];
						const targetOrder = orders.find(
							(o) => o.id === order.id || o.orderId === order.orderId
						);

						if (targetOrder) {
							// 更新达人状态
							targetOrder.expertStatus = "serviceCompleted";
							targetOrder.expertStatusText = "服务已完成";
							targetOrder.expertProgress = 5;

							// 添加日志
							if (!targetOrder.stateLogs) targetOrder.stateLogs = [];
							targetOrder.stateLogs.unshift({
								state: "serviceCompleted",
								time: new Date().toISOString(),
								role: "expert",
								remark: "达人已完成服务，等待用户确认",
							});

							// 更新全局数据和本地存储
							app.publishedOrders = orders;
							uni.setStorageSync("publishedOrders", JSON.stringify(orders));

							// 刷新当前页面
							this.loadOrderDetail(this.orderId);

							uni.hideLoading();
							uni.showToast({
								title: "已标记为完成",
								icon: "success",
							});
						} else {
							uni.hideLoading();
							uni.showToast({
								title: "操作失败",
								icon: "none",
							});
						}
					}
				},
			});
		},



		// 开始订单
		startOrder() {
			const orderId = this.order.id || this.order.orderId;
			const order = this.order;

			// 检查达人是否已确认到达
			if (!order.expertStatus || order.expertStatus !== "arrivalConfirmed") {
				uni.showToast({
					title: "等待达人确认到达",
					icon: "none",
				});
				return;
			}

			uni.showModal({
				title: "确认开始订单",
				content: "确定要开始此订单吗？订单开始后，将正式进入服务流程。",
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: "处理中",
						});

						// 调用状态变更
						changeOrderState(
							orderId,
							order.status,
							"orderStarted",
							"user",
							"用户已开始订单"
						)
							.then((success) => {
								uni.hideLoading();

								if (success) {
									uni.showToast({
										title: "订单已开始",
										icon: "success",
									});

									// 刷新订单数据
									this.loadOrderDetail(orderId);
								} else {
									uni.showToast({
										title: "操作失败",
										icon: "none",
									});
								}
							})
							.catch((err) => {
								uni.hideLoading();
								console.error("订单开始失败:", err);
								uni.showToast({
									title: "操作失败",
									icon: "none",
								});
							});
					}
				},
			});
		},

		// 确认完成订单
		confirmComplete() {
			const orderId = this.order.id || this.order.orderId;
			const order = this.order;

			// 检查达人是否已完成服务
			if (!order.expertStatus || order.expertStatus !== "serviceCompleted") {
				uni.showToast({
					title: "等待达人完成服务",
					icon: "none",
				});
				return;
			}

			uni.showModal({
				title: "确认完成订单",
				content: "确定要完成此订单吗？完成后，您可以对达人的服务进行评价。",
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: "处理中",
						});

						// 调用状态变更
						changeOrderState(
							orderId,
							order.status,
							"completed",
							"user",
							"用户已确认完成订单"
						)
							.then((success) => {
								uni.hideLoading();

								if (success) {
									uni.showToast({
										title: "订单已完成",
										icon: "success",
									});

									// 刷新订单数据
									this.loadOrderDetail(orderId);
								} else {
									uni.showToast({
										title: "操作失败",
										icon: "none",
									});
								}
							})
							.catch((err) => {
								uni.hideLoading();
								console.error("订单完成失败:", err);
								uni.showToast({
									title: "操作失败",
									icon: "none",
								});
							});
					}
				},
			});
		},

		// 跳转到评价页面
		navigateToEvaluate() {
			const orderId = this.order.id || this.order.orderId;

			// 确保订单状态为已完成
			if (this.order.status !== "completed") {
				uni.showToast({
					title: "请先完成订单",
					icon: "none",
				});
				return;
			}

			// 保存当前订单详情到缓存，方便评价页面使用
			uni.setStorageSync("currentOrderDetail", this.order);

			// 跳转到评价页面
			uni.navigateTo({
				url: `/pages/order-evaluate/order-evaluate?id=${orderId}`,
				fail: (err) => {
					console.error("导航到评价页面失败:", err);
					uni.showToast({
						title: "页面跳转失败",
						icon: "none",
					});
				},
			});
		},

		// 显示订单流程弹窗
		showOrderFlowModal() {
			// 已移除功能，此处保留函数以避免报错
		},

		// 隐藏订单流程弹窗
		hideOrderFlowModal() {
			// 已移除功能，此处保留函数以避免报错
		},

		// 查看达人个人主页
		viewExpertProfile(e) {
			const expertId = e.currentTarget.dataset.expertId;

			if (!expertId) {
				uni.showToast({
					title: "达人信息不存在",
					icon: "none",
				});
				return;
			}

			console.log("查看达人个人主页：", expertId);

			// 找到对应的达人信息
			const order = this.order;
			if (
				order &&
				order.appliedExperts &&
				Array.isArray(order.appliedExperts)
			) {
				const expertInfo = order.appliedExperts.find(
					(expert) => expert.id === expertId
				);

				if (expertInfo) {
					// 保存达人信息到本地存储，确保个人主页能够找到
					uni.setStorageSync("currentExpertInfo", expertInfo);
				}
			}

			// 跳转到个人主页
			uni.navigateTo({
				url: `/pages/personal-homepage/personal-homepage?id=${expertId}`,
				fail: (err) => {
					console.error("导航到个人主页失败:", err);
					uni.showToast({
						title: "页面跳转失败",
						icon: "none",
					});
				},
			});
		},

		// 合并所有达人账号的报名信息
		mergeAllExpertApplications(order) {
			console.log("开始合并所有达人账号的报名信息");
			// 确保appliedExperts存在
			if (!order.appliedExperts) {
				order.appliedExperts = [];
			}

			try {
				// 定义安全比较函数
				const safeCompare = (a, b) => {
					if (a === b) return true;
					if (a === undefined || b === undefined || a === null || b === null)
						return false;
					const strA = String(a || "");
					const strB = String(b || "");
					return strA === strB;
				};

				// 移除003账号的特殊合并逻辑

				console.log("合并后的报名列表:", order.appliedExperts);
			} catch (error) {
				console.error("合并达人报名信息时出错:", error);
			}

			return order;
		},

		// 合并指定达人账号的报名信息
		mergeExpertApplications(order, expertId) {
			if (!order || !expertId) return;

			console.log(`合并达人${expertId}的报名信息`);

			try {
				// 定义安全比较函数
				const safeCompare = (a, b) => {
					if (a === b) return true;
					if (a === undefined || b === undefined || a === null || b === null)
						return false;
					const strA = String(a || "");
					const strB = String(b || "");
					return strA === strB;
				};

				// 首先检查个人报名列表
				const personalOrdersKey = `expertAppliedOrders_${expertId}_${expertId}`;
				const personalOrdersStr = uni.getStorageSync(personalOrdersKey);

				let expertHasApplied = false;
				let applyTime = null;

				if (personalOrdersStr) {
					try {
						const personalOrders = JSON.parse(personalOrdersStr);
						if (Array.isArray(personalOrders)) {
							const appliedOrder = personalOrders.find(
								(o) =>
									safeCompare(o.id, order.id) ||
									safeCompare(o.orderId, order.orderId)
							);

							if (appliedOrder && appliedOrder.isApplied) {
								expertHasApplied = true;
								applyTime = appliedOrder.applyTime;
								console.log(`在个人报名列表中发现达人${expertId}已报名`);
							}
						}
					} catch (e) {
						console.error(`解析${personalOrdersKey}失败:`, e);
					}
				}

				// 如果未找到，再检查通用报名列表
				if (!expertHasApplied) {
					const expertOrdersKey = `expertAppliedOrders_${expertId}`;
					const expertOrdersStr = uni.getStorageSync(expertOrdersKey);

					if (expertOrdersStr) {
						try {
							const expertOrders = JSON.parse(expertOrdersStr);
							if (Array.isArray(expertOrders)) {
								const appliedOrder = expertOrders.find(
									(o) =>
										safeCompare(o.id, order.id) ||
										safeCompare(o.orderId, order.orderId)
								);

								if (appliedOrder && appliedOrder.isApplied) {
									expertHasApplied = true;
									applyTime = appliedOrder.applyTime;
									console.log(`在通用报名列表中发现达人${expertId}已报名`);
								}
							}
						} catch (e) {
							console.error(`解析${expertOrdersKey}失败:`, e);
						}
					}
				}

				// 如果达人已报名，检查是否已经在申请列表中
			if (expertHasApplied) {
				// 已移除测试账号逻辑，使用默认达人信息
				const existingExpert = order.appliedExperts.find(
					(e) => e.id === expertId
				);

				if (!existingExpert) {
					// 添加达人到申请列表
					order.appliedExperts.push({
						id: expertId,
						name: `达人${expertId}`,
						avatar: "/images/default-avatar.png",
						applyTime: applyTime || new Date().toISOString(),
					});

					console.log(
						`已添加达人${expertId}到申请列表，当前申请人数:`,
						order.appliedExperts.length
					);

					// 增加appliedExperts计数，如果存在
					if (typeof order.applicants === "number") {
						order.applicants = order.appliedExperts.length;
					}
				}
			}
			} catch (error) {
				console.error(`合并达人${expertId}报名信息时出错:`, error);
			}
		},

		// 清除所有达人订单存储
		clearExpertOrderStorage() {
			try {
				// 1. 清除通用达人申请订单存储
				uni.removeStorageSync("expertAppliedOrders");

				// 2. 清除002账号相关存储
				uni.removeStorageSync("expertAppliedOrders_002");
				uni.removeStorageSync("expertAppliedOrders_002_002");

				// 3. 清除003账号相关存储
				uni.removeStorageSync("expertAppliedOrders_003");
				uni.removeStorageSync("expertAppliedOrders_003_003");

				// 4. 清除全局数据中的expertAppliedOrders
				if (app.expertAppliedOrders) {
					app.expertAppliedOrders = {};
				}

				console.log("已清除所有达人订单存储");

				// 显示成功提示
				uni.showToast({
					title: "清除成功",
					icon: "success",
				});
			} catch (e) {
				console.error("清除达人订单存储失败:", e);
				uni.showToast({
					title: "清除失败",
					icon: "none",
				});
			}
		},

		// 确保当前用户显示在报名列表中（用于后端数据不完整的情况）
		ensureCurrentUserInAppliedExperts() {
			if (!this.order) return;
			
			// 确保appliedExperts存在
			if (!this.order.appliedExperts) {
				this.order.appliedExperts = [];
			}
			
			// 获取当前用户信息
			const currentUserId = app.currentUserId;
			
			// 检查当前用户是否已在列表中
			const userExists = this.order.appliedExperts.some(
				expert => expert.id === currentUserId || expert.memberId === currentUserId
			);
			
			// 如果不在列表中且当前用户已报名，则添加
			if (!userExists && this.isApplied) {
				// 获取用户信息
				let userInfo;
				try {
					userInfo = uni.getStorageSync('userInfo') || {};
				} catch (e) {
					userInfo = {};
				}
				
				// 已移除测试账号逻辑，使用真实用户信息或默认值
			
			// 添加到报名列表
			this.order.appliedExperts.push({
				id: currentUserId,
				memberId: currentUserId,
				name: userInfo.nickname || app.globalData.memberInfo?.nickname || '当前用户',
				avatar: userInfo.avatar || app.globalData.memberInfo?.avatar || '/images/default-avatar.png',
				applyTime: new Date().toISOString(),
				applyTimeFormatted: '刚刚'
			});
				
				console.log("已将当前用户添加到报名列表");
			}
		},

		// 格式化活动时间
		formatActivityTime(startTime, endTime) {
			if (!startTime) return '';
			
			try {
				const start = new Date(startTime);
				const end = endTime ? new Date(endTime) : null;
				
				const formatDate = (date) => {
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					return `${year}-${month}-${day}`;
				};
				
				const formatTime = (date) => {
					const hours = String(date.getHours()).padStart(2, '0');
					const minutes = String(date.getMinutes()).padStart(2, '0');
					return `${hours}:${minutes}`;
				};
				
				if (end) {
					const startDate = formatDate(start);
					const endDate = formatDate(end);
					const startTime = formatTime(start);
					const endTime = formatTime(end);
					
					if (startDate === endDate) {
						return `${startDate} ${startTime}-${endTime}`;
					} else {
						return `${startDate} ${startTime} - ${endDate} ${endTime}`;
					}
				} else {
					return `${formatDate(start)} ${formatTime(start)}`;
				}
			} catch (error) {
				console.error('格式化活动时间失败:', error);
				return startTime;
			}
		},

		// 格式化持续时间
		formatDuration(duration) {
			if (!duration) return '';
			
			// 如果duration是数字，假设单位是小时
			if (typeof duration === 'number') {
				if (duration < 1) {
					return `${duration * 60}分钟`;
				} else if (duration === 1) {
					return '1小时';
				} else {
					return `${duration}小时`;
				}
			}
			
			// 如果duration是字符串，直接返回
			return duration;
		},

		// 格式化性别要求
		formatGender(sexRequire) {
			if (!sexRequire && sexRequire !== 0) return '不限';
			
			switch (sexRequire) {
				case 0:
				case '0':
					return '不限';
				case 1:
				case '1':
					return '男';
				case 2:
				case '2':
					return '女';
				default:
					return '不限';
			}
		},

		// 获取选中专家姓名
		getSelectedExpertName() {
			if (!this.order || !this.order.selectedList || this.order.selectedList.length === 0) {
				return '';
			}
			return this.order.selectedList[0].talentName || '';
		},

		// 获取选中专家ID
		getSelectedExpertId() {
			if (!this.order || !this.order.selectedList || this.order.selectedList.length === 0) {
				return null;
			}
			return this.order.selectedList[0].talentId || null;
		},
	},
};
</script>

<style scoped>
.container {
	min-height: 100vh;
	background-color: #f7f7f7;
	padding-bottom: 120rpx;
}

.order-card {
	padding: 20rpx 30rpx;
}

.status-bar {
	margin-bottom: 24rpx;
}

.status {
	display: inline-block;
	padding: 8rpx 24rpx;
	border-radius: 999rpx;
	font-size: 24rpx;
}

.status.pending {
	background: #fff5e6;
	color: #ff8c00;
}

.info-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.info-title {
	font-size: 30rpx;
	font-weight: 500;
	margin-bottom: 20rpx;
}

.info-row {
	display: flex;
	margin-bottom: 20rpx;
}

.info-row:last-child {
	margin-bottom: 0;
}

.label {
	width: 150rpx;
	color: #666;
	font-size: 28rpx;
}

.value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	line-height: 1.5;
}

.highlight {
	color: #ff8c00 !important;
	font-weight: 500;
	font-size: 30rpx;
}

.duration {
	color: #666;
	font-size: 26rpx;
	margin-left: 10rpx;
	font-weight: 500;
}

.location .detail {
	font-size: 26rpx;
	color: #666;
	margin-top: 6rpx;
	letter-spacing: 1rpx;
}

.fee-content {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.fee-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 28rpx;
	color: #333;
}

.fee-detail {
	display: flex;
	align-items: baseline;
	gap: 6rpx;
}

.amount {
	font-size: 36rpx;
	color: #ff8c00;
	font-weight: bold;
}

.unit {
	font-size: 24rpx;
	color: #999;
}

.total {
	padding-top: 16rpx;
	border-top: 1rpx solid #eee;
	font-weight: 500;
}

.total-amount {
	color: #ff8c00;
	font-size: 36rpx;
	font-weight: bold;
}

.per-person {
	font-size: 24rpx;
	color: #666;
	margin-left: 8rpx;
	font-weight: normal;
}

.description {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

.bottom-button {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 24rpx 30rpx;
	background: #fff;
	border-top: 1rpx solid #eee;
}

.apply-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(to right, #ff7e5f, #ff5c5c);
	color: white;
	line-height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	box-shadow: 0 6rpx 10rpx rgba(255, 92, 92, 0.3);
}

.apply-btn:active {
	opacity: 0.9;
	transform: scale(0.98);
}

.status-group {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.applicants-count {
	font-size: 24rpx;
	color: #666;
	background: #f5f5f5;
	padding: 4rpx 12rpx;
	border-radius: 999rpx;
}

/* 订单编号样式 */
.order-id-row {
	background: #f8f8f8;
	padding: 16rpx;
	border-radius: 8rpx;
	margin-bottom: 24rpx;
}

.order-id-container {
	flex: 1;
	display: inline-flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	overflow: hidden;
}

.order-id-value {
	font-family: monospace;
	color: #666;
	flex: 1;
	word-break: break-all;
	padding-right: 10rpx;
}

.copy-btn {
	font-size: 24rpx;
	color: #ff8c00;
	padding: 4rpx 12rpx;
	border-radius: 999rpx;
	white-space: nowrap;
	flex-shrink: 0;
}

/* 报名信息部分样式 */
.applicants-section {
	margin-top: 24rpx;
	padding-top: 24rpx;
	border-top: 1rpx solid #eee;
	display: flex;
	flex-direction: column;
}

/* 新的头像展示区域样式 */
.info-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.applicants-count {
	font-size: 24rpx;
	color: #666;
	margin-left: 10rpx;
}

.view-all {
	font-size: 24rpx;
	color: #ffd700;
	font-weight: 500;
}

/* 头像展示区域 */
.experts-avatars-section {
	padding: 20rpx;
	background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
	border-radius: 16rpx;
	border: 1px solid #ffd700;
	box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.1);
}

.avatars-container {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-bottom: 20rpx;
}

.avatar-item {
	position: relative;
	width: 80rpx;
	height: 80rpx;
}

.expert-avatar-small {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 3px solid #ffd700;
	box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
	transition: all 0.3s ease;
}

.expert-avatar-small:active {
	transform: scale(0.95);
}

.selected-badge {
	position: absolute;
	top: -5rpx;
	right: -5rpx;
	width: 24rpx;
	height: 24rpx;
	background: #ffd700;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16rpx;
	color: #333;
	font-weight: bold;
	border: 2px solid #fff;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.more-avatar-item {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	border: 3px solid #fff;
	box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
	cursor: pointer;
	transition: all 0.3s ease;
}

.more-avatar-item:active {
	transform: scale(0.95);
}

.more-count {
	font-size: 20rpx;
	color: #333;
	font-weight: bold;
}

.view-all-btn {
	padding: 16rpx 24rpx;
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	border-radius: 25rpx;
	text-align: center;
	box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
	transition: all 0.3s ease;
}

.view-all-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 6rpx rgba(255, 215, 0, 0.3);
}

.view-all-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 600;
}

.applicants-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 16rpx;
}

.applicants-avatars {
	padding: 10rpx 0;
}

.avatar-stack {
	display: flex;
	position: relative;
	height: 70rpx;
	margin-left: 10rpx;
}

.applicant-avatar {
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	border: 2rpx solid #fff;
	position: absolute;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.more-avatars {
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	background: #f8f8f8;
	color: #666;
	font-size: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	left: 240rpx;
	border: 2rpx solid #fff;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	z-index: 9;
}

.no-applicants {
	color: #999;
	font-size: 26rpx;
	padding: 10rpx 0;
}

/* 移除原有的状态相关样式 */
.status-bar,
.status-group,
.status {
	display: none;
}

/* 地点信息样式 */
.location-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.location-content {
		padding: 20rpx;
		background: #f9f9f9;
		border-radius: 12rpx;
		border-left: 4rpx solid #ffd700;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

.location-info {
	flex: 1;
}

.location-area {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 8rpx;
}

.location-detail {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	letter-spacing: 1rpx;
	margin-top: 4rpx;
}

.location-full {
	font-size: 28rpx;
	color: #333;
}

.location-nav-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 10rpx;
}

.nav-icon {
		font-size: 40rpx;
		color: #ffd700;
	}

/* 服务项目显示样式 */
.service-display {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.service-type {
	color: #444;
	background-color: #f5f5f5;
	padding: 8rpx 20rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	font-weight: 500;
}

.service-subtype {
		color: #b8860b;
		font-weight: bold;
		font-size: 30rpx;
		background-color: #fff9e6;
		padding: 8rpx 20rpx;
		border-radius: 8rpx;
		text-shadow: 0 1rpx 2rpx rgba(255, 215, 0, 0.15);
	}

.service-connector {
	color: #999;
	font-size: 24rpx;
	margin: 0 6rpx;
	font-weight: bold;
}

/* 加载中样式 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
}

.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f5f5f5;
		border-top: 4rpx solid #ffd700;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

/* 状态样式 */
.status-section {
	margin-bottom: 24rpx;
}

.status-content {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.status-row {
	display: flex;
	align-items: center;
}

.status-value {
	padding: 4rpx 16rpx;
	border-radius: 999rpx;
	font-size: 24rpx;
	background-color: #f5f5f5;
}

.status-value.published {
	color: #ff9800;
	background-color: rgba(255, 152, 0, 0.1);
}

.status-value.expertSelected {
	color: #2196f3;
	background-color: rgba(33, 150, 243, 0.1);
}

.status-value.orderStarted {
	color: #4caf50;
	background-color: rgba(76, 175, 80, 0.1);
}

.status-value.completed {
	color: #9c27b0;
	background-color: rgba(156, 39, 176, 0.1);
}

.status-value.evaluated {
	color: #607d8b;
	background-color: rgba(96, 125, 139, 0.1);
}

.status-value.applied {
	color: #ff9800;
	background-color: rgba(255, 152, 0, 0.1);
}

.status-value.chosen {
	color: #2196f3;
	background-color: rgba(33, 150, 243, 0.1);
}

.status-value.departureConfirmed {
	color: #4caf50;
	background-color: rgba(76, 175, 80, 0.1);
}

.status-value.arrivalConfirmed {
	color: #ff5722;
	background-color: rgba(255, 87, 34, 0.1);
}

.status-value.serviceCompleted {
	color: #9c27b0;
	background-color: rgba(156, 39, 176, 0.1);
}

/* 报名达人列表样式 */
.applicants-section {
	margin-bottom: 20rpx;
}

.applicants-count {
	font-size: 26rpx;
	color: #666;
	font-weight: normal;
	margin-left: 10rpx;
}

.view-all {
	font-size: 26rpx;
	color: #2196f3;
	float: right;
	font-weight: normal;
}

.expert-list {
	padding: 10rpx 0;
}

.expert-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.expert-item:last-child {
	border-bottom: none;
}

.expert-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.expert-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.expert-details {
	display: flex;
	flex-direction: column;
}

.expert-name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 6rpx;
}

.expert-label {
	font-size: 22rpx;
	color: #2196f3;
	background-color: rgba(33, 150, 243, 0.1);
	padding: 2rpx 10rpx;
	border-radius: 4rpx;
	display: inline-block;
}

.select-btn {
	padding: 10rpx 20rpx;
	font-size: 24rpx;
	background-color: #2196f3;
	color: #fff;
	border-radius: 8rpx;
	margin-left: 20rpx;
	min-width: 120rpx;
	text-align: center;
}

.more-experts {
	text-align: center;
	padding: 20rpx 0;
	color: #666;
	font-size: 26rpx;
}

/* 达人通知区域样式 */
.expert-notice {
	background-color: #f8f8ff;
	border-left: 4rpx solid #4caf50;
}

.notice-content {
	display: flex;
	flex-direction: column;
	padding: 10rpx 0;
}

.notice-status {
	font-size: 30rpx;
	color: #4caf50;
	font-weight: 500;
	margin-bottom: 10rpx;
}

.notice-tip {
	font-size: 26rpx;
	color: #666;
}

/* 底部按钮区域 */
.bottom-buttons {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background-color: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	z-index: 100;
}

/* 主按钮样式 */
.primary-btn {
	flex: 1;
	height: 88rpx;
	line-height: 88rpx;
	background: linear-gradient(135deg, #ffd700, #ffed4e);
	color: #fff;
	font-size: 32rpx;
	font-weight: 500;
	text-align: center;
	border-radius: 44rpx;
	margin: 0 10rpx;
	box-shadow: 0 4rpx 8rpx rgba(255, 140, 0, 0.2);
}

.primary-btn.selected-expert {
	background-color: #07c160;
	display: flex;
	align-items: center;
	justify-content: center;
}

.selected-icon {
	margin-right: 10rpx;
	font-weight: bold;
}

/* 次要按钮样式 */
.secondary-btn {
	width: 200rpx;
	height: 88rpx;
	line-height: 88rpx;
	background-color: #f2f2f2;
	color: #666;
	font-size: 32rpx;
	text-align: center;
	border-radius: 44rpx;
}

/* 禁用按钮样式 */
.disabled-btn {
	flex: 1;
	height: 88rpx;
	line-height: 88rpx;
	background-color: #f2f2f2;
	color: #999;
	font-size: 32rpx;
	text-align: center;
	border-radius: 44rpx;
	margin: 0 10rpx;
}

/* 查看达人按钮 */
.view-experts {
	position: relative;
}

/* 通知红点 */
.notification-dot {
	position: absolute;
	top: 10rpx;
	right: 36rpx;
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background-color: #ff4d4f;
}

/* 报名达人区域 */
.applicants-section {
	margin-bottom: 24rpx;
}

.applicants-count {
	font-size: 26rpx;
	color: #999;
	margin-left: 8rpx;
}

.view-all {
	font-size: 26rpx;
	color: #ff8c00;
	float: right;
}

.expert-list {
	margin-top: 20rpx;
}

.applicants-progress {
	margin: 20rpx 0;
	padding: 10rpx;
}

.progress-text {
	display: flex;
	justify-content: space-between;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.progress-percent {
	color: #ff8c00;
	font-weight: bold;
}

.progress-bar {
	height: 10rpx;
	background-color: #f0f0f0;
	border-radius: 10rpx;
	overflow: hidden;
}

.progress-inner {
	height: 100%;
	background-color: #ff8c00;
	border-radius: 10rpx;
	transition: width 0.3s;
}

.apply-time {
	font-size: 24rpx;
	color: #666;
	font-weight: 400;
	line-height: 1.4;
}

.empty-experts {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx 0;
	color: #999;
	font-size: 26rpx;
	border-top: 1px dashed #eee;
	margin-top: 20rpx;
}

/* +N样式 */
.more-experts-badge {
	display: flex;
	align-items: center;
	margin-top: 20rpx;
	padding: 10rpx;
	cursor: pointer;
	border-top: 1px dashed #eee;
	padding-top: 20rpx;
}

.more-avatar {
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(135deg, #ffd700, #ffed4e);
		color: #333;
		font-weight: bold;
		font-size: 24rpx;
	}
	
	.more-text {
		margin-left: 16rpx;
		font-size: 26rpx;
		color: #ffd700;
		font-weight: 600;
	}

/* 暂无达人报名样式 */
.no-experts-tip {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
}

.empty-icon {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 20rpx;
	opacity: 0.5;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.empty-subtext {
	font-size: 24rpx;
	color: #bbb;
}

.expert-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	position: relative;
}

.expert-item:last-child {
	border-bottom: none;
}

.expert-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.expert-avatar-wrapper {
	position: relative;
	margin-right: 24rpx;
}

.expert-avatar {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
	border: 2rpx solid #f0f0f0;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.online-status {
	position: absolute;
	bottom: 2rpx;
	right: 2rpx;
	width: 20rpx;
	height: 20rpx;
	background-color: #52c41a;
	border: 2rpx solid #fff;
	border-radius: 50%;
}

.expert-details {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-width: 0;
}

.expert-name-row {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.expert-name {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-right: 12rpx;
}

.expert-label {
	display: inline-block;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 500;
}

.expert-label.selected {
	color: #52c41a;
	background-color: #f6ffed;
	border: 1rpx solid #b7eb8f;
}

.expert-meta {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.expert-rating {
	font-size: 24rpx;
	color: #faad14;
	font-weight: 500;
}

.select-btn {
		font-size: 26rpx;
		color: #333;
		background: linear-gradient(135deg, #ffd700, #ffed4e);
		padding: 12rpx 32rpx;
		border-radius: 32rpx;
		border: none;
		box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
		font-weight: 600;
		transition: all 0.3s ease;
	}
	
	.select-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.4);
	}

.more-experts {
	text-align: center;
	padding: 20rpx 0;
	color: #1890ff;
	font-size: 28rpx;
}

/* 订单流程样式 */
.flow-section {
	margin-top: 24rpx;
}

.flow-content {
	display: flex;
	flex-direction: column;
}

.flow-item {
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.flow-item:last-child {
	border-bottom: none;
}

.flow-time {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 8rpx;
}

.flow-state {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 4rpx;
}

.flow-remark {
	font-size: 26rpx;
	color: #666;
}

.no-flow {
	color: #999;
	font-size: 26rpx;
	padding: 20rpx 0;
	text-align: center;
}

/* 发布时间样式 */
.publish-time {
	color: #666;
	font-size: 24rpx;
}

/* 等待达人到达提示 */
.waiting-tip {
	font-size: 26rpx;
	color: #ff9800;
	background-color: #fff8e1;
	padding: 12rpx 24rpx;
	border-radius: 30rpx;
	border: 1rpx dashed #ffb74d;
	margin: 20rpx 0;
	text-align: center;
	display: block;
	width: 80%;
	margin-left: auto;
	margin-right: auto;
}

/* 操作区域样式 */
.action-area {
	padding: 30rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.action-btn {
	margin: 20rpx 0;
	width: 80%;
	font-size: 30rpx;
	border-radius: 40rpx;
	color: #fff;
}

.start-order {
	background-color: #4caf50;
}

.confirm-complete {
	background-color: #ff9800;
}

.evaluate {
	background-color: #9c27b0;
}

.status-message {
	font-size: 28rpx;
	color: #666;
	margin: 20rpx 0;
	text-align: center;
}

/* 底部等待提示 */
.bottom-tip {
	width: auto;
	font-size: 26rpx;
	margin: 0 20rpx 20rpx;
	display: inline-block;
}

/* 查看流程详情按钮 */
.view-flow-btn {
	font-size: 24rpx;
	color: #1890ff;
	float: right;
	font-weight: normal;
}

.flow-section {
	position: relative;
	cursor: pointer;
}

.flow-section:active {
	opacity: 0.8;
}

.more-flow {
	text-align: center;
	padding: 20rpx 0;
	color: #1890ff;
	font-size: 28rpx;
}

/* 订单流程弹窗 */
.order-flow-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1001;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.flow-modal-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
}

.flow-modal-content {
	position: relative;
	width: 90%;
	max-height: 80%;
	background: #fff;
	border-radius: 16rpx;
	padding: 40rpx 30rpx;
	z-index: 1002;
	overflow-y: auto;
}

.flow-modal-title {
	font-size: 36rpx;
	color: #333;
	text-align: center;
	font-weight: 500;
	margin-bottom: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	padding-bottom: 20rpx;
}

.flow-entries {
	padding: 20rpx 0;
}

.flow-entry {
	display: flex;
	margin-bottom: 30rpx;
	position: relative;
	padding-left: 30rpx;
}

.flow-entry::before {
	content: "";
	position: absolute;
	left: 10rpx;
	top: 0;
	bottom: 0;
	width: 2rpx;
	background: #e8e8e8;
}

.flow-entry:last-child::before {
	bottom: 50%;
}

.flow-entry::after {
	content: "";
	position: absolute;
	left: 6rpx;
	top: 16rpx;
	width: 10rpx;
	height: 10rpx;
	border-radius: 50%;
	background: #e8e8e8;
}

.flow-entry.completed::after {
	background: #52c41a;
}

.flow-entry.current::after {
	background: #1890ff;
	width: 14rpx;
	height: 14rpx;
	left: 4rpx;
}

.flow-time {
	font-size: 24rpx;
	color: #999;
	min-width: 200rpx;
}

.flow-info {
	flex: 1;
	padding-left: 20rpx;
}

.flow-state {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 6rpx;
}

.flow-remark {
	font-size: 26rpx;
	color: #666;
}

.flow-role {
	font-size: 24rpx;
	color: #999;
	margin-top: 4rpx;
}

.flow-role.user {
	color: #1890ff;
}

.flow-role.expert {
	color: #52c41a;
}

.flow-role.system {
	color: #faad14;
}

.flow-modal-close {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	font-size: 40rpx;
	color: #999;
	line-height: 1;
}

.empty-flow {
	text-align: center;
	color: #999;
	font-size: 28rpx;
	padding: 40rpx 0;
}

/* 错误容器样式 */
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 300rpx;
	width: 100%;
	margin-top: 100rpx;
}

.error-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background-color: #ff4d4f;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 50rpx;
	margin-bottom: 20rpx;
	font-weight: bold;
}

.error-text {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 30rpx;
	text-align: center;
}

.retry-button {
	background-color: #1890ff;
	color: white;
	font-size: 28rpx;
	padding: 10rpx 40rpx;
	border-radius: 32rpx;
	border: none;
	width: auto;
	min-width: 200rpx;
}

/* 已选择达人区域样式 */
.selected-expert-section {
	background-color: #f8fffd;
	border-left: 4rpx solid #07c160;
}

.selected-expert-info {
	padding: 10rpx 0;
}

/* 已选择达人头部样式 */
.selected-expert-header {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	margin-bottom: 20rpx;
	border-bottom: 1rpx solid #eee;
}

.selected-expert-avatar {
	width: 88rpx;
	height: 88rpx;
	border-radius: 44rpx;
	margin-right: 20rpx;
	border: 2rpx solid #07c160;
}

.selected-expert-details {
	flex: 1;
}

.selected-expert-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 4rpx;
}

.selected-expert-id {
	font-size: 24rpx;
	color: #999;
}

.selected-status {
	font-size: 22rpx;
	color: #07c160;
	background-color: rgba(7, 193, 96, 0.1);
	padding: 4rpx 16rpx;
	border-radius: 16rpx;
	border: 1rpx solid rgba(7, 193, 96, 0.2);
}

.expert-row {
	display: flex;
	margin-bottom: 16rpx;
}

.expert-row:last-child {
	margin-bottom: 0;
}

.expert-row .label {
	width: 150rpx;
	color: #666;
	font-size: 28rpx;
}

.expert-row .value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.expert-row .status-text {
	color: #07c160;
}

/* 被选中达人信息样式 */
.selected-expert-info {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin: 20rpx 0;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-header {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
	padding-left: 10rpx;
	border-left: 6rpx solid #3e95ff;
}

.expert-container {
	display: flex;
	align-items: center;
	padding: 15rpx 10rpx;
}

.expert-avatar {
	width: 90rpx;
	height: 90rpx;
	border-radius: 50%;
	margin-right: 20rpx;
	border: 1px solid #eee;
}

.expert-details {
	flex: 1;
}

.expert-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 6rpx;
}

.expert-id {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 6rpx;
}

.expert-status {
	font-size: 24rpx;
	color: #3e95ff;
	background-color: rgba(62, 149, 255, 0.1);
	display: inline-block;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
}

/* 已选中达人提示样式 */
.applicants-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10rpx 0;
	margin-bottom: 12rpx;
}

.section-title {
	font-size: 26rpx;
	color: #666;
}

.selected-expert {
	font-size: 26rpx;
	color: #3e95ff;
	font-weight: 500;
}

/* 需求描述信息样式 */
.description-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.description-content {
	padding: 20rpx;
	background: #f9f9f9;
	border-radius: 12rpx;
	border-left: 4rpx solid #ffed4e;
	display: flex;
	flex-direction: column;
}

.description-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 发布人信息样式 */
.publisher-section {
	display: flex;
	padding: 24rpx;
	background: linear-gradient(135deg, #ffd700, #ffed4e);
	color: #fff;
	position: relative;
	align-items: center;
	margin-bottom: 24rpx;
	border-radius: 16rpx;
}

.publisher-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	overflow: hidden;
	border: 2rpx solid rgba(255, 255, 255, 0.6);
	margin-right: 20rpx;
	flex-shrink: 0;
}

.publisher-avatar image {
	width: 100%;
	height: 100%;
}

.publisher-info {
	flex: 1;
	overflow: hidden;
}

.publisher-name {
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 4rpx;
	max-width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.publisher-id {
	font-size: 22rpx;
	opacity: 0.8;
	margin-bottom: 4rpx;
}

.publish-time {
	font-size: 22rpx;
	opacity: 0.8;
}

.order-status-badge {
	background-color: rgba(255, 255, 255, 0.25);
	color: #fff;
	font-size: 24rpx;
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	position: absolute;
	right: 24rpx;
	top: 24rpx;
}
</style>

