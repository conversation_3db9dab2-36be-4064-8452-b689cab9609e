/**
 * 存储工具模块 - 统一管理小程序本地存储操作
 */

import { safeEqual, safeGet } from './safe';
import { OrderState } from './order-state';

// 常用存储键
export const STORAGE_KEYS = {
  // 订单相关
  PUBLISHED_ORDERS: 'publishedOrders',
  PUBLISHED_ORDERS_003: 'publishedOrders_003',
  EXPERT_ORDERS: 'expertOrders',
  EXPERT_ORDERS_003: 'expertOrders_003',
  EXPERT_APPLIED_ORDERS: 'expertAppliedOrders',
  EXPERT_APPLIED_ORDERS_003: 'expertAppliedOrders_003',
  CURRENT_ORDER_DETAIL: 'currentOrderDetail',

  // 用户相关
  USER_ROLE: 'userRole',
  CURRENT_USER_ID: 'currentUserId',
  IS_VERIFIED: 'isVerified',
  HAS_EXPERT_ROLE: 'hasExpertRole',
  CURRENT_EXPERT_INFO: 'currentExpertInfo',

  // 系统相关
  LOGS: 'logs',
  COIN_BALANCE: 'coinBalance',
  ORDER_UPDATED: 'orderUpdated'
};

/**
 * 安全获取存储数据，自动处理JSON解析
 * @param {string} key - 存储键名
 * @param {*} defaultValue - 默认值，当获取失败时返回
 * @returns {*} 解析后的数据或默认值
 */
export const getStorageData = (key, defaultValue = null) => {
  try {
    const data = uni.getStorageSync(key);
    if (data === null || data === undefined || data === '') {
      return defaultValue;
    }

    // 如果是字符串且看起来像JSON，尝试解析
    if (typeof data === 'string' && (data.startsWith('[') || data.startsWith('{'))) {
      try {
        return JSON.parse(data);
      } catch (e) {
        console.error(`解析${key}数据失败:`, e);
        return defaultValue;
      }
    }

    return data;
  } catch (e) {
    console.error(`获取${key}数据失败:`, e);
    return defaultValue;
  }
}

/**
 * 安全设置存储数据，自动处理JSON序列化
 * @param {string} key - 存储键名
 * @param {*} data - 要存储的数据
 * @returns {boolean} 是否成功
 */
export const setStorageData = (key, data) => {
  try {
    // 对象和数组需要序列化
    if (typeof data === 'object' && data !== null) {
      uni.setStorageSync(key, JSON.stringify(data));
    } else {
      uni.setStorageSync(key, data);
    }
    return true;
  } catch (e) {
    console.error(`设置${key}数据失败:`, e);
    return false;
  }
}

/**
 * 获取发布的订单
 * @param {string} userId - 用户ID，默认从全局数据获取
 * @returns {Array} 订单数组
 */
export const getPublishedOrders = (userId) => {
  const app = getApp();
  const currentUserId = userId || app.globalData.currentUserId;
  const storageKey = currentUserId === '003' ? STORAGE_KEYS.PUBLISHED_ORDERS_003 : STORAGE_KEYS.PUBLISHED_ORDERS;

  return getStorageData(storageKey, []);
}

/**
 * 保存发布的订单
 * @param {Array} orders - 订单数组
 * @param {string} userId - 用户ID，默认从全局数据获取
 * @returns {boolean} 是否成功
 */
export const savePublishedOrders = (orders, userId) => {
  const app = getApp();
  const currentUserId = userId || app.globalData.currentUserId;
  const storageKey = currentUserId === '003' ? STORAGE_KEYS.PUBLISHED_ORDERS_003 : STORAGE_KEYS.PUBLISHED_ORDERS;

  return setStorageData(storageKey, orders);
}

/**
 * 获取达人的订单（工作台）
 * @param {string} expertId - 达人ID
 * @returns {Array} 达人订单数组
 */
export const getExpertOrders = (expertId) => {
  const app = getApp();
  const currentUserId = expertId || app.globalData.currentUserId;
  const storageKey = currentUserId === '003' ? STORAGE_KEYS.EXPERT_ORDERS_003 : STORAGE_KEYS.EXPERT_ORDERS;

  return getStorageData(storageKey, []);
}

/**
 * 保存达人的订单（工作台）
 * @param {Array} orders - 订单数组
 * @param {string} expertId - 达人ID
 * @returns {boolean} 是否成功
 */
export const saveExpertOrders = (orders, expertId) => {
  const app = getApp();
  const currentUserId = expertId || app.globalData.currentUserId;
  const storageKey = currentUserId === '003' ? STORAGE_KEYS.EXPERT_ORDERS_003 : STORAGE_KEYS.EXPERT_ORDERS;

  return setStorageData(storageKey, orders);
}

/**
 * 获取达人申请的订单
 * @param {string} expertId - 达人ID
 * @returns {Array} 申请订单数组
 */
export const getExpertAppliedOrders = (expertId) => {
  const app = getApp();

  // 确保使用正确的专家ID
  const id = expertId || app.globalData.currentUserId;

  // 使用专用存储键，确保每个达人有自己的订单存储
  const key = `expertAppliedOrders_${id}`;

  try {
    let orders = uni.getStorageSync(key) || [];

    // 如果返回的是字符串，尝试解析为JSON
    if (typeof orders === 'string') {
      try {
        orders = JSON.parse(orders);
      } catch (e) {
        console.error(`解析${key}失败:`, e);
        orders = [];
      }
    }

    // 确保返回数组
    if (!Array.isArray(orders)) {
      console.warn(`从${key}获取的数据不是数组，返回空数组`);
      orders = [];
    }

    // 筛选只属于当前达人的订单
    return orders.filter(order =>
      !order.expertId || order.expertId === id ||
      (order.appliedExperts && order.appliedExperts.some(expert => expert.id === id))
    );
  } catch (e) {
    console.error(`获取专家订单列表失败(${key}):`, e);
    return [];
  }
}

/**
 * 保存达人申请的订单
 * @param {Array} orders - 订单数组
 * @param {string} expertId - 达人ID
 * @returns {boolean} 是否成功
 */
export const saveExpertAppliedOrders = (orders, expertId) => {
  const app = getApp();

  // 确保使用正确的专家ID
  const id = expertId || app.globalData.currentUserId;

  // 使用专用存储键，确保每个达人有自己的订单存储
  const key = `expertAppliedOrders_${id}`;

  // 确保orders是数组
  if (!Array.isArray(orders)) {
    console.error(`保存专家订单失败：orders不是数组`);
    return false;
  }

  try {
    // 在保存前确保所有订单都标记属于当前达人
    const markedOrders = orders.map(order => {
      // 如果订单没有expertId，添加当前达人ID
      if (!order.expertId) {
        return { ...order, expertId: id };
      }
      return order;
    });

    // 尝试序列化为JSON
    let jsonStr;
    try {
      jsonStr = JSON.stringify(markedOrders);
    } catch (e) {
      console.error(`序列化专家订单数据失败:`, e);
      return false;
    }

    // 保存到存储
    uni.setStorageSync(key, jsonStr);
    console.log(`已保存${markedOrders.length}个订单到${key}`);
    return true;
  } catch (e) {
    console.error(`保存专家订单失败(${key}):`, e);
    return false;
  }
}

/**
 * 获取当前订单详情
 * @returns {Object} 订单详情
 */
export const getCurrentOrderDetail = () => {
  return getStorageData(STORAGE_KEYS.CURRENT_ORDER_DETAIL, null);
}

/**
 * 保存当前订单详情
 * @param {Object} orderDetail - 订单详情
 * @returns {boolean} 是否成功
 */
export const saveCurrentOrderDetail = (orderDetail) => {
  return setStorageData(STORAGE_KEYS.CURRENT_ORDER_DETAIL, orderDetail);
}

/**
 * 根据订单ID查找订单
 * @param {string} orderId - 订单ID
 * @param {Array} locations - 要搜索的存储位置数组，默认搜索所有位置
 * @returns {Object} 找到的订单对象和它的存储位置
 */
export const findOrderById = (orderId, locations = ['published', 'expert', 'applied']) => {
  const app = getApp();
  const currentUserId = app.globalData.currentUserId;
  let result = { order: null, location: null };

  // 安全比较函数
  const compareIds = (a, b) => {
    return safeEqual(a, b);
  };

  // 检查发布的订单
  if (locations.includes('published')) {
    const publishedOrders = getPublishedOrders();
    const order = publishedOrders.find(o => compareIds(o.id, orderId) || compareIds(o.orderId, orderId));
    if (order) {
      return { order, location: STORAGE_KEYS.PUBLISHED_ORDERS };
    }

    // 003特殊检查
    if (currentUserId === '003') {
      const orders003 = getStorageData(STORAGE_KEYS.PUBLISHED_ORDERS_003, []);
      const order003 = orders003.find(o => compareIds(o.id, orderId) || compareIds(o.orderId, orderId));
      if (order003) {
        return { order: order003, location: STORAGE_KEYS.PUBLISHED_ORDERS_003 };
      }
    }
  }

  // 检查达人工作台订单
  if (locations.includes('expert')) {
    const expertOrders = getExpertOrders();
    const order = expertOrders.find(o => compareIds(o.id, orderId) || compareIds(o.orderId, orderId));
    if (order) {
      return { order, location: currentUserId === '003' ? STORAGE_KEYS.EXPERT_ORDERS_003 : STORAGE_KEYS.EXPERT_ORDERS };
    }
  }

  // 检查达人申请的订单
  if (locations.includes('applied')) {
    const appliedOrders = getExpertAppliedOrders();
    const order = appliedOrders.find(o => compareIds(o.id, orderId) || compareIds(o.orderId, orderId));
    if (order) {
      return { order, location: currentUserId === '003' ? STORAGE_KEYS.EXPERT_APPLIED_ORDERS_003 : STORAGE_KEYS.EXPERT_APPLIED_ORDERS };
    }
  }

  return result;
}

/**
 * 更新订单（在所有存储位置）
 * @param {Object} order - 要更新的订单
 * @returns {boolean} 是否成功更新了至少一个位置
 */
export const updateOrderInAllStorages = (order) => {
  const orderId = safeGet(order, 'id') || safeGet(order, 'orderId');
  if (!orderId) {
    console.error('更新订单失败: 未提供有效的订单ID');
    return false;
  }

  let updateSuccess = false;
  const app = getApp();

  // 安全比较函数
  const compareIds = (a, b) => {
    return safeEqual(a, b);
  };

  // 1. 更新发布的订单
  try {
    const publishedOrders = getPublishedOrders();
    const orderIndex = publishedOrders.findIndex(o =>
      compareIds(o.id, orderId) || compareIds(o.orderId, orderId)
    );

    if (orderIndex !== -1) {
      publishedOrders[orderIndex] = { ...publishedOrders[orderIndex], ...order };
      savePublishedOrders(publishedOrders);
      updateSuccess = true;

      // 同步更新全局数据
      app.globalData.publishedOrders = publishedOrders;
    }

    // 特殊处理003账号的发布订单
    const orders003 = getStorageData(STORAGE_KEYS.PUBLISHED_ORDERS_003, []);
    const order003Index = orders003.findIndex(o =>
      compareIds(o.id, orderId) || compareIds(o.orderId, orderId)
    );

    if (order003Index !== -1) {
      orders003[order003Index] = { ...orders003[order003Index], ...order };
      setStorageData(STORAGE_KEYS.PUBLISHED_ORDERS_003, orders003);
      updateSuccess = true;
    }
  } catch (e) {
    console.error('更新发布订单失败:', e);
  }

  // 2. 更新达人订单（工作台）
  const expertStorageUpdated = updateExpertOrderStorage(order, ['002', '003']);
  if (expertStorageUpdated) {
    updateSuccess = true;
  }

  // 3. 更新达人申请订单
  const appliedStorageUpdated = updateExpertAppliedOrderStorage(order, ['002', '003']);
  if (appliedStorageUpdated) {
    updateSuccess = true;
  }

  return updateSuccess;
}

/**
 * 更新达人的工作台订单存储
 * @param {Object} order - 要更新的订单
 * @param {Array} expertIds - 要更新的达人ID数组
 * @returns {boolean} 是否成功
 */
export const updateExpertOrderStorage = (order, expertIds = []) => {
  const orderId = safeGet(order, 'id') || safeGet(order, 'orderId');
  if (!orderId) {
    console.error('更新达人工作台订单失败: 未提供有效的订单ID');
    return false;
  }

  let updateSuccess = false;

  // 确保默认更新当前用户
  const app = getApp();
  if (!expertIds || expertIds.length === 0) {
    expertIds = [app.globalData.currentUserId];
  }

  // 为每个达人ID更新存储
  expertIds.forEach(expertId => {
    try {
      const expertOrders = getExpertOrders(expertId);
      const orderIndex = expertOrders.findIndex(o =>
        safeEqual(o.id, orderId) || safeEqual(o.orderId, orderId)
      );

      if (orderIndex !== -1) {
        expertOrders[orderIndex] = { ...expertOrders[orderIndex], ...order };
        saveExpertOrders(expertOrders, expertId);
        updateSuccess = true;
      }
    } catch (e) {
      console.error(`更新达人${expertId}工作台订单失败:`, e);
    }
  });

  return updateSuccess;
}

/**
 * 更新达人的申请订单存储
 * @param {Object} order - 要更新的订单
 * @param {Array} expertIds - 要更新的达人ID数组
 * @param {boolean} isSelected - 当前更新的达人是否被选中（可选）
 * @returns {boolean} 是否成功
 */
export const updateExpertAppliedOrderStorage = (order, expertIds = [], isSelected = null) => {
  const orderId = safeGet(order, 'id') || safeGet(order, 'orderId');
  if (!orderId) {
    console.error('更新达人申请订单失败: 未提供有效的订单ID');
    return false;
  }

  // 确保orderId是字符串格式
  const normalizedOrderId = orderId.toString();
  console.log(`更新达人申请订单, 订单ID: ${normalizedOrderId}`);

  let updateSuccess = false;

  // 确保默认更新当前用户
  const app = getApp();
  let idsToUpdate = expertIds && expertIds.length > 0 ? expertIds : [app.globalData.currentUserId];

  // 从订单中获取选中的达人ID和信息
  const selectedExpertId = safeGet(order, 'selectedExpertId');
  const selectedExpertName = safeGet(order, 'selectedExpertName') || '未知达人';
  const selectedExpertAvatar = safeGet(order, 'selectedExpertAvatar') || '/images/default-avatar.png';

  console.log(`更新订单 ${normalizedOrderId} 的达人状态，选中达人ID: ${selectedExpertId}, 名称: ${selectedExpertName}`);
  console.log(`将为以下达人ID更新订单状态: ${idsToUpdate.join(', ')}`);

  // 导入订单状态常量
  const OrderState = {
    CHOSEN: 'chosen',
    FAILED: 'failed'
  };

  // 为每个达人ID更新存储
  idsToUpdate.forEach(expertId => {
    try {
      console.log(`处理达人 ${expertId} 的订单状态更新`);

      // 使用当前达人的专属存储键
      const key = `expertAppliedOrders_${expertId}`;

      // 获取达人的订单列表
      let appliedOrders = [];
      try {
        const appliedOrdersStr = uni.getStorageSync(key);
        if (appliedOrdersStr) {
          // 尝试解析JSON
          if (typeof appliedOrdersStr === 'string') {
            try {
              appliedOrders = JSON.parse(appliedOrdersStr);
            } catch (parseError) {
              console.error(`解析达人${expertId}的订单列表失败:`, parseError);
              appliedOrders = [];
            }
          } else if (Array.isArray(appliedOrdersStr)) {
            appliedOrders = appliedOrdersStr;
          }
        }
      } catch (e) {
        console.error(`读取达人${expertId}的订单列表失败:`, e);
      }

      // 确保是数组
      if (!Array.isArray(appliedOrders)) {
        console.warn(`达人${expertId}的订单列表不是数组，初始化为空数组`);
        appliedOrders = [];
      }

      const orderIndex = appliedOrders.findIndex(o =>
        safeEqual(o.id, normalizedOrderId) || safeEqual(o.orderId, normalizedOrderId)
      );

      if (orderIndex !== -1) {
        // 创建订单的深拷贝
        const updatedOrder = { ...appliedOrders[orderIndex], ...order };

        // 根据达人是否被选中设置正确的状态
        const thisExpertSelected = isSelected !== null ?
          isSelected : // 如果明确指定了选中状态，使用指定的状态
          (selectedExpertId && safeEqual(expertId, selectedExpertId)); // 否则根据订单中的selectedExpertId判断

        // 确保显示选中达人信息
        updatedOrder.selectedExpertId = selectedExpertId;
        updatedOrder.selectedExpertName = selectedExpertName;
        updatedOrder.selectedExpertAvatar = selectedExpertAvatar;

        // 确保标记订单属于当前达人
        updatedOrder.expertId = expertId;

        if (thisExpertSelected) {
          console.log(`达人${expertId}被选中，更新为"已被选中"状态`);

          // 更新状态为"已被选中"（可进入下一阶段 - 确认出发）
          updatedOrder.status = OrderState.CHOSEN;
          updatedOrder.statusText = '已被选中';
          updatedOrder.expertStatus = OrderState.CHOSEN;
          updatedOrder.expertStatusText = '已被选中';
          updatedOrder.selected = true;
          updatedOrder.isSelected = true;
          updatedOrder.displayStyle = 'highlight'; // 高亮显示
          updatedOrder.selectable = true; // 可进入下一阶段
          updatedOrder.canConfirmDeparture = true; // 可以确认出发
          updatedOrder.showConfirmButton = true; // 显示确认按钮

          // 添加达人状态提示信息
          updatedOrder.expertStatusTip = '恭喜！您已被选为此订单的达人，请确认出发';

          // 添加选中时间
          if (!updatedOrder.selectionTime) {
            updatedOrder.selectionTime = new Date().toISOString();
          }

          // 确保订单进度正确
          updatedOrder.expertProgress = 2; // 进度为已被选中

          // 显示自己是被选中的达人
          updatedOrder.isCurrentExpertSelected = true;
          updatedOrder.showSelectedExpertInfo = true;
        } else {
          console.log(`达人${expertId}未被选中，更新为"接单失败"状态`);

          // 更新状态为"接单失败"（不可进入下一阶段）
          updatedOrder.status = 'orderFailed';
          updatedOrder.statusText = '接单失败';
          updatedOrder.expertStatus = 'failed';
          updatedOrder.expertStatusText = '未被选中';
          updatedOrder.selected = false;
          updatedOrder.isSelected = false;
          updatedOrder.displayStyle = 'gray'; // 灰色显示
          updatedOrder.selectable = false; // 不可进入下一阶段
          updatedOrder.canConfirmDeparture = false; // 不可确认出发
          updatedOrder.showConfirmButton = false; // 不显示确认按钮
          updatedOrder.failReason = '创建者已选择其他达人';

          // 添加失败状态提示信息
          updatedOrder.expertStatusTip = `很遗憾，订单创建者已选择达人"${selectedExpertName}"`;

          // 禁用所有操作按钮
          updatedOrder.disableActions = true;

          // 显示被选中的达人信息
          updatedOrder.isCurrentExpertSelected = false;
          updatedOrder.showSelectedExpertInfo = true;
          updatedOrder.otherExpertSelected = true;

          // 确保订单进度不变
          updatedOrder.expertProgress = 1; // 保持在已报名阶段
        }

        // 更新订单列表
        appliedOrders[orderIndex] = updatedOrder;

        // 保存更新后的订单列表
        try {
          uni.setStorageSync(key, JSON.stringify(appliedOrders));
          console.log(`达人${expertId}的订单列表已更新，当前包含${appliedOrders.length}个订单`);
          updateSuccess = true;
        } catch (saveError) {
          console.error(`保存达人${expertId}的订单列表失败:`, saveError);
        }

        console.log(`成功更新达人${expertId}的申请订单状态，选中状态:${thisExpertSelected}`);

        // 同时更新全局数据
        if (app.globalData.expertAppliedOrders && app.globalData.expertAppliedOrders[expertId]) {
          const globalOrderIndex = app.globalData.expertAppliedOrders[expertId].findIndex(o =>
            safeEqual(o.id, normalizedOrderId) || safeEqual(o.orderId, normalizedOrderId)
          );

          if (globalOrderIndex !== -1) {
            app.globalData.expertAppliedOrders[expertId][globalOrderIndex] = updatedOrder;
            console.log(`已更新全局数据中达人${expertId}的订单状态`);
          }
        }
      } else {
        console.log(`在达人${expertId}的申请订单中未找到订单${normalizedOrderId}`);
      }
    } catch (e) {
      console.error(`更新达人${expertId}申请订单失败:`, e);
    }
  });

  return updateSuccess;
}

export const storageUtils = () => { }