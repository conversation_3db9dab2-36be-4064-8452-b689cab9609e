<template>
  <view class="container">
    <view class="header">
      <view class="title">闲时达人认证</view>
      <view class="subtitle">个人服务者认证，完成后可接单赚钱</view>
    </view>

    <!-- 已成为达人状态 -->
    <block v-if="hasExpertRole">
      <view class="success-box">
        <view class="success-icon">✓</view>
        <view class="success-msg">恭喜，您已获得达人身份</view>
        <view class="success-desc">现在您可以接单赚钱了</view>
        <button class="btn-primary" @tap="goBack">
          返回
        </button>
      </view>
    </block>

    <!-- 申请条件和表单 -->
    <block v-else>
      <view class="requirements">
        <view class="requirement-title">认证条件 <text class="requirement-hint">（必须全部满足）</text></view>

        <view :class="['requirement-item', profileCompletion >= 50 ? 'completed' : 'uncompleted']">
          <view class="req-status">{{
            profileCompletion >= 50 ? "✓" : ""
            }}</view>
          <view class="req-name"><text class="highlight">个人主页完整度 ≥ 50%</text></view>
          <view class="req-value">{{ profileCompletion }}%</view>
          <view class="req-action" @tap="goToProfile">去完善 ></view>
        </view>

        <view :class="['requirement-item', isVerified ? 'completed' : 'uncompleted']">
          <view class="req-status">{{ isVerified ? "✓" : "" }}</view>
          <view class="req-name"><text class="highlight">实名认证</text></view>
          <view class="req-value">{{ isVerified ? "实名完成" : "实名未完成" }}</view>
          <view class="req-action" @tap="goToVerify">去认证 ></view>
        </view>
        
        <view :class="['requirement-item', hasJoinedGuild ? 'completed' : 'uncompleted']">
          <view class="req-status">{{ hasJoinedGuild ? "✓" : "" }}</view>
          <view class="req-name"><text class="highlight">加入当地闲时公会</text></view>
          <view class="req-value">{{ hasJoinedGuild ? "已加入" : "未加入" }}</view>
          <view class="req-action" @tap="goToJoinGuild">去加入 ></view>
        </view>
      </view>

      <view class="form-section">
        <view class="form-title"><text class="tips-icon">💡</text> 认证说明</view>
        <view class="form-desc">完成以上<text class="highlight">全部条件</text>后即可提交申请，审核通过后您将成为闲时达人，可以接单赚钱。请确保您的个人信息真实有效。</view>

        <button :class="['submit-btn', canApply ? 'can-apply' : 'disabled']" @tap="submitApply">
          {{ canApply ? "提交申请" : "请先满足申请条件" }}
        </button>
      </view>
    </block>
  </view>
</template>

<script>
import { getCurrentMemberInfo } from '@/api/index.js';
const app = getApp().globalData;
export default {
  data() {
    return {
      profileCompletion: 0,
      isVerified: false,
      hasJoinedGuild: false,
      canApply: false,
      hasExpertRole: false,
      userInfo: {}
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkRequirements();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 先获取用户信息，确保数据是最新的
    this.getUserInfo().then(() => {
      this.checkRequirements();
    });
    
    // 从本地存储检查是否已加入公会
    const guildInfo = uni.getStorageSync('guildInfo') || {};
    if (guildInfo.hasJoined) {
      console.log('检测到已加入公会:', guildInfo.guildName);
      
      // 显示加入的公会信息
      setTimeout(() => {
        // 只有在成功加入公会且之前未加入的情况下显示提示
        if (!this.hasJoinedGuild) {
          uni.showToast({
            title: `已加入${guildInfo.guildName}`,
            icon: 'success',
            duration: 2000
          });
        }
        // 刷新状态
        this.checkRequirements();
      }, 300);
    }
  },
  methods: {
    // 获取用户信息
    async getUserInfo() {
      try {
        let res = await getCurrentMemberInfo({ withExtra: true, withPrivacy: true });
        console.log("获取用户信息", res);

        if (res.code === 200 && res.data) {
          this.userInfo = res.data;
          // 根据realNameStatus设置实名认证状态
          this.isVerified = res.data.realNameStatus === 1;
          
          // 保存到本地存储
          uni.setStorageSync('userInfo', this.userInfo);
        }
      } catch (error) {
        console.error("获取用户信息失败", error);
        // 如果API调用失败，尝试从本地存储获取
        const localUserInfo = uni.getStorageSync('userInfo');
        if (localUserInfo) {
          this.userInfo = localUserInfo;
          this.isVerified = localUserInfo.realNameStatus === 1;
        }
      }
    },

    // 检查申请条件
    async checkRequirements() {
      try {
        // 调用后端API获取资料完整度
        await this.getProfileCompletion();
        
        // 检查是否已加入闲时公会
        const guildInfo = uni.getStorageSync('guildInfo') || {};
        this.hasJoinedGuild = guildInfo.hasJoined || false;
        
        // 检查是否已经是达人（从用户信息的角色中判断）
        if (this.userInfo && this.userInfo.roles && Array.isArray(this.userInfo.roles)) {
          this.hasExpertRole = this.userInfo.roles.some(role => role.id === '2' || role.code === '2');
        } else {
          this.hasExpertRole = false;
        }
        
        // 更新是否可以申请的状态
        this.canApply = this.profileCompletion >= 50 && this.isVerified && this.hasJoinedGuild && !this.hasExpertRole;
        
        console.log("检查条件结果", {
          isVerified: this.isVerified,
          profileCompletion: this.profileCompletion,
          hasJoinedGuild: this.hasJoinedGuild,
          hasExpertRole: this.hasExpertRole,
          canApply: this.canApply
        });
      } catch (error) {
        console.error("检查条件失败", error);
        this.canApply = false;
      }
    },
    
    // 获取个人资料完整度
    async getProfileCompletion() {
      try {
        const app = getApp().globalData;
        const res = await this.$requestHttp.get(app.commonApi.getMemberCompletion, {});
        
        if (res.code === 200) {
          this.profileCompletion = res.data;
          console.log('个人资料完整度:', this.profileCompletion + '%');
        } else {
          console.error('获取资料完整度失败:', res);
          this.profileCompletion = 0;
        }
      } catch (error) {
        console.error('获取资料完整度异常:', error);
        this.profileCompletion = 0;
      }
    },
    
    // 去完善资料
    goToProfile() {
      uni.navigateTo({
        url: "/pages/personal-homepage/personal-homepage",
      });
    },

    // 去实名认证
    goToVerify() {
      uni.navigateTo({
        url: "/pages/verify-identity/verify-identity",
      });
    },
    
    // 前往加入闲时公会
    goToJoinGuild() {
      // 跳转到公会选择页面
      uni.navigateTo({
        url: '/pages/guild-selection/guild-selection',
        success: () => {
          console.log('成功跳转到公会选择页面');
        },
        fail: (err) => {
          console.error('跳转失败', err);
          uni.showToast({
            title: '页面加载失败',
            icon: 'none'
          });
        }
      });
    },

    // 提交申请
    async submitApply() {
      if (!this.canApply) {
        uni.showToast({
          title: "请先满足申请条件",
          icon: "none",
        });
        return;
      }
      
      // 验证必填信息
      if (!this.userInfo.context || this.userInfo.context.trim() === '') {
        uni.showToast({
          title: "请先完善个人资料中的自我介绍",
          icon: "none",
        });
        return;
      }
      
      // 检查身份标签 - identify字段是逗号分隔的字符串
      const identifyTags = this.userInfo.identify ? this.userInfo.identify.split(',').filter(tag => tag.trim() !== '') : [];
      if (identifyTags.length === 0) {
        uni.showToast({
          title: "请先完善个人资料中的身份标签",
          icon: "none",
        });
        return;
      }
      
      console.log("userInfo", this.userInfo);
      uni.showLoading({
        title: "提交中...",
      });
      
      try {
        // 申请达人认证 - 修改为POST请求
        const app = getApp().globalData;
        // 处理身份标签数据
        const identifyTags = this.userInfo.identify ? this.userInfo.identify.split(',').filter(tag => tag.trim() !== '') : [];
        
        let res = await this.$requestHttp.post(app.commonApi.signExpertAuth, {
          data: {
            introduction: this.userInfo.context.trim(), // 使用context作为自我介绍
            skills: identifyTags.join(','), // 使用处理后的身份标签作为技能特长
            photos: this.userInfo.images ? this.userInfo.images.split(',').filter(img => img.trim() !== '') : [], // 修改为数组格式并过滤空值
            video: this.userInfo.video || ''
          }
        });
        if (res.code == 200) {
          // 更新本地用户申请状态
          let userInfo = uni.getStorageSync("userInfo") || {};
          userInfo.applyStatus = '1'; // 设置为审核中状态
          uni.setStorageSync("userInfo", userInfo);
          
          // 更新全局变量
          getApp().globalData.userApplyStatus = '1';
          
          uni.hideLoading();
          uni.showModal({
            title: "申请提交成功",
            content: "您的达人申请已提交成功，请耐心等待审核结果！审核结果将通过系统消息通知您。",
            confirmText: "我知道了",
            success: (res) => {
              if (res.confirm) {
                uni.navigateBack();
              } else {
                uni.navigateBack();
              }
            },
          });
        } else {
          uni.hideLoading();
          uni.showToast({
            title: res.message || "申请提交失败，请稍后再试",
            icon: "error",
            duration: 1500
          });
        }
        console.log("申请达人资料已提交!!!", res);
      } catch (error) {
        uni.hideLoading();
        console.error("申请提交失败", error);
        uni.showToast({
          title: "网络错误，请稍后再试",
          icon: "error",
          duration: 1500
        });
      }

      return;
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  },
};
</script>

<style scoped>
.container {
  padding: 30rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
  box-sizing: border-box;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 成功状态样式 */
.success-box {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #4caf50;
  color: white;
  font-size: 70rpx;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.success-msg {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

.btn-primary {
  width: 100%;
  height: 90rpx;
  background-color: #ff8c00;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.btn-secondary {
  width: 100%;
  height: 90rpx;
  background-color: #f0f0f0;
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 申请条件样式 */
.requirements {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.requirement-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
}

.requirement-hint {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.highlight {
  color: #ff8c00;
  font-weight: 600;
}

.tips-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.requirement-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.requirement-item:last-child {
  border-bottom: none;
}

.requirement-item.completed {
  color: #4caf50;
}

.requirement-item.uncompleted {
  color: #ff8c00; /* 未完成状态的提示颜色 */
}

.req-status {
  width: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #4caf50;
}

.req-name {
  flex: 1;
  font-size: 28rpx;
}

.req-value {
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.req-action {
  background-color: #f0f0f0;
  font-size: 24rpx;
  color: #333;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
}

/* 表单区域样式 */
.form-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.form-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.form-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  background-color: #ff8c00;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.disabled {
  background-color: #ccc;
  color: #fff;
}

.submit-btn.can-apply {
  background-color: #ff8c00;
  color: white;
}
</style>
