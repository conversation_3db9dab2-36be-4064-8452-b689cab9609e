/* eslint-disable */
import router from './router';
import store from './store';
import { Message } from 'element-ui';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken } from '@/utils/auth';

NProgress.configure({ showSpinner: false });

const whiteList = ['/login']; // 无需登录白名单

router.beforeEach(async(to, from, next) => {
  NProgress.start();
  
  // 获取令牌判断用户是否登录
  const hasToken = getToken();
  
  if (hasToken) {
    if (to.path === '/login') {
      // 已登录且要跳转的是登录页，重定向到首页
      next({ path: '/' });
      NProgress.done();
    } else {
      // 判断当前用户是否已获取用户信息
      const hasRoles = store.getters.roles && store.getters.roles.length > 0;
      if (hasRoles) {
        next();
      } else {
        try {
          // 获取用户信息，包含角色
          const { roles } = await store.dispatch('user/getInfo');
          
          // 基于角色生成可访问路由
          const accessRoutes = await store.dispatch('permission/generateRoutes', roles);
          
          // 动态添加可访问路由
          accessRoutes.forEach(route => {
            router.addRoute(route);
          });
          
          // 确保路由已完全添加
          next({ ...to, replace: true });
        } catch (error) {
          // 移除令牌重新登录
          await store.dispatch('user/resetToken');
          Message.error(error || '验证失败，请重新登录');
          next(`/login?redirect=${to.path}`);
          NProgress.done();
        }
      }
    }
  } else {
    // 没有令牌
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      // 其他没有访问权限的页面将重定向到登录页面
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  // 完成进度条
  NProgress.done();
}); 