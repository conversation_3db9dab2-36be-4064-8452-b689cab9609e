<template>
	<view class="chat-container">
		<!-- 导航栏 -->
		<!-- 在聊天头部添加连接状态指示 -->
		<view class="chat-header">
			<view class="header-left" @click="goBack">
				<text class="back-icon">◀</text>
			</view>
			<view class="header-center">
				<text class="chat-title">{{ chatUser.name }}</text>
				<view class="online-status" :class="{ 
					'status-connected': connectionStatus === 'connected',
					'status-connecting': connectionStatus === 'connecting' || connectionStatus === 'reconnecting',
					'status-disconnected': connectionStatus === 'disconnected'
				}">
					{{ getConnectionStatusText() }}
				</view>
			</view>
			<view class="header-right">
				<text class="more-icon" @click="showMoreOptions">⋯</text>
			</view>
		</view>
		
		<!-- 消息列表 -->
		<scroll-view 
			class="message-list" 
			scroll-y="true" 
			:scroll-top="scrollTop"
			@scrolltoupper="loadMoreMessages"
		>
			<view class="message-item" 
				v-for="(message, index) in messages" 
				:key="message.id || index"
				:class="{ 'own-message': message.fromUserId === currentUserId }"
			>
				<!-- 时间分隔线 -->
				<view class="time-divider" v-if="shouldShowTime(message, index)">
					<text class="time-text">{{ formatMessageTime(message.timestamp) }}</text>
				</view>
				
				<!-- 消息内容 -->
				<view class="message-content">
					<!-- 头像 -->
					<image 
						class="avatar" 
						:src="message.fromUserId === currentUserId ? currentUser.avatar : chatUser.avatar"
						mode="aspectFill"
					></image>
					
					<!-- 消息气泡 -->
					<view class="message-bubble">
						<text class="message-text">{{ message.content }}</text>
						<!-- 消息状态 -->
						<view class="message-status" v-if="message.fromUserId === currentUserId">
							<text class="status-text" :class="getMessageStatusClass(message.status)">{{ getMessageStatusText(message.status) }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 加载更多提示 -->
			<view class="loading-more" v-if="loadingMore">
				<text>加载中...</text>
			</view>
		</scroll-view>
		
		<!-- 输入框 -->
		<view class="input-area">
			<view class="input-container">
				<textarea 
					class="message-input" 
					v-model="inputMessage"
					placeholder="请输入消息..."
					:auto-height="true"
					:maxlength="500"
					@focus="onInputFocus"
					@blur="onInputBlur"
				></textarea>
				<button 
					class="send-btn" 
					:class="{ 'active': inputMessage.trim() }"
					:disabled="!inputMessage.trim() || sending"
					@click="sendMessage"
				>
					{{ sending ? '发送中' : '发送' }}
				</button>
			</view>
		</view>
		
		<!-- 更多选项弹窗 - 使用原生实现 -->
		<view class="popup-mask" v-if="showMorePopup" @click="closeMoreOptions">
			<view class="more-options" @click.stop>
				<view class="option-item" @click="clearChatHistory">
					<text>清空聊天记录</text>
				</view>
				<view class="option-item" @click="closeMoreOptions">
					<text>取消</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getChatHistory, sendChatMessage, deleteChatHistory, checkUserOnline } from '@/api/index.js';
import { getCurrentMemberInfo } from '@/api/index.js';
import WebSocketClient from '@/utils/websocket-client.js';

export default {
	data() {
		return {
			// 聊天对象信息
			chatUser: {
				id: '',
				name: '',
				avatar: ''
			},
			// 当前用户信息
			currentUser: {
				id: '',
				name: '',
				avatar: ''
			},
			currentUserId: '',
			// 消息列表
			messages: [],
			// 输入框内容
			inputMessage: '',
			// 发送状态
			sending: false,
			// 在线状态
			isOnline: false,
			// 滚动位置
			scrollTop: 0,
			// 加载更多
			loadingMore: false,
			// WebSocket客户端
			webSocketClient: null,
			// 页面参数
			pageParams: {},
			// 添加缺少的属性
			connectionStatus: 'disconnected',
			reconnectAttempts: 0,
			maxReconnectAttempts: 5,
			showMorePopup: false
		};
	},
	
	onLoad(options) {
		console.log('页面参数:', options);
		this.pageParams = options;
		this.chatUser.id = options.id;
		this.chatUser.name = decodeURIComponent(options.name || '未知用户');
		this.chatUser.avatar = decodeURIComponent(options.avatar || '/static/images/default-avatar.png');
		
		// 设置导航栏标题
		uni.setNavigationBarTitle({
			title: this.chatUser.name
		});
		
		console.log('聊天用户信息:', this.chatUser);
		
		// 初始化
		this.initChat();
	},
	
	onShow() {
		// 页面显示时重新连接WebSocket
		if (this.webSocketClient && !this.webSocketClient.connected) {
			this.webSocketClient.connect();
		}
	},
	
	onHide() {
		// 页面隐藏时断开WebSocket
		if (this.webSocketClient) {
			this.webSocketClient.disconnect();
		}
	},
	
	onUnload() {
		// 页面卸载时清理资源
		if (this.webSocketClient) {
			this.webSocketClient.disconnect();
			this.webSocketClient = null;
		}
	},
	
	methods: {
		/**
		 * 初始化聊天
		 */
		async initChat() {
			await this.getCurrentUserInfo();
			
			// 确保用户信息获取成功后再初始化WebSocket
			if (this.currentUserId) {
				await this.initWebSocket();
				await this.loadChatHistory();
			}
		},
		
		/**
		 * 获取当前用户信息
		 */
		async getCurrentUserInfo() {
			try {
				const response = await getCurrentMemberInfo();
				console.log('获取用户信息响应:', response);
				
				if (response.code === 200 && response.data) {
					this.currentUser = response.data;
					this.currentUserId = response.data.id;
					
					// 更新本地存储
					uni.setStorageSync('userInfo', response.data);
				} else {
					console.error('获取用户信息失败:', response.message);
					
					// API调用失败时，不从本地存储获取，直接提示重新登录
					uni.showModal({
						title: '提示',
						content: '获取用户信息失败，请重新登录',
						showCancel: false,
						success: () => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
					return;
				}
			} catch (error) {
				console.error('获取用户信息异常:', error);
				
				// 检查是否是401错误（token过期）
				if (error.statusCode === 401) {
					uni.showModal({
						title: '登录过期',
						content: '登录已过期，请重新登录',
						showCancel: false,
						success: () => {
							uni.removeStorageSync('token');
							uni.removeStorageSync('userInfo');
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
					return;
				}
				
				// API调用失败时，不从本地存储获取，避免显示上一个用户的信息
				uni.showToast({
					title: '获取用户信息失败，请重试',
					icon: 'none'
				});
			}
		},
		
		/**
		 * 加载聊天历史
		 */
		async loadChatHistory() {
			try {
				const response = await getChatHistory(this.chatUser.id);
				// 修复：将 response.status === 1 改为 response.code === 200
				if (response.code === 200 && response.data) {
					console.log('获取聊天历史成功:', response.data); // 添加调试日志
					this.messages = response.data.map(item => ({
						id: item.id,
						fromUserId: item.memberId,
						toUserId: item.targetId,
						content: this.parseMessageContent(item.contentJson),
						timestamp: new Date(item.createTime),
						status: 'DELIVERED'
					}));
					
					// 滚动到底部显示最新消息
					this.$nextTick(() => {
						this.scrollToBottom();
					});
				} else {
					console.log('获取聊天历史失败:', response);
				}
			} catch (error) {
				console.error('加载聊天历史失败:', error);
			}
		},
		
		/**
		 * 解析消息内容
		 */
		parseMessageContent(contentJson) {
			try {
				if (typeof contentJson === 'string') {
					// 如果是JSON字符串，尝试解析
					const parsed = JSON.parse(contentJson);
					return parsed.text || parsed.content || contentJson;
				}
				return contentJson;
			} catch (error) {
				// 解析失败，直接返回原内容
				return contentJson;
			}
		},
		
		/**
		 * 检查在线状态
		 */
		async checkOnlineStatus() {
			try {
				const response = await checkUserOnline(this.chatUser.id);
				if (response.status === 1) {
					this.isOnline = response.data;
				}
			} catch (error) {
				console.error('检查在线状态失败:', error);
			}
		},
		
		/**
		 * 初始化WebSocket
		 */
		async initWebSocket() {
			const token = uni.getStorageSync('token');
			
			if (!token || !this.currentUserId) {
				console.error('Token或用户ID缺失');
				uni.showToast({
					title: 'Token或用户ID缺失，请重新登录',
					icon: 'none'
				});
				// 跳转到登录页
				uni.reLaunch({
					url: '/pages/login/login'
				});
				return;
			}
			
			// 确保token格式正确
			let formattedToken = token;
			if (!token.startsWith('Bearer ')) {
				formattedToken = 'Bearer ' + token;
			}
			
			this.webSocketClient = new WebSocketClient({
				url: 'ws://127.0.0.1:9019/ws',
				token: formattedToken,
				userId: this.currentUserId,
				debug: true,
				maxReconnectAttempts: this.maxReconnectAttempts,
				onOpen: (event) => {
					console.log('WebSocket连接成功');
					this.connectionStatus = 'connected';
					this.reconnectAttempts = 0;
				},
				onMessage: (message) => {
					this.handleWebSocketMessage(message);
				},
				onClose: (event) => {
					console.log('WebSocket连接关闭', event);
					this.connectionStatus = 'disconnected';
				},
				onError: (error) => {
					console.error('WebSocket错误', error);
					this.connectionStatus = 'error';
					
					if (error.type === 'AUTH_FAILED' || error.type === 'MAX_RECONNECT_REACHED') {
						uni.showModal({
							title: '连接失败',
							content: error.message || 'WebSocket连接失败，请重新登录',
							showCancel: false,
							success: () => {
								// 清除本地存储并跳转到登录页
								uni.removeStorageSync('token');
								uni.removeStorageSync('userInfo');
								uni.reLaunch({
									url: '/pages/login/login'
								});
							}
						});
					}
				}
			});
			
			this.webSocketClient.connect();
		},
		
		/**
		 * 处理WebSocket消息
		 */
		handleWebSocketMessage(message) {
		    console.log('收到WebSocket消息:', message);
		    
		    if (message.type === 'CHAT') {
		        // 检查是否是发给当前聊天对象的消息
		        if (message.fromUserId === this.chatUser.id && message.toUserId === this.currentUserId) {
		            // 检查消息是否已存在（避免重复）
		            const existingMessage = this.messages.find(msg => msg.id === message.id);
		            if (!existingMessage) {
		                // 添加接收到的消息到消息列表
		                const receivedMessage = {
		                    id: message.id,
		                    fromUserId: message.fromUserId,
		                    toUserId: message.toUserId,
		                    content: message.content,
		                    timestamp: new Date(message.timestamp),
		                    status: 'DELIVERED'
		                };
		                
		                this.messages.push(receivedMessage);
		                
		                // 滚动到底部显示新消息
		                this.$nextTick(() => {
		                    this.scrollToBottom();
		                });
		                
		                // 播放消息提示音（可选）
		                // uni.showToast({
		                //     title: '收到新消息',
		                //     icon: 'none',
		                //     duration: 1000
		                // });
		            }
		        }
		    }
		    // 处理ACK消息（确认消息已发送）
		    else if (message.type === 'ACK') {
		        // 更新消息状态
		        const messageIndex = this.messages.findIndex(msg => msg.id === message.id);
		        if (messageIndex !== -1) {
		            this.messages[messageIndex].status = message.status || 'DELIVERED';
		            this.messages[messageIndex].canRetry = false;
		        }
		    }
		    // 处理连接确认消息
		    else if (message.type === 'CONNECT') {
		        console.log('WebSocket连接确认:', message.content);
		        this.connectionStatus = 'connected';
		    }
		    // 处理心跳消息
		    else if (message.type === 'PING') {
		        console.log('收到心跳响应');
		    }
		    // 处理系统消息
		    else if (message.type === 'SYSTEM') {
		        console.log('收到系统消息:', message.content);
		        // 可以在这里处理系统通知
		    }
		},
		
		// 修改发送消息的回调处理
		// 在sendMessage方法中修改
		async sendMessage() {
			if (!this.inputMessage.trim()) {
				return;
			}
			
			// 检查连接状态
			if (!this.webSocketClient || this.connectionStatus !== 'connected') {
				// 如果正在重连，提示用户等待
				if (this.connectionStatus === 'reconnecting') {
					uni.showToast({
						title: '正在重连，请稍后重试',
						icon: 'loading'
					});
				} else {
					// 尝试重新连接
					uni.showModal({
						title: '连接断开',
						content: '网络连接已断开，是否重新连接？',
						success: (res) => {
							if (res.confirm) {
								this.reconnectAttempts = 0;
								this.initWebSocket();
							}
						}
					});
				}
				return;
			}
			
			const content = this.inputMessage.trim();
			this.inputMessage = '';
			this.sending = true;
			
			try {
				// 生成唯一消息ID
				const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
				
				// 创建临时消息对象
				const tempMessage = {
					id: messageId,
					fromUserId: this.currentUserId,
					toUserId: this.chatUser.id,
					content: content,
					timestamp: new Date(),
					status: 'SENDING'
				};
				
				// 添加到消息列表
				this.messages.push(tempMessage);
				
				// 滚动到底部
				this.$nextTick(() => {
					this.scrollToBottom();
				});
				
				// 只通过WebSocket发送消息（移除HTTP API重复发送）
				const chatMessage = {
					id: messageId,
					type: 'CHAT',
					toUserId: this.chatUser.id,
					content: content
				};
				
				this.webSocketClient.send(chatMessage, (error, response) => {
					if (error) {
						console.error('发送消息失败:', error);
						// 更新消息状态为失败
						const messageIndex = this.messages.findIndex(msg => msg.id === messageId);
						if (messageIndex !== -1) {
							this.messages[messageIndex].status = 'FAILED';
							this.messages[messageIndex].canRetry = true;
						}
						uni.showToast({
							title: '发送失败，点击消息可重试',
							icon: 'none'
						});
					} else {
						console.log('消息发送成功，等待服务器确认');
					}
				});
				
				// 移除这部分HTTP API发送代码，因为WebSocket已经处理了消息持久化
				// await sendChatMessage({
				//     targetId: this.chatUser.id,
				//     content: JSON.stringify({ text: content, type: 'text' })
				// });
				
			} catch (error) {
				console.error('发送消息失败:', error);
				uni.showToast({
					title: '发送失败',
					icon: 'none'
				});
			} finally {
				this.sending = false;
			}
		},
		
		/**
		 * 滚动到底部
		 */
		scrollToBottom() {
			this.scrollTop = 999999;
		},
		
		/**
		 * 是否显示时间
		 */
		shouldShowTime(message, index) {
			if (index === 0) return true;
			
			const prevMessage = this.messages[index - 1];
			const timeDiff = new Date(message.timestamp) - new Date(prevMessage.timestamp);
			
			// 超过5分钟显示时间
			return timeDiff > 5 * 60 * 1000;
		},
		
		/**
		 * 格式化消息时间
		 */
		formatMessageTime(timestamp) {
			const date = new Date(timestamp);
			const now = new Date();
			
			// 今天
			if (date.toDateString() === now.toDateString()) {
				return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
			}
			
			// 昨天
			const yesterday = new Date(now);
			yesterday.setDate(yesterday.getDate() - 1);
			if (date.toDateString() === yesterday.toDateString()) {
				return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
			}
			
			// 其他日期
			return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
		},
		
		/**
		 * 获取消息状态样式
		 */
		getMessageStatusClass(status) {
			return {
				'status-sending': status === 'SENDING',
				'status-sent': status === 'SENT',
				'status-delivered': status === 'DELIVERED',
				'status-failed': status === 'FAILED'
			};
		},
		
		/**
		 * 获取消息状态文本
		 */
		// 获取连接状态文本
		getConnectionStatusText() {
			switch (this.connectionStatus) {
				case 'connected':
					return '在线';
				case 'connecting':
					return '连接中...';
				case 'reconnecting':
					return '重连中...';
				case 'disconnected':
					return '已断开';
				default:
					return '未知状态';
			}
		},
		
		// 获取消息状态文本
		getMessageStatusText(status) {
			switch (status) {
				case 'SENDING':
					return '发送中';
				case 'SENT':
					return '已发送';
				case 'DELIVERED':
					return '已送达';
				case 'FAILED':
					return '发送失败';
				default:
					return '';
			}
		},
		
		// 处理消息点击事件
		handleMessageTap(message) {
			if (message.canRetry && message.status === 'FAILED') {
				this.retryMessage(message);
			}
		},
		
		/**
		 * 输入框获得焦点
		 */
		onInputFocus() {
			// 滚动到底部
			setTimeout(() => {
				this.scrollToBottom();
			}, 300);
		},
		
		/**
		 * 输入框失去焦点
		 */
		onInputBlur() {
			// 可以在这里处理一些逻辑
		},
		
		/**
		 * 加载更多消息
		 */
		loadMoreMessages() {
			// 这里可以实现分页加载历史消息
			console.log('加载更多消息');
		},
		
		/**
		 * 显示更多选项
		 */
		showMoreOptions() {
			this.showMorePopup = true;
		},
		
		/**
		 * 关闭更多选项
		 */
		closeMoreOptions() {
			this.showMorePopup = false;
		},  // 在这里添加逗号
		
		/**
		 * 清空聊天记录
		 */
		async clearChatHistory() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空与该用户的聊天记录吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							await deleteChatHistory(this.chatUser.id);
							this.messages = [];
							uni.showToast({
								title: '清空成功',
								icon: 'success'
							});
						} catch (error) {
							console.error('清空聊天记录失败:', error);
							uni.showToast({
								title: '清空失败',
								icon: 'none'
							});
						}
					}
					this.closeMoreOptions();
				}
			});
		},
		
		/**
		 * 返回上一页
		 */
		goBack() {
			uni.navigateBack();
		}
	}
};
</script>

<style scoped>
.chat-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 聊天头部 */
.chat-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #e5e5e5;
	position: sticky;
	top: 0;
	z-index: 100;
}

.header-left {
	width: 80rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	font-size: 32rpx;
	color: #333333;
}

.header-center {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.chat-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.online-status {
	font-size: 24rpx;
	color: #999999;
}

.online-status.online {
	color: #07c160;
}

.header-right {
	width: 80rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.more-icon {
	font-size: 32rpx;
	color: #333333;
}

/* 消息列表 */
.message-list {
	flex: 1;
	padding: 20rpx;
	overflow-y: auto;
}

.message-item {
	margin-bottom: 30rpx;
}

.message-item.own-message .message-content {
	flex-direction: row-reverse;
}

.message-item.own-message .message-bubble {
	background-color: #07c160;
	color: #ffffff;
	margin-left: 20rpx;
	margin-right: 0;
}

.time-divider {
	display: flex;
	justify-content: center;
	margin: 20rpx 0;
}

.time-text {
	font-size: 24rpx;
	color: #999999;
	background-color: rgba(0, 0, 0, 0.1);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.message-content {
	display: flex;
	align-items: flex-start;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.message-bubble {
	max-width: 500rpx;
	padding: 20rpx;
	background-color: #ffffff;
	border-radius: 20rpx;
	position: relative;
	word-wrap: break-word;
}

.message-text {
	font-size: 28rpx;
	line-height: 1.4;
	color: #333333;
}

.message-item.own-message .message-text {
	color: #ffffff;
}

.message-status {
	margin-top: 8rpx;
	text-align: right;
}

.status-text {
	font-size: 22rpx;
}

.status-sending {
	color: #999999;
}

.status-sent {
	color: #cccccc;
}

.status-delivered {
	color: #cccccc;
}

.status-failed {
	color: #ff4444;
}

.loading-more {
	display: flex;
	justify-content: center;
	padding: 20rpx;
	font-size: 24rpx;
	color: #999999;
}

/* 输入区域 */
.input-area {
	padding: 20rpx;
	background-color: #ffffff;
	border-top: 1rpx solid #e5e5e5;
}

.input-container {
	display: flex;
	align-items: flex-end;
	gap: 20rpx;
}

.message-input {
	flex: 1;
	min-height: 80rpx;
	max-height: 200rpx;
	padding: 20rpx;
	border: 1rpx solid #e5e5e5;
	border-radius: 20rpx;
	background-color: #f8f8f8;
	font-size: 28rpx;
	line-height: 1.4;
	resize: none;
}

.send-btn {
	width: 120rpx;
	height: 80rpx;
	border-radius: 20rpx;
	background-color: #e5e5e5;
	color: #999999;
	font-size: 28rpx;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.send-btn.active {
	background-color: #07c160;
	color: #ffffff;
}

.send-btn:disabled {
	background-color: #e5e5e5;
	color: #999999;
}

/* 更多选项弹窗 */
.more-options {
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx 0;
}

.option-item {
	padding: 30rpx 40rpx;
	font-size: 32rpx;
	color: #333333;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.option-item:last-child {
	border-bottom: none;
	color: #999999;
}

.option-item:active {
	background-color: #f0f0f0;
}

/* 弹窗遮罩 */
.popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

/* 更多选项弹窗 */
.more-options {
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx 0;
	width: 100%;
	max-width: 750rpx;
}

.option-item {
	padding: 30rpx 40rpx;
	font-size: 32rpx;
	color: #333333;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.option-item:last-child {
	border-bottom: none;
	color: #999999;
}

.option-item:active {
	background-color: #f0f0f0;
}
</style>
