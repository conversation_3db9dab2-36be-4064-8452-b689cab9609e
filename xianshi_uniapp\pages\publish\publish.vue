<template>
  <view class="publish-popup" :class="{ show: show }">
    <view class="mask" @tap="hidePopup"></view>
    <view class="popup-content">
      <!-- 发布选项 -->
      <view class="publish-options">
        <view
          class="option-item"
          @tap="navigateToPublish"
          data-type="notice"
        >
          <view class="option-icon notice">
            <text class="fas fa-bullhorn"></text>
          </view>
          <view class="option-info">
            <text class="option-title">发布通告</text>
            <text class="option-desc">按人/天收费</text>
          </view>
        </view>

        <view class="option-item" @tap="navigateToExpert" data-type="order">
          <view class="option-icon order">
            <text class="fas fa-file-alt"></text>
          </view>
          <view class="option-info">
            <text class="option-title">发布约单</text>
            <text class="option-desc">多人一起玩</text>
          </view>
        </view>



      </view>

      <!-- 取消按钮 -->
      <view class="cancel-btn" @tap="hidePopup">取消</view>
    </view>
  </view>
  <Tabbar currentPath="/pages/publish/publish"></Tabbar>
</template>

<script>
import { checkAuth } from "../../utils/auth";
export default {
  data() {
    return {
      show: false,
    };
  },
  onLoad() {
    // 更新tabbar激活状态
    uni.$emit('updateTabbar', '/pages/publish/publish');
  },
  onShow() {
    // 确保每次显示页面时tabbar状态正确
    uni.$emit('updateTabbar', '/pages/publish/publish');
    
    // 显示弹窗
    this.show = true;
  },
  methods: {
    hidePopup() {
      this.show = false;
      // 延迟返回，等待动画完成
      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/home/<USER>",
        });
      }, 300);
    },

    navigateToPublish(e) {
      const type = e.currentTarget.dataset.type;
      let url = "";

      switch (type) {
        case "notice":
          url = "/pages/publish-demand/publish-demand";
          break;
        case "order":
          url = "/pages/publish-demand/publish-demand";
          break;
        case "work":
          // 作品发布页面
          break;
        case "card":
          // 名片制作页面
          break;
      }

      if (url) {
        uni.navigateTo({
          url: url,
        });
      }
    },

    navigateToExpert() {
      console.log('开始跳转到专家页面');
      uni.switchTab({
        url: '/pages/expert/expert',
        success: () => {
          console.log('跳转到专家页面成功');
        },
        fail: (err) => {
          console.error('跳转到专家页面失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },


    onPublish() {
      if (!checkAuth()) return;
      // 发布逻辑
    },
  },
};
</script>

<style scoped>
.container {
  padding: 20rpx;
}

.publish-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.publish-popup.show {
  visibility: visible;
  opacity: 1;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
}

.popup-content {
  position: absolute;
  bottom: 100px;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.publish-popup.show .popup-content {
  transform: translateY(0);
}

.publish-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  padding: 20rpx 0 40rpx 0;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f8f8;
  border-radius: 16rpx;
}

.option-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #fff;
  margin-right: 20rpx;
}

.option-icon.notice {
  background: #ff8c00;
}

.option-icon.order {
  background: #1890ff;
}

.option-icon.work {
  background: #722ed1;
}

.option-icon.card {
  background: #52c41a;
}

.option-info {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.option-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.cancel-btn {
  margin-top: 20rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  background: #f7f7f7;
  border-radius: 999rpx;
}

.cancel-btn:active {
  opacity: 0.8;
}
</style>
