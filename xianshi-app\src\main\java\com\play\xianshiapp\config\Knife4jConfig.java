package com.play.xianshiapp.config;

import com.github.xiaoymin.knife4j.spring.model.MarkdownFiles;
import com.github.xiaoymin.knife4j.spring.web.Knife4jController;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.SimpleUrlHandlerMapping;
import org.springframework.web.servlet.resource.PathResourceResolver;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;
import org.springframework.web.servlet.resource.ResourceResolver;
import org.springframework.web.util.UrlPathHelper;
import springfox.documentation.annotations.ApiIgnore;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.RequestHandlerProvider;
import springfox.documentation.spring.web.DocumentationCache;
import springfox.documentation.spring.web.json.Json;
import springfox.documentation.spring.web.json.JsonSerializer;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger.web.ApiResourceController;
import springfox.documentation.swagger.web.SecurityConfiguration;
import springfox.documentation.swagger.web.SwaggerResource;
import springfox.documentation.swagger.web.UiConfiguration;
import springfox.documentation.swagger2.annotations.EnableSwagger2;
import springfox.documentation.swagger2.mappers.ServiceModelToSwagger2Mapper;
import springfox.documentation.swagger2.web.Swagger2Controller;

/**
 * <AUTHOR>
 * @date 2025/06/03
 */
@Configuration
@EnableSwagger2
@Profile({"dev", "test"})
public class Knife4jConfig {
    private static final String DEFAULT_PATH = "/api/swagger/doc";

    public Knife4jConfig() {
    }

    @Bean
    public Docket frontApi() {
        ApiInfo apiInfo = (new ApiInfoBuilder()).title("闲时APP-前台API").description("闲时APP-前台API").version("1.0").build();
        return (new Docket(DocumentationType.SWAGGER_2)).apiInfo(apiInfo).groupName("闲时APP-前台API").select().apis(RequestHandlerSelectors.basePackage("com.play")).paths(PathSelectors.any()).build();
    }

    @Bean
    public SimpleUrlHandlerMapping swaggerUrlHandlerMapping(ServletContext servletContext, @Value("${swagger.mapping.order:10}") int order) throws Exception {
        SimpleUrlHandlerMapping urlHandlerMapping = new SimpleUrlHandlerMapping();
        Map<String, ResourceHttpRequestHandler> urlMap = new HashMap<>(10);
        PathResourceResolver pathResourceResolver = new PathResourceResolver();
        pathResourceResolver.setAllowedLocations(new Resource[]{new ClassPathResource("META-INF/resources/webjars/")});
        pathResourceResolver.setUrlPathHelper(new UrlPathHelper());
        ResourceHttpRequestHandler resourceHttpRequestHandler = new ResourceHttpRequestHandler();
        resourceHttpRequestHandler.setLocations(Collections.<Resource>singletonList(new ClassPathResource("META-INF/resources/webjars/")));
        resourceHttpRequestHandler.setResourceResolvers(Collections.<ResourceResolver>singletonList(pathResourceResolver));
        resourceHttpRequestHandler.setServletContext(servletContext);
        resourceHttpRequestHandler.afterPropertiesSet();
        urlMap.put("/api/swagger/doc/webjars/**", resourceHttpRequestHandler);
        pathResourceResolver = new PathResourceResolver();
        pathResourceResolver.setAllowedLocations(new Resource[]{new ClassPathResource("META-INF/resources/")});
        pathResourceResolver.setUrlPathHelper(new UrlPathHelper());
        resourceHttpRequestHandler = new ResourceHttpRequestHandler();
        resourceHttpRequestHandler.setLocations(Collections.<Resource>singletonList(new ClassPathResource("META-INF/resources/")));
        resourceHttpRequestHandler.setResourceResolvers(Collections.<ResourceResolver>singletonList(pathResourceResolver));
        resourceHttpRequestHandler.setServletContext(servletContext);
        resourceHttpRequestHandler.afterPropertiesSet();
        urlMap.put("/api/swagger/doc/**", resourceHttpRequestHandler);
        urlHandlerMapping.setUrlMap(urlMap);
        urlHandlerMapping.setOrder(order);
        return urlHandlerMapping;
    }

    @Controller
    @ApiIgnore
    @RequestMapping({"/api/swagger/doc"})
    @Profile({"dev", "test"})
    public static class SwaggerResourceController implements InitializingBean {
        @Autowired
        private ApiResourceController apiResourceController;
        @Autowired
        private Environment environment;
        @Autowired
        private DocumentationCache documentationCache;
        @Autowired
        private ServiceModelToSwagger2Mapper mapper;
        @Autowired
        private JsonSerializer jsonSerializer;
        private Swagger2Controller swagger2Controller;
        @Autowired
        private List<RequestHandlerProvider> handlerProviders;
        @Autowired
        private ObjectProvider<MarkdownFiles> markdownFilesObjectProvider;
        private Knife4jController knife4jController;

        public SwaggerResourceController() {
        }

        public void afterPropertiesSet() {
            this.swagger2Controller = new Swagger2Controller(this.environment, this.documentationCache, this.mapper, this.jsonSerializer);
            this.knife4jController = new Knife4jController(this.environment, this.mapper, this.documentationCache, this.jsonSerializer, this.handlerProviders, this.markdownFilesObjectProvider);
        }

        @RequestMapping
        public ModelAndView index() {
            return new ModelAndView("redirect:/api/swagger/doc/doc.html");
        }

        @RequestMapping({"/swagger-resources/configuration/security"})
        @ResponseBody
        public ResponseEntity<SecurityConfiguration> securityConfiguration() {
            return this.apiResourceController.securityConfiguration();
        }

        @RequestMapping({"/swagger-resources/configuration/ui"})
        @ResponseBody
        public ResponseEntity<UiConfiguration> uiConfiguration() {
            return this.apiResourceController.uiConfiguration();
        }

        @RequestMapping({"/swagger-resources"})
        @ResponseBody
        public ResponseEntity<List<SwaggerResource>> swaggerResources() {
            return this.apiResourceController.swaggerResources();
        }

        @RequestMapping(
                value = {"/v2/api-docs"},
                method = {RequestMethod.GET},
                produces = {"application/json", "application/hal+json"}
        )
        @ResponseBody
        public ResponseEntity<Json> getDocumentation(@RequestParam(value = "group",required = false) String swaggerGroup, HttpServletRequest servletRequest) {
            return this.swagger2Controller.getDocumentation(swaggerGroup, servletRequest);
        }

        @RequestMapping(
                value = {"/v2/api-docs-ext"},
                method = {RequestMethod.GET},
                produces = {"application/json", "application/hal+json"}
        )
        @ResponseBody
        public ResponseEntity<Json> apiSorts(@RequestParam(value = "group",required = false) String swaggerGroup, HttpServletRequest servletRequest) {
            return this.knife4jController.apiSorts(swaggerGroup, servletRequest);
        }
    }
}
