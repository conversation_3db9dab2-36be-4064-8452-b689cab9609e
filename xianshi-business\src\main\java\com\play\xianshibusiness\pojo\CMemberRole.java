package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * (CMemberRole)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_member_role")
@ApiModel("会员角色关系表")
public class CMemberRole extends BaseObjPo {

    //会员ID
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    //角色ID
    @ApiModelProperty(value = "角色ID")
    private String roleId;

}

