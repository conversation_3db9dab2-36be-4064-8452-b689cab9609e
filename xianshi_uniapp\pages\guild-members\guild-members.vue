<template>
  <view class="container">
    <!-- 头部统计 -->
    <view class="header-stats">
      <view class="stat-item">
        <text class="stat-value">{{ totalMembers }}</text>
        <text class="stat-label">成员总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ activeMembers }}</text>
        <text class="stat-label">在线成员</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ newMembers }}</text>
        <text class="stat-label">本月新增</text>
      </view>
    </view>
    
    <!-- 搜索和筛选 -->
    <view class="search-bar">
      <view class="search-input">
        <text class="search-icon">🔍</text>
        <input type="text" placeholder="搜索成员" v-model="searchQuery" @input="handleSearch" />
      </view>
      <view class="filter-btn" @tap="showFilterOptions">
        <text>筛选</text>
      </view>
    </view>
    
    <!-- 成员列表 -->
    <view class="member-list">
      <view class="talent-card" v-for="(member, index) in filteredMembers" :key="index" @tap="viewMemberDetail(member.id)">
        <!-- 卡片头部信息 -->
        <view class="talent-header">
          <view class="talent-avatar-container">
            <image class="talent-avatar" :src="member.avatar" mode="aspectFill" />
            <view class="talent-online-dot" v-if="member.status === 'online'"></view>
          </view>
          <view class="talent-info">
            <view class="talent-name-row">
              <text class="talent-name">{{ member.name }}</text>
              <text class="gender-icon">{{ member.gender === 'female' ? '♀' : '♂' }}</text>
              <text class="talent-id">{{ member.id }}</text>
            </view>
            <view class="talent-profile">
              <text class="profile-item">{{ member.age }}岁</text>
              <text class="profile-divider">|</text>
              <text class="profile-item">{{ member.height }}</text>
              <text class="profile-divider">|</text>
              <text class="profile-item">{{ member.weight }}</text>
              <text class="profile-divider">|</text>
              <text class="profile-item">{{ member.city }}</text>
            </view>
          </view>
          <view class="talent-status" :class="{'status-online': member.status === 'online'}">
            {{ getMemberStatusText(member) }}
          </view>
        </view>
        
        <!-- 卡片图片展示区 -->
        <view class="talent-photos">
          <view class="photo-placeholder" v-if="!member.photos || member.photos.length === 0">
            <text>暂无照片</text>
          </view>
          <image 
            v-for="(photo, photoIndex) in (member.photos || []).slice(0, 3)" 
            :key="photoIndex" 
            class="talent-photo" 
            :src="photo" 
            mode="aspectFill"
          />
        </view>
        
        <!-- 标签和收益信息 -->
        <view class="talent-footer">
          <view class="talent-tags">
            <text class="talent-tag" v-for="(tag, tagIndex) in member.tags" :key="tagIndex">{{ tag }}</text>
          </view>
          <view class="talent-income">
            <text class="income-label">本月收益</text>
            <text class="income-value">{{ member.income }}元</text>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="talent-actions">
          <view class="action-btn contact-btn" @tap.stop="contactMember(member.id)">
            <text>联系达人</text>
          </view>
          <view class="action-btn manage-btn" @tap.stop="manageMember(member.id)">
            <text>管理</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredMembers.length === 0">
        <image class="empty-icon" src="/static/images/empty-data.png"></image>
        <text class="empty-text">暂无成员数据</text>
      </view>
    </view>
    
    <!-- 底部添加按钮 -->
    <view class="add-member-btn" @tap="showAddMemberDialog">
      <text class="add-icon">+</text>
      <text>添加成员</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 统计数据
      totalMembers: 24,
      activeMembers: 18,
      newMembers: 5,
      
      // 搜索相关
      searchQuery: '',
      
      // 成员列表
      members: [
        {
          id: '1001',
          name: '张三',
          avatar: '/static/images/default-avatar.png',
          gender: 'male',
          age: 25,
          height: 178,
          weight: 70,
          city: '北京',
          level: 3,
          orderCount: 32,
          rating: 4.8,
          joinTime: '2023-03-15',
          status: 'online',
          lastSeen: new Date(), // 当前时间，表示在线
          photos: [
            '/static/images/default-avatar.png',
            '/static/images/default-avatar.png',
            '/static/images/default-avatar.png'
          ],
          tags: ['游戏', '唱歌', '聊天'],
          income: 3680
        },
        {
          id: '1002',
          name: '李四',
          avatar: '/static/images/default-avatar.png',
          gender: 'female',
          age: 22,
          height: 165,
          weight: 48,
          city: '上海',
          level: 2,
          orderCount: 15,
          rating: 4.5,
          joinTime: '2023-04-20',
          status: 'offline',
          lastSeen: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前
          photos: [
            '/static/images/default-avatar.png',
            '/static/images/default-avatar.png'
          ],
          tags: ['摄影', '旅行'],
          income: 1520
        },
        {
          id: '1003',
          name: '王五',
          avatar: '/static/images/default-avatar.png',
          gender: 'male',
          age: 28,
          height: 180,
          weight: 75,
          city: '广州',
          level: 1,
          orderCount: 8,
          rating: 4.2,
          joinTime: '2023-05-05',
          status: 'offline',
          lastSeen: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15天前
          photos: [],
          tags: ['桌游', '电影'],
          income: 850
        },
        {
          id: '1004',
          name: '赵六',
          avatar: '/static/images/default-avatar.png',
          gender: 'female',
          age: 24,
          height: 168,
          weight: 52,
          city: '深圳',
          level: 2,
          orderCount: 21,
          rating: 4.6,
          joinTime: '2023-01-10',
          status: 'offline',
          lastSeen: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45天前，超过1个月
          photos: [
            '/static/images/default-avatar.png'
          ],
          tags: ['唱歌', 'K歌', '派对'],
          income: 2450
        },
        {
          id: '1005',
          name: '钱七',
          avatar: '/static/images/default-avatar.png',
          gender: 'female',
          age: 26,
          height: 170,
          weight: 54,
          city: '成都',
          level: 3,
          orderCount: 43,
          rating: 4.9,
          joinTime: '2023-02-15',
          status: 'offline',
          lastSeen: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90天前，超过3个月
          photos: [
            '/static/images/default-avatar.png',
            '/static/images/default-avatar.png',
            '/static/images/default-avatar.png'
          ],
          tags: ['游戏', '陪玩', '聊天'],
          income: 5820
        }
      ]
    };
  },
  computed: {
    filteredMembers() {
      if (!this.searchQuery) return this.members;
      
      const query = this.searchQuery.toLowerCase();
      return this.members.filter(member => 
        member.name.toLowerCase().includes(query)
      );
    }
  },
  methods: {
    handleSearch() {
      // 实时搜索，已通过计算属性处理
    },
    
    showFilterOptions() {
      uni.showActionSheet({
        itemList: ['全部成员', '活跃成员', '非活跃成员', '最近加入'],
        success: (res) => {
          // 处理筛选逻辑
          uni.showToast({
            title: '筛选功能开发中',
            icon: 'none'
          });
        }
      });
    },
    
    viewMemberDetail(memberId) {
      uni.navigateTo({
        url: `/pages/profile/preview?id=${memberId}&isGuildMember=true`
      });
    },
    
    showAddMemberDialog() {
      uni.showToast({
        title: '添加成员功能开发中',
        icon: 'none'
      });
    },
    
    formatDate(dateStr) {
      return dateStr;
    },
    
    getMemberStatusText(member) {
      // 如果状态是在线，直接返回"在线"
      if (member.status === 'online') {
        return '在线';
      }
      
      // 对于离线状态，根据最后在线时间显示不同文本
      if (!member || !member.lastSeen) return '未知';
      
      const lastSeen = new Date(member.lastSeen);
      const now = new Date();
      const diffMs = now - lastSeen;
      const diffMins = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffMonths = Math.floor(diffDays / 30);
      
      // 根据时间差返回不同的文本
      if (diffMins < 5) {
        return '刚刚来过';
      } else if (diffHours < 1) {
        return `${diffMins}分钟前`;
      } else if (diffHours < 24) {
        return `${diffHours}小时前`;
      } else if (diffDays <= 30) {
        return `${diffDays}天前`;
      } else {
        return `${diffMonths}个月前`;
      }
    },

    // 联系成员
    contactMember(memberId) {
      uni.showToast({
        title: '正在联系达人: ' + memberId,
        icon: 'none'
      });
    },
    
    // 管理成员
    manageMember(memberId) {
      uni.showActionSheet({
        itemList: ['查看详情', '修改信息', '移除成员'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.viewMemberDetail(memberId);
              break;
            case 1:
              uni.showToast({
                title: '修改成员信息',
                icon: 'none'
              });
              break;
            case 2:
              uni.showModal({
                title: '确认移除',
                content: '确定要将该成员从公会中移除吗？',
                success: (res) => {
                  if (res.confirm) {
                    uni.showToast({
                      title: '成员已移除',
                      icon: 'success'
                    });
                  }
                }
              });
              break;
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 头部统计样式 */
.header-stats {
  display: flex;
  background-color: #ff8c00;
  padding: 30rpx 0;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.stat-item:not(:last-child):after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.3);
}

.stat-value {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  padding: 20rpx;
  background-color: #ffffff;
}

.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 20rpx;
  margin-right: 20rpx;
}

.search-icon {
  margin-right: 10rpx;
  color: #999;
}

.search-input input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
}

.filter-btn {
  width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  border-radius: 36rpx;
  font-size: 28rpx;
  color: #666;
}

/* 成员列表样式 */
.member-list {
  padding: 20rpx;
}

.talent-card {
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.talent-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.talent-avatar-container {
  position: relative;
  margin-right: 20rpx;
}

.talent-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.talent-online-dot {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16rpx;
  height: 16rpx;
  background-color: #52c41a;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.talent-info {
  flex: 1;
}

.talent-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.talent-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.talent-id {
  font-size: 24rpx;
  color: #999;
}

.gender-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
  font-weight: bold;
}

.gender-icon:empty {
  display: none;
}

/* 性别图标颜色 */
.gender-icon:contains('♂') {
  color: #409eff;
}

.gender-icon:contains('♀') {
  color: #ff69b4;
}

.talent-profile {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.profile-item {
  font-size: 24rpx;
  color: #666;
}

.profile-divider {
  margin: 0 6rpx;
  color: #ddd;
  font-size: 24rpx;
}

.talent-status {
  font-size: 24rpx;
  color: #999;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.status-online {
  font-weight: 500;
  color: #52c41a;
}

.talent-photos {
  display: flex;
  justify-content: space-between;
  height: 220rpx;
  overflow: hidden;
  margin-top: 10rpx;
}

.talent-photo {
  width: 32%;
  height: 100%;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  object-fit: cover;
}

.photo-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 28rpx;
}

/* 标签和收益信息样式 */
.talent-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}

.talent-tags {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.talent-tag {
  font-size: 22rpx;
  color: #ff8c00;
  background-color: rgba(255, 140, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.talent-income {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.income-label {
  font-size: 22rpx;
  color: #999;
}

.income-value {
  font-size: 28rpx;
  color: #ff8c00;
  font-weight: bold;
}

/* 操作按钮样式 */
.talent-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 35rpx;
  font-size: 28rpx;
}

.contact-btn {
  background-color: #ff8c00;
  color: #fff;
  margin-right: 10rpx;
}

.manage-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-left: 10rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 添加成员按钮 */
.add-member-btn {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ff8c00;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 140, 0, 0.3);
}

.add-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}
</style> 