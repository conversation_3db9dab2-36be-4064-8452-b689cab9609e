<template>
  <div v-if="!item.hidden">
    <template v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown': !isNest}" class="menu-item-wrapper">
          <item :icon="onlyOneChild.meta.icon || (item.meta && item.meta.icon)" :title="onlyOneChild.meta.title" />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body class="submenu-wrapper">
      <template slot="title">
        <item v-if="item.meta" :icon="item.meta && item.meta.icon" :title="item.meta.title" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path';
import { isExternal } from '@/utils/validate';
import Item from './Item';
import AppLink from './Link';

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  props: {
    // 路由对象
    item: {
      type: Object,
      required: true
    },
    // 是否嵌套
    isNest: {
      type: Boolean,
      default: false
    },
    // 基础路径
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    this.onlyOneChild = null;
    return {};
  },
  mounted() {
    // 延迟执行，确保DOM已经渲染完成
    setTimeout(() => {
      this.fixSpanUnderlines();
    }, 300);
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false;
        } else {
          // 临时设置
          this.onlyOneChild = item;
          return true;
        }
      });

      // 当只有一个子路由时，默认展示
      if (showingChildren.length === 1) {
        return true;
      }

      // 如果没有子路由则显示父路由
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true };
        return true;
      }

      return false;
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath;
      }
      if (isExternal(this.basePath)) {
        return this.basePath;
      }
      return path.resolve(this.basePath, routePath);
    },
    fixSpanUnderlines() {
      // 查找当前组件内的所有span元素
      if (this.$el) {
        const spans = this.$el.querySelectorAll('span');
        spans.forEach(span => {
          // 直接设置内联样式
          span.style.setProperty('border-bottom', 'none', 'important');
          span.style.setProperty('text-decoration', 'none', 'important');
          span.style.setProperty('border', 'none', 'important');
          span.style.setProperty('box-shadow', 'none', 'important');
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.el-submenu.is-active > .el-submenu__title {
  color: var(--primary-color) !important;
}

.menu-item-wrapper {
  border: none !important;
  border-bottom: none !important;
  box-shadow: none !important;
}

.submenu-wrapper {
  border: none !important;
  
  ::v-deep .el-submenu__title {
    border: none !important;
    border-bottom: none !important;
  }
}

.nest-menu {
  ::v-deep .el-menu-item {
    border: none !important;
    border-bottom: none !important;
  }
}

::v-deep .el-menu--inline {
  border: none !important;
  border-bottom: none !important;
}

/* 强制覆盖所有span元素的下划线 */
::v-deep span {
  border-bottom: none !important;
  text-decoration: none !important;
  border: none !important;
  box-shadow: none !important;
}
</style>