{"name": "xianshi-admin", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.1", "core-js": "^3.6.5", "echarts": "^5.1.2", "element-ui": "^2.15.6", "js-cookie": "^3.0.1", "nprogress": "^0.2.0", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "prettier": "^2.2.1", "sass": "^1.89.2", "sass-loader": "^10.1.1", "vue-template-compiler": "^2.6.11", "webpack-dev-server": "^3.11.2"}}