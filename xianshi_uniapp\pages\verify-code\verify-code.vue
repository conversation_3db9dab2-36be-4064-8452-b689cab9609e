<template>
  <view class="container">
    <video
        class="bg-video"
        src="../../static/videos/login-bg.mp4"
        object-fit="cover"
        autoplay
        loop
        muted
        :controls="false"
        show-center-play-btn="false"
        show-fullscreen-btn="false"
        show-play-btn="false"
        enable-progress-gesture="false"
        disable-progress="true"
    />
    <view class="content">
      <view class="close" @tap="goBack">
        <text class="iconfont icon-close"></text>
      </view>
      <view class="welcome">
        <view class="logo">
          <text class="logo-text">FREE TIME</text>
        </view>
        <text class="subtitle">闲时有伴</text>
      </view>
      <view class="verify-box">
        <view class="phone-number">{{ phoneNumber }}</view>
        <view class="verify-title">请输入验证码</view>
        <view class="code-input-box">
          <input class="hidden-input" type="number" v-model="code" maxlength="6" focus @input="onCodeInput" />
          <view class="code-dots">
            <view class="code-item">
              <text class="code-text" v-if="code.length >= 1">{{
                code[0]
                }}</text>
            </view>
            <view class="code-item">
              <text class="code-text" v-if="code.length >= 2">{{
                code[1]
                }}</text>
            </view>
            <view class="code-item">
              <text class="code-text" v-if="code.length >= 3">{{
                code[2]
                }}</text>
            </view>
            <view class="code-item">
              <text class="code-text" v-if="code.length >= 4">{{
                code[3]
                }}</text>
            </view>
            <view class="code-item">
              <text class="code-text" v-if="code.length >= 5">{{
                code[4]
                }}</text>
            </view>
            <view class="code-item">
              <text class="code-text" v-if="code.length >= 6">{{
                code[5]
                }}</text>
            </view>
          </view>
        </view>
        <view class="resend" :class="{ disabled: counting }" @tap="!counting ? onResend() : null">
          {{ counting ? countDown + "秒后重新发送" : "重新发送" }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp().globalData;
export default {
  data() {
    return {
      phoneNumber: "",
      fullPhone: "", // 存储完整手机号
      code: "",
      isPassword: false,
      counting: false,
      countDown: 60,
    };
  },
  onLoad(options) {
    if (options.phone) {
      this.fullPhone = options.phone; // 保存完整手机号
      // 格式化手机号 例如：138****8888
      const phone = options.phone;
      const formatted = phone.substr(0, 3) + "****" + phone.substr(7);
      this.phoneNumber = formatted;
      this.startCountDown();

      // 自动请求验证码
      this.sendCaptcha(options.phone);
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    onCodeInput(e) {
      const code = e.detail.value;
      this.code = code;
      if (code.length === 6) {
        this.verifyCode(code);
      }
    },

    // 发送验证码方法
    async sendCaptcha(phoneNumber) {
      uni.showLoading({
        title: '获取验证码中...'
      });
      let res = await this.$requestHttp.get(app.commonApi.getLoginRole, {
        data: {
          phone: phoneNumber,
          purpose: 'LOGIN'
        },
      })
      console.log('验证码发送成功', res);
      if (res.code === 200) {
        uni.showToast({
          title: '验证码发送成功',
          icon: 'none'
        });
        uni.hideLoading();
      } else {
        uni.showToast({
          title: res.message || '获取验证码失败',
          icon: 'none'
        });
        uni.hideLoading();
      }
    },

    async verifyCode(code) {
      console.log('开始验证码登录:', code);
      
      // 调用登录接口
      uni.showLoading({
        title: "登录中...",
      });

      try {
        let res = await this.$requestHttp.post(app.commonApi.login, {
          data: {
            "phone": this.fullPhone,
            "code": code,
            "loginMethod": "PHONE_CODE"
          },
        });
        
        console.log('登录API响应:', res);
        
        if (res.code === 200 && res.data) {
          uni.hideLoading();
          console.log('登录成功', res);
          
          // 保存token等信息
          if (res.data.token) {
            uni.setStorageSync('token', res.data.token);
          }
          if (res.data.avatar) {
            uni.setStorageSync('avatar', res.data.avatar);
          }
          if (res.data.currentRoleId) {
            uni.setStorageSync('currentRoleId', res.data.currentRoleId);
          }
          if (res.data.nickname) {
            uni.setStorageSync('nickname', res.data.nickname);
          }
          
          // 重要：登录成功后立即获取用户信息并设置到全局数据
          const appInstance = getApp();
          if (appInstance && appInstance.getUserInfo) {
            try {
              await appInstance.getUserInfo();
              console.log('登录后用户信息更新成功:', appInstance.globalData.currentUserId);
            } catch (error) {
              console.error('登录后获取用户信息失败:', error);
              // 如果获取用户信息失败，尝试初始化用户信息
              appInstance.initUserInfo();
            }
          }

          // 验证成功后跳转到首页
          uni.switchTab({
            url: "/pages/home/<USER>",
          });
        } else {
          uni.hideLoading();
          uni.showToast({
            title: res.message || '登录失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('登录请求失败:', error);
        uni.showToast({
          title: '网络请求失败，请重试',
          icon: 'none'
        });
      }
    },

    startCountDown() {
      this.counting = true;
      this.countDown = 60;

      const timer = setInterval(() => {
        if (this.countDown <= 1) {
          clearInterval(timer);
          this.counting = false;
        } else {
          this.countDown = this.countDown - 1;
        }
      }, 1000);
    },

    onResend() {
      // 重新发送验证码
      this.sendCaptcha(this.fullPhone);
      this.startCountDown();
    },
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.bg-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 100rpx 40rpx;
  box-sizing: border-box;
}
.bg-video {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  object-fit: cover;
}
.close {
  position: absolute;
  top: 100rpx;
  left: 40rpx;
  color: #fff;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close .iconfont {
  font-size: 40rpx;
}

.welcome {
  text-align: center;
  color: #fff;
  margin-top: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: bold;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.logo-text {
  font-size: 48rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 32rpx;
  margin-top: 10rpx;
  font-weight: bold;
}

.verify-box {
  margin-top: 100rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.phone-number {
  color: #fff;
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.verify-title {
  color: #fff;
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.code-input-box {
  width: 100%;
  position: relative;
  height: 100rpx;
  margin-bottom: 40rpx;
}

.hidden-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  opacity: 0;
}

.code-dots {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.code-item {
  width: 80rpx;
  height: 80rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-text {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

.resend {
  color: #fff;
  font-size: 28rpx;
  padding: 20rpx;
}

.resend.disabled {
  opacity: 0.5;
}
</style>
