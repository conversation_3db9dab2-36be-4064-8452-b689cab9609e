<template>
  <view class="profile-container">
    <!-- 个人基本信息 -->
    <view class="profile-header">
      <!-- 返回按钮已删除 -->

      <image
        class="avatar"
        :src="expertInfo?.avatar || '/static/images/default-avatar.png'"
        mode="aspectFill"
      ></image>
      <view class="username-row">
        <text class="username">{{ expertInfo?.name || '用户' }}</text>
        <text :class="['gender', expertInfo?.gender === '女' ? 'female' : 'male']">
          {{ expertInfo?.gender === "女" ? "♀" : "♂" }}
        </text>
      </view>
      <view class="user-id">ID: {{ formatUserId(expertInfo?.id) }}</view>
      <view class="user-type">普通用户</view>

      <!-- 用户数据统计 -->
      <view class="user-stats">
        <view class="stat-item">
          <view class="stat-num">{{ expertInfo?.popularity || 0 }}</view>
          <view class="stat-label">人气</view>
        </view>
        <view class="stat-item">
          <view class="stat-num">{{ expertInfo?.fansCount || 0 }}</view>
          <view class="stat-label">粉丝</view>
        </view>
        <view class="stat-item">
          <view class="stat-num">{{ expertInfo?.followCount || 0 }}</view>
          <view class="stat-label">关注</view>
        </view>
        <view class="stat-item">
          <view class="stat-num">{{ expertInfo?.viewCount || 0 }}</view>
          <view class="stat-label">浏览量</view>
        </view>
      </view>

      <!-- 操作按钮已移至底部 -->
    </view>

    <!-- 页面标签栏 -->
    <view class="tab-bar">
      <view
        :class="['tab', activeTab === 'home' ? 'active' : '']"
        @tap="switchTab"
        data-tab="home"
      >
        主页
      </view>
      <view
        :class="['tab', activeTab === 'works' ? 'active' : '']"
        @tap="switchTab"
        data-tab="works"
      >
        作品
      </view>
      <view
        :class="['tab', activeTab === 'notice' ? 'active' : '']"
        @tap="switchTab"
        data-tab="notice"
      >
        通告
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-area">
      <!-- 主页内容 -->
      <view class="home-content" v-if="activeTab === 'home'">
        <!-- 形象信息 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">形象信息</text>
            <text class="edit-btn" v-if="isSelf">编辑</text>
          </view>
          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">{{ expertInfo?.age || "--" }}</text>
              <text class="info-value">年龄</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{ expertInfo?.height || "--" }}</text>
              <text class="info-value">身高</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{ expertInfo?.weight || "--" }}</text>
              <text class="info-value">体重</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{ expertInfo?.city || "--" }}</text>
              <text class="info-value">城市</text>
            </view>
          </view>
        </view>

        <!-- 自我简介 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">自我简介</text>
            <text class="edit-btn" v-if="isSelf">编辑</text>
          </view>
          <view class="intro-content" v-if="introduction">
            <text>{{ introduction }}</text>
          </view>
          <view class="empty-placeholder" v-else>
            <text class="placeholder-text">请填写自我简介</text>
          </view>
        </view>

        <!-- 形象照片 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">形象照片</text>
            <text class="add-btn" v-if="isSelf">添加</text>
          </view>
          <view
            class="photos-grid"
            v-if="expertInfo?.photos && expertInfo.photos.length > 0"
          >
            <image
              v-for="(item, index) in expertInfo.photos"
              :key="index"
              class="profile-photo"
              :src="item"
              mode="aspectFill"
              @tap="previewImage"
              :data-index="index"
            ></image>
          </view>
          <view class="empty-photos-grid" v-else>
            <view class="empty-photo-item">
              <view class="placeholder-icon">+</view>
            </view>
            <view class="empty-photo-item">
              <view class="placeholder-icon">+</view>
            </view>
            <view class="empty-photo-item">
              <view class="placeholder-icon">+</view>
            </view>
          </view>
        </view>

        <!-- 形象视频 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">形象视频</text>
            <text class="add-btn" v-if="isSelf">添加</text>
          </view>
          <view class="video-container" v-if="videoUrl">
            <video 
              class="profile-video" 
              :src="videoUrl" 
              controls
              object-fit="cover"
              poster="/static/images/default-video-poster.jpg"
            ></video>
          </view>
          <view class="empty-video-container" v-else>
            <view class="empty-video">
              <view class="placeholder-icon">+</view>
              <text class="placeholder-text">添加视频</text>
            </view>
          </view>
        </view>

        <!-- 形象风格 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">形象风格</text>
            <text class="edit-btn" v-if="isSelf">编辑</text>
          </view>
          <view
            class="tag-content"
            v-if="styleList && styleList.length > 0"
          >
            <view class="tag-item" v-for="(item, index) in styleList" :key="index">{{
              item
            }}</view>
          </view>
          <view class="empty-placeholder" v-else>
            <text class="placeholder-text">请添加形象风格</text>
          </view>
        </view>

        <!-- 身份标签 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">身份标签</text>
            <text class="edit-btn" v-if="isSelf">编辑</text>
          </view>
          <view class="tag-content" v-if="tagList && tagList.length > 0">
            <view class="tag-item" v-for="(item, index) in tagList" :key="index">{{
              item
            }}</view>
          </view>
          <view class="empty-placeholder" v-else>
            <text class="placeholder-text">请添加身份标签</text>
          </view>
        </view>
      </view>

      <!-- 作品内容 -->
      <view class="works-content" v-if="activeTab === 'works'">
        <view class="empty-placeholder">
          <text class="placeholder-text">暂无作品</text>
        </view>
      </view>

      <!-- 通告内容 -->
      <view class="notice-content" v-if="activeTab === 'notice'">
        <view class="empty-placeholder">
          <text class="placeholder-text">暂无通告</text>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <!-- 返回按钮已删除 -->
      <view class="bottom-action-btn" @tap="toggleFavorite">
        <view class="action-icon" :class="{'favorite-icon-active': expertInfo?.isFavorite}">♥</view>
      </view>
      <view class="bottom-action-btn" @tap="shareExpert">
        <view class="action-icon share-icon">↗</view>
      </view>
      <view class="bottom-action-btn contact-btn" @tap="chatWithExpert">
        <text class="contact-btn-text">极速联系</text>
      </view>
      <view class="bottom-action-btn order-btn" @tap="makeOrder">
        <text class="order-btn-text">约单</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" v-if="isLoading">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>
</template>

<script>
import { getMemberInfo, followMember, unfollowMember, isFollowing } from '@/api/index.js';

export default {
  data() {
    return {
      expertInfo: {
        id: '',
        name: '',
        avatar: '',
        gender: '',
        age: '',
        height: '',
        weight: '',
        city: '',
        photos: [],
        popularity: 0,
        fansCount: 0,
        followCount: 0,
        viewCount: 0,
        isFavorite: false
      },
      activeTab: "home",
      completionRate: 0,
      introduction: "",
      videoUrl: "", // 修改为空字符串，从后端获取
      styleList: [], // 形象风格列表
      tagList: [], // 身份标签列表
      isSelf: false,
      loaded: false,
      id: null,
      name: "",
      tabs: ["动态", "照片", "介绍"],
      currentTab: 0,
      postList: [
        {
          id: 1,
          content: "今天的旅拍真的很开心，阳光正好，希望大家喜欢我分享的照片~",
          images: [
            "https://ai-public.mastergo.com/ai/img_res/f9f96db8c75bb9a3796f89b787483315.jpg",
            "https://ai-public.mastergo.com/ai/img_res/d9ceb72046b725e7fac5c92a88a5d16f.jpg",
          ],
          likeCount: 356,
          commentCount: 42,
          time: "2小时前",
        },
        {
          id: 2,
          content: "分享一组最近的生活照，周末愉快！",
          images: [
            "https://ai-public.mastergo.com/ai/img_res/770071c7d2c2f4d7714748e9b3343097.jpg",
            "https://ai-public.mastergo.com/ai/img_res/df7007ddb7e7fa522e869336633b8666.jpg",
            "https://ai-public.mastergo.com/ai/img_res/4ef9bf46e1eb2f5cccdc17d3603458b4.jpg",
          ],
          likeCount: 218,
          commentCount: 26,
          time: "昨天",
        },
      ],
      photoAlbums: [
        {
          id: 1,
          name: "旅行日记",
          cover:
            "https://ai-public.mastergo.com/ai/img_res/f9f96db8c75bb9a3796f89b787483315.jpg",
          count: 24,
        },
        {
          id: 2,
          name: "日常生活",
          cover:
            "https://ai-public.mastergo.com/ai/img_res/770071c7d2c2f4d7714748e9b3343097.jpg",
          count: 36,
        },
        {
          id: 3,
          name: "美食记录",
          cover:
            "https://ai-public.mastergo.com/ai/img_res/df7007ddb7e7fa522e869336633b8666.jpg",
          count: 18,
        },
      ],
      skills: ["摄影", "旅行", "美食", "穿搭"],
      hobbyTags: ["看电影", "听音乐", "读书", "徒步", "咖啡"],
    };
  },
  onLoad: function (options) {
    console.log("预览页面加载，参数:", options);

    // 获取路由参数
    const id = options.id;
    const name = options.name || "达人";

    this.id = id;
    this.name = name;

    // 调用API获取达人信息
    this.fetchExpertInfo(id);

    // 修改导航栏标题
    uni.setNavigationBarTitle({
      title: this.name + "的主页",
    });
  },
  methods: {
    // 获取达人信息
    fetchExpertInfo(memberId) {
      // 调用会员信息接口，包含附加信息（粉丝、关注等）
      getMemberInfo(memberId, { 
        withExtra: true, 
        withPrivacy: false 
      }).then(res => {
        console.log('API返回数据:', res.data);
        if (res && res.data) {
          const expertInfo = res.data;
          
          // 处理照片字段，如果是字符串则按逗号分割成数组
          let photos = [];
          if (expertInfo.images) {
            if (typeof expertInfo.images === 'string') {
              photos = expertInfo.images.split(',').filter(item => item);
            } else if (Array.isArray(expertInfo.images)) {
              photos = expertInfo.images;
            }
          }
          
          // 字段映射
          const mappedExpertInfo = {
            id: expertInfo.id,
            name: expertInfo.nickname || '',
            avatar: expertInfo.avatar || '/static/images/default-avatar.png',
            gender: expertInfo.gender === 2 ? '女' : '男',
            age: expertInfo.age || '',
            constellation: expertInfo.constellation || '',
            height: expertInfo.height ? expertInfo.height + 'cm' : '',
            weight: expertInfo.weight ? expertInfo.weight + 'kg' : '',
            city: expertInfo.cityList && expertInfo.cityList.length > 0 ? expertInfo.cityList[0] : '',
            photos: photos, // 使用处理后的照片数组
            tags: expertInfo.tags || [],
            // 统计字段
            popularity: expertInfo.popularity || 0,
            fansCount: expertInfo.fansCount || 0,
            followCount: expertInfo.followCount || 0,
            viewCount: expertInfo.viewCount || 0,
            // 关注状态
            isFavorite: expertInfo.following || false,
          };
          
          this.expertInfo = mappedExpertInfo;
          this.loaded = true;
          
          // 设置其他数据
          this.introduction = expertInfo.context || '';
          this.videoUrl = expertInfo.video || '';
          
          // 处理风格和标签列表，如果是字符串则按逗号分割
          if (expertInfo.styleIds) {
            if (typeof expertInfo.styleIds === 'string') {
              this.styleList = expertInfo.styleIds.split(',').filter(item => item);
            } else if (Array.isArray(expertInfo.styleIds)) {
              this.styleList = expertInfo.styleIds;
            } else {
              this.styleList = [];
            }
          }
          
          if (expertInfo.identify) {
            if (typeof expertInfo.identify === 'string') {
              this.tagList = expertInfo.identify.split(',').filter(item => item);
            } else if (Array.isArray(expertInfo.identify)) {
              this.tagList = expertInfo.identify;
            } else {
              this.tagList = [];
            }
          }
          
          // 获取资料完成度
          this.completionRate =  this.getCompletionRate();
          
          // 如果没有自我介绍，生成一个
          if (!this.introduction) {
            this.introduction = this.generateRandomIntroduction(mappedExpertInfo);
          }
          
          console.log('映射后的数据:', this.expertInfo);
          console.log('照片数组:', photos);
          console.log('视频URL:', this.videoUrl);
          console.log('风格列表:', this.styleList);
          console.log('标签列表:', this.tagList);
        }
      }).catch((error) => {
        console.error("获取达人信息失败:", error);
        uni.showToast({
          title: "获取达人信息失败",
          icon: "none",
          duration: 2000,
        });
      });
    },

    // 获取资料完成度
    async getCompletionRate() {
      try {
        const app = getApp().globalData;
        const res = await this.$requestHttp.get(app.commonApi.getMemberCompletion, {});
        
        if (res.code === 200) {
          console.log('个人资料完整度:', res.data + '%');
          return res.data;
        } else {
          console.error('获取资料完整度失败:', res);
          return 0;
        }
      } catch (error) {
        console.error('获取资料完整度异常:', error);
        return 0;
      }
    },

    // 生成随机自我介绍文本
    generateRandomIntroduction: function (expertInfo) {
      const intros = [
        `大家好，我是${expertInfo.name}，${expertInfo.age}岁，来自${expertInfo.city}。我热爱生活，喜欢结交新朋友，希望能在这个平台上认识更多志同道合的朋友。`,
        `嗨！我是${expertInfo.name}，${expertInfo.gender}，${
          expertInfo.city
        }人。性格开朗，喜欢${
          expertInfo.gender === "女"
            ? "旅行、看书、听音乐"
            : "运动、摄影、户外活动"
        }。希望能在这里遇见有趣的灵魂。`,
      ];

      // 随机选择一个介绍
      const randomIndex = Math.floor(Math.random() * intros.length);
      return intros[randomIndex];
    },

    // 生成形象风格列表
    generateStyleList: function (expertInfo) {
      const allStyles = [
        "甜美",
        "清新",
        "时尚",
        "成熟",
        "优雅",
        "可爱",
        "帅气",
        "阳光",
        "街头",
        "复古",
        "文艺",
        "日系",
        "欧美",
        "韩系",
        "古风",
        "运动",
      ];

      // 根据性别选择风格
      let styles = [];
      if (expertInfo.gender === "女") {
        styles = ["甜美", "清新", "时尚"];
        if (parseInt(expertInfo.age) > 25) {
          styles.push("优雅");
        } else {
          styles.push("可爱");
        }
      } else {
        styles = ["帅气", "阳光", "时尚"];
        if (parseInt(expertInfo.age) > 25) {
          styles.push("成熟");
        } else {
          styles.push("街头");
        }
      }

      return styles;
    },

    // 生成身份标签列表
    generateTagList: function (expertInfo) {
      const allTags = [
        "模特",
        "摄影师",
        "舞者",
        "演员",
        "歌手",
        "KOL",
        "旅行博主",
        "美食博主",
        "运动达人",
        "时尚博主",
        "美妆达人",
        "健身达人",
      ];

      // 随机选择2-3个标签
      const count = Math.floor(Math.random() * 2) + 2;
      let tags = [];

      // 如果之前有标签数据就用之前的
      if (expertInfo.tags && expertInfo.tags.length > 0) {
        return expertInfo.tags;
      }

      // 否则随机生成
      const shuffled = [...allTags].sort(() => 0.5 - Math.random());
      tags = shuffled.slice(0, count);

      return tags;
    },

    // 切换标签页
    switchTab: function (e) {
      const tab = e.currentTarget.dataset.tab;
      // 直接赋值
      this.activeTab = tab;
    },

    // 点击关注
    followExpert: function () {
      uni.showToast({
        title: "关注成功",
        icon: "success",
        duration: 1500,
      });
    },

    // 点击私聊
    chatWithExpert: function () {
      const expertInfo = this.expertInfo;
      if (!expertInfo || !expertInfo.id) {
        uni.showToast({
          title: "用户信息不完整",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      uni.navigateTo({
        url: "/pages/chat/chat-detail?id=" + expertInfo.id + "&name=" + (expertInfo.name || '用户'),
        fail: function (err) {
          console.error("跳转聊天页面失败:", err);
          uni.showToast({
            title: "聊天功能暂未开放",
            icon: "none",
            duration: 1500,
          });
        },
      });
    },

    // 切换收藏状态
    toggleFavorite: function () {
      // 获取当前达人信息
      const expertInfo = this.expertInfo;

      if (!expertInfo || !expertInfo.id) {
        uni.showToast({
          title: "用户信息不完整",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      // 显示加载提示
      uni.showLoading({
        title: expertInfo.isFavorite ? "取消关注中..." : "关注中...",
        mask: true
      });

      // 调用关注/取消关注API
      const apiCall = expertInfo.isFavorite ? unfollowMember(expertInfo.id) : followMember(expertInfo.id);
      
      apiCall.then(res => {
        uni.hideLoading();
        if (res && res.data) {
          // 更新当前页面关注状态
          expertInfo.isFavorite = !expertInfo.isFavorite;
          this.expertInfo = {...expertInfo}; // 使用解构赋值确保视图更新
          
          // 提示操作成功
          uni.showToast({
            title: expertInfo.isFavorite ? "关注成功" : "取消关注成功",
            icon: "success",
            duration: 1500,
          });
          
          // 更新本地存储，用于返回达人列表时同步状态
          uni.setStorageSync("currentExpertInfo", expertInfo);
        } else {
          uni.showToast({
            title: "操作失败",
            icon: "none",
            duration: 1500,
          });
        }
      }).catch((error) => {
        uni.hideLoading();
        console.error("关注操作失败:", error);
        uni.showToast({
          title: "操作失败，请重试",
          icon: "none",
          duration: 1500,
        });
      });
    },



    // 点赞动态
    likePost: function (e) {
      const postId = e.currentTarget.dataset.id;
      const postList = this.postList;
      const post = postList.find((item) => item.id === postId);

      if (post) {
        post.likeCount++;
        // 直接赋值
        this.postList = postList;

        uni.showToast({
          title: "点赞成功",
          icon: "success",
          duration: 1500,
        });
      }
    },

    // 查看相册
    viewAlbum: function (e) {
      const albumId = e.currentTarget.dataset.id;
      const albumName = e.currentTarget.dataset.name;

      uni.showToast({
        title: "查看相册: " + albumName,
        icon: "none",
        duration: 1500,
      });
    },

    // 预览图片
    previewImage: function (e) {
      const index = e.currentTarget.dataset.index;
      const photos = this.expertInfo?.photos || [];
      
      if (!photos.length) {
        uni.showToast({
          title: "没有可预览的照片",
          icon: "none",
          duration: 1500,
        });
        return;
      }
      
      // 确保当前照片存在
      if (photos[index]) {
        uni.previewImage({
          current: photos[index],
          urls: photos,
          fail: (err) => {
            console.error("预览图片失败:", err);
            uni.showToast({
              title: "预览图片失败",
              icon: "none",
              duration: 1500,
            });
          }
        });
      } else {
        uni.showToast({
          title: "照片不存在",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 返回上一页
    goBack: function () {
      uni.navigateBack();
    },

    // 分享达人信息
    shareExpert: function() {
      const expertInfo = this.expertInfo;
      if (!expertInfo || !expertInfo.id) {
        uni.showToast({
          title: "用户信息不完整",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      // 小程序环境使用小程序分享API
      if (uni.canIUse('button.open-type.share')) {
        uni.showToast({
          title: '请点击右上角分享',
          icon: 'none',
          duration: 2000
        });
      } else {
        // H5或APP环境
        uni.share({
          provider: "weixin",
          scene: "WXSceneSession",
          type: 0,
          title: expertInfo.name + "的个人主页",
          summary: "来自闲时有伴的达人主页",
          imageUrl: expertInfo.avatar || '/static/images/default-avatar.png',
          href: "/pages/profile/preview?id=" + expertInfo.id + "&name=" + (expertInfo.name || '用户'),
          success: function (res) {
            console.log("分享成功：", res);
          },
          fail: function (err) {
            console.log("分享失败：", err);
            uni.showToast({
              title: '分享功能暂未开放',
              icon: 'none'
            });
          }
        });
      }
    },

    onShareAppMessage: function () {
      const expertInfo = this.expertInfo;
      const id = expertInfo?.id || this.id || '';
      const name = expertInfo?.name || this.name || '用户';

      return {
        title: name + "的个人主页 - 闲时有伴",
        path: "/pages/profile/preview?id=" + id + "&name=" + name,
      };
    },

    // 约单功能
    makeOrder: function () {
      const expertInfo = this.expertInfo;
      if (!expertInfo || !expertInfo.id) {
        uni.showToast({
          title: "用户信息不完整",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      uni.navigateTo({
        url: "/pages/publish-demand/publish-demand?expertId=" + 
             expertInfo.id + "&expertName=" + (expertInfo.name || '用户'),
        fail: function (res) {
          console.error("跳转约单页面失败:", res);
          uni.showToast({
            title: "约单功能开发中",
            icon: "none",
            duration: 1500,
          });
        },
      });
    },

    // 格式化用户ID
    formatUserId: function (id) {
      if (!id) return "0000000";
      
      // 如果ID已经是纯数字且长度为7，则直接返回
      if (/^\d{7}$/.test(id)) {
        return id;
      }
      
      // 如果ID是纯数字但长度不是7
      if (/^\d+$/.test(id)) {
        // 如果长度小于7，在前面补0
        if (id.toString().length < 7) {
          return id.toString().padStart(7, '0');
        }
        // 如果长度大于7，取后7位
        return id.toString().substring(id.toString().length - 7);
      }
      
      // 如果ID不是纯数字，生成一个基于ID的哈希值
      let hash = 0;
      for (let i = 0; i < id.toString().length; i++) {
        hash = ((hash << 5) - hash) + id.toString().charCodeAt(i);
        hash |= 0; // 转换为32位整数
      }
      
      // 取绝对值并截取后7位，不足7位前面补0
      const numStr = Math.abs(hash).toString();
      if (numStr.length < 7) {
        return numStr.padStart(7, '0');
      }
      return numStr.substring(numStr.length - 7);
    }
  },
};
</script>

<style scoped>
/* 整体容器 */
.profile-container {
  min-height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 顶部导航栏 */
.nav-bar {
  height: 90rpx;
  background-color: #ff8c00;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  position: relative;
  color: #fff;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.back-icon {
  font-size: 36rpx;
  font-weight: bold;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
}

.right-actions {
  display: flex;
  align-items: center;
}

.action-dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #fff;
  border-radius: 50%;
  margin: 0 4rpx;
}

.share-icon {
  margin-left: 20rpx;
  font-size: 36rpx;
}

/* 个人基本信息区域 */
.profile-header {
  background-color: #ff8c00;
  padding: 100rpx 30rpx 40rpx;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  margin-bottom: 20rpx;
}

/* 姓名和性别行 */
.username-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 8rpx;
  line-height: 1;
}

.gender {
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  line-height: 1;
  transform: translateY(-12rpx);
  position: relative;
  top: -1rpx;
  margin-left: 4rpx;
}

.gender.female {
  color: #fff;
  text-shadow: 0 0 3rpx rgba(0, 0, 0, 0.2);
}

.gender.male {
  color: #fff;
  text-shadow: 0 0 3rpx rgba(0, 0, 0, 0.2);
}

/* 用户类型 */
.user-type {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20rpx;
  padding: 4rpx 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
}

.completion-bar {
  width: 80%;
  margin-bottom: 30rpx;
}

.completion-text {
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

.progress-bar {
  height: 10rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 10rpx;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #fff;
  border-radius: 10rpx;
}

/* 用户数据统计 */
.user-stats {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-num {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 标签栏 */
.tab-bar {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.tab {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #ff8c00;
  font-weight: 500;
}

.tab.active:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff8c00;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 20rpx;
}

/* 信息区块通用样式 */
.info-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.edit-btn,
.add-btn {
  font-size: 26rpx;
  color: #ff8c00;
}

/* 形象信息样式 */
.info-grid {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.info-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.info-value {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 自我简介样式 */
.intro-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 空白占位符 */
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.empty-placeholder .placeholder-icon {
  margin-bottom: 12rpx;
}

/* 视频样式 */
.video-container {
  width: 100%;
  margin-top: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #000;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.profile-video {
  width: 100%;
  height: 420rpx; /* 16:9比例的高度 */
  border-radius: 12rpx;
  background-color: #000;
}

.empty-video-container {
  width: 100%;
  margin-top: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.empty-video {
  width: 100%;
  height: 420rpx; /* 与视频相同高度 */
  background-color: #f7f7f7;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-video .placeholder-icon {
  font-size: 60rpx;
  color: #ccc;
  margin-bottom: 10rpx;
}

.empty-video .placeholder-text {
  font-size: 26rpx;
  color: #999;
}

/* 照片网格样式 */
.photos-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 11rpx;
}

.profile-photo {
  width: calc((100% - 24rpx) / 3); /* 3列布局，间隔12rpx */
  height: 280rpx;
  border-radius: 12rpx;
  background-color: #f2f2f2;
}

/* 空照片网格样式 */
.empty-photos-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 11rpx;
}

.empty-photo-item {
  width: calc((100% - 24rpx) / 3); /* 3列布局，间隔12rpx */
  height: 280rpx;
  background-color: #f7f7f7;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 占位图标样式 */
.placeholder-icon {
  font-size: 48rpx;
  color: #ccc;
  font-weight: 300;
}

/* 标签样式 */
.tag-content {
  display: flex;
  flex-wrap: wrap;
  margin: -6rpx;
}

.tag-item {
  padding: 8rpx 20rpx;
  background-color: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  border-radius: 999rpx;
  font-size: 24rpx;
  margin: 6rpx;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  width: 100%;
  margin-top: 30rpx;
  justify-content: center;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #fff;
  margin: 0 10rpx;
  max-width: 150rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border: 2rpx solid #fff;
}

.action-btn.follow {
  background-color: #ff8c00;
  border: 2rpx solid #ff8c00;
}

.action-btn.followed {
  background-color: rgba(255, 255, 255, 0.3);
  border: 2rpx solid #fff;
}

.action-btn.chat {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.action-btn.favorite {
  /* 关注按钮 */
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.action-btn.favorite-active {
  /* 已关注按钮 */
  background-color: #fff;
  color: #ff8c00;
  border: 2rpx solid #fff;
  font-weight: 500;
}

.action-btn.order {
  background-color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
  color: #fff;
  font-weight: 500;
}

/* 新的返回按钮样式 */
.back-btn-new {
  position: absolute;
  top: 20rpx;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.back-icon-new {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  transform: rotate(180deg); /* 将 > 旋转为 < */
}

/* 浮动返回按钮 */
.back-btn-floating {
  position: absolute;
  left: 30rpx;
  top: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.back-icon {
  font-size: 32rpx;
  color: #333;
}

/* 用户ID样式 */
.user-id {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.bottom-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
  height: 80rpx;
}

.action-icon {
  font-size: 48rpx;
  color: #666;
}

.favorite-icon-active {
  color: #ff5792;
}

.share-icon {
  color: #333;
  font-size: 42rpx;
  transform: rotate(45deg);
}

.contact-btn {
  background-color: #333333;
  height: 70rpx;
  border-radius: 35rpx;
  padding: 0 25rpx;
  margin: 0 10rpx;
  flex: 1;
  max-width: 180rpx;
}

.contact-btn-text {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 500;
}

.order-btn {
  background-color: #ff8c00;
  height: 70rpx;
  border-radius: 35rpx;
  padding: 0 25rpx;
  margin: 0 10rpx;
  flex: 1;
  max-width: 180rpx;
}

.order-btn-text {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 500;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid #f3f3f3;
  border-top: 8rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
