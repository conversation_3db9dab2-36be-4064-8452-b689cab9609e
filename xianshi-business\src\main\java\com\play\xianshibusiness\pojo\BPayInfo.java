package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * (BPayInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("b_pay_info")
@ApiModel("支付表")
public class BPayInfo extends BaseObjPo {

    //支付类型【枚举】金币/订单
    @ApiModelProperty(value = "支付类型【枚举】金币/订单")
    private String type;
    //订单ID
    @ApiModelProperty(value = "订单ID ")
    private String businessId;
    //订单总金额
    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalPrice;
    //支付返回参数
    @ApiModelProperty(value = "支付返回参数")
    private String payInfo;
    //支付时间
    @ApiModelProperty(value = "支付时间")
    private LocalDateTime payTime;
    //过期时间
    @ApiModelProperty(value = "过期时间")
    private LocalDateTime expireTime;
    //生成支付链接时间
    @ApiModelProperty(value = "生成支付链接时间")
    private LocalDateTime generateTime;
    //第三方系统订单ID
    @ApiModelProperty(value = "第三方系统订单ID")
    private String otherSysOrderId;
    //支付信息，如卡号
    @ApiModelProperty(value = "支付信息，如卡号")
    private String bankInfo;


}

