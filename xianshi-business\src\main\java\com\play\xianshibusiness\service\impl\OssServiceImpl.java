package com.play.xianshibusiness.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.play.xianshibusiness.config.OssConfig;
import com.play.xianshibusiness.service.OssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * OSS文件上传服务实现类
 */
@Slf4j
@Service
public class OssServiceImpl implements OssService {
    
    @Autowired
    private OSS ossClient;
    
    @Autowired
    private OssConfig ossConfig;
    
    // 允许的图片类型
    private static final String[] ALLOWED_IMAGE_TYPES = {
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp","image/avif"
    };
    
    // 最大文件大小 (10MB)
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;
    
    @Override
    public String uploadFile(MultipartFile file, String folder) {
        try {
            // 参数校验
            validateFile(file);
            
            // 生成文件路径
            String filePath = generateFilePath(file.getOriginalFilename(), folder);
            
            // 上传文件
            InputStream inputStream = file.getInputStream();
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossConfig.getBucketName(), filePath, inputStream);
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            
            // 生成访问URL
            String fileUrl = "https://" + ossConfig.getBucketName() + "." + ossConfig.getEndpoint().replace("http://", "") + "/" + filePath;
            
            log.info("文件上传成功: {}, URL: {}", filePath, fileUrl);
            return fileUrl;
            
        } catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public String uploadImage(MultipartFile file, String folder) {
        // 校验文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isImageFile(contentType)) {
            throw new RuntimeException("不支持的文件类型，请上传图片文件");
        }
        
        return uploadFile(file, folder);
    }
    
    @Override
    public boolean deleteFile(String fileUrl) {
        try {
            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(fileUrl);
            
            // 删除文件
            ossClient.deleteObject(ossConfig.getBucketName(), filePath);
            
            log.info("文件删除成功: {}", filePath);
            return true;
            
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public String generateFilePath(String originalFilename, String folder) {
        // 获取文件扩展名
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        
        // 生成UUID文件名
        String fileName = UUID.randomUUID().toString().replace("-", "") + extension;
        
        // 确保文件夹路径以/结尾
        if (!folder.endsWith("/")) {
            folder = folder + "/";
        }
        
        return folder + fileName;
    }
    
    /**
     * 测试OSS连接
     */
    public boolean testOssConnection() {
        try {
            // 尝试列出存储桶中的对象（最多1个）
            ossClient.listObjects(ossConfig.getBucketName()).getObjectSummaries().stream().limit(1).count();
            log.info("OSS连接测试成功");
            return true;
        } catch (Exception e) {
            log.error("OSS连接测试失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public Map<String, String> generateStsCredentials() {
        try {
            // 由于我们目前没有使用阿里云STS服务的完整依赖，这里提供一个简单的实现
            // 在实际生产环境中，应该使用阿里云STS SDK来获取临时凭证
            Map<String, String> credentials = new HashMap<>();
            credentials.put("accessKeyId", ossConfig.getAccessKeyId());
            credentials.put("accessKeySecret", ossConfig.getAccessKeySecret());
            // 这里没有真正的securityToken，我们直接使用空字符串
            credentials.put("securityToken", "");
            // 设置一个小时后过期
            Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000);
            credentials.put("expiration", expiration.toString());
            // 设置区域和存储桶
            credentials.put("region", ossConfig.getEndpoint().replace("http://", "").replace(".aliyuncs.com", ""));
            credentials.put("bucket", ossConfig.getBucketName());
            
            log.info("生成OSS临时凭证成功");
            return credentials;
        } catch (Exception e) {
            log.error("生成OSS临时凭证失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成OSS临时凭证失败: " + e.getMessage());
        }
    }
    
    /**
     * 校验文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }
        
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new RuntimeException("文件大小不能超过10MB");
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new RuntimeException("文件名不能为空");
        }
    }
    
    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String contentType) {
        for (String allowedType : ALLOWED_IMAGE_TYPES) {
            if (allowedType.equals(contentType)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 从URL中提取文件路径
     */
    private String extractFilePathFromUrl(String fileUrl) {
        // 移除协议和域名部分
        String bucketDomain = "https://" + ossConfig.getBucketName() + "." + ossConfig.getEndpoint().replace("http://", "") + "/";
        if (fileUrl.startsWith(bucketDomain)) {
            return fileUrl.substring(bucketDomain.length());
        }
        return fileUrl;
    }
} 