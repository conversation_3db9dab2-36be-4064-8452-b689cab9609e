package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.member.CMemberDto;
import com.play.xianshibusiness.enums.FansAttentionType;
import com.play.xianshibusiness.enums.ResultCode;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.CMemberFansAttentionMapper;
import com.play.xianshibusiness.mapper.CMemberMapper;
import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.pojo.CMemberFansAttention;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 关注/粉丝
 */
@Service
public class CMemberFansAttentionService {

    @Resource
    private CMemberFansAttentionMapper cMemberFansAttentionMapper;

    @Resource
    private CMemberMapper cMemberMapper;

    /**
     * 填充会员粉丝和关注信息
     * @param cMemberDto 会员DTO
     */
    public void fullMemberFansAttention(CMemberDto cMemberDto) {
        // 查询粉丝数量
        String fansCount = countFans(cMemberDto.getId());
        cMemberDto.setFansCount(fansCount);
        
        // 查询关注数量
        String followCount = countFollowing(cMemberDto.getId());
        cMemberDto.setFollowCount(followCount);
    }
    
    /**
     * 获取粉丝数量
     * @param memberId 会员ID
     * @return 粉丝数量
     */
    public String countFans(String memberId) {
        LambdaQueryWrapper<CMemberFansAttention> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CMemberFansAttention::getTargetId, memberId)
              .eq(CMemberFansAttention::getType, FansAttentionType.FOLLOW.getCode())
              .eq(CMemberFansAttention::getDeleted, false);
        return String.valueOf(cMemberFansAttentionMapper.selectCount(wrapper));
    }
    
    /**
     * 获取关注数量
     * @param memberId 会员ID
     * @return 关注数量
     */
    public String countFollowing(String memberId) {
        LambdaQueryWrapper<CMemberFansAttention> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CMemberFansAttention::getMemberId, memberId)
              .eq(CMemberFansAttention::getType, FansAttentionType.FOLLOW.getCode())
              .eq(CMemberFansAttention::getDeleted, false);
        return String.valueOf(cMemberFansAttentionMapper.selectCount(wrapper));
    }
    
    /**
     * 判断是否关注
     * @param memberId 会员ID
     * @param targetId 目标会员ID
     * @return 是否关注
     */
    public boolean isFollowing(String memberId, String targetId) {
        LambdaQueryWrapper<CMemberFansAttention> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CMemberFansAttention::getMemberId, memberId)
              .eq(CMemberFansAttention::getTargetId, targetId)
              .eq(CMemberFansAttention::getType, FansAttentionType.FOLLOW.getCode())
              .eq(CMemberFansAttention::getDeleted, false);
        return cMemberFansAttentionMapper.selectCount(wrapper) > 0;
    }
    
    /**
     * 关注会员
     * @param memberId 当前会员ID
     * @param targetId 目标会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean followMember(String memberId, String targetId) {
        // 验证目标会员是否存在
        CMember targetMember = cMemberMapper.selectById(targetId);
        if (targetMember == null || targetMember.getDeleted() || !targetMember.getAvailable()) {
            throw new GlobalException(ResultCode.InValid_Param, "目标会员不存在或已被禁用");
        }
        
        // 自己不能关注自己
        if (memberId.equals(targetId)) {
            throw new GlobalException(ResultCode.InValid_Param, "不能关注自己");
        }
        
        // 检查是否已关注
        if (isFollowing(memberId, targetId)) {
            return true; // 已关注，直接返回成功
        }
        
        // 创建关注记录
        CMemberFansAttention attention = new CMemberFansAttention();
        attention.setId(UUID.randomUUID().toString());
        attention.setMemberId(memberId);
        attention.setTargetId(targetId);
        attention.setType(FansAttentionType.FOLLOW.getCode());
        attention.setCreateTime(LocalDateTime.now());
        attention.setUpdateTime(LocalDateTime.now());
        attention.setAvailable(true);
        attention.setDeleted(false);
        
        return cMemberFansAttentionMapper.insert(attention) > 0;
    }
    
    /**
     * 取消关注
     * @param memberId 当前会员ID
     * @param targetId 目标会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean unfollowMember(String memberId, String targetId) {
        // 查找关注记录
        LambdaQueryWrapper<CMemberFansAttention> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CMemberFansAttention::getMemberId, memberId)
              .eq(CMemberFansAttention::getTargetId, targetId)
              .eq(CMemberFansAttention::getType, FansAttentionType.FOLLOW.getCode())
              .eq(CMemberFansAttention::getDeleted, false);
        
        CMemberFansAttention attention = cMemberFansAttentionMapper.selectOne(wrapper);
        if (attention == null) {
            return true; // 未关注，直接返回成功
        }
        
        // 逻辑删除
        attention.setDeleted(true);
        attention.setUpdateTime(LocalDateTime.now());
        
        return cMemberFansAttentionMapper.updateById(attention) > 0;
    }
    
    /**
     * 获取用户的关注列表
     * @param memberId 会员ID
     * @param page 页码
     * @param size 每页大小
     * @return 关注的会员列表
     */
    public List<CMemberDto> getFollowingList(String memberId, Integer page, Integer size) {
        // 分页查询
        IPage<CMemberFansAttention> pageModel = new Page<>(page, size);
        LambdaQueryWrapper<CMemberFansAttention> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CMemberFansAttention::getMemberId, memberId)
               .eq(CMemberFansAttention::getType, FansAttentionType.FOLLOW.getCode())
               .eq(CMemberFansAttention::getDeleted, false)
               .orderByDesc(CMemberFansAttention::getCreateTime);
        
        IPage<CMemberFansAttention> followPage = cMemberFansAttentionMapper.selectPage(pageModel, wrapper);
        
        // 提取目标用户ID列表
        List<String> targetIds = followPage.getRecords().stream()
                .map(CMemberFansAttention::getTargetId)
                .collect(Collectors.toList());
        
        if (targetIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询会员信息
        List<CMember> members = cMemberMapper.selectList(
                new LambdaQueryWrapper<CMember>()
                        .in(CMember::getId, targetIds)
                        .eq(CMember::getDeleted, false)
                        .eq(CMember::getAvailable, true)
        );
        
        // 转换为DTO
        return members.stream().map(member -> {
            CMemberDto dto = new CMemberDto();
            BeanUtils.copyProperties(member, dto);
            // 填充粉丝关注信息
            fullMemberFansAttention(dto);
            return dto;
        }).collect(Collectors.toList());
    }
    
    /**
     * 获取用户的粉丝列表
     * @param memberId 会员ID
     * @param page 页码
     * @param size 每页大小
     * @return 粉丝会员列表
     */
    public List<CMemberDto> getFansList(String memberId, Integer page, Integer size) {
        // 分页查询
        IPage<CMemberFansAttention> pageModel = new Page<>(page, size);
        LambdaQueryWrapper<CMemberFansAttention> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CMemberFansAttention::getTargetId, memberId)
               .eq(CMemberFansAttention::getType, FansAttentionType.FOLLOW.getCode())
               .eq(CMemberFansAttention::getDeleted, false)
               .orderByDesc(CMemberFansAttention::getCreateTime);
        
        IPage<CMemberFansAttention> fansPage = cMemberFansAttentionMapper.selectPage(pageModel, wrapper);
        
        // 提取粉丝用户ID列表
        List<String> fanIds = fansPage.getRecords().stream()
                .map(CMemberFansAttention::getMemberId)
                .collect(Collectors.toList());
        
        if (fanIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询会员信息
        List<CMember> members = cMemberMapper.selectList(
                new LambdaQueryWrapper<CMember>()
                        .in(CMember::getId, fanIds)
                        .eq(CMember::getDeleted, false)
                        .eq(CMember::getAvailable, true)
        );
        
        // 转换为DTO
        return members.stream().map(member -> {
            CMemberDto dto = new CMemberDto();
            BeanUtils.copyProperties(member, dto);
            // 填充粉丝关注信息
            fullMemberFansAttention(dto);
            // 判断当前用户是否已关注此粉丝（互相关注）
            dto.setFollowing(isFollowing(memberId, member.getId()));
            return dto;
        }).collect(Collectors.toList());
    }
    
    /**
     * 获取用户的关注数和粉丝数
     * @param memberId 会员ID
     * @return 关注数和粉丝数的Map
     */
    public Map<String, Object> getFollowCounts(String memberId) {
        Map<String, Object> result = new HashMap<>();
        result.put("followingCount", countFollowing(memberId));
        result.put("fansCount", countFans(memberId));
        return result;
    }
}

