package com.play.xianshibusiness.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.play.xianshibusiness.pojo.IMemberChat;
import org.apache.ibatis.annotations.Select;

/**
 * (IMemberChat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:18
 */
public interface IMemberChatMapper extends BaseMapper<IMemberChat> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<IMemberChat> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<IMemberChat> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<IMemberChat> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<IMemberChat> entities);

    /**
     * 获取与指定会员有聊天记录的所有会员ID列表
     * @param memberId 会员ID
     * @return 聊天对象ID列表
     */
    @Select("SELECT DISTINCT target_id FROM i_member_chat WHERE member_id = #{memberId} AND deleted = 0 " +
           "UNION " +
           "SELECT DISTINCT member_id FROM i_member_chat WHERE target_id = #{memberId} AND deleted = 0")
    List<String> getChatList(@Param("memberId") String memberId);

}

