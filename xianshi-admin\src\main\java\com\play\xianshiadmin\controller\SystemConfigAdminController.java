package com.play.xianshiadmin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.dto.config.BasicConfigDTO;
import com.play.xianshibusiness.dto.config.DataDictQueryDTO;
import com.play.xianshibusiness.dto.config.DataDictTypeDTO;
import com.play.xianshibusiness.dto.config.DataDictValueDTO;
import com.play.xianshibusiness.pojo.TBasicConfig;
import com.play.xianshibusiness.pojo.TBasicInfo;
import com.play.xianshibusiness.pojo.TDataDictType;
import com.play.xianshibusiness.pojo.TDataDictValue;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.TBasicConfigService;
import com.play.xianshibusiness.service.TBasicInfoService;
import com.play.xianshibusiness.service.TDataDictTypeService;
import com.play.xianshibusiness.service.TDataDictValueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 管理端系统配置控制器
 */
@RestController
@RequestMapping("/admin/system-config")
@Api(tags = "管理端系统配置API")
@RequireAdmin(checkRole = true, roles = {"ADMIN"})
public class SystemConfigAdminController {

    @Resource
    private TBasicConfigService basicConfigService;
    
    @Resource
    private TBasicInfoService basicInfoService;
    
    @Resource
    private TDataDictTypeService dataDictTypeService;
    
    @Resource
    private TDataDictValueService dataDictValueService;
    
    /**
     * 获取所有基础配置
     */
    @GetMapping("/basic-config")
    @ApiOperation(value = "获取所有基础配置")
    public Result<List<TBasicConfig>> getAllBasicConfig() {
        return ResultUtils.success(basicConfigService.getAllBasicConfig());
    }
    
    /**
     * 更新基础配置
     */
    @PutMapping("/basic-config")
    @ApiOperation(value = "更新基础配置")
    public Result<Boolean> updateBasicConfig(@RequestBody @Valid BasicConfigDTO configDTO) {
        return ResultUtils.success(basicConfigService.updateBasicConfig(configDTO));
    }
    
    /**
     * 获取系统基础信息
     */
    @GetMapping("/basic-info")
    @ApiOperation(value = "获取系统基础信息")
    public Result<TBasicInfo> getBasicInfo() {
        return ResultUtils.success(basicInfoService.getBasicInfo());
    }
    
    /**
     * 更新系统基础信息
     */
    @PutMapping("/basic-info")
    @ApiOperation(value = "更新系统基础信息")
    public Result<Boolean> updateBasicInfo(@RequestBody @Valid TBasicInfo basicInfo) {
        return ResultUtils.success(basicInfoService.updateBasicInfo(basicInfo));
    }
    
    /**
     * 获取数据字典类型列表
     */
    @GetMapping("/dict/types")
    @ApiOperation(value = "获取数据字典类型列表")
    public Result<Page<TDataDictType>> pageDictTypes(DataDictQueryDTO queryDTO) {
        return ResultUtils.success(dataDictTypeService.pageDictTypes(queryDTO));
    }
    
    /**
     * 获取数据字典类型详情
     */
    @GetMapping("/dict/type/{id}")
    @ApiOperation(value = "获取数据字典类型详情")
    public Result<TDataDictType> getDictTypeDetail(
            @ApiParam(value = "类型ID", required = true) @PathVariable String id) {
        return ResultUtils.success(dataDictTypeService.getDictTypeDetail(id));
    }
    
    /**
     * 创建数据字典类型
     */
    @PostMapping("/dict/type")
    @ApiOperation(value = "创建数据字典类型")
    public Result<String> createDictType(@RequestBody @Valid DataDictTypeDTO typeDTO) {
        return ResultUtils.success(dataDictTypeService.createDictType(typeDTO));
    }
    
    /**
     * 更新数据字典类型
     */
    @PutMapping("/dict/type/{id}")
    @ApiOperation(value = "更新数据字典类型")
    public Result<Boolean> updateDictType(
            @ApiParam(value = "类型ID", required = true) @PathVariable String id,
            @RequestBody @Valid DataDictTypeDTO typeDTO) {
        return ResultUtils.success(dataDictTypeService.updateDictType(id, typeDTO));
    }
    
    /**
     * 删除数据字典类型
     */
    @DeleteMapping("/dict/type/{id}")
    @ApiOperation(value = "删除数据字典类型")
    public Result<Boolean> deleteDictType(
            @ApiParam(value = "类型ID", required = true) @PathVariable String id) {
        return ResultUtils.success(dataDictTypeService.deleteDictType(id));
    }
    
    /**
     * 获取数据字典值列表
     */
    @GetMapping("/dict/values")
    @ApiOperation(value = "获取数据字典值列表")
    public Result<List<TDataDictValue>> getDictValues(
            @ApiParam(value = "类型编码", required = true) @RequestParam String typeCode) {
        return ResultUtils.success(dataDictValueService.getDictValuesByTypeCode(typeCode));
    }
    
    /**
     * 获取数据字典值详情
     */
    @GetMapping("/dict/value/{id}")
    @ApiOperation(value = "获取数据字典值详情")
    public Result<TDataDictValue> getDictValueDetail(
            @ApiParam(value = "值ID", required = true) @PathVariable String id) {
        return ResultUtils.success(dataDictValueService.getDictValueDetail(id));
    }
    
    /**
     * 创建数据字典值
     */
    @PostMapping("/dict/value")
    @ApiOperation(value = "创建数据字典值")
    public Result<String> createDictValue(@RequestBody @Valid DataDictValueDTO valueDTO) {
        return ResultUtils.success(dataDictValueService.createDictValue(valueDTO));
    }
    
    /**
     * 更新数据字典值
     */
    @PutMapping("/dict/value/{id}")
    @ApiOperation(value = "更新数据字典值")
    public Result<Boolean> updateDictValue(
            @ApiParam(value = "值ID", required = true) @PathVariable String id,
            @RequestBody @Valid DataDictValueDTO valueDTO) {
        return ResultUtils.success(dataDictValueService.updateDictValue(id, valueDTO));
    }
    
    /**
     * 删除数据字典值
     */
    @DeleteMapping("/dict/value/{id}")
    @ApiOperation(value = "删除数据字典值")
    public Result<Boolean> deleteDictValue(
            @ApiParam(value = "值ID", required = true) @PathVariable String id) {
        return ResultUtils.success(dataDictValueService.deleteDictValue(id));
    }
    
    /**
     * 批量更新数据字典值排序
     */
    @PostMapping("/dict/value/sort")
    @ApiOperation(value = "批量更新数据字典值排序")
    public Result<Boolean> batchUpdateDictValueSort(
            @RequestBody Map<String, Integer> sortMap) {
        return ResultUtils.success(dataDictValueService.batchUpdateSort(sortMap));
    }
} 