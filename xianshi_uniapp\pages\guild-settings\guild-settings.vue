<template>
  <view class="container">
    <!-- 公会信息卡片 -->
    <view class="guild-info-card">
      <view class="cover-image">
        <image src="/static/images/default-banner.jpg" mode="aspectFill" />
        <view class="edit-cover" @tap="uploadCover">
          <text class="edit-icon">📷</text>
        </view>
      </view>
      
      <view class="guild-profile">
        <image class="guild-logo" :src="guildInfo.logo" mode="aspectFill" />
        <view class="profile-content">
          <view class="guild-name">{{ guildInfo.name }}</view>
          <view class="guild-desc">{{ guildInfo.description }}</view>
        </view>
        <view class="edit-profile" @tap="editBasicInfo">
          <text class="edit-text">编辑</text>
        </view>
      </view>
    </view>
    
    <!-- 设置项列表 -->
    <view class="settings-list">
      <!-- 基本信息设置 -->
      <view class="setting-group">
        <view class="group-title">基本信息</view>
        
        <view class="setting-item" @tap="editGuildName">
          <text class="setting-label">公会名称</text>
          <view class="setting-value">
            <text class="value-text">{{ guildInfo.name }}</text>
            <text class="arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @tap="editGuildDesc">
          <text class="setting-label">公会简介</text>
          <view class="setting-value">
            <text class="value-text ellipsis">{{ guildInfo.description }}</text>
            <text class="arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @tap="editContactInfo">
          <text class="setting-label">联系方式</text>
          <view class="setting-value">
            <text class="value-text">{{ guildInfo.contact || '未设置' }}</text>
            <text class="arrow">></text>
          </view>
        </view>
      </view>
      
      <!-- 业务设置 -->
      <view class="setting-group">
        <view class="group-title">业务设置</view>
        
        <view class="setting-item" @tap="editServiceAreas">
          <text class="setting-label">服务地区</text>
          <view class="setting-value">
            <text class="value-text">{{ guildInfo.serviceAreas.join(', ') || '未设置' }}</text>
            <text class="arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @tap="editServiceCategories">
          <text class="setting-label">服务类别</text>
          <view class="setting-value">
            <text class="value-text ellipsis">{{ guildInfo.serviceCategories.join(', ') || '未设置' }}</text>
            <text class="arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @tap="editMemberRequirements">
          <text class="setting-label">成员要求</text>
          <view class="setting-value">
            <text class="value-text">{{ guildInfo.memberRequirements || '未设置' }}</text>
            <text class="arrow">></text>
          </view>
        </view>
      </view>
      
      <!-- 认证信息 -->
      <view class="setting-group">
        <view class="group-title">认证信息</view>
        
        <view class="setting-item" @tap="viewVerification">
          <text class="setting-label">实名认证</text>
          <view class="setting-value">
            <text class="status-tag" :class="guildInfo.verified ? 'verified' : 'unverified'">
              {{ guildInfo.verified ? '实名完成' : '实名未完成' }}
            </text>
            <text class="arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @tap="viewLicense">
          <text class="setting-label">营业执照</text>
          <view class="setting-value">
            <text class="status-tag" :class="guildInfo.hasLicense ? 'verified' : 'unverified'">
              {{ guildInfo.hasLicense ? '已上传' : '未上传' }}
            </text>
            <text class="arrow">></text>
          </view>
        </view>
      </view>
      
      <!-- 账号设置 -->
      <view class="setting-group">
        <view class="group-title">账号设置</view>
        
        <view class="setting-item" @tap="changePassword">
          <text class="setting-label">修改密码</text>
          <view class="setting-value">
            <text class="arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @tap="bindPhone">
          <text class="setting-label">绑定手机</text>
          <view class="setting-value">
            <text class="value-text">{{ maskPhone(guildInfo.phone) || '未绑定' }}</text>
            <text class="arrow">></text>
          </view>
        </view>
        
<!-- 已移除切换角色功能 -->
      </view>
      
      <!-- 安全退出 -->
      <view class="logout-btn" @tap="logout">
        安全退出
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      guildInfo: {
        name: '闲时娱乐公会',
        logo: '/static/images/default-avatar.png',
        description: '专注于提供高品质休闲娱乐陪玩服务，拥有多名专业达人，为用户打造愉悦的闲暇时光。',
        contact: '13800138000',
        serviceAreas: ['成都', '重庆', '杭州'],
        serviceCategories: ['休闲娱乐', 'KTV', '桌游', '运动'],
        memberRequirements: '年龄20岁以上，有相关经验',
        verified: true,
        hasLicense: true,
        phone: '13800138000'
      }
    };
  },
  methods: {
    uploadCover() {
      uni.showToast({
        title: '上传封面功能开发中',
        icon: 'none'
      });
    },
    
    editBasicInfo() {
      uni.navigateTo({
        url: '/pages/guild-settings/basic-info'
      });
    },
    
    editGuildName() {
      this.navigateToEdit('name', this.guildInfo.name, '公会名称');
    },
    
    editGuildDesc() {
      this.navigateToEdit('description', this.guildInfo.description, '公会简介', true);
    },
    
    editContactInfo() {
      this.navigateToEdit('contact', this.guildInfo.contact, '联系方式');
    },
    
    editServiceAreas() {
      uni.navigateTo({
        url: '/pages/guild-settings/service-areas'
      });
    },
    
    editServiceCategories() {
      uni.navigateTo({
        url: '/pages/guild-settings/service-categories'
      });
    },
    
    editMemberRequirements() {
      this.navigateToEdit('memberRequirements', this.guildInfo.memberRequirements, '成员要求', true);
    },
    
    viewVerification() {
      uni.navigateTo({
        url: '/pages/guild-settings/verification'
      });
    },
    
    viewLicense() {
      uni.navigateTo({
        url: '/pages/guild-settings/license'
      });
    },
    
    changePassword() {
      uni.navigateTo({
        url: '/pages/guild-settings/change-password'
      });
    },
    
    bindPhone() {
      uni.navigateTo({
        url: '/pages/guild-settings/bind-phone'
      });
    },
    
    // switchRole方法已移除
    
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除登录信息
            uni.removeStorageSync('token');
            uni.removeStorageSync('userInfo');
            
            // 跳转到登录页
            uni.reLaunch({
              url: '/pages/login/login'
            });
          }
        }
      });
    },
    
    // 通用编辑页面导航
    navigateToEdit(field, value, title, isTextarea = false) {
      uni.navigateTo({
        url: `/pages/guild-settings/edit-field?field=${field}&value=${encodeURIComponent(value || '')}&title=${encodeURIComponent(title)}&isTextarea=${isTextarea}`
      });
    },
    
    // 手机号码脱敏
    maskPhone(phone) {
      if (!phone) return '';
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 公会信息卡片 */
.guild-info-card {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  position: relative;
}

.cover-image {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.cover-image image {
  width: 100%;
  height: 100%;
}

.edit-cover {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.guild-profile {
  padding: 30rpx;
  display: flex;
  position: relative;
}

.guild-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
  margin-top: -80rpx;
  background-color: #ffffff;
}

.profile-content {
  margin-left: 20rpx;
  flex: 1;
}

.guild-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.guild-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.edit-profile {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
}

.edit-text {
  font-size: 28rpx;
  color: #ff8c00;
}

/* 设置列表 */
.settings-list {
  padding: 0 20rpx;
}

.setting-group {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.group-title {
  font-size: 28rpx;
  color: #999;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 30rpx;
  color: #333;
}

.setting-value {
  display: flex;
  align-items: center;
}

.value-text {
  font-size: 28rpx;
  color: #999;
  margin-right: 10rpx;
  max-width: 400rpx;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow {
  color: #ccc;
  font-size: 28rpx;
}

.status-tag {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.verified {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.unverified {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

/* 退出按钮 */
.logout-btn {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  text-align: center;
  font-size: 32rpx;
  color: #ff5a5f;
  margin-top: 40rpx;
}
</style>