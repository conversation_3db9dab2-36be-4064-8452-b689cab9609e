package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (ISysMessage)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("i_sys_message")
@ApiModel("系统消息表")
public class ISysMessage extends BaseObjPo {

    //推送标题
    @ApiModelProperty(value = "推送标题")
    private String title;
    //推送副标题
    @ApiModelProperty(value = "推送副标题")
    private String shortTitle;
    //推送内容
    @ApiModelProperty(value = "推送内容")
    private String context;

    //类型【枚举】按角色/ID
    @ApiModelProperty(value = "类型【枚举】按角色/ID/所有角色")
    private String type;

    @ApiModelProperty(value = "目标对象")
    private String targetIds;
    //开始时间
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    //结束时间
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    //已读会员IDS
    @ApiModelProperty(value = "已读会员IDS")
    private String readMemberIds;

    @ApiModelProperty(value = "是否已读")
    @TableField(exist = false)
    private boolean hasRead;


}

