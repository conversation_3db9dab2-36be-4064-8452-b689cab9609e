/**
 * 格式化时间为友好显示格式
 * @param {string|Date|number} time - 时间字符串、Date对象或时间戳
 * @returns {string} 格式化后的时间
 */
export const formatTimeAgo = (time) => {
  if (!time) return '刚刚';
  
  let date;
  
  try {
    // 处理不同类型的时间输入
    if (time instanceof Date) {
      // 如果已经是Date对象
      date = time;
    } else if (typeof time === 'number') {
      // 如果是时间戳（毫秒或秒）
      // 判断是毫秒还是秒级时间戳
      const timestamp = time > 9999999999 ? time : time * 1000;
      date = new Date(timestamp);
    } else if (typeof time === 'string') {
      // 如果是空字符串，使用当前时间
      if (time.trim() === '') {
        date = new Date();
      } else {
        // 如果是字符串，先处理可能包含环境信息的ISO时间
        let timeStr = time;
        
        // 检查是否包含额外的环境信息，如"2024-07-01T12:00:00(env: Windows,mp,1.06.2409140; lib: 2.16.0)"
        if (timeStr.includes('(env:')) {
          // 提取ISO部分，忽略环境信息
          timeStr = timeStr.split('(')[0].trim();
        }
        
        // 处理带有上午/下午格式的时间
        if (timeStr.includes('上午') || timeStr.includes('下午')) {
          try {
            // 提取日期部分和时间部分
            let datePart = '';
            let timePart = '';
            let isPM = false;
            
            if (timeStr.includes('上午')) {
              const parts = timeStr.split('上午');
              datePart = parts[0].trim();
              timePart = parts[1].trim();
              isPM = false;
            } else if (timeStr.includes('下午')) {
              const parts = timeStr.split('下午');
              datePart = parts[0].trim();
              timePart = parts[1].trim();
              isPM = true;
            }
            
            // 处理时间部分
            if (timePart) {
              const timeComponents = timePart.split(':');
              let hours = parseInt(timeComponents[0], 10);
              const minutes = timeComponents.length > 1 ? parseInt(timeComponents[1], 10) : 0;
              
              // 转换12小时制到24小时制
              if (isPM && hours < 12) {
                hours += 12;
              } else if (!isPM && hours === 12) {
                hours = 0;
              }
              
              // 组装标准时间字符串
              timeStr = `${datePart} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
            } else {
              // 如果没有提取到时间部分，添加默认时间
              timeStr = `${datePart} 00:00:00`;
            }
          } catch (e) {
            console.error('处理上午/下午格式时间失败:', e);
          }
        }
        
        // 尝试修复常见的错误格式
        if (timeStr.includes('-')) {
          // 将-替换为/以确保在所有平台都能正确解析
          // 但ISO格式时间不要替换T前面的部分
          if (timeStr.includes('T')) {
            const parts = timeStr.split('T');
            parts[0] = parts[0].replace(/-/g, '/');
            timeStr = parts.join('T');
          } else {
            timeStr = timeStr.replace(/-/g, '/');
          }
        }
        
        // 确保有时间部分
        if (!timeStr.includes(':')) {
          timeStr += ' 00:00:00';
        }
        
        try {
          // 尝试直接解析ISO格式时间
          date = new Date(timeStr);
          
          // 如果解析无效，尝试另一种方式
          if (isNaN(date.getTime()) && timeStr.includes('T')) {
            // 将ISO格式转换为更通用的格式
            timeStr = timeStr.replace('T', ' ').substring(0, 19);
            date = new Date(timeStr);
          }
          
          // 如果仍然无效，再尝试其他方式
          if (isNaN(date.getTime())) {
            // 尝试使用特定格式解析
            const dateParts = timeStr.split(/[\/\-\s:]/);
            if (dateParts.length >= 3) {
              // 获取年、月、日部分
              const year = parseInt(dateParts[0], 10);
              const month = parseInt(dateParts[1], 10) - 1; // 月份从0开始
              const day = parseInt(dateParts[2], 10);
              
              // 获取时、分部分（如果有）
              const hour = dateParts.length > 3 ? parseInt(dateParts[3], 10) : 0;
              const minute = dateParts.length > 4 ? parseInt(dateParts[4], 10) : 0;
              const second = dateParts.length > 5 ? parseInt(dateParts[5], 10) : 0;
              
              // 创建日期对象
              date = new Date(year, month, day, hour, minute, second);
            }
          }
        } catch (e) {
          console.error('时间格式解析失败，使用当前时间:', e);
          date = new Date();
        }
      }
    } else {
      console.warn('不支持的时间格式:', typeof time, time);
      return '刚刚';
    }
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的时间格式:', time);
      return '刚刚';
    }
  
  // 计算时间差（毫秒）
  const now = new Date();
  const diff = now - date;
    
    // 如果时间差为负（未来时间），显示"刚刚"
    if (diff < 0) {
      console.warn('检测到未来时间:', time);
      return '刚刚';
    }
  
  // 转换为分钟
  const minutes = Math.floor(diff / 60000);
  
  // 小于10分钟显示"刚刚"
  if (minutes < 10) {
    return '刚刚';
  }
  
  // 小于60分钟显示"xx分钟前"
  if (minutes < 60) {
    return `${minutes}分钟前`;
  }
  
  // 小于24小时显示"xx小时前"
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return `${hours}小时前`;
  }
  
  // 小于30天显示"xx天前"
  const days = Math.floor(hours / 24);
  if (days < 30) {
    return `${days}天前`;
  }
  
  // 大于30天显示具体日期
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('formatTimeAgo出错:', error, time);
    return '刚刚';
  }
}

/**
 * 格式化时间为具体的日期时间
 * @param {string|Date|number} time - 时间字符串、Date对象或时间戳
 * @returns {string} 格式化后的时间
 */
export const formatDateTime = (time) => {
  if (!time) {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hour = now.getHours().toString().padStart(2, '0');
    const minute = now.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  }
  
  let date;
  
  try {
    // 处理不同类型的时间输入
    if (time instanceof Date) {
      // 如果已经是Date对象
      date = time;
    } else if (typeof time === 'number') {
      // 如果是时间戳（毫秒或秒）
      // 判断是毫秒还是秒级时间戳
      const timestamp = time > 9999999999 ? time : time * 1000;
      date = new Date(timestamp);
    } else if (typeof time === 'string') {
      // 如果是空字符串，使用当前时间
      if (time.trim() === '') {
        date = new Date();
      } else {
        // 如果是字符串，先处理可能包含环境信息的ISO时间
        let timeStr = time;
        
        // 检查是否包含额外的环境信息，如"2024-07-01T12:00:00(env: Windows,mp,1.06.2409140; lib: 2.16.0)"
        if (timeStr.includes('(env:')) {
          // 提取ISO部分，忽略环境信息
          timeStr = timeStr.split('(')[0].trim();
        }
        
        // 处理带有上午/下午格式的时间
        if (timeStr.includes('上午') || timeStr.includes('下午')) {
          try {
            // 提取日期部分和时间部分
            let datePart = '';
            let timePart = '';
            let isPM = false;
            
            if (timeStr.includes('上午')) {
              const parts = timeStr.split('上午');
              datePart = parts[0].trim();
              timePart = parts[1].trim();
              isPM = false;
            } else if (timeStr.includes('下午')) {
              const parts = timeStr.split('下午');
              datePart = parts[0].trim();
              timePart = parts[1].trim();
              isPM = true;
            }
            
            // 处理时间部分
            if (timePart) {
              const timeComponents = timePart.split(':');
              let hours = parseInt(timeComponents[0], 10);
              const minutes = timeComponents.length > 1 ? parseInt(timeComponents[1], 10) : 0;
              
              // 转换12小时制到24小时制
              if (isPM && hours < 12) {
                hours += 12;
              } else if (!isPM && hours === 12) {
                hours = 0;
              }
              
              // 组装标准时间字符串
              timeStr = `${datePart} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
            } else {
              // 如果没有提取到时间部分，添加默认时间
              timeStr = `${datePart} 00:00:00`;
            }
          } catch (e) {
            console.error('处理上午/下午格式时间失败:', e);
          }
        }
        
        // 尝试修复常见的错误格式
        if (timeStr.includes('-')) {
          // 将-替换为/以确保在所有平台都能正确解析
          // 但ISO格式时间不要替换T前面的部分
          if (timeStr.includes('T')) {
            const parts = timeStr.split('T');
            parts[0] = parts[0].replace(/-/g, '/');
            timeStr = parts.join('T');
          } else {
            timeStr = timeStr.replace(/-/g, '/');
          }
        }
        
        // 确保有时间部分
        if (!timeStr.includes(':')) {
          timeStr += ' 00:00:00';
        }
        
        try {
          // 尝试直接解析ISO格式时间
          date = new Date(timeStr);
          
          // 如果解析无效，尝试另一种方式
          if (isNaN(date.getTime()) && timeStr.includes('T')) {
            // 将ISO格式转换为更通用的格式
            timeStr = timeStr.replace('T', ' ').substring(0, 19);
            date = new Date(timeStr);
          }
          
          // 如果仍然无效，再尝试其他方式
          if (isNaN(date.getTime())) {
            // 尝试使用特定格式解析
            const dateParts = timeStr.split(/[\/\-\s:]/);
            if (dateParts.length >= 3) {
              // 获取年、月、日部分
              const year = parseInt(dateParts[0], 10);
              const month = parseInt(dateParts[1], 10) - 1; // 月份从0开始
              const day = parseInt(dateParts[2], 10);
              
              // 获取时、分部分（如果有）
              const hour = dateParts.length > 3 ? parseInt(dateParts[3], 10) : 0;
              const minute = dateParts.length > 4 ? parseInt(dateParts[4], 10) : 0;
              const second = dateParts.length > 5 ? parseInt(dateParts[5], 10) : 0;
              
              // 创建日期对象
              date = new Date(year, month, day, hour, minute, second);
            }
          }
        } catch (e) {
          console.error('时间格式解析失败，使用当前时间:', e);
          date = new Date();
        }
      }
    } else {
      console.warn('不支持的时间格式，使用当前时间:', typeof time, time);
      date = new Date();
    }
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的时间格式，使用当前时间:', time);
      date = new Date();
    }
  
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}`;
  } catch (error) {
    console.error('formatDateTime出错，使用当前时间:', error, time);
    // 发生错误时使用当前时间
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hour = now.getHours().toString().padStart(2, '0');
    const minute = now.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  }
}
