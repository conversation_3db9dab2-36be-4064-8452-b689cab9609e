package com.play.xianshibusiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单类型枚举
 */
@Getter
@AllArgsConstructor
public enum OrderTypeEnum {
    
    DISPATCH("DISPATCH", "派单模式"),     // 公开发布，所有达人可申请
    APPOINTMENT("APPOINTMENT", "约单模式"); // 指定达人，私有订单

    private final String code;
    private final String desc;
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果找不到返回null
     */
    public static OrderTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (OrderTypeEnum type : OrderTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}