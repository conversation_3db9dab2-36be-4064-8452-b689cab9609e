package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * (CMemberQd)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_member_qd")
@ApiModel("会员签到表")
public class CMemberQd extends BaseObjPo {

    //签到类型【枚举】
    @ApiModelProperty(value = "签到类型【枚举】")
    private String type;
    //会员ID
    @ApiModelProperty(value = "会员ID")
    private String memberId;

}

