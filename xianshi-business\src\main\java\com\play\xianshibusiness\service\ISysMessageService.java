package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.message.MessageBroadcastDTO;
import com.play.xianshibusiness.dto.message.MessageQueryDTO;
import com.play.xianshibusiness.dto.message.MessageVO;
import com.play.xianshibusiness.enums.MessageType;
import com.play.xianshibusiness.enums.MessageTypeEnum;
import com.play.xianshibusiness.enums.ResultCode;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.CMemberMapper;
import com.play.xianshibusiness.mapper.ISysMessageMapper;
import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.pojo.ISysMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (ISysMessage)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:15
 */
@Service
@Slf4j
public class ISysMessageService {

    @Resource
    private ISysMessageMapper iSysMessageMapper;

    @Resource
    private CMemberMapper memberMapper;

    /**
     * 推送订单消息
     * 
     * @param iSysMessage 消息对象
     * @return 消息ID
     */
    @Transactional
    public String pushOrderMessage(ISysMessage iSysMessage) {
        log.info("推送订单消息：{}", iSysMessage);

        // 设置消息类型为订单消息
        iSysMessage.setType(MessageTypeEnum.ORDER.getCode());

        // 设置默认时间范围
        if (iSysMessage.getStartTime() == null) {
            iSysMessage.setStartTime(LocalDateTime.now());
        }
        if (iSysMessage.getEndTime() == null) {
            iSysMessage.setEndTime(LocalDateTime.now().plusYears(1)); // 默认有效期一年
        }

        // 保存消息
        iSysMessageMapper.insert(iSysMessage);

        return iSysMessage.getId();
    }

    /**
     * 发送消息（适用于订单模块）
     * 
     * @param memberId    接收消息的会员ID
     * @param content     消息内容
     * @param messageType 消息类型
     * @return 消息ID
     */
    @Transactional
    public String sendMessage(String memberId, String content, MessageType messageType) {
        if (StringUtils.isEmpty(memberId) || StringUtils.isEmpty(content)) {
            log.warn("发送消息失败：接收者ID或消息内容为空");
            return null;
        }

        ISysMessage message = new ISysMessage();
        message.setId(UUID.randomUUID().toString());
        message.setTitle(getMessageTitle(messageType));
        message.setContext(content);
        message.setTargetIds(memberId);
        message.setStartTime(LocalDateTime.now());
        message.setEndTime(LocalDateTime.now().plusYears(1)); // 默认有效期一年
        message.setType(MessageTypeEnum.ORDER.getCode()); // 订单类消息
        message.setCreateTime(LocalDateTime.now());
        message.setUpdateTime(LocalDateTime.now());
        message.setDeleted(false);
        message.setAvailable(true);

        // 保存消息
        iSysMessageMapper.insert(message);

        log.info("发送消息成功：{}", message);
        return message.getId();
    }

    /**
     * 根据消息类型获取消息标题
     * 
     * @param messageType 消息类型
     * @return 消息标题
     */
    private String getMessageTitle(MessageType messageType) {
        if (messageType == null) {
            return "订单相关通知";
        }

        if (MessageType.ORDER_AUDIT.equals(messageType)) {
            return "订单审核通知";
        } else if (MessageType.ORDER_CANCEL.equals(messageType)) {
            return "订单取消通知";
        } else if (MessageType.ORDER_ENROLL.equals(messageType)) {
            return "订单报名通知";
        } else if (MessageType.ORDER_WAITING_SELECT.equals(messageType)) {
            return "待选择达人通知";
        } else if (MessageType.ORDER_SELECTED.equals(messageType)) {
            return "达人被选中通知";
        } else if (MessageType.ORDER_UPDATE.equals(messageType)) {
            return "订单状态更新通知";
        } else if (MessageType.ORDER_COMPLETED.equals(messageType)) {
            return "订单完成通知";
        } else {
            return "订单相关通知";
        }
    }

    /**
     * 获取消息详情并自动标记为已读
     */
    public ISysMessage fetchMessage(String messageId, String memberId) {
        if (StringUtils.isEmpty(messageId)) {
            return null;
        }
        ISysMessage message = iSysMessageMapper.selectById(messageId);
        if (message == null) {
            return null;
        }
        // 自动标记为已读
        if (memberId != null) {
            String readMemberIds = message.getReadMemberIds();
            if (StringUtils.isEmpty(readMemberIds)) {
                message.setReadMemberIds(memberId);
                iSysMessageMapper.updateById(message);
            } else {
                List<String> readMembers = Arrays.asList(readMemberIds.split(","));
                if (!readMembers.contains(memberId)) {
                    message.setReadMemberIds(readMemberIds + "," + memberId);
                    iSysMessageMapper.updateById(message);
                }
            }
        }
        return message;
    }

    /**
     * 获取消息详情（不标记已读）
     * 
     * @param messageId 消息ID
     * @return 消息详情
     */
    public ISysMessage fetchMessage(String messageId) {
        if (StringUtils.isEmpty(messageId)) {
            return null;
        }
        return iSysMessageMapper.selectById(messageId);
    }

    /**
     * 获取-订单消息
     * 
     * @param memberId 会员ID
     * @return 订单消息列表
     */
    public List<ISysMessage> fetchOrderMessage(String memberId) {
        LambdaQueryWrapper<ISysMessage> queryWrapper = new LambdaQueryWrapper<>();

        // 查询条件：订单消息类型
        queryWrapper.eq(ISysMessage::getType, MessageTypeEnum.ORDER.getCode());

        // 当前有效的消息时间范围内
        LocalDateTime now = LocalDateTime.now();
        queryWrapper.le(ISysMessage::getStartTime, now);
        queryWrapper.ge(ISysMessage::getEndTime, now);

        // 按创建时间倒序排列，最新消息在前
        queryWrapper.orderByDesc(ISysMessage::getCreateTime);
        // 是否已读
        List<ISysMessage> iSysMessages = iSysMessageMapper.selectList(queryWrapper);
        iSysMessages.forEach(message -> {
            String readMemberIds = message.getReadMemberIds();
            if (!StringUtils.isEmpty(readMemberIds)) {
                // 转list
                List<String> readMemberIdList = Arrays.asList(readMemberIds.split(","));
                message.setHasRead(readMemberIdList.contains(memberId));
            }
        });
        // 按创建时间倒序排列，最新消息在前

        return iSysMessages;
    }

    /**
     * 获取-系统消息
     * 
     * @param memberId 会员ID
     * @return 系统消息列表
     */
    public List<ISysMessage> fetchSysMessage(String memberId) {
        LambdaQueryWrapper<ISysMessage> queryWrapper = new LambdaQueryWrapper<>();

        // 查询条件：系统消息类型
        queryWrapper.eq(ISysMessage::getType, MessageTypeEnum.SYSTEM.getCode());

        // 当前有效的消息时间范围内
        LocalDateTime now = LocalDateTime.now();
        queryWrapper.le(ISysMessage::getStartTime, now);
        queryWrapper.ge(ISysMessage::getEndTime, now);
        // 查询所有会员可见的系统消息，或者指定会员ID可见的消息
        // queryWrapper.and(wrapper ->
        // wrapper.isNotNull(ISysMessage::getTargetIds).in(ISysMessage::getTargetIds,
        // memberId)
        // .eq(ISysMessage::getType, MessageTypeEnum.ALL.getCode())
        // );

        // 按创建时间倒序排列，最新消息在前
        queryWrapper.orderByDesc(ISysMessage::getCreateTime);
        // 是否已读
        List<ISysMessage> iSysMessages = iSysMessageMapper.selectList(queryWrapper);
        iSysMessages.forEach(message -> {
            String readMemberIds = message.getReadMemberIds();
            if (!StringUtils.isEmpty(readMemberIds)) {
                // 转list
                List<String> readMemberIdList = Arrays.asList(readMemberIds.split(","));
                message.setHasRead(readMemberIdList.contains(memberId));
            }

        });
        return iSysMessages;
    }

    /**
     * 获取用户未读消息数量
     * 
     * @param memberId 会员ID
     * @return 未读消息数
     */
    public Integer getUnreadCount(String memberId) {
        LambdaQueryWrapper<ISysMessage> queryWrapper = new LambdaQueryWrapper<>();

        // 当前有效的消息时间范围内
        LocalDateTime now = LocalDateTime.now();
        queryWrapper.le(ISysMessage::getStartTime, now);
        queryWrapper.ge(ISysMessage::getEndTime, now);

        // 查询所有会员可见的消息，或者指定会员ID可见的消息
        queryWrapper.and(wrapper -> wrapper.like(ISysMessage::getTargetIds, memberId)
                .or()
                .eq(ISysMessage::getType, MessageTypeEnum.ALL.getCode()));

        // 未读条件：readMemberIds为空或者不包含当前会员ID
        queryWrapper.and(wrapper -> wrapper.isNull(ISysMessage::getReadMemberIds)
                .or()
                .notLike(ISysMessage::getReadMemberIds, memberId));

        return iSysMessageMapper.selectCount(queryWrapper).intValue();
    }

    /**
     * 管理端分页查询系统消息
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    public Page<MessageVO> adminPageMessages(MessageQueryDTO queryDTO) {
        if (queryDTO == null) {
            queryDTO = new MessageQueryDTO();
        }

        // 构建查询条件
        LambdaQueryWrapper<ISysMessage> queryWrapper = new LambdaQueryWrapper<>();

        // 条件过滤
        if (!StringUtils.isEmpty(queryDTO.getMemberId())) {
            queryWrapper.like(ISysMessage::getTargetIds, queryDTO.getMemberId());
        }

        if (!StringUtils.isEmpty(queryDTO.getMemberName())) {
            // 根据会员昵称查询会员ID
            List<String> memberIds = memberMapper.selectList(new LambdaQueryWrapper<CMember>()
                    .like(CMember::getNickname, queryDTO.getMemberName())
                    .select(CMember::getId))
                    .stream()
                    .map(CMember::getId)
                    .collect(Collectors.toList());

            if (!memberIds.isEmpty()) {
                queryWrapper.and(wrapper -> {
                    for (String memberId : memberIds) {
                        wrapper.or().like(ISysMessage::getTargetIds, memberId);
                    }
                });
            }
        }

        if (queryDTO.getMessageType() != null) {
            String messageType = null;
            Integer msgType = queryDTO.getMessageType();

            if (msgType == 1) {
                messageType = MessageTypeEnum.SYSTEM.getCode();
            } else if (msgType == 2) {
                messageType = MessageTypeEnum.ORDER.getCode();
            } else if (msgType == 3) {
                messageType = MessageTypeEnum.ALL.getCode();
            }

            if (messageType != null) {
                queryWrapper.eq(ISysMessage::getType, messageType);
            }
        }

        if (!StringUtils.isEmpty(queryDTO.getCreateTimeStart())) {
            try {
                LocalDateTime startTime = LocalDateTime.parse(queryDTO.getCreateTimeStart(),
                        java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                queryWrapper.ge(ISysMessage::getCreateTime, startTime);
            } catch (Exception e) {
                log.error("解析开始时间错误", e);
            }
        }

        if (!StringUtils.isEmpty(queryDTO.getCreateTimeEnd())) {
            try {
                LocalDateTime endTime = LocalDateTime.parse(queryDTO.getCreateTimeEnd(),
                        java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                queryWrapper.le(ISysMessage::getCreateTime, endTime);
            } catch (Exception e) {
                log.error("解析结束时间错误", e);
            }
        }

        // 排序
        if (!StringUtils.isEmpty(queryDTO.getOrderBy())) {
            boolean isAsc = "asc".equalsIgnoreCase(queryDTO.getOrderDirection());
            String orderBy = queryDTO.getOrderBy();

            if ("createTime".equals(orderBy)) {
                queryWrapper.orderBy(true, isAsc, ISysMessage::getCreateTime);
            } else if ("startTime".equals(orderBy)) {
                queryWrapper.orderBy(true, isAsc, ISysMessage::getStartTime);
            } else if ("endTime".equals(orderBy)) {
                queryWrapper.orderBy(true, isAsc, ISysMessage::getEndTime);
            } else {
                queryWrapper.orderByDesc(ISysMessage::getCreateTime);
            }
        } else {
            queryWrapper.orderByDesc(ISysMessage::getCreateTime);
        }

        // 执行查询
        Page<ISysMessage> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<ISysMessage> resultPage = iSysMessageMapper.selectPage(page, queryWrapper);

        // 转换结果
        List<MessageVO> records = resultPage.getRecords().stream()
                .map(this::convertToMessageVO)
                .collect(Collectors.toList());

        Page<MessageVO> resultVOPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        resultVOPage.setRecords(records);

        return resultVOPage;
    }

    /**
     * 管理端获取消息详情
     *
     * @param messageId 消息ID
     * @return 消息详情
     */
    public MessageVO adminGetMessageDetail(String messageId) {
        if (StringUtils.isEmpty(messageId)) {
            throw new GlobalException(400, "消息ID不能为空");
        }

        ISysMessage message = iSysMessageMapper.selectById(messageId);
        if (message == null) {
            throw new GlobalException(400, "消息不存在");
        }

        return convertToMessageVO(message);
    }

    /**
     * 发送系统广播消息
     *
     * @param broadcastDTO 广播参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean sendBroadcastMessage(MessageBroadcastDTO broadcastDTO) {
        if (broadcastDTO == null || StringUtils.isEmpty(broadcastDTO.getContent())) {
            throw new GlobalException(400, "消息内容不能为空");
        }

        ISysMessage message = new ISysMessage();
        message.setId(UUID.randomUUID().toString());
        message.setTitle(getMessageTitleByType(broadcastDTO.getMessageType()));
        message.setContext(broadcastDTO.getContent());
        message.setStartTime(LocalDateTime.now());
        message.setEndTime(LocalDateTime.now().plusYears(1)); // 默认有效期一年
        message.setCreateTime(LocalDateTime.now());
        message.setUpdateTime(LocalDateTime.now());
        message.setDeleted(false);
        message.setAvailable(true);

        // 根据接收者类型设置目标ID和消息类型
        if (broadcastDTO.getReceiverType() == 1) {
            // 所有用户
            message.setType(MessageTypeEnum.ALL.getCode());
            message.setTargetIds("ALL");
        } else if (broadcastDTO.getReceiverType() == 3 && broadcastDTO.getMemberIds() != null
                && !broadcastDTO.getMemberIds().isEmpty()) {
            // 指定用户
            message.setType(getMessageTypeByBroadcastType(broadcastDTO.getMessageType()));
            message.setTargetIds(String.join(",", broadcastDTO.getMemberIds()));
        } else {
            // 默认为所有用户
            message.setType(MessageTypeEnum.ALL.getCode());
            message.setTargetIds("ALL");
        }

        // 保存消息
        iSysMessageMapper.insert(message);

        return true;
    }

    /**
     * 根据广播消息类型获取消息类型
     */
    private String getMessageTypeByBroadcastType(Integer broadcastType) {
        if (broadcastType == null) {
            return MessageTypeEnum.SYSTEM.getCode();
        }

        if (broadcastType == 1) {
            return MessageTypeEnum.SYSTEM.getCode();
        } else if (broadcastType == 2) {
            return MessageTypeEnum.ORDER.getCode();
        } else {
            return MessageTypeEnum.SYSTEM.getCode();
        }
    }

    /**
     * 根据广播消息类型获取消息标题
     */
    private String getMessageTitleByType(Integer messageType) {
        if (messageType == null) {
            return "系统通知";
        }

        if (messageType == 1) {
            return "系统通知";
        } else if (messageType == 2) {
            return "订单通知";
        } else if (messageType == 3) {
            return "活动通知";
        } else {
            return "系统通知";
        }
    }

    /**
     * 管理端向指定用户发送消息
     *
     * @param memberId 会员ID
     * @param content  消息内容
     * @param type     消息类型：1-系统通知，2-订单通知，3-活动通知
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean adminSendMessageToMember(String memberId, String content, Integer type) {
        if (StringUtils.isEmpty(memberId) || StringUtils.isEmpty(content)) {
            throw new GlobalException(400, "会员ID和消息内容不能为空");
        }

        ISysMessage message = new ISysMessage();
        message.setId(UUID.randomUUID().toString());
        message.setTitle(getMessageTitleByType(type));
        message.setContext(content);
        message.setTargetIds(memberId);
        message.setStartTime(LocalDateTime.now());
        message.setEndTime(LocalDateTime.now().plusYears(1)); // 默认有效期一年
        message.setType(getMessageTypeByBroadcastType(type));
        message.setCreateTime(LocalDateTime.now());
        message.setUpdateTime(LocalDateTime.now());
        message.setDeleted(false);
        message.setAvailable(true);

        // 保存消息
        iSysMessageMapper.insert(message);

        return true;
    }

    /**
     * 管理端删除消息
     *
     * @param messageId 消息ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean adminDeleteMessage(String messageId) {
        if (StringUtils.isEmpty(messageId)) {
            throw new GlobalException(400, "消息ID不能为空");
        }

        ISysMessage message = iSysMessageMapper.selectById(messageId);
        if (message == null) {
            throw new GlobalException(400, "消息不存在");
        }

        // 逻辑删除消息
        message.setDeleted(true);
        message.setAvailable(false);
        message.setUpdateTime(LocalDateTime.now());

        return iSysMessageMapper.updateById(message) > 0;
    }

    /**
     * 管理端获取消息统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> adminGetMessageStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 统计系统消息数量
        long systemMessageCount = iSysMessageMapper.selectCount(new LambdaQueryWrapper<ISysMessage>()
                .eq(ISysMessage::getType, MessageTypeEnum.SYSTEM.getCode())
                .eq(ISysMessage::getDeleted, false));
        statistics.put("systemMessageCount", systemMessageCount);

        // 统计订单消息数量
        long orderMessageCount = iSysMessageMapper.selectCount(new LambdaQueryWrapper<ISysMessage>()
                .eq(ISysMessage::getType, MessageTypeEnum.ORDER.getCode())
                .eq(ISysMessage::getDeleted, false));
        statistics.put("orderMessageCount", orderMessageCount);

        // 统计全局消息数量
        long allMessageCount = iSysMessageMapper.selectCount(new LambdaQueryWrapper<ISysMessage>()
                .eq(ISysMessage::getType, MessageTypeEnum.ALL.getCode())
                .eq(ISysMessage::getDeleted, false));
        statistics.put("allMessageCount", allMessageCount);

        // 统计今日消息数量
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        long todayMessageCount = iSysMessageMapper.selectCount(new LambdaQueryWrapper<ISysMessage>()
                .ge(ISysMessage::getCreateTime, today)
                .eq(ISysMessage::getDeleted, false));
        statistics.put("todayMessageCount", todayMessageCount);

        // 统计总消息数量
        long totalMessageCount = iSysMessageMapper.selectCount(new LambdaQueryWrapper<ISysMessage>()
                .eq(ISysMessage::getDeleted, false));
        statistics.put("totalMessageCount", totalMessageCount);

        return statistics;
    }

    /**
     * 将ISysMessage转换为MessageVO
     *
     * @param message 系统消息
     * @return 消息VO
     */
    private MessageVO convertToMessageVO(ISysMessage message) {
        if (message == null) {
            return null;
        }

        MessageVO vo = new MessageVO();
        vo.setId(message.getId());
        vo.setContent(message.getContext());

        // 设置消息类型
        Integer messageType = 1; // 默认系统通知
        if (MessageTypeEnum.ORDER.getCode().equals(message.getType())) {
            messageType = 2; // 订单通知
        } else if (MessageTypeEnum.ALL.getCode().equals(message.getType())) {
            messageType = 3; // 活动通知
        }
        vo.setMessageType(messageType);
        vo.setMessageTypeDesc(getMessageTitleByType(messageType));

        // 设置接收者信息
        if (!StringUtils.isEmpty(message.getTargetIds()) && !"ALL".equals(message.getTargetIds())) {
            vo.setReceiverId(message.getTargetIds());

            // 如果是单个接收者，获取接收者信息
            if (!message.getTargetIds().contains(",")) {
                try {
                    CMember receiver = memberMapper.selectById(message.getTargetIds());
                    if (receiver != null) {
                        vo.setReceiverName(receiver.getNickname());
                        vo.setReceiverAvatar(receiver.getAvatar());
                    }
                } catch (Exception e) {
                    log.error("获取接收者信息失败", e);
                }
            }
        }
        // 设置创建时间
        vo.setCreateTime(message.getCreateTime());

        return vo;
    }
}
