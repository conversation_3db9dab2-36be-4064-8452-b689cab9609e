package com.play.xianshibusiness.dto.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Map;

/**
 * 基础配置DTO
 */
@Data
@ApiModel("基础配置DTO")
public class BasicConfigDTO {
    
    @ApiModelProperty(value = "配置键值对", required = true)
    @NotEmpty(message = "配置不能为空")
    private Map<String, String> configs;
    
    /**
     * 单个配置项
     */
    @Data
    @ApiModel(value = "配置项")
    public static class ConfigItem {
        
        @NotBlank(message = "配置键不能为空")
        @ApiModelProperty(value = "配置键", required = true)
        private String key;
        
        @NotBlank(message = "配置值不能为空")
        @ApiModelProperty(value = "配置值", required = true)
        private String value;
        
        @ApiModelProperty(value = "配置描述")
        private String description;
        
        @ApiModelProperty(value = "配置组")
        private String group;
        
        @ApiModelProperty(value = "是否启用")
        private Boolean enabled = true;
        
        @ApiModelProperty(value = "排序")
        private Integer sort = 0;
    }
} 