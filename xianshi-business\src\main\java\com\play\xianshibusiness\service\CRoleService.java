package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.mapper.CRoleMapper;
import com.play.xianshibusiness.pojo.CRole;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * (CRole)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:12
 */
@Service
public class CRoleService {
    
    @Resource
    private CRoleMapper roleMapper;
    
    /**
     * 根据角色ID列表获取角色信息
     * 
     * @param roleIds 角色ID列表
     * @return 角色信息列表
     */
    public List<CRole> getRolesByIds(List<String> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<CRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CRole::getCode, roleIds);
        
        return roleMapper.selectList(queryWrapper);
    }
    
    /**
     * 获取所有角色
     * 
     * @return 所有角色列表
     */
    public List<CRole> getAllRoles() {
        return roleMapper.selectList(null);
    }
    
    /**
     * 根据角色ID获取角色信息
     * 
     * @param roleId 角色ID
     * @return 角色信息
     */
    public CRole getRoleById(String roleId) {
        return roleMapper.selectById(roleId);
    }
}

