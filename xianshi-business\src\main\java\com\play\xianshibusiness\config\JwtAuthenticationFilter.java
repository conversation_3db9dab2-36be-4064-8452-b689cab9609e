package com.play.xianshibusiness.config;

import com.play.xianshibusiness.utils.JwtSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证过滤器，用于从请求中提取JWT令牌并验证认证信息
 */
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String ADMIN_ID_HEADER = "AdminId";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        try {
            // 从请求头获取Authorization
            String token = extractToken(request);

            // 如果请求包含令牌，尝试验证并设置认证信息
            if (token != null) {
                // 先检查令牌是否过期
                if (JwtSignUtils.validateTokenExpiration(token)) {
                    try {
                        // 验证令牌并获取认证信息
                        Authentication authentication = JwtSignUtils.validateToken(token);

                        // 如果认证信息有效，设置到安全上下文
                        if (authentication != null) {
                            SecurityContextHolder.getContext().setAuthentication(authentication);
                            log.debug("Set Authentication to security context for '{}', uri: {}",
                                    authentication.getName(), request.getRequestURI());

                            // 检查是否为管理员令牌
                            String adminId = JwtSignUtils.getAdminIdFromToken(token.replace("Bearer ", ""));
                            if (adminId != null) {
                                // 如果是管理员令牌，设置管理员ID到请求属性
                                request.setAttribute(ADMIN_ID_HEADER, adminId);
                                log.debug("Set admin ID to request attribute: {}", adminId);
                            }
                        }
                    } catch (RuntimeException e) {
                        if ("INVALID_SIGNATURE".equals(e.getMessage())) {
                            log.warn("JWT签名验证失败，可能是密钥更改导致，请重新登录");
                            // 对于签名异常，清除安全上下文，让后续的认证检查处理
                            SecurityContextHolder.clearContext();
                        } else {
                            log.warn("JWT validation error: {}", e.getMessage());
                        }
                    } catch (Exception e) {
                        log.warn("JWT validation error: {}", e.getMessage());
                    }
                } else {
                    log.debug("JWT token is expired or invalid");
                }
            }
        } catch (Exception e) {
            log.error("Cannot set user authentication in security context", e);
            // 清除安全上下文避免使用可能已损坏的认证信息
            SecurityContextHolder.clearContext();
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中提取JWT令牌
     */
    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader(AUTHORIZATION_HEADER);
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken;
        }
        return null;
    }
}