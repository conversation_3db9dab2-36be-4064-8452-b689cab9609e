import CryptoJS from 'crypto-js'

/**
 * debounce 防抖
 * 设置方法最小触发间隔，例： 用于实时搜素
 * 简单理解：【延迟】触发
 * @param {*} func 触发函数
 * @param {*} outTime 最小间隔时长
 */
function debounce(func, outTime) {
	let timer = null;
	return function () {
		if (timer) {
			clearTimeout(timer);
		}
		timer = setTimeout(() => {
			func.apply(this, arguments);
		}, outTime);
	};
}

/**
 * throttle 节流
 * 控制平均触发频率，高帧频控制 例： 用于滚动渲染
 * 简单理解：触发过快，希望【平均】触发
 * @param {*} func 触发函数
 * @param {*} outTime 触发间隔时长
 */
function throttle(func, outTime) {
	let canRun = true;
	return function () {
		if (!canRun) return;
		canRun = false;
		setTimeout(() => {
			func.apply(this, arguments);
			canRun = true;
		}, outTime);
	};
}

function systemInfo() {
	return new Promise((resolve, reject) => {
		uni.getSystemInfo({
			success: (res) => {
				// 状态栏高度（单位：px），不同系统会有差异
				let statusBarHeight = res.statusBarHeight || 0;
				console.log("statusBarHeight", statusBarHeight);
				resolve(res);
			},
			fail: (err) => {
				reject(err);
			}
		});
	});
}

// 判断一个对象是否为空
function isEmpty(obj) {
	return Object.keys(obj).length === 0;
}

// 根据传入的日期偏移量（offset），生成一个格式为 "yyyy-mm-dd 至 yyyy-mm-dd" 的日期范围字符串。
// 0 - 今天 / -1 - 昨天 / >1 - 近n天
function formatDateRange(offset = 0, text = '-') {
	const today = new Date();
	const endDate = new Date(today);
	const startDate = new Date(today);

	if (offset > 1) {
		// 近 N 天（含今天）
		startDate.setDate(today.getDate() - (offset - 1));
	} else {
		// 单日范围（如今日/昨日）
		startDate.setDate(today.getDate() + offset);
		endDate.setDate(today.getDate() + offset);
	}

	const format = (date) => {
		const yyyy = date.getFullYear();
		const mm = String(date.getMonth() + 1).padStart(2, '0');
		const dd = String(date.getDate()).padStart(2, '0');
		return `${yyyy}-${mm}-${dd}`;
	};

	return `${format(startDate)} ${text} ${format(endDate)}`;
}

function parseDateRange(dateRangeStr, format = 'timestamp') {
	if (typeof dateRangeStr !== 'string') {
		throw new Error('参数必须是字符串');
	}

	const parts = dateRangeStr.trim().split(/\s+/); // 按空格分隔
	if (parts.length < 3) {
		throw new Error('字符串格式不正确，应为 "开始日期 空格 至 空格 结束日期"');
	}

	const startDateStr = parts[0];
	const endDateStr = parts[2];

	// 开始时间为 00:00:00
	const startDate = new Date(startDateStr.replace(/-/g, '/'));
	startDate.setHours(0, 0, 0, 0);

	// 结束时间为 23:59:59
	const endDate = new Date(endDateStr.replace(/-/g, '/'));
	endDate.setHours(23, 59, 59, 0);

	if (format === 'timestamp') {
		const startTs = Math.floor(startDate.getTime() / 1000); // 秒级时间戳
		const endTs = Math.floor(endDate.getTime() / 1000);

		if (isNaN(startTs) || isNaN(endTs)) {
			throw new Error('日期格式无效，请使用 yyyy-mm-dd');
		}

		return {
			startTs,
			endTs
		};
	} else if (format === 'datetime') {
		// 格式化日期为 yyyy-mm-dd hh:mm:ss
		function formatDate(date) {
			const pad = n => n.toString().padStart(2, '0');
			return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +
				`${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
		}

		const startDatetime = formatDate(startDate);
		const endDatetime = formatDate(endDate);

		return {
			startTs: startDatetime,
			endTs: endDatetime
		};
	} else {
		throw new Error('不支持的格式类型，请使用 "timestamp" 或 "datetime"');
	}
}
/**
 * AES 加密（ECB + PKCS7）
 * @param {string} plaintext 明文（如 JSON 字符串）
 * @param {string} appSecret 密钥
 * @returns {string} 加密后 base64-url 编码的字符串
 */
function encryptAES(plaintext, appSecret) {
	const key = CryptoJS.enc.Utf8.parse(appSecret.slice(0, 16)); // 取前16位作为AES密钥
	const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
		mode: CryptoJS.mode.ECB,
		padding: CryptoJS.pad.Pkcs7
	});

	// URL 安全的 base64 编码（= 替换为 ''，+ → -, / → _）
	return encrypted.toString()
		.replace(/=/g, '')
		.replace(/\+/g, '-')
		.replace(/\//g, '_');
}

/**
 * 生成签名
 * @param {string} body 加密后的请求体字符串
 * @param {string} secretKey app密钥
 * @returns {string} SHA256 签名字符串
 */
function genSign(timestamp, body, secretKey) {
	const content = `${body}.${secretKey}`;
	return CryptoJS.SHA256(content).toString(CryptoJS.enc.Hex);
}

/**
 * 根据 roleId 在 roleList 中的位置返回头像路径
 * @param {number} roleId - 角色ID
 * @param {Array} roleList - 角色数组（顺序重要）
 * @returns {string} 头像路径
 */
function getAvatarPath(roleId, roleList) {
	return '/static/images/avatar_admin.png';
	// const index = roleList.findIndex(role => role.roleId === roleId);

	// if (index === 0) {
	// 	return '/static/images/avatar_super.png';
	// } else if (index === 1) {
	// 	return '/static/images/avatar_admin.png';
	// } else {
	// 	return '/static/images/avatar_user.png';
	// }
}

/**
 * 校验字段
 * @param {string} type 校验的类型 (chineseName, email, ...)
 * @param {string} value 需要校验的值
 * @returns {boolean} true=校验通过, false=校验不通过
 */
function validateField(type, value) {
	switch (type) {
		case 'cName': {
			// 2-10位中文
			const reg = /^[\u4e00-\u9fa5]{2,10}$/;
			return reg.test(value);
		}
		case 'email': {
			// 邮箱
			const reg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			return reg.test(value);
		}
		case 'phone': {
			// 手机号
			const reg = /^1[3-9]\d{9}$/;
			return reg.test(value);
		}
		case 'creCode': {
			// 统一社会信用代码
			const reg = /^[0-9A-Z]{18}$/;
			return reg.test(value);
		}
		case 'password': {
			// 8-18位，包含大写、小写、数字和特殊字符
			const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,18}$/;
			return reg.test(value);
		}
		case 'yyyy-MM-dd': {
			// yyyy-MM-dd 日期格式（必须是合法日期）
			const reg = /^\d{4}-\d{2}-\d{2}$/;
			return reg.test(value)
		}
		case 'validDate': {
			const reg = /^\d{4}-\d{2}-\d{2}$/;
			if (!reg.test(value.trim())) return false;

			const parts = value.split('-');
			const year = parseInt(parts[0], 10);
			const month = parseInt(parts[1], 10);
			const day = parseInt(parts[2], 10);

			const date = new Date(year, month - 1, day);

			// 检查日期的有效性
			const isValidDate =
				date.getFullYear() === year &&
				date.getMonth() === month - 1 &&
				date.getDate() === day;

			if (!isValidDate) return false;

			// 检查日期是否大于今天（含今天）
			const now = new Date();
			now.setHours(0, 0, 0, 0); // 抹掉时间部分，仅保留日期
			if (date <= now) return false;

			return true;
		}
		case 'ip': {
			const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
			if (!ipRegex.test(value)) return false;
			return value.split('.').every(seg => {
				const num = Number(seg);
				return num >= 0 && num <= 255;
			});
		}
		case 'metric': {
			const num = Number(value);
			return (
				!isNaN(num) &&
				Number.isInteger(num) &&
				num >= 0 &&
				num <= 255
			);
		}

		default:
			return true; // 其他类型默认通过
	}
}

// 校验权限
function validatePermissions(field) {
	const app = getApp().globalData;
	const userPermission = app.userPermission;
	if (userPermission.includes('*:*:*')) {
		return true;
	}
	return userPermission.includes(field);
}



export {
	debounce,
	throttle,
	systemInfo,
	isEmpty,
	formatDateRange,
	parseDateRange,
	encryptAES,
	genSign,
	getAvatarPath,
	validateField,
	validatePermissions
}