import request from '@/utils/request';

/**
 * 获取系统基础配置
 * @returns {Promise}
 */
export function getBasicConfig() {
  return request({
    url: '/system-config/basic-config',
    method: 'get'
  });
}

/**
 * 保存系统基础配置
 * @param {Object} data 配置数据
 * @returns {Promise}
 */
export function saveBasicConfig(data) {
  // 将对象转换为configs格式
  const configData = {
    configs: {}
  };
  
  // 遍历对象属性，转换为键值对
  for (const key in data) {
    configData.configs[key] = data[key];
  }
  
  return request({
    url: '/system-config/basic-config',
    method: 'put',
    data: configData
  });
}

/**
 * 获取会员配置
 * @returns {Promise}
 */
export function getMemberConfig() {
  return request({
    url: '/system-config/basic-config',
    method: 'get',
    params: { type: 'member' }
  });
}

/**
 * 保存会员配置
 * @param {Object} data 配置数据
 * @returns {Promise}
 */
export function saveMemberConfig(data) {
  // 将对象转换为configs格式
  const configData = {
    configs: {}
  };
  
  // 遍历对象属性，转换为键值对
  for (const key in data) {
    configData.configs[key] = data[key];
  }
  
  return request({
    url: '/system-config/basic-config',
    method: 'put',
    data: configData
  });
}

/**
 * 获取订单配置
 * @returns {Promise}
 */
export function getOrderConfig() {
  return request({
    url: '/system-config/basic-config',
    method: 'get',
    params: { type: 'order' }
  });
}

/**
 * 保存订单配置
 * @param {Object} data 配置数据
 * @returns {Promise}
 */
export function saveOrderConfig(data) {
  // 将对象转换为configs格式
  const configData = {
    configs: {}
  };
  
  // 处理publishGoldTable数组
  if (data.publishGoldTable && Array.isArray(data.publishGoldTable)) {
    configData.configs['publishGoldTable'] = JSON.stringify(data.publishGoldTable);
  }
  
  // 遍历其他属性
  for (const key in data) {
    if (key !== 'publishGoldTable') {
      configData.configs[key] = data[key];
    }
  }
  
  return request({
    url: '/system-config/basic-config',
    method: 'put',
    data: configData
  });
}

/**
 * 获取支付配置
 * @returns {Promise}
 */
export function getPaymentConfig() {
  return request({
    url: '/system-config/basic-config',
    method: 'get',
    params: { type: 'payment' }
  });
}

/**
 * 保存支付配置
 * @param {Object} data 配置数据
 * @returns {Promise}
 */
export function savePaymentConfig(data) {
  // 将对象转换为configs格式
  const configData = {
    configs: {}
  };
  
  // 遍历对象属性，转换为键值对
  for (const key in data) {
    configData.configs[key] = data[key];
  }
  
  return request({
    url: '/system-config/basic-config',
    method: 'put',
    data: configData
  });
}

/**
 * 上传文件
 * @param {FormData} data 文件数据
 * @returns {Promise}
 */
export function uploadFile(data) {
  return request({
    url: '/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
} 