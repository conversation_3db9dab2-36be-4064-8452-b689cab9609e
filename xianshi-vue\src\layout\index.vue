<template>
  <div class="app-wrapper" :class="{'sidebar-collapsed': !sidebar.opened}">
    <!-- 侧边栏 -->
    <sidebar class="sidebar-container" />
    
    <div class="main-container">
      <!-- 顶部导航 -->
      <navbar />
      
      <!-- 主要内容区域 -->
      <app-main />
    </div>
  </div>
</template>

<script>
import { Sidebar, Navbar, AppMain } from './components';
import { mapGetters } from 'vuex';

export default {
  name: 'Layout',
  components: {
    Sidebar,
    Navbar,
    AppMain
  },
  computed: {
    ...mapGetters([
      'sidebar'
    ])
  },
  watch: {
    // 监听侧边栏状态变化
    'sidebar.opened': {
      immediate: true,
      handler(val) {
        // 在DOM更新后执行
        this.$nextTick(() => {
          this.adjustLayout();
        });
      }
    }
  },
  mounted() {
    // 页面加载时调整布局
    this.adjustLayout();
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.adjustLayout);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.adjustLayout);
  },
  methods: {
    adjustLayout() {
      const mainContainer = document.querySelector('.main-container');
      // 根据侧边栏状态调整宽度
      const sidebarWidth = this.sidebar.opened ? '250px' : '64px';
      
      if (mainContainer) {
        mainContainer.style.width = `calc(100% - ${sidebarWidth})`;
        mainContainer.style.marginLeft = sidebarWidth;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  
  .sidebar-container {
    width: 250px;
    height: 100%;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    background-color: #ffffff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    transition: width 0.3s;
    overflow: hidden;
    border-right: 1px solid rgba(0, 0, 0, 0.03);
  }
  
  .main-container {
    min-height: 100%;
    margin-left: 250px;
    width: calc(100% - 250px);
    position: relative;
    background-color: #f8f8f8;
    transition: all 0.3s;
    flex: 1;
  }
  
  &.sidebar-collapsed {
    .sidebar-container {
      width: 64px;
    }
    
    .main-container {
      margin-left: 64px;
      width: calc(100% - 64px);
    }
  }
}

@media (max-width: 768px) {
  .app-wrapper {
    .sidebar-container {
      width: 0;
    }
    
    .main-container {
      margin-left: 0;
      width: 100%;
    }
  }
}
</style> 