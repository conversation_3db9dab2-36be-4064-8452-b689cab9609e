package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.dto.order.OrderCommentDTO;
import com.play.xianshibusiness.dto.order.OrderDetailStatusUpdateDTO;
import com.play.xianshibusiness.dto.order.OrderEnrollDetailUpdateDTO;
import com.play.xianshibusiness.dto.order.OrderEnrollDetailVO;
import com.play.xianshibusiness.enums.MessageType;
import com.play.xianshibusiness.enums.OrderEnrollDetailStatusEnum;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.BOrderEnrollDetailMapper;
import com.play.xianshibusiness.mapper.BOrderMapper;
import com.play.xianshibusiness.mapper.CMemberMapper;
import com.play.xianshibusiness.pojo.BOrder;
import com.play.xianshibusiness.pojo.BOrderEnrollDetail;
import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.pojo.ISysMessage;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单报名详情服务
 */
@Service
public class BOrderEnrollDetailService {
    
    @Resource
    private BOrderEnrollDetailMapper orderEnrollDetailMapper;
    
    @Resource
    private BOrderMapper orderMapper;
    
    @Resource
    private CMemberMapper memberMapper;
    
    @Resource
    private BOrderStatusService orderStatusService;
    
    @Resource
    private BOrderService orderService;
    
    @Resource
    private BOrderCommentService orderCommentService;
    
    @Resource
    private ISysMessageService sysMessageService;
    
    /**
     * 校验状态流转是否合法
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     */
    private void checkStatusTransition(OrderEnrollDetailStatusEnum currentStatus, OrderEnrollDetailStatusEnum targetStatus) {
        if (currentStatus == targetStatus) {
            return; // 状态相同，不需要流转
        }
        
        // 根据业务规则校验状态流转是否合法
        switch (currentStatus) {
            case ENROLLED:
                if (targetStatus != OrderEnrollDetailStatusEnum.SELECTED && targetStatus != OrderEnrollDetailStatusEnum.CANCELED) {
                    throw new GlobalException(400, "已报名状态只能流转为被选中或已取消");
                }
                break;
            case SELECTED:
                if (targetStatus != OrderEnrollDetailStatusEnum.DEPARTED && targetStatus != OrderEnrollDetailStatusEnum.CANCELED) {
                    throw new GlobalException(400, "被选中状态只能流转为已出发或已取消");
                }
                break;
            case DEPARTED:
                if (targetStatus != OrderEnrollDetailStatusEnum.ARRIVED && targetStatus != OrderEnrollDetailStatusEnum.CANCELED) {
                    throw new GlobalException(400, "已出发状态只能流转为已到达或已取消");
                }
                break;
            case ARRIVED:
                if (targetStatus != OrderEnrollDetailStatusEnum.SERVICING && targetStatus != OrderEnrollDetailStatusEnum.CANCELED) {
                    throw new GlobalException(400, "已到达状态只能流转为服务中或已取消");
                }
                break;
            case SERVICING:
                if (targetStatus != OrderEnrollDetailStatusEnum.SERVICE_COMPLETED && targetStatus != OrderEnrollDetailStatusEnum.CANCELED) {
                    throw new GlobalException(400, "服务中状态只能流转为服务完成或已取消");
                }
                break;
            case SERVICE_COMPLETED:
                if (targetStatus != OrderEnrollDetailStatusEnum.EVALUATED) {
                    throw new GlobalException(400, "服务完成状态只能流转为已评价");
                }
                break;
            case EVALUATED:
                throw new GlobalException(400, "已评价状态不能再流转");
            case CANCELED:
                throw new GlobalException(400, "已取消状态不能再流转");
            default:
                throw new GlobalException(400, "未知状态");
        }
    }
    
    /**
     * 更新子订单状态
     * @param dto 子订单状态更新DTO
     * @param memberId 会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrderEnrollDetailStatus(OrderEnrollDetailUpdateDTO dto, String memberId) {
        // 1.获取子订单信息
        BOrderEnrollDetail detail = orderEnrollDetailMapper.selectById(dto.getId());
        if (detail == null || detail.getDeleted()) {
            throw new GlobalException(400, "子订单不存在");
        }
        
        // 2.校验是否是达人本人操作
        if (!memberId.equals(detail.getMemberId())) {
            throw new GlobalException(400, "只有达人本人才能操作");
        }
        
        // 3.获取订单信息
        BOrder order = orderMapper.selectById(detail.getOrderId());
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }
        
        // 4.校验订单状态
        if (order.getStatus().getCode().equals("PROCESSING")) {
            throw new GlobalException(400, "只有进行中的订单才能更新状态");
        }
        
        // 5.校验子订单状态
        OrderEnrollDetailStatusEnum currentStatus = detail.getStatus();
        OrderEnrollDetailStatusEnum targetStatus = OrderEnrollDetailStatusEnum.valueOf(dto.getTargetStatus());
        
        // 6.校验状态流转是否合法
        checkStatusTransition(currentStatus, targetStatus);
        
        // 7.更新子订单状态
        detail.setStatus(targetStatus);
        detail.setUpdateTime(LocalDateTime.now());
        orderEnrollDetailMapper.updateById(detail);
        
        // 8.记录状态变更
        orderStatusService.recordOrderDetailStatus(detail.getId(), targetStatus);
        
        // 9.如果状态是服务完成，检查订单是否全部完成
        if (targetStatus == OrderEnrollDetailStatusEnum.SERVICE_COMPLETED) {
            orderService.checkOrderCompleted(detail.getOrderId());
        }
        
        return true;
    }
    
    /**
     * 获取子订单详情
     * @param id 子订单ID
     * @return 子订单详情
     */
    public OrderEnrollDetailVO getOrderEnrollDetail(String id) {
        // 1.获取子订单信息
        BOrderEnrollDetail detail = orderEnrollDetailMapper.selectById(id);
        if (detail == null || detail.getDeleted()) {
            throw new GlobalException(400, "订单报名记录不存在");
        }
        
        // 2.获取会员信息
        CMember member = memberMapper.selectById(detail.getMemberId());
        
        // 3.获取订单信息
        BOrder order = orderMapper.selectById(detail.getOrderId());
        
        // 4.构建VO
        OrderEnrollDetailVO vo = new OrderEnrollDetailVO();
        BeanUtils.copyProperties(detail, vo);
        vo.setStatus(detail.getStatus());
        vo.setStatusDesc(detail.getStatus().getDesc());
        
        if (member != null) {
            vo.setMemberNickname(member.getNickname());
            vo.setMemberAvatar(member.getAvatar());
        }
        
        // 5.计算剩余服务时间
        if (order != null && detail.getServiceStartTime() != null && detail.getStatus() == OrderEnrollDetailStatusEnum.SERVICING) {
            LocalDateTime endTime = detail.getServiceStartTime().plusMinutes((long) (order.getLongTime() * 60));
            long remainingMinutes = ChronoUnit.MINUTES.between(LocalDateTime.now(), endTime);
            vo.setRemainingMinutes(remainingMinutes > 0 ? remainingMinutes : 0);
        }
        
        return vo;
    }
    
    /**
     * 获取订单下的所有子订单
     * @param orderId 订单ID
     * @return 子订单列表
     */
    public List<OrderEnrollDetailVO> listByOrderId(String orderId) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }
        
        // 2.获取所有子订单
        List<BOrderEnrollDetail> details = orderEnrollDetailMapper.selectList(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                .eq(BOrderEnrollDetail::getOrderId, orderId)
                .eq(BOrderEnrollDetail::getDeleted, false)
        );
        
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        
        // 3.获取会员信息
        List<String> memberIds = details.stream()
                .map(BOrderEnrollDetail::getMemberId)
                .collect(Collectors.toList());
        
        Map<String, CMember> memberMap = memberMapper.selectList(
                new LambdaQueryWrapper<CMember>()
                .in(CMember::getId, memberIds)
        ).stream().collect(Collectors.toMap(CMember::getId, Function.identity(), (o1, o2) -> o1));
        
        // 4.构建VO
        return details.stream().map(detail -> {
            OrderEnrollDetailVO vo = new OrderEnrollDetailVO();
            BeanUtils.copyProperties(detail, vo);
            vo.setStatus(detail.getStatus());
            vo.setStatusDesc(detail.getStatus().getDesc());
            
            CMember member = memberMap.get(detail.getMemberId());
            if (member != null) {
                vo.setMemberNickname(member.getNickname());
                vo.setMemberAvatar(member.getAvatar());
            }
            
            // 计算剩余服务时间
            if (detail.getServiceStartTime() != null && detail.getStatus() == OrderEnrollDetailStatusEnum.SERVICING) {
                LocalDateTime endTime = detail.getServiceStartTime().plusMinutes((long) (order.getLongTime() * 60));
                long remainingMinutes = ChronoUnit.MINUTES.between(LocalDateTime.now(), endTime);
                vo.setRemainingMinutes(remainingMinutes > 0 ? remainingMinutes : 0);
            }
            
            return vo;
        }).collect(Collectors.toList());
    }


    /**
     * 服务完成
     * @param id 子订单ID
     * @param memberId 达人ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeService(String id, String memberId) {
        // 1.获取子订单信息
        BOrderEnrollDetail detail = orderEnrollDetailMapper.selectById(id);
        if (detail == null || detail.getDeleted()) {
            throw new GlobalException(400, "订单报名记录不存在");
        }
        
        // 2.校验权限
        if (!detail.getMemberId().equals(memberId)) {
            throw new GlobalException(400, "您无权操作此订单报名记录");
        }
        
        // 3.校验状态
        if (detail.getStatus() != OrderEnrollDetailStatusEnum.SERVICING) {
            throw new GlobalException(400, "只有服务中状态的订单才能完成服务");
        }
        
        // 4.更新状态
        detail.setStatus(OrderEnrollDetailStatusEnum.SERVICE_COMPLETED);
        detail.setServiceEndTime(LocalDateTime.now());
        detail.setUpdateTime(LocalDateTime.now());
        orderEnrollDetailMapper.updateById(detail);
        
        // 5.记录状态变更
        orderStatusService.recordOrderDetailStatus(detail.getId(), OrderEnrollDetailStatusEnum.SERVICE_COMPLETED);
        
        // 6.获取订单信息
        BOrder order = orderMapper.selectById(detail.getOrderId());
        
        // 7.通知订单所有者
        if (order != null) {
            sysMessageService.sendMessage(order.getMemberId(), 
                    "达人已完成服务，请确认并评价", 
                    MessageType.ORDER_UPDATE);
        }
        
        // 8.检查订单是否已完成
        orderService.checkOrderCompleted(detail.getOrderId());
        
        return true;
    }
    
    /**
     * 确认完成并评价
     * @param dto 评价DTO
     * @param memberId 会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmAndComment(OrderCommentDTO dto, String memberId) {
        // 1.获取子订单信息
        BOrderEnrollDetail detail = orderEnrollDetailMapper.selectById(dto.getEnrollDetailId());
        if (detail == null || detail.getDeleted()) {
            throw new GlobalException(400, "订单报名记录不存在");
        }
        
        // 2.获取订单信息
        BOrder order = orderMapper.selectById(detail.getOrderId());
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }
        
        // 3.校验权限
        if (!order.getMemberId().equals(memberId)) {
            throw new GlobalException(400, "您无权操作此订单");
        }
        
        // 4.校验状态
        if (detail.getStatus() != OrderEnrollDetailStatusEnum.SERVICE_COMPLETED) {
            throw new GlobalException(400, "只有服务完成状态的订单才能评价");
        }
        
        // 5.创建评价
        orderCommentService.createComment(dto, memberId, detail.getOrderId());
        
        // 6.更新状态
        detail.setStatus(OrderEnrollDetailStatusEnum.EVALUATED);
        detail.setUpdateTime(LocalDateTime.now());
        orderEnrollDetailMapper.updateById(detail);
        
        // 7.记录状态变更
        orderStatusService.recordOrderDetailStatus(detail.getId(), OrderEnrollDetailStatusEnum.EVALUATED);
        
        // 8.通知达人
        sysMessageService.sendMessage(detail.getMemberId(), 
                "用户已确认服务完成并评价", 
                MessageType.ORDER_UPDATE);
        
        // 9.检查订单是否已完成
        orderService.checkOrderCompleted(detail.getOrderId());
        
        return true;
    }
    
    /**
     * 管理员更新子订单状态
     * @param dto 子订单状态更新DTO
     * @param adminId 管理员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean adminUpdateOrderDetailStatus(OrderDetailStatusUpdateDTO dto, String adminId) {
        // 1.获取子订单信息
        BOrderEnrollDetail detail = orderEnrollDetailMapper.selectById(dto.getDetailId());
        if (detail == null || detail.getDeleted()) {
            throw new GlobalException(400, "子订单不存在");
        }
        
        // 2.获取订单信息
        BOrder order = orderMapper.selectById(detail.getOrderId());
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }
        
        // 3.校验子订单状态
        OrderEnrollDetailStatusEnum newStatus = OrderEnrollDetailStatusEnum.getByCode(dto.getStatus());
        if (newStatus == null) {
            throw new GlobalException(400, "无效的状态");
        }
        
        // 4.更新子订单状态
        detail.setStatus(newStatus);
        detail.setUpdateTime(LocalDateTime.now());
        
        // 根据状态设置相应的时间字段
        switch (newStatus) {
            case DEPARTED:
                // 已出发，设置出发时间
                detail.setDepartureTime(LocalDateTime.now());
                break;
            case ARRIVED:
                // 已到达，设置到达时间
                detail.setArrivalTime(LocalDateTime.now());
                break;
            case SERVICING:
                // 服务中，设置服务开始时间
                detail.setServiceStartTime(LocalDateTime.now());
                break;
            case SERVICE_COMPLETED:
                // 服务完成，设置服务结束时间
                detail.setServiceEndTime(LocalDateTime.now());
                break;
            default:
                break;
        }
        
        orderEnrollDetailMapper.updateById(detail);
        
        // 5.记录状态变更
        orderStatusService.recordOrderDetailStatus(dto.getDetailId(), newStatus);
        
        // 6.检查是否所有子订单都已完成，如果是，则更新主订单状态
        if (newStatus == OrderEnrollDetailStatusEnum.SERVICE_COMPLETED) {
            orderService.checkOrderCompleted(detail.getOrderId());
        }
        
        return true;
    }
}

