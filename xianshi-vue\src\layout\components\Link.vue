<template>
  <component :is="type" v-bind="linkProps(to)">
    <slot />
  </component>
</template>

<script>
import { isExternal } from '@/utils/validate';

export default {
  name: 'AppLink',
  props: {
    to: {
      type: String,
      required: true
    }
  },
  computed: {
    // 如果是外部链接则使用a标签，否则使用router-link
    type() {
      if (isExternal(this.to)) {
        return 'a';
      }
      return 'router-link';
    }
  },
  methods: {
    linkProps(url) {
      if (isExternal(url)) {
        return {
          href: url,
          target: '_blank',
          rel: 'noopener'
        };
      }
      return {
        to: url
      };
    }
  }
};
</script> 