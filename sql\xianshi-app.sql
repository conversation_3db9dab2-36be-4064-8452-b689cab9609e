/*
 Navicat Premium Data Transfer

 Source Server         : xianshiapp
 Source Server Type    : MySQL
 Source Server Version : 80024 (8.0.24)
 Source Host           : *************:3306
 Source Schema         : xianshi-app

 Target Server Type    : MySQL
 Target Server Version : 80024 (8.0.24)
 File Encoding         : 65001

 Date: 24/07/2025 10:43:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for b_order
-- ----------------------------
DROP TABLE IF EXISTS `b_order`;
CREATE TABLE `b_order`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `sys_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统ID',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `start_time` datetime NULL DEFAULT NULL COMMENT '活动开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '活动结束时间',
  `sex_require` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别要求 【枚举】',
  `address_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '地点',
  `address_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '地图地点JSON',
  `person_count` int NULL DEFAULT NULL COMMENT '参与人数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '需求描述',
  `long_time` decimal(19, 2) NULL DEFAULT NULL COMMENT '时长',
  `status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单状态【枚举】',
  `target_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '目标会员IDS',
  `hourly_rate` decimal(19, 2) NULL DEFAULT NULL COMMENT '小时费用',
  `activity_type_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '活动类型ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '标题',
  `longitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经度',
  `latitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '纬度',
  `total_fee` decimal(19, 2) NULL DEFAULT NULL COMMENT '总费用',
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '地址',
  `location` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '地点',
  `audit_result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核结果：0-未审核，1-通过，2-拒绝',
  `audit_comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核意见',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `auditor_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人ID',
  `cancel_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取消原因',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `gold_cost` decimal(10, 2) NULL DEFAULT NULL COMMENT '金币消耗',
  `enroll_count` int NULL DEFAULT 0 COMMENT '报名人数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of b_order
-- ----------------------------
INSERT INTO `b_order` VALUES ('1444f3b31cc4a46a986cc04c57ccd46e', NULL, 1, 0, '2025-07-22 09:59:49', NULL, NULL, '35533dcde2e9', '2025-07-22 12:00:00', '2025-07-22 20:00:00', '2', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 2, '闲的没事儿干，来斗地主', 8.00, 'DRAFT', NULL, 50.00, '8a487cfb-fbef-4d55-abdd-558fe7865261', '唱歌 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 400.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', NULL, NULL, NULL, NULL, NULL, NULL, 1200.00);
INSERT INTO `b_order` VALUES ('179d39cc548180b0cb78eee9df586ae4', NULL, 1, 0, '2025-07-22 09:56:34', NULL, NULL, 'a19832a240cd', '2025-07-22 12:00:00', '2025-07-22 20:00:00', '2', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 2, '闲的没事儿干，来斗地主', 8.00, 'DRAFT', NULL, 50.00, '8a487cfb-fbef-4d55-abdd-558fe7865261', '唱歌 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 400.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', NULL, NULL, NULL, NULL, NULL, NULL, 1200.00);
INSERT INTO `b_order` VALUES ('1e6e58f53154058a04d958bfb38153f7', NULL, 1, 0, '2025-07-06 18:14:05', NULL, NULL, '05896a650635', '2025-07-08 14:00:00', '2025-07-08 17:00:00', '1', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 1, '432', 3.00, 'DRAFT', NULL, 234.00, '5f79675f-0760-4755-963c-0bc66d1fe63d', '游戏陪玩 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 702.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', NULL, NULL, NULL, NULL, NULL, NULL, 702.00);
INSERT INTO `b_order` VALUES ('38227acb4aad7a22a02a5643122d630d', NULL, 1, 0, '2025-07-06 16:58:19', NULL, NULL, '4c75738f6dc3', '2025-07-07 14:00:00', '2025-07-07 17:00:00', '0', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 3, '唱歌陪玩，需要3个人！！', 3.00, 'CANCELED', NULL, 100.00, '8a487cfb-fbef-4d55-abdd-558fe7865261', '唱歌 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 300.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', '1', '', '2025-07-06 17:00:51', '5b54c4e7e9a747f7bea36d9234078f9d', '达人主动取消报名', '2025-07-13 17:52:15', 900.00);
INSERT INTO `b_order` VALUES ('50131963a4c22524816dd074b602de67', NULL, 1, 0, '2025-07-22 19:43:58', '2025-07-22 19:46:13', 'f8c782fe9fa3', 'ccda3124-41e5-4508-a958-3fbd76017496', '2025-08-22 14:00:00', '2025-08-22 17:00:00', '0', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', NULL, 'oop', 3.00, 'PUBLISHED', NULL, 299.00, 'c71a8399-070d-480b-b1c6-b8623d879376', '桌球 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 897.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', '1', 'dd', '2025-07-22 19:46:13', '5b54c4e7e9a747f7bea36d9234078f9d', NULL, NULL, 897.00);
INSERT INTO `b_order` VALUES ('515ae42c53da7532554ae8f17b60f102', NULL, 1, 0, '2025-07-06 18:07:11', NULL, NULL, 'd86957878e52', '2025-08-06 14:00:00', '2025-08-06 17:00:00', '1', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 2, '43', 3.00, 'DRAFT', NULL, 134.00, '6f2ddf51-f110-4fff-8ce1-4f9f19e72250', 'KTV - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 402.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', NULL, NULL, NULL, NULL, NULL, NULL, 804.00);
INSERT INTO `b_order` VALUES ('5ba7390331a04b3f1f72e3bd0bc3f6fd', NULL, 1, 0, '2025-07-06 21:49:55', NULL, NULL, '33b6afe07034', '2025-07-07 15:00:00', '2025-07-07 16:00:00', '1', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', NULL, '测试', 1.00, 'PUBLISHED', NULL, 400.00, '5f79675f-0760-4755-963c-0bc66d1fe63d', '游戏陪玩 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 400.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', '1', 'ces', '2025-07-06 21:54:18', '5b54c4e7e9a747f7bea36d9234078f9d', NULL, NULL, 1200.00);
INSERT INTO `b_order` VALUES ('81f1e9422fec9d2a9e54c5ec2dfa8020', NULL, 1, 0, '2025-07-06 18:09:33', NULL, NULL, '28d28a849a92', '2025-09-06 14:01:00', '2025-09-06 15:00:00', '1', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 2, '23', 0.98, 'DRAFT', NULL, 23.00, '6f2ddf51-f110-4fff-8ce1-4f9f19e72250', 'KTV - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 22.62, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', NULL, NULL, NULL, NULL, NULL, NULL, 45.23);
INSERT INTO `b_order` VALUES ('9884cae89c26ed63d5e027649e93591a', NULL, 1, 0, '2025-07-06 21:40:07', NULL, NULL, 'fc23d34286b8', '2025-07-07 14:00:00', '2025-07-07 17:00:00', '0', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', NULL, '测试', 3.00, 'DRAFT', NULL, 300.00, '5f79675f-0760-4755-963c-0bc66d1fe63d', '游戏陪玩 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 900.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', NULL, NULL, NULL, NULL, NULL, NULL, 1800.00);
INSERT INTO `b_order` VALUES ('c72f40ad5097b25a26c5ad1dea308ce3', NULL, 1, 0, '2025-07-06 18:13:02', NULL, NULL, '3e26b48bb376', '2025-07-07 14:00:00', '2025-07-07 17:00:00', '1', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 2, '244', 3.00, 'DRAFT', NULL, 23.00, '5f79675f-0760-4755-963c-0bc66d1fe63d', '游戏陪玩 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 69.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', NULL, NULL, NULL, NULL, NULL, NULL, 138.00);
INSERT INTO `b_order` VALUES ('cb56eebe16d87cda4eb123b89bc2abfc', NULL, 1, 0, '2025-07-06 21:58:49', NULL, NULL, 'e686b219431f', '2025-07-07 14:00:00', '2025-07-07 15:00:00', '1', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', NULL, '測試發佈', 1.00, 'PUBLISHED', NULL, 99.00, 'c71a8399-070d-480b-b1c6-b8623d879376', '桌球 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 99.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', '1', '通過', '2025-07-06 21:59:21', '5b54c4e7e9a747f7bea36d9234078f9d', NULL, NULL, 396.00);
INSERT INTO `b_order` VALUES ('d01fbe2ee46b460b4b7ba0cfe135f118', NULL, 1, 0, '2025-07-06 18:11:06', NULL, NULL, '6bc7b5d30815', '2027-07-06 14:00:00', '2027-07-06 17:00:00', '2', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 2, '234', 3.00, 'DRAFT', NULL, 234.00, '6f2ddf51-f110-4fff-8ce1-4f9f19e72250', 'KTV - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 702.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', NULL, NULL, NULL, NULL, NULL, NULL, 1404.00);
INSERT INTO `b_order` VALUES ('d7a1c0c5a7c35c5fe93388dc4bef17a8', NULL, 1, 0, '2025-07-06 22:14:42', NULL, NULL, 'ccaec87fcb7f', '2025-07-06 22:30:00', '2025-07-06 23:00:00', '1', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', NULL, '測試 即使訂單', 0.50, 'PUBLISHED', NULL, 77.00, '5f79675f-0760-4755-963c-0bc66d1fe63d', '游戏陪玩 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 38.50, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', '1', '通過', '2025-07-06 22:15:01', '5b54c4e7e9a747f7bea36d9234078f9d', NULL, NULL, 115.50);
INSERT INTO `b_order` VALUES ('da4225d63cd93db412a81027acd8ec55', NULL, 1, 0, '2025-07-06 16:57:21', NULL, NULL, '3e69f3fa6cdb', '2025-07-07 14:00:00', '2025-07-07 17:00:00', '0', '四川省成都市龙泉驿区欧鹏大道349号', '{\"name\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"address\":\"四川省成都市龙泉驿区欧鹏大道349号\",\"latitude\":\"30.572816\",\"longitude\":\"104.066803\",\"province\":\"四川省\",\"city\":\"成都\",\"district\":\"四川省成都市龙泉驿区\"}', 3, 'bell 测试', 3.00, 'DRAFT', NULL, 100.00, '5f79675f-0760-4755-963c-0bc66d1fe63d', '游戏陪玩 - 四川省成都市龙泉驿区欧鹏大道349号', '104.066803', '30.572816', 300.00, '四川省成都市龙泉驿区欧鹏大道349号', '四川省成都市龙泉驿区欧鹏大道349号', NULL, NULL, NULL, NULL, NULL, NULL, 900.00);

-- ----------------------------
-- Table structure for b_order_comment
-- ----------------------------
DROP TABLE IF EXISTS `b_order_comment`;
CREATE TABLE `b_order_comment`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `order_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单ID',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `comment_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '评价勾选内容',
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '评价内容',
  `comment_starts` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '评价星级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of b_order_comment
-- ----------------------------

-- ----------------------------
-- Table structure for b_order_enroll_detail
-- ----------------------------
DROP TABLE IF EXISTS `b_order_enroll_detail`;
CREATE TABLE `b_order_enroll_detail`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `order_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主订单ID',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报名会员ID',
  `is_select` tinyint NULL DEFAULT NULL COMMENT '是否被选择',
  `service_start_time` datetime NULL DEFAULT NULL COMMENT '服务开始时间',
  `service_end_time` datetime NULL DEFAULT NULL COMMENT '服务结束时间',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '状态',
  `select_time` datetime NULL DEFAULT NULL COMMENT '选中时间',
  `arrival_time` datetime NULL DEFAULT NULL COMMENT '到达时间',
  `selected` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否被选中',
  `departure_time` datetime NULL DEFAULT NULL COMMENT '出发时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of b_order_enroll_detail
-- ----------------------------
INSERT INTO `b_order_enroll_detail` VALUES ('0029f9de-cb16-42b9-b86d-57c150df949c', NULL, 1, 0, '2025-06-16 23:20:31', NULL, NULL, '478b6fd68569b49cc8708f103e112fb5', 1, NULL, NULL, 'DEPARTED', NULL, NULL, NULL, '2025-06-16 23:23:07');
INSERT INTO `b_order_enroll_detail` VALUES ('0ce41e64-60b6-4eaf-8bd6-f8c3bbc930bb', NULL, 1, 0, '2025-06-14 03:35:09', NULL, NULL, '1933588071390961665', 1, '2025-06-14 03:39:16', '2025-06-14 03:40:15', 'EVALUATED', NULL, NULL, NULL, NULL);
INSERT INTO `b_order_enroll_detail` VALUES ('5250855b-4312-4eb7-af1e-bb230ca362a5', NULL, 1, 0, '2025-06-16 22:39:12', NULL, NULL, '105f7c185b09abbff484fa611ac42620', 1, NULL, NULL, 'ARRIVED', NULL, '2025-06-16 22:56:31', NULL, '2025-06-16 22:58:09');

-- ----------------------------
-- Table structure for b_order_status
-- ----------------------------
DROP TABLE IF EXISTS `b_order_status`;
CREATE TABLE `b_order_status`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '变更内容类型【枚举】MAIN-主订单/SUB-子订单',
  `bussiness_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '业务ID 订单或子订单ID',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '变更内容',
  `status` int NULL DEFAULT NULL COMMENT '主订单或子订单状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of b_order_status
-- ----------------------------
INSERT INTO `b_order_status` VALUES ('011d6f0d-b63b-45ec-b410-5dc5b5d76f61', NULL, 1, 0, '2025-07-06 17:00:51', NULL, 'Order', '38227acb4aad7a22a02a5643122d630d', '发布中', 2);
INSERT INTO `b_order_status` VALUES ('0281fca6-4790-484a-8cae-c4067189caff', NULL, 1, 0, '2025-06-15 16:38:47', NULL, 'Order', '5ae0b89308f17867484291e1ec53fafc', NULL, NULL);
INSERT INTO `b_order_status` VALUES ('09f907e8-3716-4aef-a227-055f65d97e20', NULL, 1, 0, '2025-07-06 18:07:11', NULL, 'Order', '515ae42c53da7532554ae8f17b60f102', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('0b81724b-f9a3-46eb-b684-6648d399422d', NULL, 1, 0, '2025-06-16 23:20:52', NULL, 'Order', '478b6fd68569b49cc8708f103e112fb5', '进行中', 4);
INSERT INTO `b_order_status` VALUES ('0dba7013-a28d-4a4c-b4d5-374fb453aaf3', NULL, 1, 0, '2025-07-13 17:52:15', NULL, 'Order', '38227acb4aad7a22a02a5643122d630d', '已取消', 6);
INSERT INTO `b_order_status` VALUES ('0fde111d-3c20-4910-8469-12f489a88c48', NULL, 1, 0, '2025-07-06 22:14:42', NULL, 'Order', 'd7a1c0c5a7c35c5fe93388dc4bef17a8', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('1657fe1c-ba65-4ad3-9e06-38a42c00602b', NULL, 1, 0, '2025-06-15 16:24:28', NULL, 'Order', '4d5ba8bb691b617e4a1d8c4a8f5ae391', NULL, NULL);
INSERT INTO `b_order_status` VALUES ('1d6dc532-9291-491d-8dc2-0f63825c8207', NULL, 1, 0, '2025-06-16 23:20:52', NULL, 'Order_Detail', '0029f9de-cb16-42b9-b86d-57c150df949c', '被选中', 1);
INSERT INTO `b_order_status` VALUES ('21b818f0-5ae3-492f-8292-d7acfe22eabd', NULL, 1, 0, '2025-07-22 09:59:49', NULL, 'Order', '1444f3b31cc4a46a986cc04c57ccd46e', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('292c4454-c9c5-4325-b694-9dd61556b136', NULL, 1, 0, '2025-06-16 22:31:29', NULL, 'Order', '105f7c185b09abbff484fa611ac42620', '发布中', 2);
INSERT INTO `b_order_status` VALUES ('29762658-341a-4eca-a445-1f6e4680f3d1', NULL, 1, 0, '2025-06-16 23:23:07', NULL, 'Order_Detail', '0029f9de-cb16-42b9-b86d-57c150df949c', '已出发', 2);
INSERT INTO `b_order_status` VALUES ('2b8fc2c0-bb5a-4c34-8bfd-1b76f60d8644', NULL, 1, 0, '2025-07-06 21:54:18', NULL, 'Order', '5ba7390331a04b3f1f72e3bd0bc3f6fd', '发布中', 2);
INSERT INTO `b_order_status` VALUES ('31027653-2bda-4351-8de0-be9fea4819ca', NULL, 1, 0, '2025-06-14 03:35:09', NULL, 'Order_Detail', '0ce41e64-60b6-4eaf-8bd6-f8c3bbc930bb', '达人已报名', NULL);
INSERT INTO `b_order_status` VALUES ('35c0aa8f-f071-4a57-b295-dd23571d6395', NULL, 1, 0, '2025-07-06 16:40:16', NULL, 'Order', 'a73de0dd0dc4dc2678ae2629aacecd40', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('37763ae7-9c65-4d7f-a981-63c8e6dad6a9', NULL, 1, 0, '2025-06-14 03:39:16', NULL, 'Order_Detail', '0ce41e64-60b6-4eaf-8bd6-f8c3bbc930bb', '服务中', NULL);
INSERT INTO `b_order_status` VALUES ('3aad72d6-b8d2-4f03-b4b6-b20b7c33db98', NULL, 1, 0, '2025-07-05 23:38:28', NULL, 'Order', '72f42096bfc05975da5c6f7a6882f02c', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('3c4f5fcc-b73e-4a1c-b2c6-8ce8d0914e07', NULL, 1, 0, '2025-06-15 16:23:02', NULL, 'Order', '5ae0b89308f17867484291e1ec53fafc', NULL, NULL);
INSERT INTO `b_order_status` VALUES ('3cd9cfe6-c3f6-4ff2-8fd8-46b3297a9340', NULL, 1, 0, '2025-07-06 22:15:01', NULL, 'Order', 'd7a1c0c5a7c35c5fe93388dc4bef17a8', '发布中', 2);
INSERT INTO `b_order_status` VALUES ('3df4295f-9224-4fa9-8f91-058176497a38', NULL, 1, 0, '2025-06-16 23:20:31', NULL, 'Order_Detail', '0029f9de-cb16-42b9-b86d-57c150df949c', '达人已报名', 0);
INSERT INTO `b_order_status` VALUES ('4371dc84-d8e5-4bc6-b542-aece5f588f06', NULL, 1, 0, '2025-07-06 21:58:49', NULL, 'Order', 'cb56eebe16d87cda4eb123b89bc2abfc', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('46b21bad-f53e-49a0-a88c-bc7c839dfa1f', NULL, 1, 0, '2025-07-06 18:14:05', NULL, 'Order', '1e6e58f53154058a04d958bfb38153f7', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('46f67719-80d6-423d-af3f-cf172dd4e317', NULL, 1, 0, '2025-06-14 03:32:40', NULL, 'Order', '1933588071390961665', NULL, NULL);
INSERT INTO `b_order_status` VALUES ('4963b6c0-4267-4ec0-aff9-c5342b4c97d7', NULL, 1, 0, '2025-07-06 18:09:33', NULL, 'Order', '81f1e9422fec9d2a9e54c5ec2dfa8020', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('56633505-11f8-43b5-8991-8665979e2942', NULL, 1, 0, '2025-06-16 23:13:11', NULL, 'Order', '478b6fd68569b49cc8708f103e112fb5', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('5f85ba63-b1f8-4e9a-8e44-4f1963131ba1', NULL, 1, 0, '2025-07-22 19:43:58', '2025-07-22 19:43:58', 'Order', '50131963a4c22524816dd074b602de67', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('654ec212-a875-4d27-b0f0-7c00637f7585', NULL, 1, 0, '2025-07-06 22:14:43', NULL, 'Order', 'd7a1c0c5a7c35c5fe93388dc4bef17a8', '待审核', 1);
INSERT INTO `b_order_status` VALUES ('664441d7-f3c8-4474-99a8-219419722fd3', NULL, 1, 0, '2025-07-06 21:40:07', NULL, 'Order', '9884cae89c26ed63d5e027649e93591a', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('69455e3b-1486-475d-978a-1c0b7c7bdc52', NULL, 1, 0, '2025-06-14 03:42:16', NULL, 'Order_Detail', '0ce41e64-60b6-4eaf-8bd6-f8c3bbc930bb', '已评价', NULL);
INSERT INTO `b_order_status` VALUES ('6c6b988a-0828-4e3d-8546-821aa0faaea7', NULL, 1, 0, '2025-07-06 18:11:06', NULL, 'Order', 'd01fbe2ee46b460b4b7ba0cfe135f118', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('6d06244c-e0eb-4e32-96d4-dc43d4fe467d', NULL, 1, 0, '2025-06-15 16:24:13', NULL, 'Order', '5ae0b89308f17867484291e1ec53fafc', NULL, NULL);
INSERT INTO `b_order_status` VALUES ('782c2a92-32c3-4eaf-bb06-0fed883d31af', NULL, 1, 0, '2025-07-05 23:36:40', NULL, 'Order', 'e75f86ea77445139b7c8cc8e9821022c', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('7c9a485a-f687-460f-bfa3-e50c8a8a0999', NULL, 1, 0, '2025-06-16 22:30:57', NULL, 'Order', '105f7c185b09abbff484fa611ac42620', '待审核', 1);
INSERT INTO `b_order_status` VALUES ('7eabee48-6eb5-4de5-a241-362134d43fa9', NULL, 1, 0, '2025-06-16 22:58:09', NULL, 'Order_Detail', '5250855b-4312-4eb7-af1e-bb230ca362a5', '已到达', 3);
INSERT INTO `b_order_status` VALUES ('8451c830-aa05-4bde-b9a1-1c675766cbd2', NULL, 1, 0, '2025-07-06 21:49:56', NULL, 'Order', '5ba7390331a04b3f1f72e3bd0bc3f6fd', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('8c3b400a-178c-467d-a260-123988e5219e', NULL, 1, 0, '2025-06-14 02:11:35', NULL, 'Order', '1933588071390961665', NULL, NULL);
INSERT INTO `b_order_status` VALUES ('94cad7be-0a98-41a1-a09c-4a2418f6fd14', NULL, 1, 0, '2025-06-16 23:13:57', NULL, 'Order', '478b6fd68569b49cc8708f103e112fb5', '待审核', 1);
INSERT INTO `b_order_status` VALUES ('9629c82b-62cc-46c4-bf52-5d48ab95a222', NULL, 1, 0, '2025-07-22 09:56:34', NULL, 'Order', '179d39cc548180b0cb78eee9df586ae4', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('9aaf3d56-a34a-41ff-8c31-0c923ef3766a', NULL, 1, 0, '2025-06-16 23:19:07', NULL, 'Order', '478b6fd68569b49cc8708f103e112fb5', '发布中', 2);
INSERT INTO `b_order_status` VALUES ('a2dc1c31-42ef-4953-874e-d8a902f65eaa', NULL, 1, 0, '2025-07-06 16:57:21', NULL, 'Order', 'da4225d63cd93db412a81027acd8ec55', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('a65da96a-6feb-4e91-baad-d275f4818d5c', NULL, 1, 0, '2025-07-06 16:49:09', NULL, 'Order', '1afac66d0ad7d3d6a3e7931949c52afa', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('a7bf6eaa-bdc0-4c2a-8a60-496f191bebf6', NULL, 1, 0, '2025-06-14 03:40:15', NULL, 'Order', '1933588071390961665', NULL, NULL);
INSERT INTO `b_order_status` VALUES ('aa2c98a0-2bcb-43e7-9bfd-242dd6f0ccc5', NULL, 1, 0, '2025-07-06 21:49:57', NULL, 'Order', '5ba7390331a04b3f1f72e3bd0bc3f6fd', '待审核', 1);
INSERT INTO `b_order_status` VALUES ('ac96098d-99c6-4193-a81a-8bb982bc24f1', NULL, 1, 0, '2025-07-06 18:13:02', NULL, 'Order', 'c72f40ad5097b25a26c5ad1dea308ce3', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('adee29fd-e5e1-49e7-94b7-141dcc17feab', NULL, 1, 0, '2025-06-14 03:38:44', NULL, 'Order_Detail', '0ce41e64-60b6-4eaf-8bd6-f8c3bbc930bb', '已到达', NULL);
INSERT INTO `b_order_status` VALUES ('ae00f266-e4d4-48d1-8890-4a9572a4423e', NULL, 1, 0, '2025-06-14 03:40:15', NULL, 'Order_Detail', '0ce41e64-60b6-4eaf-8bd6-f8c3bbc930bb', '服务完成', NULL);
INSERT INTO `b_order_status` VALUES ('af7aaf8f-c154-41f3-8b70-9a4eb3a51908', NULL, 1, 0, '2025-06-14 03:37:45', NULL, 'Order_Detail', '0ce41e64-60b6-4eaf-8bd6-f8c3bbc930bb', '被选中', NULL);
INSERT INTO `b_order_status` VALUES ('b22acec4-aace-4db5-87a2-506c27883267', NULL, 1, 0, '2025-07-06 21:59:21', NULL, 'Order', 'cb56eebe16d87cda4eb123b89bc2abfc', '发布中', 2);
INSERT INTO `b_order_status` VALUES ('babf02ce-51da-4207-9e75-ece90339b2b7', NULL, 1, 0, '2025-07-06 16:58:19', NULL, 'Order', '38227acb4aad7a22a02a5643122d630d', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('c21b4bae-3607-4396-864e-98b1af68bfdb', NULL, 1, 0, '2025-06-16 22:29:56', NULL, 'Order', '105f7c185b09abbff484fa611ac42620', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('c4bf02f9-b549-4b9f-bf38-1f6b2d6b553a', NULL, 1, 0, '2025-06-14 03:37:46', NULL, 'Order', '1933588071390961665', NULL, NULL);
INSERT INTO `b_order_status` VALUES ('c78902d5-692a-4cb7-9a8e-338276f1bdba', NULL, 1, 0, '2025-07-06 21:58:50', NULL, 'Order', 'cb56eebe16d87cda4eb123b89bc2abfc', '待审核', 1);
INSERT INTO `b_order_status` VALUES ('cd30a433-32a9-4057-b311-44bbac683ed0', NULL, 1, 0, '2025-07-22 19:43:58', '2025-07-22 19:43:58', 'Order', '50131963a4c22524816dd074b602de67', '待审核', 1);
INSERT INTO `b_order_status` VALUES ('ce13da9a-cbb8-4e74-9d32-7897ba91e5ab', NULL, 1, 0, '2025-06-14 03:38:25', NULL, 'Order_Detail', '0ce41e64-60b6-4eaf-8bd6-f8c3bbc930bb', '已出发', NULL);
INSERT INTO `b_order_status` VALUES ('ce52c21e-6b8c-435d-a95c-7c8c98913139', NULL, 1, 0, '2025-06-16 22:55:21', NULL, 'Order', '105f7c185b09abbff484fa611ac42620', '进行中', 4);
INSERT INTO `b_order_status` VALUES ('cee3d6d7-17a4-49d9-ae73-4dafdfff5317', NULL, 1, 0, '2025-06-25 21:15:05', NULL, 'Order', '5ce2b166e2269a6ebff4e1956c7548b3', '待发布', 0);
INSERT INTO `b_order_status` VALUES ('d15e2fdb-e47e-448b-a065-9eaf365d371b', NULL, 1, 0, '2025-06-16 22:39:12', NULL, 'Order_Detail', '5250855b-4312-4eb7-af1e-bb230ca362a5', '达人已报名', 0);
INSERT INTO `b_order_status` VALUES ('dfe88b17-7b3e-4780-90d0-cb13c153665a', NULL, 1, 0, '2025-06-16 22:56:31', NULL, 'Order_Detail', '5250855b-4312-4eb7-af1e-bb230ca362a5', '已出发', 2);
INSERT INTO `b_order_status` VALUES ('e3253e88-bbaf-4f7d-a511-871f24fdd7a6', NULL, 1, 0, '2025-07-22 19:46:13', '2025-07-22 19:46:13', 'Order', '50131963a4c22524816dd074b602de67', '发布中', 2);
INSERT INTO `b_order_status` VALUES ('ea73ddc0-1880-4172-9287-5a141c4677f7', NULL, 1, 0, '2025-07-06 16:59:48', NULL, 'Order', '38227acb4aad7a22a02a5643122d630d', '待审核', 1);
INSERT INTO `b_order_status` VALUES ('ed9fcd5d-1d0e-44b6-b91d-097e6533ed7f', NULL, 1, 0, '2025-06-16 22:55:21', NULL, 'Order_Detail', '5250855b-4312-4eb7-af1e-bb230ca362a5', '被选中', 1);

-- ----------------------------
-- Table structure for b_order_type
-- ----------------------------
DROP TABLE IF EXISTS `b_order_type`;
CREATE TABLE `b_order_type`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `parent_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父类ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类名称',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类图标',
  `need_gold` decimal(19, 2) NULL DEFAULT NULL COMMENT '所需金币',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of b_order_type
-- ----------------------------
INSERT INTO `b_order_type` VALUES ('5f79675f-0760-4755-963c-0bc66d1fe63d', 0, 1, 0, '2025-06-14 02:03:32', '2025-06-14 02:03:32', 'bbf6503b-1302-4dc6-ae41-254dda051e69', '游戏陪玩', '游戏陪玩', NULL, 100.00);
INSERT INTO `b_order_type` VALUES ('6f2ddf51-f110-4fff-8ce1-4f9f19e72250', 0, 1, 0, '2025-06-14 02:05:26', '2025-06-14 02:05:26', 'bbf6503b-1302-4dc6-ae41-254dda051e69', 'KTV', 'KTV', NULL, 100.00);
INSERT INTO `b_order_type` VALUES ('8a487cfb-fbef-4d55-abdd-558fe7865261', 2, 1, 0, '2025-06-14 02:06:24', '2025-06-14 02:06:24', 'fcf59119-18b8-49ee-9194-a2aa445db420', '唱歌', '唱歌', NULL, 100.00);
INSERT INTO `b_order_type` VALUES ('bbf6503b-1302-4dc6-ae41-254dda051e69', 0, 1, 0, '2025-06-14 02:01:08', '2025-06-14 02:04:41', NULL, '休闲娱乐', 'abc', NULL, 100.00);
INSERT INTO `b_order_type` VALUES ('c71a8399-070d-480b-b1c6-b8623d879376', 2, 1, 0, '2025-06-14 02:05:38', '2025-06-14 02:05:38', 'bbf6503b-1302-4dc6-ae41-254dda051e69', '桌球', '桌球', NULL, 100.00);
INSERT INTO `b_order_type` VALUES ('fcf59119-18b8-49ee-9194-a2aa445db420', 2, 1, 0, '2025-06-14 02:05:51', '2025-06-14 02:05:51', NULL, '商务活动', '商务活动', NULL, 100.00);

-- ----------------------------
-- Table structure for b_pay_info
-- ----------------------------
DROP TABLE IF EXISTS `b_pay_info`;
CREATE TABLE `b_pay_info`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '支付类型【枚举】金币/订单',
  `business_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单ID ',
  `total_price` decimal(19, 2) NULL DEFAULT NULL COMMENT '订单总金额',
  `pay_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '支付返回参数',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `generate_time` datetime NULL DEFAULT NULL COMMENT '生成支付链接时间',
  `other_sys_order_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '第三方系统订单ID',
  `bank_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '支付信息，如卡号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of b_pay_info
-- ----------------------------

-- ----------------------------
-- Table structure for b_sub_order
-- ----------------------------
DROP TABLE IF EXISTS `b_sub_order`;
CREATE TABLE `b_sub_order`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `main_order_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主订单ID',
  `talent_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '被选中的达人ID',
  `status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '子订单状态【枚举】',
  `departed_time` datetime NULL DEFAULT NULL COMMENT '达人出发时间',
  `arrived_time` datetime NULL DEFAULT NULL COMMENT '达人到达时间',
  `service_start_time` datetime NULL DEFAULT NULL COMMENT '服务开始时间',
  `service_end_time` datetime NULL DEFAULT NULL COMMENT '服务结束时间',
  `evaluation_time` datetime NULL DEFAULT NULL COMMENT '评价时间',
  `location_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '达人位置JSON',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_main_order_id`(`main_order_id` ASC) USING BTREE,
  INDEX `idx_talent_id`(`talent_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单子订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of b_sub_order
-- ----------------------------

-- ----------------------------
-- Table structure for c_member
-- ----------------------------
DROP TABLE IF EXISTS `c_member`;
CREATE TABLE `c_member`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `sys_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统ID',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  `age` int NULL DEFAULT NULL COMMENT '年龄',
  `constellation` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '星座',
  `height` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身高',
  `context` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '简述',
  `video` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '视频',
  `images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '照片集合',
  `identify` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '身份字典IDS',
  `style_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '风格字典IDS',
  `gold` decimal(19, 2) NULL DEFAULT NULL COMMENT '金币余额',
  `popularity` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '人气值',
  `is_vip` tinyint NULL DEFAULT NULL COMMENT '是否VIP',
  `current_role_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '当前角色ID',
  `tel` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `wx_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信CODE',
  `wx_open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信OPEN_ID',
  `wx_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信名称',
  `wx_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信头像',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `apply_status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核状态【枚举 】',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '上次登录时间',
  `gender` tinyint NULL DEFAULT NULL COMMENT '性别',
  `weight` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '体重',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_member
-- ----------------------------
INSERT INTO `c_member` VALUES ('1', 1, 1, 0, '2025-06-05 23:09:29', NULL, 'bell', '001', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 20000400.00, NULL, NULL, '2', '19973409989', NULL, NULL, NULL, NULL, NULL, '2', '2025-07-13 18:00:13', NULL, NULL);
INSERT INTO `c_member` VALUES ('14ce004e-a84e-40c2-8368-527f32df4a25', NULL, 1, 0, '2025-07-22 11:17:14', NULL, 'bears', 'XS：5', '/static/images/default-avatar.png', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1000.00, NULL, NULL, '1', '13547654518', NULL, NULL, NULL, NULL, NULL, '0', '2025-07-22 12:00:44', NULL, NULL);
INSERT INTO `c_member` VALUES ('31e5b429-141b-4ebe-9f88-171632c1e8ad', NULL, 1, 0, '2025-07-24 10:33:52', '2025-07-24 10:33:52', '闲时会员141', 'XS：8', '/static/images/default-avatar.png', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1000.00, NULL, NULL, '2', '18859896784', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL);
INSERT INTO `c_member` VALUES ('3b0640a9-a347-40db-9869-3a11ea5288ef', NULL, 1, 0, '2025-07-22 15:58:39', '2025-07-23 22:13:25', '闲时会员170', 'XS：6', '/static/images/default-avatar.png', 23, '天蝎座', '168', 'O_o', 'https://xiasnhi1.oss-cn-chengdu.aliyuncs.com/member/videos/7b953324722249759207813dcea7a870.mp4', 'https://xiasnhi1.oss-cn-chengdu.aliyuncs.com/member/photos/d3c0ff22f81246e79205378dbcedaf45.png', '会日语,会摄影,会游泳,会瑜伽,会街舞', '潮流时尚,学院风,成熟稳重,阳光帅气,硬朗', 1000.00, NULL, NULL, '3', '19272871072', NULL, NULL, NULL, NULL, NULL, '2', '2025-07-23 22:11:27', NULL, '56');
INSERT INTO `c_member` VALUES ('44aa5526-f177-4b30-9437-8a0493b2c98f', NULL, 1, 0, '2025-06-16 22:33:22', NULL, '阿萨姆', 'XS：2', '/static/images/default-avatar.png', 11, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 100.00, NULL, 1, '2', '19933400000', NULL, NULL, NULL, NULL, NULL, '2', '2025-06-16 22:38:06', NULL, NULL);
INSERT INTO `c_member` VALUES ('8322d305-d27b-4d1a-9ec2-b82918773e42', NULL, 1, 0, '2025-06-15 16:40:55', NULL, '闲时会员895', 'XS：1', '/static/images/default-avatar.png', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.00, NULL, NULL, '2', '19973409999', NULL, NULL, NULL, NULL, NULL, '2', '2025-06-16 22:03:54', NULL, NULL);
INSERT INTO `c_member` VALUES ('84502921-7948-49df-a3fd-72dc4cf31a1a', NULL, 1, 0, '2025-07-07 21:58:02', NULL, '闲时会员214', 'XS：4', '/static/images/default-avatar.png', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.00, NULL, NULL, '1', '10093409989', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL);
INSERT INTO `c_member` VALUES ('8c774678-87c7-4390-85ec-ebeffc17c791', NULL, 1, 0, '2025-07-24 09:30:03', '2025-07-24 09:57:14', '闲时会员136', 'XS：7', '/static/images/default-avatar.png', 18, '金牛座', '175', 'jj', NULL, NULL, '会开车,会日语', NULL, 1000.00, NULL, NULL, '2', '15678789999', NULL, NULL, NULL, NULL, NULL, '2', '2025-07-24 09:52:03', NULL, '68');
INSERT INTO `c_member` VALUES ('ccda3124-41e5-4508-a958-3fbd76017496', NULL, 1, 0, '2025-07-22 19:42:26', '2025-07-23 14:08:48', '闲时会员413', 'XS：6', '/static/images/default-avatar.png', 18, '金牛座', '160', '我是最帅的男人', 'https://xiasnhi1.oss-cn-chengdu.aliyuncs.com/member/videos/fbe05bfa12d649ddabe6a7ad6907808d.mp4', 'https://xiasnhi1.oss-cn-chengdu.aliyuncs.com/member/photos/d8f4790ef7d3476d8318adce58644401.jfif,https://xiasnhi1.oss-cn-chengdu.aliyuncs.com/member/photos/11865744dfa841299aed0825932ea734.webp,https://xiasnhi1.oss-cn-chengdu.aliyuncs.com/member/photos/78ca76e0c6a94893b718308a3f4d798b.jpg,https://xiasnhi1.oss-cn-chengdu.aliyuncs.com/member/photos/ec6a842caaa84331a25cf576553037b6.avif', '会英语,会化妆,能喝酒,会魔术,会唱歌,会吉他,会开车', NULL, 900.00, NULL, 1, '2', '18798767803', NULL, NULL, NULL, NULL, NULL, '0', '2025-07-24 10:17:24', NULL, '55');

-- ----------------------------
-- Table structure for c_member_collect
-- ----------------------------
DROP TABLE IF EXISTS `c_member_collect`;
CREATE TABLE `c_member_collect`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `target_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标ID',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '浏览/收藏【枚举】',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_member_collect
-- ----------------------------

-- ----------------------------
-- Table structure for c_member_fans_attention
-- ----------------------------
DROP TABLE IF EXISTS `c_member_fans_attention`;
CREATE TABLE `c_member_fans_attention`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `target_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标ID',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '粉丝/关注【枚举】',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_member_fans_attention
-- ----------------------------
INSERT INTO `c_member_fans_attention` VALUES ('01ea380e-e471-4d80-a78e-7fa9fa37af70', NULL, 1, 1, '2025-07-23 08:57:38', '2025-07-23 08:57:39', 'ccda3124-41e5-4508-a958-3fbd76017496', '1', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('0784a746-fb27-424c-b935-90f13444389b', NULL, 1, 0, '2025-07-06 17:18:29', NULL, 'd65b63b4-a472-4685-ab85-cfd14959ee80', '1', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('0e473b47-5204-40e5-9b23-3df92432fbb8', NULL, 1, 1, '2025-07-23 10:08:16', '2025-07-23 10:08:17', 'ccda3124-41e5-4508-a958-3fbd76017496', '1', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('16d74ca1-ec63-4dd7-b320-f54f3b0408ae', NULL, 1, 1, '2025-07-23 10:08:14', '2025-07-23 10:08:15', 'ccda3124-41e5-4508-a958-3fbd76017496', '1', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('1e11e88b-6c53-4383-b5ef-f8d1361e4e7b', NULL, 1, 1, '2025-07-24 09:58:30', '2025-07-24 09:58:32', 'ccda3124-41e5-4508-a958-3fbd76017496', '8c774678-87c7-4390-85ec-ebeffc17c791', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('2ad55579-07cc-4749-ab9f-fce0404282a4', NULL, 1, 0, '2025-07-06 17:18:40', NULL, 'd65b63b4-a472-4685-ab85-cfd14959ee80', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('2c0ba0df-04b2-495c-b0f0-c78a684ce5a1', NULL, 1, 1, '2025-07-23 01:53:14', '2025-07-23 01:53:15', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('30bdc365-3c06-4085-b398-fbb1b4ed76bf', NULL, 1, 1, '2025-07-23 01:53:18', '2025-07-23 01:53:19', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('3ab8a24c-5e56-4210-b35f-c7df89d7392d', NULL, 1, 1, '2025-07-23 01:36:36', '2025-07-23 01:36:37', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('4ba18e3c-d275-4448-8c65-9edc230e74f2', NULL, 1, 1, '2025-07-22 20:52:09', '2025-07-22 20:52:13', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('544a0293-88d1-4e91-9eb9-de880139a9a5', NULL, 1, 1, '2025-07-23 03:55:38', '2025-07-23 03:55:39', 'ccda3124-41e5-4508-a958-3fbd76017496', '8322d305-d27b-4d1a-9ec2-b82918773e42', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('554f3aa1-5f4a-42a9-b060-4d3f7b50bff3', NULL, 1, 1, '2025-07-23 08:52:46', '2025-07-23 08:52:46', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('5c568722-6fce-4024-9bc1-3bcee5658748', NULL, 1, 1, '2025-07-23 03:55:35', '2025-07-23 03:55:37', 'ccda3124-41e5-4508-a958-3fbd76017496', '8322d305-d27b-4d1a-9ec2-b82918773e42', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('65fd0508-d03f-4ecb-9bf7-583474639c63', NULL, 1, 1, '2025-07-23 02:09:46', '2025-07-23 02:09:48', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('6af2dbf7-1a1a-4372-aceb-fb6ab16202e5', NULL, 1, 1, '2025-07-06 21:57:46', NULL, '1', 'd65b63b4-a472-4685-ab85-cfd14959ee80', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('6faba641-1d70-4e19-ac8f-6dfa62ff2517', NULL, 1, 0, '2025-07-06 17:33:08', NULL, '1', '8322d305-d27b-4d1a-9ec2-b82918773e42', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('7d8faa13-2791-49ee-af37-426be0e540bc', NULL, 1, 1, '2025-07-13 17:33:41', NULL, '1', 'd65b63b4-a472-4685-ab85-cfd14959ee80', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('8231ad65-166e-46ae-a0c6-c379808ffe23', NULL, 1, 1, '2025-07-23 01:36:32', '2025-07-23 01:36:33', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('83368a2e-7755-4b1f-a80c-165ad44f45e7', NULL, 1, 0, '2025-07-06 21:57:40', NULL, '1', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('8b543a8d-ef33-4872-8c50-d7cd4754c592', NULL, 1, 0, '2025-07-06 17:41:23', NULL, 'd65b63b4-a472-4685-ab85-cfd14959ee80', '8322d305-d27b-4d1a-9ec2-b82918773e42', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('9d64792a-6928-4af7-901b-83bcfa6236af', NULL, 1, 1, '2025-07-23 03:53:28', '2025-07-23 03:53:30', 'ccda3124-41e5-4508-a958-3fbd76017496', '1', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('b50110cd-f7a6-422e-a6f9-b276b6faa638', NULL, 1, 1, '2025-07-23 01:37:41', '2025-07-23 01:37:44', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('b5505eb5-155f-4621-b2ef-ea732303fc30', NULL, 1, 1, '2025-07-06 17:18:41', NULL, 'd65b63b4-a472-4685-ab85-cfd14959ee80', '8322d305-d27b-4d1a-9ec2-b82918773e42', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('be8aeac3-1009-4ff5-9acd-baa56fae80cf', NULL, 1, 1, '2025-07-23 02:09:34', '2025-07-23 02:09:37', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('c7f9a62d-642a-439f-9dec-149383e3f9ae', NULL, 1, 1, '2025-07-23 01:36:34', '2025-07-23 01:36:35', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('cc3b461a-d496-4622-9c75-7f8101aaec35', NULL, 1, 1, '2025-07-23 22:16:32', '2025-07-23 22:16:32', 'ccda3124-41e5-4508-a958-3fbd76017496', '3b0640a9-a347-40db-9869-3a11ea5288ef', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('cd571ea4-1f45-4a7c-8454-6c88295578f2', NULL, 1, 1, '2025-07-23 02:09:32', '2025-07-23 02:09:33', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('d0b10ee6-64d6-4d2b-81b0-2950438520e5', NULL, 1, 1, '2025-07-12 22:22:43', NULL, '1', 'd65b63b4-a472-4685-ab85-cfd14959ee80', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('d648bcc1-e919-475e-9a58-7fa624df4869', NULL, 1, 1, '2025-07-23 08:57:35', '2025-07-23 08:57:36', 'ccda3124-41e5-4508-a958-3fbd76017496', '1', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('e5a5416b-526d-40e4-aaa5-9f273b95f3c1', NULL, 1, 1, '2025-07-23 08:52:47', '2025-07-23 08:52:48', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');
INSERT INTO `c_member_fans_attention` VALUES ('f8da551a-215e-4fbf-8cf8-675cd2999aaf', NULL, 1, 1, '2025-07-23 01:53:16', '2025-07-23 01:53:17', 'ccda3124-41e5-4508-a958-3fbd76017496', '44aa5526-f177-4b30-9437-8a0493b2c98f', 'FOLLOW');

-- ----------------------------
-- Table structure for c_member_gold_record
-- ----------------------------
DROP TABLE IF EXISTS `c_member_gold_record`;
CREATE TABLE `c_member_gold_record`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `operation_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作类型：1-充值，2-消费，3-系统调整-增加，4-系统调整-减少，5-任务奖励',
  `gold_value` int NULL DEFAULT NULL COMMENT '变更金币数量',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `pay_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '支付信息',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收支类型',
  `create_op` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建操作人',
  `update_op` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人ID',
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员金币记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_member_gold_record
-- ----------------------------
INSERT INTO `c_member_gold_record` VALUES ('138ac0bc-bbec-40ca-9df9-3f24ac6fc456', NULL, 1, 0, '2025-06-15 16:24:13', '2025-06-15 16:24:13', '1', NULL, NULL, '提交订单3117ec573905', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('15f426c5-e1a8-4251-841a-c1f8030ca1f1', NULL, 1, 0, '2025-07-06 22:14:43', '2025-07-06 22:14:43', '1', NULL, NULL, '提交订单ccaec87fcb7f', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('1d598c54-4b23-49c4-b694-027f9e527dfe', NULL, 1, 0, '2025-07-06 21:49:56', '2025-07-06 21:49:56', '1', NULL, NULL, '提交订单33b6afe07034', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('3a632a47-841c-4ef4-a78b-7a0d5bc6f18a', NULL, 1, 0, '2025-07-06 16:59:47', '2025-07-06 16:59:47', '1', NULL, NULL, '提交订单4c75738f6dc3', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('40133d6c-6051-4fd0-8c07-02ca2521c068', NULL, 1, 0, '2025-07-11 08:17:12', '2025-07-11 08:17:12', '1', '3', 100, '管理员充值金币，订单号：CZ20250711081711338235', NULL, 'SYSTEM_ADD', NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('41a9e46d-12f6-4484-af50-6fdc95c79ada', NULL, 1, 0, '2025-07-06 21:58:50', '2025-07-06 21:58:50', '1', NULL, NULL, '提交订单e686b219431f', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('4750f9da-9d85-488a-b0f7-25ee50aba2af', NULL, 1, 0, '2025-07-22 19:42:26', '2025-07-22 19:42:26', 'ccda3124-41e5-4508-a958-3fbd76017496', 'WELCOME_GIFT', 1000, '新用户注册赠送', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('4c773a18-757f-4366-9491-abae06eb3289', NULL, 1, 0, '2025-07-22 15:58:39', '2025-07-22 15:58:39', '3b0640a9-a347-40db-9869-3a11ea5288ef', 'WELCOME_GIFT', 1000, '新用户注册赠送', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('50e76892-86de-4c5e-8324-4fbc425b5acb', NULL, 1, 0, '2025-07-11 08:19:06', '2025-07-11 08:19:06', '1', '3', 1000, '管理员充值金币，订单号：CZ20250711081905868866', NULL, 'SYSTEM_ADD', NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('82e70073-4543-4691-90a3-6568da2aeea8', NULL, 1, 0, '2025-06-16 23:13:57', '2025-06-16 23:13:57', '1', NULL, NULL, '提交订单31f5fbc77489', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('877ae336-140f-4e2d-8f5f-f87d1f5f83d5', NULL, 1, 0, '2025-07-24 10:33:52', '2025-07-24 10:33:52', '31e5b429-141b-4ebe-9f88-171632c1e8ad', 'WELCOME_GIFT', 1000, '新用户注册赠送', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('96fc9edf-ee0f-4eca-9773-819348242af0', NULL, 1, 0, '2025-07-24 09:30:03', '2025-07-24 09:30:03', '8c774678-87c7-4390-85ec-ebeffc17c791', 'WELCOME_GIFT', 1000, '新用户注册赠送', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('a3224253-e9d7-4f8c-9214-42a707b94ece', NULL, 1, 0, '2025-07-22 19:43:58', '2025-07-22 19:43:58', 'ccda3124-41e5-4508-a958-3fbd76017496', 'CONSUMPTION', 100, '提交订单f8c782fe9fa3', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('a42e967f-040f-4d54-8496-52f88a34de22', NULL, 1, 0, '2025-07-22 11:17:14', '2025-07-22 11:17:14', '14ce004e-a84e-40c2-8368-527f32df4a25', '5', 1000, '新用户注册赠送', NULL, 'WELCOME_GIFT', NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('b295d6db-6f4e-4bdf-ba9a-1366ef2d73ae', NULL, 1, 0, '2025-06-14 03:32:40', '2025-06-14 03:32:40', '1', '2', -100, '提交订单3b45944d607d', NULL, 'EXPENSE', NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('c9a03983-4ee3-4199-a5ee-554183638bd9', NULL, 1, 0, '2025-07-11 08:24:36', '2025-07-11 08:24:36', '1', '1', 100, '管理员充值金币，订单号：CZ20250711082435603047', NULL, 'RECHARGE', NULL, NULL, NULL);
INSERT INTO `c_member_gold_record` VALUES ('d3b140ea-c218-40ce-ad85-50c73141ddf9', NULL, 1, 0, '2025-06-16 22:30:57', '2025-06-16 22:30:57', '1', NULL, NULL, '提交订单e9f2c4def16e', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for c_member_qd
-- ----------------------------
DROP TABLE IF EXISTS `c_member_qd`;
CREATE TABLE `c_member_qd`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '签到类型【枚举】',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_member_qd
-- ----------------------------

-- ----------------------------
-- Table structure for c_member_recharge
-- ----------------------------
DROP TABLE IF EXISTS `c_member_recharge`;
CREATE TABLE `c_member_recharge`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单号',
  `amount` decimal(19, 2) NULL DEFAULT NULL COMMENT '充值金额(元)',
  `gold_amount` decimal(19, 2) NULL DEFAULT NULL COMMENT '充值金币数量',
  `pay_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '充值方式',
  `status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '充值状态',
  `package_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '充值套餐ID',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '支付流水号',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员充值记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_member_recharge
-- ----------------------------
INSERT INTO `c_member_recharge` VALUES ('5065b4a3-d7ff-41ff-b05a-a7d44c401969', NULL, 1, 0, '2025-07-11 08:17:11', '2025-07-11 08:17:11', '1', 'CZ20250711081711338235', 100.00, 100.00, 'ADMIN', 'SUCCESS', NULL, '2025-07-11 08:17:11', NULL, '');
INSERT INTO `c_member_recharge` VALUES ('6f9ad40a-45c0-4592-96b4-1c314b13aeea', NULL, 1, 0, '2025-06-14 03:30:17', '2025-06-14 03:30:17', '1', 'CZ20250614033016214007', 954.00, 954.00, 'WECHAT', 'PENDING', NULL, NULL, NULL, 'test 充值');
INSERT INTO `c_member_recharge` VALUES ('de9dff0d-2f1b-49b4-87d0-5414900cfe09', NULL, 1, 0, '2025-07-11 08:24:35', '2025-07-11 08:24:35', '1', 'CZ20250711082435603047', 100.00, 100.00, 'ADMIN', 'SUCCESS', NULL, '2025-07-11 08:24:35', NULL, '');
INSERT INTO `c_member_recharge` VALUES ('e73f4208-8c2d-4d0c-8d48-b13ed8cc736a', NULL, 1, 0, '2025-07-11 08:19:06', '2025-07-11 08:19:06', '1', 'CZ20250711081905868866', 1000.00, 1000.00, 'ADMIN', 'SUCCESS', NULL, '2025-07-11 08:19:06', NULL, '');

-- ----------------------------
-- Table structure for c_member_role
-- ----------------------------
DROP TABLE IF EXISTS `c_member_role`;
CREATE TABLE `c_member_role`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `role_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_member_id`(`member_id` ASC) USING BTREE,
  INDEX `idx_role_id`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_member_role
-- ----------------------------
INSERT INTO `c_member_role` VALUES ('0433c95b4a99395d288a206f7cf4b775', NULL, NULL, NULL, NULL, NULL, '44aa5526-f177-4b30-9437-8a0493b2c98f', '1');
INSERT INTO `c_member_role` VALUES ('0e98331c90509d0056c167228153c532', NULL, NULL, 0, '2025-07-24 10:33:51', NULL, '31e5b429-141b-4ebe-9f88-171632c1e8ad', '1');
INSERT INTO `c_member_role` VALUES ('252e4f2cb785a26d0ee425f985260855', NULL, NULL, NULL, NULL, NULL, '3b0640a9-a347-40db-9869-3a11ea5288ef', '1');
INSERT INTO `c_member_role` VALUES ('39d22ce6479a6729d966b766b8f4d2aa', NULL, NULL, NULL, NULL, NULL, '8c774678-87c7-4390-85ec-ebeffc17c791', '2');
INSERT INTO `c_member_role` VALUES ('4f6944e93c02ecb103d86c6e1cb320fa', NULL, NULL, NULL, NULL, NULL, '8c774678-87c7-4390-85ec-ebeffc17c791', '1');
INSERT INTO `c_member_role` VALUES ('790a00bcdcb96384a0cd6dcaf4c1fb82', NULL, NULL, NULL, NULL, NULL, '14ce004e-a84e-40c2-8368-527f32df4a25', '1');
INSERT INTO `c_member_role` VALUES ('81fbcb8d837476cd006caa6b320ca9cf', NULL, NULL, NULL, NULL, NULL, '84502921-7948-49df-a3fd-72dc4cf31a1a', '1');
INSERT INTO `c_member_role` VALUES ('870ea13955a38d333590f3b0e63a94f2', NULL, NULL, NULL, NULL, NULL, '44aa5526-f177-4b30-9437-8a0493b2c98f', '2');
INSERT INTO `c_member_role` VALUES ('998cb39b4c1a71a9c84efe9899247777', NULL, NULL, NULL, NULL, NULL, 'd65b63b4-a472-4685-ab85-cfd14959ee80', '1');
INSERT INTO `c_member_role` VALUES ('c3acfdd6f8e0ad1ead3e66e163d10b63', NULL, NULL, NULL, NULL, NULL, 'ccda3124-41e5-4508-a958-3fbd76017496', '1');

-- ----------------------------
-- Table structure for c_member_view_history
-- ----------------------------
DROP TABLE IF EXISTS `c_member_view_history`;
CREATE TABLE `c_member_view_history`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `target_member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标用户ID',
  `first_view_time` datetime NULL DEFAULT NULL COMMENT '首次浏览时间',
  `last_view_time` datetime NULL DEFAULT NULL COMMENT '最后一次浏览时间',
  `view_count` bigint NULL DEFAULT NULL COMMENT '浏览次数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_member_id`(`member_id` ASC) USING BTREE,
  INDEX `idx_target_id`(`target_member_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员浏览历史表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_member_view_history
-- ----------------------------

-- ----------------------------
-- Table structure for c_recharge_package
-- ----------------------------
DROP TABLE IF EXISTS `c_recharge_package`;
CREATE TABLE `c_recharge_package`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '套餐名称',
  `amount` decimal(10, 2) NOT NULL COMMENT '套餐金额(元)',
  `bonus_gold` decimal(10, 2) NOT NULL COMMENT '赠送金币数量',
  `base_gold` decimal(10, 2) NOT NULL COMMENT '基础金币数量',
  `total_gold` decimal(10, 2) NOT NULL COMMENT '总金币数量',
  `discount` decimal(5, 2) NULL DEFAULT NULL COMMENT '折扣',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '套餐描述',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '套餐图标',
  `is_recommend` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否推荐(0-不推荐,1-推荐)',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '充值套餐表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_recharge_package
-- ----------------------------
INSERT INTO `c_recharge_package` VALUES ('58b75c4a-ef7a-48de-b0d9-5fd9b5890d7d', NULL, 1, 0, '2025-06-15 18:50:39', '2025-06-15 18:50:39', '100元', 100.00, 5.00, 100.00, 105.00, 10.00, '充值100赠送5金币', '', 1, 0);

-- ----------------------------
-- Table structure for c_role
-- ----------------------------
DROP TABLE IF EXISTS `c_role`;
CREATE TABLE `c_role`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色编码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色描述',
  `parent_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父类ID',
  `need_gold` double NULL DEFAULT NULL COMMENT '所需金币',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_role
-- ----------------------------
INSERT INTO `c_role` VALUES ('1', 0, 1, 0, '2025-06-01 00:00:00', '2025-06-01 00:00:00', 'USER', '普通用户', NULL, 0, '普通用户角色');
INSERT INTO `c_role` VALUES ('2', 1, 1, 0, '2025-06-01 00:00:00', '2025-06-01 00:00:00', 'TALENT', '达人', NULL, 100, '达人角色');
INSERT INTO `c_role` VALUES ('3', 2, 1, 0, '2025-07-24 10:39:28', NULL, 'GUILD', '公会', NULL, 100, '会长角色');

-- ----------------------------
-- Table structure for c_talent_apply
-- ----------------------------
DROP TABLE IF EXISTS `c_talent_apply`;
CREATE TABLE `c_talent_apply`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `status` int NULL DEFAULT NULL COMMENT '申请状态：0-待审核，1-已通过，2-已拒绝',
  `introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自我介绍',
  `skills` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '技能特长',
  `photos` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '照片列表',
  `video` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '视频',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `auditor_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人ID',
  `audit_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '达人申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_talent_apply
-- ----------------------------
INSERT INTO `c_talent_apply` VALUES ('24054947-4b73-4678-874a-908c9710c1c4', NULL, 1, 0, '2025-07-23 22:11:45', '2025-07-23 22:13:25', '3b0640a9-a347-40db-9869-3a11ea5288ef', 1, NULL, NULL, NULL, NULL, '2025-07-23 22:13:25', '5b54c4e7e9a747f7bea36d9234078f9d', '资料完善，通过');
INSERT INTO `c_talent_apply` VALUES ('8a6f73d8-403b-46fe-8bb5-ce10842578a1', NULL, 1, 0, '2025-07-24 09:56:26', '2025-07-24 09:57:14', '8c774678-87c7-4390-85ec-ebeffc17c791', 1, 'jj', '会开车,会日语', NULL, '', '2025-07-24 09:57:14', '5b54c4e7e9a747f7bea36d9234078f9d', '资料完善，通过');

-- ----------------------------
-- Table structure for c_talent_city
-- ----------------------------
DROP TABLE IF EXISTS `c_talent_city`;
CREATE TABLE `c_talent_city`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `talent_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '达人ID',
  `city_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '城市编码',
  `city_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '城市名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '达人城市设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_talent_city
-- ----------------------------

-- ----------------------------
-- Table structure for c_talent_project
-- ----------------------------
DROP TABLE IF EXISTS `c_talent_project`;
CREATE TABLE `c_talent_project`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `talent_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '达人ID',
  `project_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目ID',
  `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目名称',
  `price` int NULL DEFAULT NULL COMMENT '价格(元/小时)',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '达人项目设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of c_talent_project
-- ----------------------------

-- ----------------------------
-- Table structure for i_member_chat
-- ----------------------------
DROP TABLE IF EXISTS `i_member_chat`;
CREATE TABLE `i_member_chat`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `member_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '会员ID',
  `target_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标会员ID',
  `content_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '内容JSON',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of i_member_chat
-- ----------------------------

-- ----------------------------
-- Table structure for i_sys_message
-- ----------------------------
DROP TABLE IF EXISTS `i_sys_message`;
CREATE TABLE `i_sys_message`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '推送标题',
  `short_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '推送副标题',
  `context` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '推送内容',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型【枚举】按角色/ID/所有会员',
  `message_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '消息类型【枚举】系统通知/订单通知/其他',
  `business_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '业务ID（如订单ID）',
  `target_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '目标角色',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `read_member_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '已读会员IDS',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of i_sys_message
-- ----------------------------
INSERT INTO `i_sys_message` VALUES ('08842bf5-4c32-4867-9902-1dead63cc8ed', NULL, 1, 0, '2025-07-11 08:19:06', NULL, '订单相关通知', NULL, '管理员已为您充值1000元，获得1000金币', NULL, 'ORDER', NULL, NULL, '2025-07-11 08:19:06', '2026-07-11 08:19:06', '1');
INSERT INTO `i_sys_message` VALUES ('0ae4e290-c588-4a75-a274-70e79fe0c770', NULL, 1, 0, '2025-06-16 22:55:21', NULL, '达人被选中通知', NULL, '恭喜您，您已被选中参与订单e9f2c4def16e', NULL, 'ORDER', NULL, NULL, '2025-06-16 22:55:21', '2026-06-16 22:55:21', NULL);
INSERT INTO `i_sys_message` VALUES ('17f946ce-89c5-402d-ade5-01c6a50ec7c6', NULL, 1, 0, '2025-06-14 03:40:15', NULL, '订单完成通知', NULL, '您的订单3b45944d607d已完成', NULL, 'ORDER', NULL, NULL, '2025-06-14 03:40:15', '2026-06-14 03:40:15', NULL);
INSERT INTO `i_sys_message` VALUES ('1932454575872200705', 0, 1, 0, '2025-06-10 22:55:29', NULL, '重要系统通知：请关注最新平台规则更新', '重要系统通知', '这是一条系统通知，请注意查收相关重要信息。', NULL, 'SYSTEM', NULL, NULL, '2025-06-10 22:55:29', '2026-06-10 22:55:29', '1,d65b63b4-a472-4685-ab85-cfd14959ee80');
INSERT INTO `i_sys_message` VALUES ('1932454605832114178', 0, 1, 0, '2025-06-10 22:55:29', NULL, '重要系统通知：请关注最新平台规则更新', '重要系统通知', '这是一条系统通知，请注意查收相关重要信息2222222。', NULL, 'SYSTEM', NULL, NULL, '2025-06-10 22:55:29', '2026-06-10 22:55:29', 'd65b63b4-a472-4685-ab85-cfd14959ee80,1');
INSERT INTO `i_sys_message` VALUES ('193a4ccf-daac-4003-9478-7e54027d188d', NULL, 1, 0, '2025-06-16 23:20:52', NULL, '达人被选中通知', NULL, '恭喜您，您已被选中参与订单31f5fbc77489', NULL, 'ORDER', NULL, NULL, '2025-06-16 23:20:52', '2026-06-16 23:20:52', NULL);
INSERT INTO `i_sys_message` VALUES ('2011a974-de13-4e14-9208-02e027ad3af9', NULL, 1, 0, '2025-07-24 09:56:26', '2025-07-24 09:56:26', '订单审核通知', NULL, '您的达人认证申请已提交，请等待审核', 'ORDER', NULL, NULL, '8c774678-87c7-4390-85ec-ebeffc17c791', '2025-07-24 09:56:26', '2026-07-24 09:56:26', NULL);
INSERT INTO `i_sys_message` VALUES ('278e9b97-3170-4ac5-bc79-330704e4fbec', NULL, 1, 0, '2025-06-16 22:31:29', NULL, '订单审核通知', NULL, '您的订单e9f2c4def16e已通过审核，现在可以接收达人报名了', NULL, 'ORDER', NULL, NULL, '2025-06-16 22:31:29', '2026-06-16 22:31:29', NULL);
INSERT INTO `i_sys_message` VALUES ('2aa8c0ca-5b34-4629-bd4c-a7a1ba685c76', NULL, 1, 0, '2025-06-14 03:37:46', NULL, '达人被选中通知', NULL, '恭喜您，您已被选中参与订单3b45944d607d', NULL, 'ORDER', NULL, NULL, '2025-06-14 03:37:46', '2026-06-14 03:37:46', NULL);
INSERT INTO `i_sys_message` VALUES ('399d87a6-0c19-411d-9ecd-3dc2bd223ef0', NULL, 1, 0, '2025-07-06 21:54:18', NULL, '订单审核通知', NULL, '您的订单33b6afe07034已通过审核，现在可以接收达人报名了', NULL, 'ORDER', NULL, NULL, '2025-07-06 21:54:18', '2026-07-06 21:54:18', '1');
INSERT INTO `i_sys_message` VALUES ('493114b0-6423-4615-ab9d-2f79bfd76abc', NULL, 1, 0, '2025-06-14 03:39:16', NULL, '订单状态更新通知', NULL, '服务已开始', NULL, 'ORDER', NULL, NULL, '2025-06-14 03:39:16', '2026-06-14 03:39:16', '1');
INSERT INTO `i_sys_message` VALUES ('4a45b4e7-ac4a-4451-b684-04c7b989f22a', NULL, 1, 0, '2025-06-16 23:23:07', NULL, '订单状态更新通知', NULL, '达人已出发前往服务地点', NULL, 'ORDER', NULL, NULL, '2025-06-16 23:23:07', '2026-06-16 23:23:07', '1');
INSERT INTO `i_sys_message` VALUES ('5b0782f9-ebf0-4845-8b1b-1a040ceb5321', NULL, 1, 0, '2025-07-11 08:24:36', NULL, '订单相关通知', NULL, '管理员已为您充值100元，获得100金币', NULL, 'ORDER', NULL, NULL, '2025-07-11 08:24:36', '2026-07-11 08:24:36', '1');
INSERT INTO `i_sys_message` VALUES ('76c1cb95-6d63-4405-b646-ca667ddd223c', NULL, 1, 0, '2025-06-16 23:19:07', NULL, '订单审核通知', NULL, '您的订单31f5fbc77489已通过审核，现在可以接收达人报名了', NULL, 'ORDER', NULL, NULL, '2025-06-16 23:19:07', '2026-06-16 23:19:07', NULL);
INSERT INTO `i_sys_message` VALUES ('84d5289c-8436-42a2-8a55-3316c0d9b90e', NULL, 1, 0, '2025-07-24 09:57:14', '2025-07-24 09:57:14', '订单审核通知', NULL, '恭喜您，您的达人认证已通过审核，现在可以切换到达人角色', 'ORDER', NULL, NULL, '8c774678-87c7-4390-85ec-ebeffc17c791', '2025-07-24 09:57:14', '2026-07-24 09:57:14', NULL);
INSERT INTO `i_sys_message` VALUES ('88b2c5d3-c46c-49a3-978c-6dc9eba57030', NULL, 1, 0, '2025-07-23 20:24:47', '2025-07-23 20:24:47', '订单审核通知', NULL, '您的达人认证申请已提交，请等待审核', 'ORDER', NULL, NULL, '3b0640a9-a347-40db-9869-3a11ea5288ef', '2025-07-23 20:24:47', '2026-07-23 20:24:47', NULL);
INSERT INTO `i_sys_message` VALUES ('8a9c317d-14a5-4897-b733-729e3e1424ef', NULL, 1, 0, '2025-06-14 03:40:15', NULL, '订单状态更新通知', NULL, '达人已完成服务，请确认并评价', NULL, 'ORDER', NULL, NULL, '2025-06-14 03:40:15', '2026-06-14 03:40:15', NULL);
INSERT INTO `i_sys_message` VALUES ('8b00a68c-c86a-46db-af7e-aff2b563dd9e', NULL, 1, 0, '2025-07-23 22:11:45', '2025-07-23 22:11:45', '订单审核通知', NULL, '您的达人认证申请已提交，请等待审核', 'ORDER', NULL, NULL, '3b0640a9-a347-40db-9869-3a11ea5288ef', '2025-07-23 22:11:45', '2026-07-23 22:11:45', NULL);
INSERT INTO `i_sys_message` VALUES ('908b56c2-a990-4c79-887a-3c57d1162b30', NULL, 1, 0, '2025-06-14 03:42:16', NULL, '订单状态更新通知', NULL, '用户已确认服务完成并评价', NULL, 'ORDER', NULL, NULL, '2025-06-14 03:42:16', '2026-06-14 03:42:16', NULL);
INSERT INTO `i_sys_message` VALUES ('9096aa4b-fef5-4bbd-a759-0464a86dd9a5', NULL, 1, 0, '2025-06-14 03:35:09', NULL, '订单报名通知', NULL, '有达人报名了您的订单3b45944d607d', NULL, 'ORDER', NULL, NULL, '2025-06-14 03:35:09', '2026-06-14 03:35:09', NULL);
INSERT INTO `i_sys_message` VALUES ('922b0fc3-ab20-4779-aa37-97cb33847b82', NULL, 1, 0, '2025-07-06 21:59:21', NULL, '订单审核通知', NULL, '您的订单e686b219431f已通过审核，现在可以接收达人报名了', NULL, 'ORDER', NULL, NULL, '2025-07-06 21:59:21', '2026-07-06 21:59:21', '1');
INSERT INTO `i_sys_message` VALUES ('97bb084d-37eb-4582-aef5-91e019237568', NULL, 1, 0, '2025-06-16 22:58:09', NULL, '订单状态更新通知', NULL, '达人已到达服务地点', NULL, 'ORDER', NULL, NULL, '2025-06-16 22:58:09', '2026-06-16 22:58:09', NULL);
INSERT INTO `i_sys_message` VALUES ('9d01abf8-b3de-4a4b-87e6-49f26e75d03e', NULL, 1, 0, '2025-06-15 16:38:47', NULL, '订单审核通知', NULL, '您的订单3117ec573905已通过审核，现在可以接收达人报名了', NULL, 'ORDER', NULL, NULL, '2025-06-15 16:38:47', '2026-06-15 16:38:47', '1');
INSERT INTO `i_sys_message` VALUES ('af667387-bfe6-43a0-93ea-a7e538f74136', NULL, 1, 0, '2025-07-22 19:46:13', '2025-07-22 19:46:13', '订单审核通知', NULL, '您的订单f8c782fe9fa3已通过审核，现在可以接收达人报名了', 'ORDER', NULL, NULL, 'ccda3124-41e5-4508-a958-3fbd76017496', '2025-07-22 19:46:13', '2026-07-22 19:46:13', NULL);
INSERT INTO `i_sys_message` VALUES ('b013cc46-c1fa-4599-bff0-591335d46894', NULL, 1, 0, '2025-07-11 08:17:12', NULL, '订单相关通知', NULL, '管理员已为您充值100元，获得100金币', NULL, 'ORDER', NULL, NULL, '2025-07-11 08:17:12', '2026-07-11 08:17:12', '1');
INSERT INTO `i_sys_message` VALUES ('bc57957b-17d4-43c5-8b22-a74253a11856', NULL, 1, 0, '2025-07-06 17:00:51', NULL, '订单审核通知', NULL, '您的订单4c75738f6dc3已通过审核，现在可以接收达人报名了', NULL, 'ORDER', NULL, NULL, '2025-07-06 17:00:51', '2026-07-06 17:00:51', NULL);
INSERT INTO `i_sys_message` VALUES ('cc6b721e-f4c4-42ea-9ad7-c3772a1d9b4f', NULL, 1, 0, '2025-06-16 22:56:31', NULL, '订单状态更新通知', NULL, '达人已出发前往服务地点', NULL, 'ORDER', NULL, NULL, '2025-06-16 22:56:31', '2026-06-16 22:56:31', '1');
INSERT INTO `i_sys_message` VALUES ('d0b14953-8003-4663-b8da-b6bf59f98625', NULL, 1, 0, '2025-06-16 22:39:12', NULL, '订单报名通知', NULL, '有达人报名了您的订单e9f2c4def16e', NULL, 'ORDER', NULL, NULL, '2025-06-16 22:39:12', '2026-06-16 22:39:12', NULL);
INSERT INTO `i_sys_message` VALUES ('d246b064-2e6a-423b-bbce-1a9e2fca7d57', NULL, 1, 0, '2025-06-14 03:38:25', NULL, '订单状态更新通知', NULL, '达人已出发前往服务地点', NULL, 'ORDER', NULL, NULL, '2025-06-14 03:38:25', '2026-06-14 03:38:25', NULL);
INSERT INTO `i_sys_message` VALUES ('d7464c06-4f84-48fe-a375-be7671669f56', NULL, 1, 0, '2025-06-16 23:20:31', NULL, '订单报名通知', NULL, '有达人报名了您的订单31f5fbc77489', NULL, 'ORDER', NULL, NULL, '2025-06-16 23:20:31', '2026-06-16 23:20:31', '1');
INSERT INTO `i_sys_message` VALUES ('dda1ef6c-d8a6-4741-853b-37803afa7bb6', NULL, 1, 0, '2025-07-06 22:15:01', NULL, '订单审核通知', NULL, '您的订单ccaec87fcb7f已通过审核，现在可以接收达人报名了', NULL, 'ORDER', NULL, NULL, '2025-07-06 22:15:01', '2026-07-06 22:15:01', '1,anonymousUser');
INSERT INTO `i_sys_message` VALUES ('e6f4920f-a6ad-4dc4-8eb5-3affd2a6b577', NULL, 1, 0, '2025-06-16 22:35:55', NULL, '订单审核通知', NULL, '您的达人认证申请已提交，请等待审核', NULL, 'ORDER', NULL, NULL, '2025-06-16 22:35:55', '2026-06-16 22:35:55', NULL);
INSERT INTO `i_sys_message` VALUES ('f6d93c17-5cb9-48ad-b295-135f89644bb4', NULL, 1, 0, '2025-06-14 03:38:44', NULL, '订单状态更新通知', NULL, '达人已到达服务地点', NULL, 'ORDER', NULL, NULL, '2025-06-14 03:38:44', '2026-06-14 03:38:44', NULL);
INSERT INTO `i_sys_message` VALUES ('fb27b7df-bfe3-4a96-bd21-fba195271955', NULL, 1, 0, '2025-07-23 22:13:26', '2025-07-23 22:13:26', '订单审核通知', NULL, '恭喜您，您的达人认证已通过审核，现在可以切换到达人角色', 'ORDER', NULL, NULL, '3b0640a9-a347-40db-9869-3a11ea5288ef', '2025-07-23 22:13:26', '2026-07-23 22:13:26', NULL);

-- ----------------------------
-- Table structure for sys_admin
-- ----------------------------
DROP TABLE IF EXISTS `sys_admin`;
CREATE TABLE `sys_admin`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  `roles` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色（逗号分隔的角色编码）',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除',
  `comparable` int NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint(1) NULL DEFAULT 1 COMMENT '启用状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_admin
-- ----------------------------
INSERT INTO `sys_admin` VALUES ('5b54c4e7e9a747f7bea36d9234078f9d', '19973409989', '$2a$10$72M37yOmfrdBfv9DVz5r5ObbI64iwf73Lv6ZEbr1rCnFCWsyDv69G', '管理员', '19973409989', NULL, '/static/images/admin-avatar.png', 'ADMIN', '2025-07-23 20:25:46', NULL, '2025-06-15 13:01:38', '2025-06-15 13:01:38', 0, NULL, 1);

-- ----------------------------
-- Table structure for t_basic_config
-- ----------------------------
DROP TABLE IF EXISTS `t_basic_config`;
CREATE TABLE `t_basic_config`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置值',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置名称',
  `config_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置描述',
  `config_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'default' COMMENT '配置组',
  `enabled` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_basic_config
-- ----------------------------
INSERT INTO `t_basic_config` VALUES ('8e44f33b66a492dbd4094536704b6dda', 0, 1, NULL, '2025-06-16 21:54:54', '2025-06-16 21:54:54', 'signInGold', '1', 'signInGold', NULL, 'default', 1, 0);
INSERT INTO `t_basic_config` VALUES ('db43d05457458834e2bb6197348ffd70', 0, 1, NULL, '2025-06-16 21:54:54', '2025-06-16 21:54:54', 'registerGold', '0', 'registerGold', NULL, 'default', 1, 0);

-- ----------------------------
-- Table structure for t_basic_info
-- ----------------------------
DROP TABLE IF EXISTS `t_basic_info`;
CREATE TABLE `t_basic_info`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `system_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统名称',
  `system_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统Logo',
  `system_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统描述',
  `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '版本号',
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司名称',
  `company_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司地址',
  `contact_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `icp` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备案号',
  `copyright` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '版权信息',
  `privacy_policy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '隐私政策',
  `user_agreement` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '用户协议',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_basic_info
-- ----------------------------

-- ----------------------------
-- Table structure for t_data_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `t_data_dict_type`;
CREATE TABLE `t_data_dict_type`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_data_dict_type
-- ----------------------------

-- ----------------------------
-- Table structure for t_data_dict_value
-- ----------------------------
DROP TABLE IF EXISTS `t_data_dict_value`;
CREATE TABLE `t_data_dict_value`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `comparable` bigint NULL DEFAULT NULL COMMENT '排序',
  `available` tinyint NULL DEFAULT NULL COMMENT '启用状态',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `dict_type_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '字典类型ID',
  `dict_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '字典类型',
  `dict_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '字典标签',
  `dict_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '字典键值',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `enabled` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '数据字典值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_data_dict_value
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
