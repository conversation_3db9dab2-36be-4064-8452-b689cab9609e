package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员充值记录实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_member_recharge")
@ApiModel("会员充值记录")
public class CMemberRecharge extends BaseObjPo {

    @ApiModelProperty(value = "会员ID")
    private String memberId;
    
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    
    @ApiModelProperty(value = "充值金额(元)")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "充值金币数量")
    private BigDecimal goldAmount;
    
    @ApiModelProperty(value = "充值方式")
    private String payType; // ALIPAY, WECHAT, BANK_CARD
    
    @ApiModelProperty(value = "充值状态")
    private String status; // PENDING, SUCCESS, FAILED
    
    @ApiModelProperty(value = "充值套餐ID")
    private String packageId;
    
    @ApiModelProperty(value = "支付时间")
    private LocalDateTime payTime;
    
    @ApiModelProperty(value = "支付流水号")
    private String transactionId;
    
    @ApiModelProperty(value = "备注")
    private String remark;
} 