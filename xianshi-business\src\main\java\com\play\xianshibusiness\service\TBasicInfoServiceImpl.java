package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.TBasicInfoMapper;
import com.play.xianshibusiness.pojo.TBasicInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 系统基础信息服务实现类
 */
@Service
public class TBasicInfoServiceImpl implements TBasicInfoService {

    @Resource
    private TBasicInfoMapper basicInfoMapper;
    
    @Override
    public TBasicInfo getBasicInfo() {
        // 查询系统基础信息，默认只有一条记录
        TBasicInfo basicInfo = basicInfoMapper.selectOne(
                new LambdaQueryWrapper<TBasicInfo>()
                        .eq(TBasicInfo::getAvailable, true)
                        .eq(TBasicInfo::getDeleted, false)
                        .last("LIMIT 1")
        );
        
        // 如果不存在则创建默认记录
        if (basicInfo == null) {
            basicInfo = createDefaultBasicInfo();
        }
        
        return basicInfo;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBasicInfo(TBasicInfo basicInfo) {
        if (basicInfo == null || basicInfo.getId() == null) {
            throw new GlobalException(400, "参数错误");
        }
        
        // 检查记录是否存在
        TBasicInfo existInfo = basicInfoMapper.selectById(basicInfo.getId());
        if (existInfo == null || existInfo.getDeleted() || !existInfo.getAvailable()) {
            throw new GlobalException(400, "记录不存在");
        }
        
        // 设置更新时间
        basicInfo.setUpdateTime(LocalDateTime.now());
        
        // 更新记录
        return basicInfoMapper.updateById(basicInfo) > 0;
    }
    
    /**
     * 创建默认系统基础信息
     */
    private TBasicInfo createDefaultBasicInfo() {
        TBasicInfo basicInfo = new TBasicInfo();
        basicInfo.setSystemName("闲时陪玩管理系统");
        basicInfo.setSystemLogo("https://example.com/logo.png");
        basicInfo.setSystemDesc("闲时陪玩管理系统是一个专业的线下陪玩服务管理平台");
        basicInfo.setVersion("1.0.0");
        basicInfo.setCompanyName("闲时科技有限公司");
        basicInfo.setCompanyAddress("北京市朝阳区");
        basicInfo.setContactPhone("************");
        basicInfo.setContactEmail("<EMAIL>");
        basicInfo.setIcp("京ICP备12345678号");
        basicInfo.setCopyright("© 2023 闲时科技有限公司 版权所有");
        basicInfo.setPrivacyPolicy("隐私政策内容");
        basicInfo.setUserAgreement("用户协议内容");
        basicInfo.setCreateTime(LocalDateTime.now());
        basicInfo.setUpdateTime(LocalDateTime.now());
        basicInfo.setDeleted(false);
        basicInfo.setAvailable(true);
        
        // 保存到数据库
        basicInfoMapper.insert(basicInfo);
        
        return basicInfo;
    }
} 