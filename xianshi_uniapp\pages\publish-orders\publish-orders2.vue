<template>
  <!-- 订单管理页面 -->
  <view class="container">
    <!-- 顶部状态切换栏 - 普通用户 -->
    <view class="status-bar" v-if="userRole !== '闲时达人'">
      <view
        class="status-item"
        :class="{ active: currentStatus === 'all' }"
        @tap="switchStatus('all')"
      >
        <text>全部</text>
      </view>
      <view
        class="status-item"
        :class="{ active: currentStatus === 'published' }"
        @tap="switchStatus('published')"
      >
        <text>已发布</text>
      </view>
      <view
        class="status-item"
        :class="{ active: currentStatus === 'expertSelected' }"
        @tap="switchStatus('expertSelected')"
      >
        <text>已选达人</text>
      </view>
      <view
        class="status-item"
        :class="{ active: currentStatus === 'orderStarted' }"
        @tap="switchStatus('orderStarted')"
      >
        <text>进行中</text>
      </view>
      <view
        class="status-item"
        :class="{ active: currentStatus === 'completed' }"
        @tap="switchStatus('completed')"
      >
        <text>已完成</text>
      </view>
    </view>

    <!-- 顶部状态切换栏 - 达人用户 -->
    <view class="status-bar" v-if="userRole === '闲时达人'">
      <view
        class="status-item"
        :class="{ active: currentStatus === 'all' }"
        @tap="switchStatus('all')"
      >
        <text>全部</text>
      </view>
      <view
        class="status-item"
        :class="{ active: currentStatus === 'applied' }"
        @tap="switchStatus('applied')"
      >
        <text>已报名</text>
      </view>
      <view
        class="status-item"
        :class="{ active: currentStatus === 'selected' }"
        @tap="switchStatus('selected')"
      >
        <text>已接单</text>
      </view>
      <view
        class="status-item"
        :class="{ active: currentStatus === 'ongoing' }"
        @tap="switchStatus('ongoing')"
      >
        <text>进行中</text>
      </view>
      <view
        class="status-item"
        :class="{ active: currentStatus === 'completed' }"
        @tap="switchStatus('completed')"
      >
        <text>已完成</text>
      </view>
    </view>

    <!-- 订单列表区域 -->
    <view class="order-list">
      <!-- 加载中提示 -->
      <view class="loading-container" v-if="isLoading">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>

      <block v-else-if="orders.length > 0">
        <view class="order-item" v-for="item in orders" :key="item.id">
          <!-- 订单卡片内容 -->
          <view class="order-header">
            <!-- <text style="color: red;">订单状态：{{ item.statusTxt }}</text> -->
            <text class="order-number">订单号：{{ item.orderId }}</text>
            <!-- 普通用户显示订单状态 -->
            <text
              class="order-status"
              :class="item.status"
              v-if="userRole !== '闲时达人'"
              >{{ item.statusText }}</text
            >
            <!-- 达人用户显示报名状态 -->
            <text
              class="order-status"
              :class="{ selected: item.selected, applied: !item.selected }"
              v-if="userRole === '闲时达人'"
            >
              <!-- {{ item.selected ? "已接单" : "已报名" }} -->
              {{ item.statusTxt }}
            </text>
          </view>
          <view class="order-content">
            <view class="order-info">
              <text class="order-title">{{
                item.title
              }}</text>
              <text class="order-time">{{ item.createTime }}</text>
            </view>
            <view class="order-price"
              >¥{{ item.price || item.hourlyRate || 0.00 }}元/小时</view
            >
          </view>

          <view class="order-detail">
            <view class="detail-row">
              <text class="detail-label">活动时间：</text>
              <text class="detail-value">{{ item.date }} {{ item.time }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">活动时长：</text>
              <text class="detail-value">{{ item.duration || 0 }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">参与人数：</text>
              <text class="detail-value"
                >{{ item.participants || item.people || 1 }}人</text
              >
            </view>
            <view class="detail-row">
              <text class="detail-label">性别要求：</text>
              <text class="detail-value">{{
                item.genderText ||
                (item.gender === "female"
                  ? "女"
                  : item.gender === "male"
                  ? "男"
                  : "不限")
              }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">活动地点：</text>
              <text class="detail-value">{{
                item.location || item.address || "未指定地点"
              }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">总费用：</text>
              <text class="detail-value">{{
                (item.totalFee || "0.00") + '元'
              }}</text>
            </view>
          </view>

          <!-- 被选中的达人信息 - 当订单已选择达人时显示 -->
          <view
            class="selected-expert-info"
            v-if="userRole !== '闲时达人' && (item.status === 'expertSelected' || item.status === 'orderStarted' || item.status === 'completed' || item.status === 'evaluated') && item.selectedExpertId"
          >
            <view class="selected-expert-title">已选择达人</view>
            <view class="selected-expert-content">
              <image
                class="selected-expert-avatar"
                :src="item?.selectedExpertAvatar || '/images/default-avatar.png'"
              ></image>
              <view class="selected-expert-details">
                <text class="selected-expert-name">{{
                  item.selectedExpertName || "未知达人"
                }}</text>
                <text class="selected-expert-id"
                  >ID: {{ item.selectedExpertId }}</text
                >
              </view>
              <view
                class="expert-selection-time"
                v-if="item.selectionTime"
              >
                <text
                  >选择时间:
                  {{ item.selectionTimeFormatted || item.selectionTime }}</text
                >
              </view>
            </view>
          </view>

          <!-- 用户状态进度条 - 只对普通用户显示 -->
          <view
            class="order-progress"
            v-if="userRole !== '闲时达人'"
            @tap="showOrderFlowModal"
            :data-id="item.id || item.orderId"
          >
            <view class="progress-title"
              >用户流程 <text class="view-flow-btn">查看详情 ></text></view
            >
            <view class="progress-bar">
              <view
                :class="['progress-step', {'completed': item.userProgress >= 1}]"
              >
                <view class="step-dot"></view>
                <text>发布订单</text>
              </view>
              <view
                :class="['progress-line', {'completed': item.userProgress >= 2}]"
              ></view>
              <view
                :class="['progress-step', {'completed': item.userProgress >= 2}]"
              >
                <view class="step-dot"></view>
                <text>选择达人</text>
              </view>
              <view
                :class="['progress-line', {'completed': item.userProgress >= 3}]"
              ></view>
              <view
                :class="['progress-step', {'completed': item.userProgress >= 3}]"
              >
                <view class="step-dot"></view>
                <text>开始订单</text>
              </view>
              <view
                :class="['progress-line', {'completed': item.userProgress >= 4}]"
              ></view>
              <view
                :class="['progress-step', {'completed': item.userProgress >= 4}]"
              >
                <view class="step-dot"></view>
                <text>确认完成</text>
              </view>
              <view
                :class="['progress-line', {'completed': item.userProgress >= 5}]"
              ></view>
              <view
                :class="['progress-step', {'completed': item.userProgress >= 5}]"
              >
                <view class="step-dot"></view>
                <text>评价</text>
              </view>
            </view>
          </view>

          <!-- 达人状态进度条 - 给达人用户也显示，让他们知道当前在哪个阶段 -->
          <view
            class="expert-progress"
            v-if="(userRole !== '闲时达人' && item.expertStatus) || (userRole === '闲时达人' && item.selected)"
            @tap="showOrderFlowModal"
            :data-id="item.id || item.orderId"
          >
            <view class="progress-title"
              >达人流程 <text class="view-flow-btn">查看详情 ></text></view
            >
            <view class="progress-bar">
              <view
                :class="['progress-step', {'completed': item.expertProgress >= 1}]"
              >
                <view class="step-dot"></view>
                <text>达人报名</text>
              </view>
              <view
                :class="['progress-line', {'completed': item.expertProgress >= 2}]"
              ></view>
              <view
                :class="['progress-step', {'completed': item.expertProgress >= 2}]"
              >
                <view class="step-dot"></view>
                <text>被选中</text>
              </view>
              <view
                :class="['progress-line', {'completed': item.expertProgress >= 3}]"
              ></view>
              <view
                :class="['progress-step', {'completed': item.expertProgress >= 3}]"
              >
                <view class="step-dot"></view>
                <text>出发确认</text>
              </view>
              <view
                :class="['progress-line', {'completed': item.expertProgress >= 4}]"
              ></view>
              <view
                :class="['progress-step', {'completed': item.expertProgress >= 4}]"
              >
                <view class="step-dot"></view>
                <text>到达确认</text>
              </view>
              <view
                :class="['progress-line', {'completed': item.expertProgress >= 5}]"
              ></view>
              <view
                :class="['progress-step', {'completed': item.expertProgress >= 5}]"
              >
                <view class="step-dot"></view>
                <text>服务完成</text>
              </view>
              <view
                :class="['progress-line', {'completed': item.expertProgress >= 6}]"
              ></view>
              <view
                :class="['progress-step', {'completed': item.expertProgress >= 6}]"
              >
                <view class="step-dot"></view>
                <text>查看评价</text>
              </view>
            </view>
          </view>

          <!-- 达人用户的状态显示 -->
          <view
            class="expert-status-bar"
            v-if="userRole === '闲时达人' && item.selected"
          >
            <view class="expert-status-label">订单状态：</view>
            <view class="expert-status-value">
              <text v-if="!item.status || item.status === 'published'"
                >待用户确认开始</text
              >
              <text v-if="item.status === 'orderStarted'">订单进行中</text>
              <text v-if="item.status === 'completed'">已完成</text>
            </view>
          </view>

          <!-- 订单发布者信息 - 当达人被选中时显示 -->
          <view
            class="order-creator-info"
            v-if="userRole === '闲时达人' && item.selected && (item.creatorId || item.creatorName)"
          >
            <view class="order-creator-title">订单发布者</view>
            <view class="order-creator-content">
              <image
                class="order-creator-avatar"
                :src="item?.creatorAvatar || '/images/default-avatar.png'"
              ></image>
              <view class="order-creator-details">
                <text class="order-creator-name">{{
                  item.creatorName || "未知用户"
                }}</text>
                <text class="order-creator-id"
                  >ID: {{ item.creatorId || "未知" }}</text
                >
              </view>
              <view class="order-selection-time" v-if="item.selectionTime">
                <text
                  >选择时间:
                  {{ item.selectionTimeFormatted || item.selectionTime }}</text
                >
              </view>
            </view>
          </view>

          <!-- 达人状态提示 -->
          <view
            :class="['expert-status-tip', 
            (item.status === 'completed' || item.status === 'evaluated') ? 'completed' : '', 
            (item.status === 'completed' && !item.evaluated) ? 'complete-animation' : '']"
            v-if="userRole === '闲时达人' && (item.expertStatusTip || item.isApplied)"
          >
            <text class="tip-icon">{{
              item.status === "completed" || item.status === "evaluated"
                ? "🎉"
                : "📌"
            }}</text>
            <text class="tip-text">{{
              item.expertStatusTip ||
              (item.isApplied ? "您已报名该订单，等待用户选择" : "")
            }}</text>
          </view>

          <!-- 用户状态提示 -->
          <view
            :class="['expert-status-tip', 'user-status-tip', 
            (item.status === 'completed' || item.status === 'evaluated') ? 'completed' : '']"
            v-if="userRole !== '闲时达人' && item.userStatusTip"
          >
            <text class="tip-icon">{{
              item.status === "completed" || item.status === "evaluated"
                ? "🎉"
                : "📝"
            }}</text>
            <text class="tip-text">{{ item.userStatusTip }}</text>
          </view>

          <!-- 用户操作按钮组 - 普通用户 -->
          <view class="action-area" v-if="userRole !== '闲时达人'">
            <!-- 查看达人报名按钮 -->
            <button
              class="action-btn view-experts"
              v-if="item.status === 'published' && item.enrollList.length > 0"
              @tap="viewEnrollList"
              :data-id="item.id || item.orderId"
            >
                          <text
              v-if="!item.viewedApplications"
              class="notification-dot"
            ></text>
              查看报名达人({{ item.enrollList.length }})
            </button>

            <!-- 开始订单按钮 -->
            <button
              class="action-btn start-order"
              v-if="item.status === 'expertSelected' && item.expertStatus === 'arrivalConfirmed'"
              @tap="startOrder"
              :data-id="item.id || item.orderId"
            >
              开始订单
            </button>

            <!-- 等待达人到达提示 -->
            <view
              class="waiting-tip"
              v-if="item.status === 'expertSelected' && item.expertStatus !== 'arrivalConfirmed'"
            >
              <text
                v-if="!item.expertStatus || item.expertStatus === 'chosen'"
                >等待达人出发...</text
              >
              <text v-if="item.expertStatus === 'departureConfirmed'"
                >达人已出发，等待到达...</text
              >
            </view>

            <!-- 确认完成按钮 -->
            <button
              class="action-btn confirm-complete"
              v-if="item.status === 'orderStarted' && item.expertStatus === 'serviceCompleted'"
              @tap="confirmComplete"
              :data-id="item.id || item.orderId"
            >
              确认完成
            </button>

            <!-- 评价按钮 -->
            <button
              class="action-btn evaluate"
              v-if="item.status === 'completed'"
              @tap="navigateToEvaluate"
              :data-id="item.id || item.orderId"
            >
              去评价
            </button>
          </view>

          <!-- 达人操作按钮组 -->
          <view class="action-area" v-if="userRole === '闲时达人'">
            <!-- 出发确认按钮 - 被选中后的第一步操作 -->
            <button
              class="action-btn departure"
              v-if="item.selected && item.status === 'expertSelected' && (!item.expertStatus || item.expertStatus === 'chosen')"
              @tap="confirmDeparture"
              :data-id="item.id || item.orderId"
            >
              确认出发
            </button>

            <!-- 到达确认按钮 - 出发确认后的操作 -->
            <button
              class="action-btn arrival"
              v-if="item.selected && item.expertStatus === 'departureConfirmed'"
              @tap="confirmArrival"
              :data-id="item.id || item.orderId"
            >
              确认到达
            </button>

            <!-- 服务完成按钮 - 到达确认后的操作，需要订单已开始 -->
            <button
              class="action-btn complete-service"
              v-if="item.selected && item.expertStatus === 'arrivalConfirmed' && item.status === 'orderStarted'"
              @tap="completeService"
              :data-id="item.id || item.orderId"
            >
              服务完成
            </button>

            <!-- 查看评价按钮 - 订单已评价后显示 -->
            <button
              class="action-btn view-evaluation"
              v-if="item.selected && item.status === 'evaluated'"
              @tap="viewEvaluation"
              :data-id="item.id || item.orderId"
            >
              查看评价
            </button>

            <!-- 等待订单开始提示 -->
            <view
              class="waiting-tip"
              v-if="item.selected && item.expertStatus === 'arrivalConfirmed' && item.status !== 'orderStarted'"
            >
              已到达，等待用户开始订单...
            </view>

            <!-- 当前状态显示 -->
            <view class="expert-status" v-if="item.expertStatusText">
              <text>当前状态: {{ item.expertStatusText }}</text>
            </view>
          </view>

          <!-- 达人按钮区域 - 增加明显的提示文字 -->
          <view
            class="expert-only"
            v-if="userRole === '闲时达人' && (item.selected || (item.expert && item.expert.id === userId))"
          >
            <view
              class="expert-status-header"
              v-if="item.expertStatusText"
            >
              <text class="expert-status-label">当前状态：</text>
              <text class="expert-status-value">{{
                item.expertStatusText
              }}</text>
            </view>
          </view>
        </view>
      </block>
      <view class="empty-state" v-else>
        <text>暂无相关订单</text>
      </view>
    </view>
  </view>

  <!-- 达人选择弹窗 -->
  <view class="expert-selection-modal" v-if="showExpertSelection">
    <view class="modal-mask" @tap="hideExpertSelection"></view>
    <view class="modal-content">
      <view class="modal-title">选择达人</view>
      <view class="expert-list">
        <view
          class="expert-item"
          v-for="item in currentEnrollList"
          :key="item.id"
        >
          <view class="expert-info">
            <image
              class="expert-avatar"
              src="../../static/images/default-avatar.png"
            ></image>
            <text class="expert-name">{{ item.name }}</text>
          </view>
          <button
            class="select-btn"
            @tap="selectExpert"
            :data-expert-id="item.id"
          >
            选择
          </button>
        </view>
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" @tap="hideExpertSelection">取消</button>
      </view>
    </view>
  </view>

  <!-- 订单流程弹窗 -->
  <view class="order-flow-modal" v-if="showOrderFlowModal_flag">
    <view class="flow-modal-mask" @tap="hideOrderFlowModal"></view>
    <view class="flow-modal-content">
      <view class="flow-modal-title">
        订单流程详情
        <text class="flow-modal-close" @tap="hideOrderFlowModal">×</text>
      </view>
      <block
        v-if="currentOrderFlow && currentOrderFlow.stateLogs && currentOrderFlow.stateLogs.length > 0"
      >
        <view class="flow-entries">
          <view
            v-for="(log, index) in currentOrderFlow.stateLogs"
            :key="index"
            :class="['flow-entry', log.state === currentOrderFlow.status || log.state === currentOrderFlow.expertStatus ? 'current' : 'completed']"
          >
            <text class="flow-time">{{ log.formattedTime || log.time }}</text>
            <view class="flow-info">
              <view class="flow-state">{{ log.stateText || log.state }}</view>
              <view class="flow-remark" v-if="log.remark">{{ log.remark }}</view>
              <view :class="['flow-role', log.role]">
                {{
                  log.role === "user"
                    ? "用户操作"
                    : log.role === "闲时达人"
                    ? "达人操作"
                    : "系统更新"
                }}
              </view>
            </view>
          </view>
        </view>
      </block>
      <view class="empty-flow" v-else>
        <text>暂无流程记录</text>
      </view>
    </view>
  </view>
</template>

<script>

// 约单管理


// 引入订单状态和流程模块
import {
  OrderState,
  StateDisplayText,
  ExpertStatusTips,
  UserStatusTips,
} from "../../utils/order-state";
import {
  changeOrderState,
  canPerformAction,
  setupRealTimeSync,
} from "../../utils/order-flow";

// 引入时间格式化工具
import { formatDateTime } from "../../utils/time-formatter";

// 引入API接口
import { selectTalents } from "../../api/index.js";
const app = getApp().globalData;
export default {
  data() {
    return {
      currentFilter: "all", // 当前选中的状态
      orders: [], // 订单列表
      userRole: "", // 用户角色：normal(普通用户)或expert(达人)
      statusMap: {
        all: "全部",
        [OrderState.PUBLISHED]: "已发布",
        [OrderState.EXPERT_SELECTED]: "已选达人",
        [OrderState.ORDER_STARTED]: "进行中",
        [OrderState.COMPLETED]: "已完成",
        [OrderState.EVALUATED]: "已评价",
      },
      expertStatusMap: {
        all: "全部",
        applied: "已报名",
        selected: "已接单",
        ongoing: "进行中",
        completed: "已完成",
      },
      // 添加用户状态提示
      statusTips: {
        published: "订单已发布",
        created: "订单已创建",
        expertSelected: "有达人报名，请查看并选择",
        expertChosenWaiting: "等待达人出发",
        departureConfirmed: "达人已出发，前往目的地",
        arrivalConfirmed: "达人已到达，等待开始订单",
        serviceCompleted: "服务已完成，等待确认",
        completed: "订单已完成",
        evaluated: "订单已评价",
        cancelled: "订单已取消",
      },
      isLoading: true,
      showExpertSelection: false, // 是否显示达人选择弹窗
      currentEnrollList: [], // 当前订单的申请达人列表
      currentOrderId: null, // 当前操作的订单ID
      hasNewApplications: false, // 是否有新的达人报名
      showOrderFlowModal_flag: false, // 是否显示订单流程弹窗
      currentOrderFlow: null, // 当前选中的订单流程
      lastUpdateTime: "", // 添加lastUpdateTime属性
      isVerified: false,
      hasExpertRole: false,
      currentStatus: "all"
    };
  },
  onLoad(options) {
    console.log("publish-orders页面加载，参数:", options);

    // 获取用户信息
    
    const userRole = app.userRole;
    const isVerified = app.isVerified;
    const currentUserId = app.currentUserId;

    console.log("当前用户信息:", {
      userRole,
      isVerified,
      currentUserId,
    });

    // 执行数据迁移，确保旧数据被正确迁移到新的存储结构
    this.migrateExpertOrderData();

    // 直接设置属性，而不是使用setData
    this.userRole = userRole;
    this.isVerified = isVerified;
    this.hasExpertRole = app.hasExpertRole;

    // 加载公共订单数据 达人订单列表
    this.loadOrders(() => {
      // 设置初始筛选状态
      this.initFilter();

      // 检查是否有新的申请
      this.checkNewApplications();

      // 关闭加载状态
      this.isLoading = false;
    });
  },

  onShow() {
    // 每次页面显示时刷新数据
    this.loadOrders();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadOrders(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    // 切换状态
    switchStatus(status) {
      this.currentFilter = status;
      this.isLoading = true;
      
      uni.showLoading({
        title: "加载中",
      });
      this.loadOrders();
    },

    // 从全局数据或缓存加载订单列表
    async loadOrders(callback) {
      // 显示加载提示
      uni.showLoading({
        title: "加载订单中",
      });

      this.isLoading = true;

      // 获取当前用户信息
      const userRole = app.userRole;
      const userId = app.currentUserId;

      console.log("加载订单数据, 用户角色:", userRole, "用户ID:", userId);

      // 设置默认的订单状态筛选
      if (!this.currentFilter) {
        this.currentFilter = "all";
      }

      // 获取订单数据
      let orderData = [];
      try {
        console.log("appppp", app, app.commonApi)
        let res = await this.$requestHttp.get(app.commonApi.getUserOrderList, {
          data: {
            pageNum:	1,
            pageSize: 10,
            status:	''
          }
        })
        console.log("获取订单数据成功", res);
        if(res.code == 200) {
          this.isLoading = false;
          orderData = res.data.records;
        } else {
          this.isLoading = false;
          uni.hideLoading();
          uni.showToast({
            title: res.message,
            icon: "error",
          });
        }
      } catch (e) {
        console.error("从存储读取订单数据失败:", e);
        orderData = [];
      }

      console.log("Zzzzzz", orderData);

      // 如果用户是专家，从已报名订单列表中加载数据
      // if (userRole === "闲时达人") {
      //   try {
      //     // 使用专用存储键加载当前达人的订单
      //     const expertAppliedOrdersKey = app.getExpertAppliedOrdersKey();
      //     console.log(
      //       `加载达人申请订单，当前用户ID: ${userId}, 使用存储键: ${expertAppliedOrdersKey}`
      //     );

      //     const appliedOrdersStr = uni.getStorageSync(expertAppliedOrdersKey);
      //     if (appliedOrdersStr) {
      //       let appliedOrders;
      //       try {
      //         if (typeof appliedOrdersStr === "string") {
      //           appliedOrders = JSON.parse(appliedOrdersStr);
      //         } else if (Array.isArray(appliedOrdersStr)) {
      //           appliedOrders = appliedOrdersStr;
      //         } else {
      //           console.error(
      //             `${expertAppliedOrdersKey}数据格式异常，将使用空数组:`,
      //             appliedOrdersStr
      //           );
      //           appliedOrders = [];
      //           // 重置存储的数据
      //           uni.setStorageSync(expertAppliedOrdersKey, "[]");
      //         }
      //       } catch (parseError) {
      //         console.error(
      //           `解析${expertAppliedOrdersKey}失败，将使用空数组:`,
      //           parseError
      //         );
      //         appliedOrders = [];
      //         // 重置存储的数据
      //         uni.setStorageSync(expertAppliedOrdersKey, "[]");
      //       }

      //       // 确保是数组
      //       if (!Array.isArray(appliedOrders)) {
      //         console.warn(
      //           `${expertAppliedOrdersKey}解析结果不是数组，将重置为空数组`
      //         );
      //         appliedOrders = [];
      //         uni.setStorageSync(expertAppliedOrdersKey, "[]");
      //       }

      //       // 过滤只属于当前达人的订单
      //       appliedOrders = appliedOrders.filter((order) => {
      //         // 检查订单是否属于当前达人
      //         return (
      //           order.expertId === userId ||
      //           (order.enrollList &&
      //             order.enrollList.some((expert) => expert.id === userId))
      //         );
      //       });

      //       if (appliedOrders.length > 0) {
      //         console.log(
      //           `从${expertAppliedOrdersKey}获取已报名订单数据，筛选后数量:`,
      //           appliedOrders.length
      //         );

      //         // 定义安全比较函数
      //         const safeCompare = (a, b) => {
      //           if (a === b) return true;
      //           if (
      //             a === undefined ||
      //             b === undefined ||
      //             a === null ||
      //             b === null
      //           )
      //             return false;

      //           const strA = String(a || "");
      //           const strB = String(b || "");

      //           return strA === strB;
      //         };

      //         // 合并已报名订单到全局数据中
      //         appliedOrders.forEach((appliedOrder) => {
      //           // 检查全局数据中是否已存在此订单
      //           const existingIndex = orderData.findIndex(
      //             (o) =>
      //               safeCompare(o.id, appliedOrder.id) ||
      //               safeCompare(o.orderId, appliedOrder.orderId)
      //           );

      //           if (existingIndex >= 0) {
      //             // 已存在，更新应用状态
      //             orderData[existingIndex].isApplied = true;

      //             // 确保有enrollList数组
      //             if (!orderData[existingIndex].enrollList) {
      //               orderData[existingIndex].enrollList = [];
      //             }

      //             // 检查是否已包含当前用户
      //             const userExists = orderData[
      //               existingIndex
      //             ].enrollList.some((expert) =>
      //               safeCompare(expert.id, userId)
      //             );

      //             // 如果没有当前用户，添加到enrollList
      //             if (!userExists) {
      //               orderData[existingIndex].enrollList.push({
      //                 id: userId,
      //                 name: app.currentUserInfo.nickName || "用户",
      //                 avatar: app.currentUserInfo.avatarUrl || "",
      //                 selected: appliedOrder.selected || false,
      //               });
      //             }
      //           } else {
      //             // 未找到匹配订单，添加到订单列表
      //             appliedOrder.isApplied = true;
      //             orderData.push(appliedOrder);
      //           }
      //         });
      //       }
      //     }
      //   } catch (e) {
      //     console.error("加载达人已报名订单数据失败:", e);
      //   }
      // }

      // 更新全局数据
      app.publishedOrders = orderData;
      console.log("最终加载到的订单数量:", orderData.length);

      // 过滤和排序订单
      const filteredOrders = this.filterOrders(orderData);

      // 更新页面数据
      this.orders = filteredOrders;
      this.isLoading = false;
      this.isEmpty = filteredOrders.length === 0;

      console.log("zzzz")
      // 隐藏加载提示
      uni.hideLoading();

      // 执行回调函数
      if (typeof callback === "function") {
        callback();
      }

      // 完成下拉刷新
      uni.stopPullDownRefresh();
    },

    // 打开达人选择弹窗
    viewEnrollList(e) {
      const orderId = e.currentTarget.dataset.id;
      const order = this.orders.find((o) => {
        // 确保id比较正确，使用字符串比较
        return (
          String(o.id) === String(orderId) ||
          String(o.orderId) === String(orderId)
        );
      });

      console.log("查看报名达人 - 当前订单:", {
        orderId: orderId,
        order: order
          ? {
              id: order.id,
              orderId: order.orderId,
              申请达人数: order.enrollList
                ? order.enrollList.length
                : 0,
            }
          : "未找到",
      });

      if (order && order.enrollList && order.enrollList.length > 0) {
        // 标记该订单的申请已被查看
        order.viewedApplications = true;

        // 更新全局数据
        
        if (app.publishedOrders) {
          // 使用安全比较查找订单
          const safeCompare = (a, b) => {
            if (a === b) return true;
            if (a === undefined || b === undefined || a === null || b === null)
              return false;

            // 转换为字符串比较
            const strA = String(a || "");
            const strB = String(b || "");

            return strA === strB;
          };

          const globalOrder = app.publishedOrders.find(
            (o) =>
              safeCompare(o.id, order.id) ||
              safeCompare(o.orderId, order.orderId)
          );

          if (globalOrder) {
            globalOrder.viewedApplications = true;
            // 保存到本地存储
            uni.setStorageSync(
              "publishedOrders",
              JSON.stringify(app.publishedOrders)
            );
          }
        }

        // 检查是否还有其他未查看的申请
        this.checkNewApplications();

        // 跳转到达人选择页面，不再使用弹窗模式
        const targetId = order.id || order.orderId;
        uni.navigateTo({
          url: `/pages/order-applicants/order-applicants?id=${targetId}`,
          fail: (err) => {
            console.error("跳转失败:", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
        });
      } else {
        uni.showToast({
          title: "暂无达人报名",
          icon: "none",
        });
      }
    },

    // 检查是否有未查看的达人申请
    checkNewApplications() {
      
      const orders = app.publishedOrders || [];
      const hasNew = orders.some(
        (order) =>
          order.creatorId === app.userId &&
          !order.viewedApplications &&
          order.enrollList &&
          order.enrollList.length > 0
      );

      this.hasNewApplications = hasNew;
    },

    // 隐藏达人选择弹窗
    hideExpertSelection() {
      this.showExpertSelection = false;
      this.currentEnrollList = [];
      this.currentOrderId = null;
    },

    // 选择达人
    selectExpert(e) {
      const expertId = e.currentTarget.dataset.expertId;
      const orderId = this.currentOrderId;

      if (!orderId || !expertId) {
        uni.showToast({
          title: "选择失败，请重试",
          icon: "none",
        });
        return;
      }

      uni.showLoading({
        title: "处理中",
      });

      // 获取订单和达人信息
      
      let orders = app.publishedOrders || [];
      let targetOrder = orders.find((o) => o.id === orderId);

      if (targetOrder) {
        // 查找选中的达人
        const selectedExpert = targetOrder.enrollList.find(
          (expert) => expert.id === expertId
        );
        if (!selectedExpert) {
          uni.hideLoading();
          uni.showToast({
            title: "达人不存在",
            icon: "none",
          });
          return;
        }

        // 更新订单状态
        changeOrderState(
          orderId,
          targetOrder.status,
          OrderState.EXPERT_SELECTED,
          "user",
          `选择了达人${selectedExpert.name}`
        ).then((success) => {
          uni.hideLoading();

          if (success) {
            // 更新选中的达人信息
            targetOrder.selectedExpertId = expertId;
            targetOrder.selectedExpertName = selectedExpert.name || "未知达人";
            targetOrder.selectedExpertAvatar =
              selectedExpert.avatar || "/images/default-avatar.png";
            targetOrder.selected = true;
            targetOrder.selectionTime = new Date().toISOString();
            targetOrder.selectionTimeFormatted = formatDateTime(
              targetOrder.selectionTime
            );

            // 更新达人侧状态
            targetOrder.expertStatus = OrderState.CHOSEN;
            targetOrder.expertStatusText = StateDisplayText[OrderState.CHOSEN];
            targetOrder.expertStatusTip =
              ExpertStatusTips[OrderState.CHOSEN] || "";
            targetOrder.expertProgress = 2;

            // 确保用户(创建者)信息存在，对达人视角有用
            
            if (!targetOrder.creatorId) {
              targetOrder.creatorId = app.currentUserId;
            }

            if (!targetOrder.creatorName || !targetOrder.creatorAvatar) {
              const creatorInfo = app.userInfo || {};
              targetOrder.creatorName =
                creatorInfo.nickname || `用户${targetOrder.creatorId}`;
              targetOrder.creatorAvatar =
                creatorInfo.avatar || "/images/default-avatar.png";
            }

            app.publishedOrders = orders;
            uni.setStorageSync("publishedOrders", JSON.stringify(orders));

            // 发送通知给达人
            try {
              await selectTalents({
                orderId: orderId,
                talentIds: [expertId]
              });
              console.log(`已成功通知达人ID: ${expertId}`);
            } catch (error) {
              console.error('通知达人失败:', error);
              uni.showToast({
                title: '通知达人失败',
                icon: 'error'
              });
              return;
            }
            
            uni.showToast({
              title: "已通知达人",
              icon: "success",
            });

            this.hideExpertSelection();
            this.loadOrders();
          } else {
            uni.showToast({
              title: "选择失败",
              icon: "none",
            });
          }
        });
      } else {
        uni.hideLoading();
        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
      }
    },

    // 达人确认出发
    confirmDeparture(e) {
      const orderId = e.currentTarget.dataset.id;
      console.log("确认出发，订单ID:", orderId, typeof orderId);

      // 查找订单
      const orderIndex = this.orders.findIndex(
        (order) =>
          String(order.id) === String(orderId) ||
          String(order.orderId) === String(orderId)
      );

      if (orderIndex === -1) {
        console.error("订单不存在，ID:", orderId);
        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
        return;
      }

      const order = this.orders[orderIndex];

      // 更新订单状态 - 修正参数
      changeOrderState(
        orderId,
        order.expertStatus || OrderState.CHOSEN, // 当前状态，默认为CHOSEN
        OrderState.DEPARTURE_CONFIRMED, // 目标状态
        "expert", // 角色
        "达人确认出发" // 备注
      )
        .then(() => {
          uni.showToast({
            title: "已确认出发",
            icon: "success",
          });

          // 更新本地订单状态
          const updatedOrder = { ...order };
          updatedOrder.expertStatus = OrderState.DEPARTURE_CONFIRMED;
          updatedOrder.expertStatusText =
            StateDisplayText[OrderState.DEPARTURE_CONFIRMED];
          updatedOrder.expertStatusTip = this.getExpertStatusTip({
            ...updatedOrder,
            selected: true,
            isApplied: true,
          });
          updatedOrder.expertProgress = this.getExpertProgressValue(
            OrderState.DEPARTURE_CONFIRMED
          );

          // 更新全局数据
          const updatedOrders = [...this.orders];
          updatedOrders[orderIndex] = updatedOrder;

          this.orders = this.filterOrders(updatedOrders) // 修正：传递更新后的订单列表
        })
        .catch((error) => {
          console.error("确认出发失败:", error);
          uni.showToast({
            title: "确认出发失败",
            icon: "none",
          });
        });
    },

    // 达人确认到达
    confirmArrival(e) {
      const orderId = e.currentTarget.dataset.id;
      console.log("确认到达，订单ID:", orderId, typeof orderId);

      // 查找订单
      const orderIndex = this.orders.findIndex(
        (order) =>
          String(order.id) === String(orderId) ||
          String(order.orderId) === String(orderId)
      );

      if (orderIndex === -1) {
        console.error("订单不存在，ID:", orderId);
        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
        return;
      }

      const order = this.orders[orderIndex];

      // 更新订单状态 - 修正参数
      changeOrderState(
        orderId,
        order.expertStatus || OrderState.DEPARTURE_CONFIRMED, // 当前状态，默认为DEPARTURE_CONFIRMED
        OrderState.ARRIVAL_CONFIRMED, // 目标状态
        "expert", // 角色
        "达人确认到达" // 备注
      )
        .then(() => {
          uni.showToast({
            title: "已确认到达",
            icon: "success",
          });

          // 更新本地订单状态
          const updatedOrder = { ...order };
          updatedOrder.expertStatus = OrderState.ARRIVAL_CONFIRMED;
          updatedOrder.expertStatusText =
            StateDisplayText[OrderState.ARRIVAL_CONFIRMED];
          updatedOrder.expertStatusTip = this.getExpertStatusTip({
            ...updatedOrder,
            selected: true,
            isApplied: true,
          });
          updatedOrder.expertProgress = this.getExpertProgressValue(
            OrderState.ARRIVAL_CONFIRMED
          );

          // 更新全局数据
          const updatedOrders = [...this.orders];
          updatedOrders[orderIndex] = updatedOrder;

          this.orders = this.filterOrders(updatedOrders) // 修正：传递更新后的订单列表
        })
        .catch((error) => {
          console.error("确认到达失败:", error);
          uni.showToast({
            title: "确认到达失败",
            icon: "none",
          });
        });
    },

    // 达人确认服务完成
    completeService(e) {
      const orderId = e.currentTarget.dataset.id;
      console.log("完成服务，订单ID:", orderId, typeof orderId);

      // 查找订单
      const orderIndex = this.orders.findIndex(
        (order) =>
          String(order.id) === String(orderId) ||
          String(order.orderId) === String(orderId)
      );

      if (orderIndex === -1) {
        console.error("订单不存在，ID:", orderId);
        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
        return;
      }

      const order = this.orders[orderIndex];

      // 更新订单状态 - 修正参数
      changeOrderState(
        orderId,
        order.expertStatus || OrderState.ARRIVAL_CONFIRMED, // 当前状态，默认为ARRIVAL_CONFIRMED
        OrderState.SERVICE_COMPLETED, // 目标状态
        "expert", // 角色
        "达人完成服务" // 备注
      )
        .then(() => {
          uni.showToast({
            title: "服务已完成",
            icon: "success",
          });

          // 更新本地订单状态
          const updatedOrder = { ...order };
          updatedOrder.expertStatus = OrderState.SERVICE_COMPLETED;
          updatedOrder.expertStatusText =
            StateDisplayText[OrderState.SERVICE_COMPLETED];
          updatedOrder.expertStatusTip = this.getExpertStatusTip({
            ...updatedOrder,
            selected: true,
            isApplied: true,
          });
          updatedOrder.expertProgress = this.getExpertProgressValue(
            OrderState.SERVICE_COMPLETED
          );

          // 更新全局数据
          const updatedOrders = [...this.orders];
          updatedOrders[orderIndex] = updatedOrder;

          this.orders = this.filterOrders(updatedOrders) // 修正：传递更新后的订单列表
        })
        .catch((error) => {
          console.error("完成服务失败:", error);
          uni.showToast({
            title: "完成服务失败",
            icon: "none",
          });
        });
    },

    // 根据专家状态获取进度值
    getExpertProgressValue(status) {
      switch (status) {
        case OrderState.APPLIED:
          return 1;
        case OrderState.CHOSEN:
          return 2;
        case OrderState.DEPARTURE_CONFIRMED:
          return 3;
        case OrderState.ARRIVAL_CONFIRMED:
          return 4;
        case OrderState.SERVICE_COMPLETED:
          return 5;
        case OrderState.EVALUATED:
          return 6;
        default:
          // 如果状态不明确，返回默认值
          if (status === "applied") return 1;
          if (status === "chosen") return 2;
          if (status === "departureConfirmed") return 3;
          if (status === "arrivalConfirmed") return 4;
          if (status === "serviceCompleted") return 5;
          if (status === "evaluated") return 6;
          return 0;
      }
    },

    // 更新达人接单列表中的订单状态
    updateExpertOrderStatus(order, newStatus) {
      // 获取达人接单列表
      let expertOrders = uni.getStorageSync("expertOrders") || [];
      if (typeof expertOrders === "string") {
        try {
          expertOrders = JSON.parse(expertOrders);
        } catch (e) {
          expertOrders = [];
        }
      }

      if (!Array.isArray(expertOrders)) {
        expertOrders = [];
      }

      // 定义安全比较函数
      const safeCompare = (a, b) => {
        if (a === b) return true;
        if (a === undefined || b === undefined || a === null || b === null)
          return false;

        const strA = String(a || "");
        const strB = String(b || "");

        return strA === strB;
      };

      // 获取应用实例
      
      const userId = app.userId;

      console.log("更新达人接单列表", {
        订单ID: order.id || order.orderId,
        订单详情: JSON.stringify(order),
        新状态: newStatus,
        当前列表: expertOrders.length,
      });

      // 更新状态
      const index = expertOrders.findIndex(
        (o) =>
          safeCompare(o.id, order.id) || safeCompare(o.orderId, order.orderId)
      );

      if (index !== -1) {
        console.log("找到需要更新的订单", expertOrders[index].id);

        // 先保存以前的值
        const previousOrder = { ...expertOrders[index] };

        // 更新值
        expertOrders[index] = {
          ...previousOrder,
          expertStatus: newStatus,
          expertStatusText: StateDisplayText[newStatus],
          expertStatusTip: ExpertStatusTips[newStatus] || "",
          expertProgress: this.getExpertProgressValue(newStatus),
          // 确保ID字段存在
          id: order.id || previousOrder.id || order.orderId,
          orderId: order.orderId || previousOrder.orderId || order.id,
        };

        // 添加日志
        if (!expertOrders[index].stateLogs) expertOrders[index].stateLogs = [];
        expertOrders[index].stateLogs.unshift({
          state: newStatus,
          time: new Date().toISOString(),
          role: "expert",
          remark: StateDisplayText[newStatus],
        });

        // 保存前验证JSON字符串是否有效
        try {
          const jsonStr = JSON.stringify(expertOrders);
          uni.setStorageSync("expertOrders", jsonStr);
          console.log("已更新达人订单状态", newStatus);
        } catch (stringifyError) {
          console.error("保存expertOrders时序列化失败:", stringifyError);
          // 尝试清理不能序列化的字段后再保存
          const cleanedOrders = expertOrders.map((o) => ({
            id: String(o.id || ""),
            orderId: String(o.orderId || ""),
            title: o.title || "",
            service: o.service || "",
            serviceSubType: o.serviceSubType || "",
            expertStatus: o.expertStatus || "",
            expertStatusText: o.expertStatusText || "",
            expertStatusTip: o.expertStatusTip || "",
            expertProgress: o.expertProgress || 0,
            selected: o.selected || false,
            isSelected: o.isSelected || false,
            createTime: o.createTime || new Date().toISOString(),
          }));
          uni.setStorageSync("expertOrders", JSON.stringify(cleanedOrders));
        }
      } else {
        console.log("未找到需要更新的订单，尝试添加新订单", {
          orderId: order.id || order.orderId,
          newStatus: newStatus,
        });

        // 构建新订单对象
        const newOrder = {
          id: expertId,
          orderId: String(orderId),
          title: details.title || "空缺职位",
          fee: details.fee,
          service: details.service,
          serviceSubType: details.serviceSubType,
          expertStatus: newStatus,
          expertStatusText: StateDisplayText[newStatus] || "未知状态",
          expertStatusTip: StateTipText[newStatus] || "",
          expertProgress: 0,
          isSelected: newStatus === ExpertOrderStatus.Confirmed,
          selected: newStatus === ExpertOrderStatus.Confirmed,
          stateLogs: [
            {
              state: newStatus,
              time: new Date().toISOString(),
              role: "expert",
              remark: StateDisplayText[newStatus],
            },
          ],
          createTime: details.createTime || new Date().toISOString(),
        };

        expertOrders.push(newOrder);

        // 保存前验证JSON字符串是否有效
        try {
          const jsonStr = JSON.stringify(expertOrders);
          uni.setStorageSync("expertOrders", jsonStr);
          console.log("已添加新的达人订单", newOrder);
        } catch (stringifyError) {
          console.error("保存新的expertOrders时序列化失败:", stringifyError);
          // 尝试清理不能序列化的字段后再保存
          const cleanedOrders = expertOrders.map((o) => ({
            id: String(o.id || ""),
            orderId: String(o.orderId || ""),
            title: o.title || "",
            service: o.service || "",
            serviceSubType: o.serviceSubType || "",
            expertStatus: o.expertStatus || "",
            expertStatusText: o.expertStatusText || "",
            expertStatusTip: o.expertStatusTip || "",
            expertProgress: o.expertProgress || 0,
            selected: o.selected || false,
            isSelected: o.isSelected || false,
            createTime: o.createTime || new Date().toISOString(),
          }));
          uni.setStorageSync("expertOrders", JSON.stringify(cleanedOrders));
        }
      }

      // 同步更新已报名订单列表中的状态
      this.updateAppliedOrderStatus(order, newStatus);
    },

    // 更新已报名订单列表中的状态
    updateAppliedOrderStatus(order, newStatus) {
      // 获取已报名订单列表
      let appliedOrders = uni.getStorageSync("expertAppliedOrders") || [];
      if (typeof appliedOrders === "string") {
        try {
          appliedOrders = JSON.parse(appliedOrders);
        } catch (e) {
          appliedOrders = [];
        }
      }

      if (!Array.isArray(appliedOrders)) {
        appliedOrders = [];
      }

      // 定义安全比较函数
      const safeCompare = (a, b) => {
        if (a === b) return true;
        if (a === undefined || b === undefined || a === null || b === null)
          return false;

        const strA = String(a || "");
        const strB = String(b || "");

        return strA === strB;
      };

      // 获取应用实例
      
      const userId = app.userId;

      console.log("准备更新已报名订单状态", {
        订单ID: order.id || order.orderId,
        原始状态: order.expertStatus,
        新状态: newStatus,
        当前列表长度: appliedOrders.length,
        isApplied: order.isApplied,
      });

      // 查找需要更新的订单
      const index = appliedOrders.findIndex(
        (o) =>
          safeCompare(o.id, order.id) || safeCompare(o.orderId, order.orderId)
      );

      if (index !== -1) {
        console.log(
          "找到需要更新的已报名订单",
          appliedOrders[index].id || appliedOrders[index].orderId
        );

        // 先保存以前的值
        const previousOrder = { ...appliedOrders[index] };

        // 如果是COMPLETED状态，设置特殊的状态提示
        const statusTip =
          newStatus === OrderState.COMPLETED ||
          order.status === OrderState.COMPLETED
            ? "恭喜您！服务已完成，用户已确认完成！"
            : ExpertStatusTips[newStatus] || "";

        // 更新状态
        appliedOrders[index] = {
          ...previousOrder,
          expertStatus: newStatus,
          expertStatusText: StateDisplayText[newStatus],
          expertStatusTip: statusTip,
          expertProgress: this.getExpertProgressValue(newStatus),
          // 保持报名状态
          isApplied: true,
          // 确保ID字段存在
          id: order.id || previousOrder.id || order.orderId,
          orderId: order.orderId || previousOrder.orderId || order.id,
        };

        // 添加日志
        if (!appliedOrders[index].stateLogs)
          appliedOrders[index].stateLogs = [];
        appliedOrders[index].stateLogs.unshift({
          state: newStatus,
          time: new Date().toISOString(),
          role: "expert",
          remark: StateDisplayText[newStatus],
        });

        // 保存前验证JSON字符串是否有效
        try {
          const jsonStr = JSON.stringify(appliedOrders);
          uni.setStorageSync("expertAppliedOrders", jsonStr);
          console.log("已更新已报名订单状态", newStatus);
        } catch (stringifyError) {
          console.error("保存expertAppliedOrders时序列化失败:", stringifyError);
          // 尝试清理不能序列化的字段
          const cleanedOrders = appliedOrders.map((o) => ({
            id: String(o.id || ""),
            orderId: String(o.orderId || ""),
            title: o.title || "",
            service: o.service || "",
            serviceSubType: o.serviceSubType || "",
            applyStatus: o.applyStatus || "",
            isApplied: o.isApplied || false,
            createTime: o.createTime || new Date().toISOString(),
          }));
          uni.setStorageSync(
            "expertAppliedOrders",
            JSON.stringify(cleanedOrders)
          );
        }
      } else {
        console.log("未找到需要更新的已报名订单，检查是否需要添加", {
          订单ID: order.id || order.orderId,
          isApplied: order.isApplied,
        });

        // 如果订单确实是已报名的，但在列表中找不到，则添加
        if (
          order.isApplied ||
          order.enrollList?.some((expert) => safeCompare(expert.id, userId))
        ) {
          console.log("添加订单到已报名列表");

          // 创建新的订单对象
          const newOrder = {
            ...order,
            expertStatus: newStatus,
            expertStatusText: StateDisplayText[newStatus],
            expertStatusTip: ExpertStatusTips[newStatus] || "",
            expertProgress: this.getExpertProgressValue(newStatus),
            isApplied: true,
          };

          // 确保ID存在
          if (!newOrder.id && newOrder.orderId) {
            newOrder.id = newOrder.orderId;
          } else if (!newOrder.orderId && newOrder.id) {
            newOrder.orderId = newOrder.id;
          }

          // 添加日志
          if (!newOrder.stateLogs) newOrder.stateLogs = [];
          newOrder.stateLogs.unshift({
            state: newStatus,
            time: new Date().toISOString(),
            role: "expert",
            remark: StateDisplayText[newStatus],
          });

          // 添加到已报名订单列表
          appliedOrders.push(newOrder);
          uni.setStorageSync(
            "expertAppliedOrders",
            JSON.stringify(appliedOrders)
          );
          console.log(
            "订单已添加到已报名列表",
            newOrder.id || newOrder.orderId
          );
        } else {
          console.log("该订单不符合添加到已报名列表的条件", {
            订单ID: order.id || order.orderId,
            isApplied: order.isApplied,
            enrollList: order.enrollList?.length || 0,
          });
        }
      }
    },

    // 开始订单
    startOrder(e) {
      const orderId = e.currentTarget.dataset.id;
      console.log("开始订单", {
        订单ID: orderId,
        ID类型: typeof orderId,
      });

      const safeCompare = (a, b) => {
        if (a === b) return true;
        if (a === undefined || b === undefined || a === null || b === null)
          return false;

        const strA = String(a || "");
        const strB = String(b || "");

        return strA === strB;
      };

      // 查找订单
      const orderIndex = this.orders.findIndex(
        (order) =>
          safeCompare(order.id, orderId) || safeCompare(order.orderId, orderId)
      );

      if (orderIndex === -1) {
        console.error("订单不存在", {
          id: orderId,
          当前订单数: this.orders.length,
        });

        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
        return;
      }

      const order = this.orders[orderIndex];

      // 确保达人已经到达才能开始订单
      if (order.expertStatus !== OrderState.ARRIVAL_CONFIRMED) {
        uni.showToast({
          title: "等待达人到达",
          icon: "none",
        });
        return;
      }

      // 更新订单状态
      changeOrderState(
        orderId,
        order.status,
        OrderState.ORDER_STARTED,
        "user",
        "用户开始订单"
      )
        .then((success) => {
          if (success) {
            uni.showToast({
              title: "订单已开始",
              icon: "success",
            });

            // 更新本地订单状态
            const updatedOrder = { ...order };
            updatedOrder.status = OrderState.ORDER_STARTED;
            updatedOrder.statusText =
              StateDisplayText[OrderState.ORDER_STARTED];
            updatedOrder.userProgress = 3; // 用户进度更新到开始订单阶段
            updatedOrder.userStatusTip =
              UserStatusTips[OrderState.ORDER_STARTED]; // 更新用户状态提示

            // 更新全局数据
            const updatedOrders = [...this.orders];
            updatedOrders[orderIndex] = updatedOrder;

            this.orders = this.filterOrders(updatedOrders)
          } else {
            uni.showToast({
              title: "操作失败",
              icon: "none",
            });
          }
        })
        .catch((error) => {
          console.error("开始订单失败:", error);
          uni.showToast({
            title: "操作失败",
            icon: "none",
          });
        });
    },

    // 确认完成订单
    confirmComplete(e) {
      // 使用String处理dataset中的ID，避免parseInt可能导致的NaN
      const orderId = e.currentTarget.dataset.id;
      console.log(
        "确认完成按钮点击 - 原始ID:",
        orderId,
        "类型:",
        typeof orderId
      );

      // 通过安全比较查找订单
      const safeCompare = (a, b) => {
        if (a === b) return true;
        if (a === undefined || b === undefined || a === null || b === null)
          return false;

        const strA = String(a || "");
        const strB = String(b || "");

        return strA === strB;
      };

      // 不再尝试parseInt，直接使用原始ID进行查找
      const order = this.orders.find(
        (o) => safeCompare(o.id, orderId) || safeCompare(o.orderId, orderId)
      );

      if (!order) {
        console.error("订单不存在", {
          orderId: orderId,
          orderId类型: typeof orderId,
          当前订单数: this.orders.length,
          当前订单IDs: this.orders
            .map((o) => `${o.id}/${o.orderId}`)
            .join(","),
          当前所有订单: JSON.stringify(
            this.orders.map((o) => ({ id: o.id, orderId: o.orderId }))
          ),
        });

        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
        return;
      }

      // 检查达人是否已完成服务
      if (order.expertStatus !== OrderState.SERVICE_COMPLETED) {
        uni.showToast({
          title: "达人尚未完成服务",
          icon: "none",
        });
        return;
      }

      uni.showModal({
        title: "确认完成订单",
        content: "确定要完成此订单吗？",
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: "处理中",
            });

            // 获取最新的订单数据
            
            let globalOrders = app.publishedOrders || [];

            // 更新状态前先确保全局数据中有此订单
            const globalOrder = globalOrders.find(
              (o) =>
                safeCompare(o.id, order.id) ||
                safeCompare(o.orderId, order.orderId) ||
                safeCompare(o.id, orderId) ||
                safeCompare(o.orderId, orderId)
            );

            if (!globalOrder) {
              console.error("全局数据中未找到订单", {
                orderId: orderId,
                orderObj: {
                  id: order.id,
                  orderId: order.orderId,
                },
              });

              // 尝试添加到全局数据
              globalOrders.push(order);
              app.publishedOrders = globalOrders;
            }

            // 调用状态变更函数前调试输出
            console.log("准备调用状态变更", {
              orderId: order.id || order.orderId,
              currentStatus: order.status,
              target: OrderState.COMPLETED,
            });

            changeOrderState(
              order.id || order.orderId, // 使用订单对象中的ID确保一致性
              order.status,
              OrderState.COMPLETED,
              "user",
              "订单已完成"
            )
              .then((success) => {
                uni.hideLoading();

                if (success) {
                  // 更新全局数据中的订单状态
                  
                  let globalOrders = app.publishedOrders || [];
                  const globalOrderIndex = globalOrders.findIndex(
                    (o) =>
                      safeCompare(o.id, order.id) ||
                      safeCompare(o.orderId, order.orderId)
                  );

                  if (globalOrderIndex !== -1) {
                    globalOrders[globalOrderIndex].status =
                      OrderState.COMPLETED;
                    globalOrders[globalOrderIndex].statusText =
                      StateDisplayText[OrderState.COMPLETED];
                    // 更新达人提示信息
                    globalOrders[globalOrderIndex].expertStatusTip =
                      "恭喜您！服务已完成，用户已确认完成！";

                    // 保存更新
                    app.publishedOrders = globalOrders;
                    uni.setStorageSync(
                      "publishedOrders",
                      JSON.stringify(globalOrders)
                    );
                  }

                  this.loadOrders();

                  uni.showToast({
                    title: "订单已完成",
                    icon: "success",
                  });
                } else {
                  uni.showToast({
                    title: "操作失败",
                    icon: "none",
                  });
                }
              })
              .catch((error) => {
                uni.hideLoading();
                console.error("确认完成订单失败:", error);
                uni.showToast({
                  title: "操作失败",
                  icon: "none",
                });
              });
          }
        },
      });
    },

    // 报名接单
    applyOrder(e) {
      const orderId = parseInt(e.currentTarget.dataset.id);
      console.log("报名订单:", orderId);

      // 获取全局数据
      
      const userId = app.currentUserId;

      // 查找订单
      let order = this.orders.find((o) => o.id === orderId);
      if (!order) {
        // 确保安全比较，查找全局数据
        const globalOrders = app.publishedOrders || [];
        order = globalOrders.find((o) => {
          const oId = String(o.id || "");
          const oOrderId = String(o.orderId || "");
          const targetId = String(orderId || "");
          return oId === targetId || oOrderId === targetId;
        });
      }

      if (!order) {
        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
        return;
      }

      // 检查是否是自己发布的订单
      if (order.creatorId === userId) {
        uni.showToast({
          title: "不能报名自己发布的订单",
          icon: "none",
        });
        return;
      }

      // 检查是否已经报名
      if (order.isApplied) {
        uni.showToast({
          title: "您已报名过此订单",
          icon: "none",
        });
        return;
      }

      uni.showModal({
        title: "确认",
        content: "确定报名此订单吗？",
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: "处理中",
            });

            // 更新订单的已报名达人列表
            if (!order.enrollList) {
              order.enrollList = [];
            }

            // 添加当前达人到已报名列表
            order.enrollList.push({
              id: userId,
              name: userName,
              avatar: userAvatar,
            });

            // 标记该订单为未查看（对订单创建者）
            order.viewedApplications = false;

            // 标记当前达人已报名该订单
            order.isApplied = true;

            // 添加日志
            if (!order.stateLogs) order.stateLogs = [];
            order.stateLogs.unshift({
              state: "expertApplied",
              time: new Date().toISOString(),
              role: "expert",
              remark: "达人已报名",
            });

            // 更新全局数据和本地存储
            app.publishedOrders = app.publishedOrders.map(
              (o) => (o.id === orderId ? order : o)
            );
            uni.setStorageSync(
              "publishedOrders",
              JSON.stringify(app.publishedOrders)
            );

            // 保存到达人已报名订单列表
            try {
              let expertAppliedOrders = [];

              try {
                const appliedOrdersStr = uni.getStorageSync(
                  "expertAppliedOrders"
                );
                if (appliedOrdersStr) {
                  expertAppliedOrders = JSON.parse(appliedOrdersStr);
                }
              } catch (parseError) {
                console.error(
                  "解析expertAppliedOrders失败，将使用空数组:",
                  parseError
                );
                expertAppliedOrders = [];
              }

              if (!Array.isArray(expertAppliedOrders)) {
                console.warn("expertAppliedOrders不是数组，将重置为空数组");
                expertAppliedOrders = [];
              }

              // 确保订单有必须的ID字段
              const orderToAdd = {
                ...order,
                applyStatus: "applied",
                isApplied: true,
              };

              // 确保ID字段存在且为字符串
              if (!orderToAdd.id && orderToAdd.orderId) {
                orderToAdd.id = String(orderToAdd.orderId);
              } else if (!orderToAdd.orderId && orderToAdd.id) {
                orderToAdd.orderId = String(orderToAdd.id);
              }

              // 安全地检查订单是否已存在
              const orderExists = expertAppliedOrders.some((o) => {
                const currentId = String(o.id || "");
                const currentOrderId = String(o.orderId || "");
                const targetId = String(orderId || "");
                return currentId === targetId || currentOrderId === targetId;
              });

              if (!orderExists) {
                expertAppliedOrders.push(orderToAdd);
                // 保存前验证JSON字符串是否有效
                try {
                  const jsonStr = JSON.stringify(expertAppliedOrders);
                  uni.setStorageSync("expertAppliedOrders", jsonStr);
                  console.log("成功保存到expertAppliedOrders");
                } catch (stringifyError) {
                  console.error(
                    "保存expertAppliedOrders时序列化失败:",
                    stringifyError
                  );
                  // 尝试清理不能序列化的字段
                  const cleanedOrders = expertAppliedOrders.map((o) => ({
                    id: String(o.id || ""),
                    orderId: String(o.orderId || ""),
                    title: o.title || "",
                    service: o.service || "",
                    serviceSubType: o.serviceSubType || "",
                    applyStatus: o.applyStatus || "",
                    isApplied: o.isApplied || false,
                    createTime: o.createTime || new Date().toISOString(),
                  }));
                  uni.setStorageSync(
                    "expertAppliedOrders",
                    JSON.stringify(cleanedOrders)
                  );
                }
              }
            } catch (e) {
              console.error("保存到达人已报名订单列表失败:", e);
            }

            uni.hideLoading();
            uni.showToast({
              title: "报名成功",
              icon: "success",
            });

            // 更新当前页面数据
            this.orders = this.orders.map((o) => {
              if (o.id === orderId) {
                return { ...o, isApplied: true, applyStatus: "applied" };
              }
              return o;
            });
          }
        },
      });
    },

    // 导航到评价页面
    navigateToEvaluate(e) {
      // 使用String处理dataset中的ID，避免parseInt可能导致的NaN
      const orderId = e.currentTarget.dataset.id;
      console.log("去评价按钮点击 - 原始ID:", orderId, "类型:", typeof orderId);

      // 通过安全比较查找订单
      const safeCompare = (a, b) => {
        if (a === b) return true;
        if (a === undefined || b === undefined || a === null || b === null)
          return false;

        const strA = String(a || "");
        const strB = String(b || "");

        return strA === strB;
      };

      // 检查订单是否存在
      const order = this.orders.find(
        (o) => safeCompare(o.id, orderId) || safeCompare(o.orderId, orderId)
      );

      if (!order) {
        console.error("订单不存在", {
          orderId: orderId,
          orderId类型: typeof orderId,
          当前订单数: this.orders.length,
        });

        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
        return;
      }

      // 导航到评价页面
      uni.navigateTo({
        url: `/pages/order-evaluate/order-evaluate?id=${orderId}`,
        fail: (err) => {
          console.error("导航到评价页面失败:", err);
          uni.showToast({
            title: "页面跳转失败",
            icon: "none",
          });
        },
      });
    },

    // 查看评价
    viewEvaluation(e) {
      const orderId = e.currentTarget.dataset.id;
      console.log("查看评价，订单ID:", orderId);

      // 查找订单，确保ID存在
      const safeCompare = (a, b) => {
        if (a === b) return true;
        if (a === undefined || b === undefined || a === null || b === null)
          return false;

        const strA = String(a || "");
        const strB = String(b || "");

        return strA === strB;
      };

      const order = this.orders.find(
        (order) =>
          safeCompare(order.id, orderId) || safeCompare(order.orderId, orderId)
      );

      if (!order) {
        console.error("订单不存在，ID:", orderId);
        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
        return;
      }

      // 跳转到评价页面，使用view模式查看评价
      uni.navigateTo({
        url: `/pages/order-evaluate/order-evaluate?id=${orderId}&mode=view`,
        fail: function (err) {
          console.error("跳转失败:", err);
          uni.showToast({
            title: "无法查看评价",
            icon: "none",
          });
        },
      });
    },

    // 跳转到达人选择页面
    navigateToExpertSelection(e) {
      const orderId = parseInt(e.currentTarget.dataset.id);
      // 直接调用viewEnrollList
      this.viewEnrollList(e);
    },

    /**
     * 过滤订单列表
     * @param {Array} orders 原始订单列表
     * @returns {Array} 过滤后的订单列表
     */
    filterOrders: function (orders) {
      if (!orders || !Array.isArray(orders)) {
        console.warn("filterOrders收到非数组参数:", orders);
        return [];
      }

      
      const currentUser = app.currentUserInfo || {};
      console.log("ASDfasdf", uni.getStorageSync('userInfo'));
      let userInfo = uni.getStorageSync('userInfo');
      const userId = userInfo.id || "";
      console.log("userid", userId);
      const userRole =
        app.userRole || this.userRole || "normal";

      console.log(
        `过滤订单，用户角色:${userRole}，当前过滤状态:${this.currentFilter}`
      );

      // 确保订单对象格式一致
      const processedOrders = orders.map((order) => {
        // 创建新对象，避免修改原始数据
        const processedOrder = { ...order };

        // 性别要求：0-不限，1-男，2-女
        // 处理性别属性，确保显示为中文
        if (processedOrder.sexRequire === "1") {
          processedOrder.genderText = "男";
        } else if (processedOrder.sexRequire === "2") {
          processedOrder.genderText = "女";
        } else {
          processedOrder.genderText = "不限";
        }

        // 格式化订单创建时间
        if (processedOrder.createTime) {
          processedOrder.createTimeFormatted = this.formatDateTime(
            processedOrder.createTime
          );
        } else {
          processedOrder.createTime = new Date().toISOString();
          processedOrder.createTimeFormatted = this.formatDateTime(
            processedOrder.createTime
          );
        }

        // 格式化达人选择时间
        if (processedOrder.selectionTime) {
          processedOrder.selectionTimeFormatted = this.formatDateTime(
            processedOrder.selectionTime
          );
        }

        // 确保达人申请列表存在
        if (!processedOrder.enrollList) {
          processedOrder.enrollList = [];
        }

        // 确保创建者信息存在且完整（对达人视角有用）
        if (
          processedOrder.creatorId &&
          (!processedOrder.creatorName || !processedOrder.creatorAvatar)
        ) {
          // 已移除测试账号逻辑，使用默认创建者信息
          processedOrder.creatorName = `用户${processedOrder.creatorId}`;
          processedOrder.creatorAvatar = "/images/default-avatar.png";
        }

        // 处理活动时间
        if (
          !processedOrder.activityTime &&
          (processedOrder.date || processedOrder.time)
        ) {
          if (processedOrder.date && processedOrder.time) {
            processedOrder.activityTime = `${processedOrder.date} ${processedOrder.time}`;
          } else {
            processedOrder.activityTime =
              processedOrder.date || processedOrder.time || "未指定时间";
          }
        }

        // 处理参与人数
        if (!processedOrder.participants && processedOrder.people) {
          processedOrder.participants = processedOrder.people;
        } else if (!processedOrder.participants) {
          processedOrder.participants = 1; // 默认最少1人
        }

        // 确保价格信息一致
        if (!processedOrder.hourlyRate && processedOrder.price) {
          processedOrder.hourlyRate = processedOrder.price;
        } else if (!processedOrder.price && processedOrder.hourlyRate) {
          processedOrder.price = processedOrder.hourlyRate;
        }

        // 确保状态字段存在
        if (!processedOrder.applyStatus)
          processedOrder.applyStatus = "notApplied";
        if (processedOrder.selected === undefined){
          processedOrder.selected = false;
        }

        // 检查当前用户是否已报名该订单
        if (
          processedOrder.enrollList &&
          Array.isArray(processedOrder.enrollList)
        ) {
          processedOrder.isApplied = processedOrder.enrollList.some(
            (expert) => expert.id === userId
          );

          // 检查当前用户是否被选中
          if (processedOrder.selectedExpertId === userId) {
            processedOrder.isSelected = true;
          } else {
            processedOrder.isSelected = false;
          }
        } else {
          processedOrder.isApplied = false;
          processedOrder.isSelected = false;
        }

        // 如果是该用户被选中的订单，确保selected标记正确
        if (processedOrder.selectedExpertId === userId) {
          processedOrder.selected = true;
        }

        // 设置达人状态提示
        if (userRole === "闲时达人") {
          // 使用getExpertStatusTip函数设置状态提示
          processedOrder.expertStatusTip =
            this.getExpertStatusTip(processedOrder);

          // 如果订单已评价，设置专家进度为6
          if (processedOrder.status === "evaluated") {
            processedOrder.expertProgress = 6;
          }
        }

        // 设置用户状态提示
        if (userRole !== "闲时达人") {
          try {
            // 从组件数据中获取状态提示
            const UserStatusTips = this.statusTips;

            // 根据订单状态设置提示信息
            if (
              processedOrder.status &&
              UserStatusTips[processedOrder.status]
            ) {
              processedOrder.userStatusTip =
                UserStatusTips[processedOrder.status];
            }

            // 如果订单状态为"已发布"，且有达人报名，则提示用户查看并选择达人
            if (
              processedOrder.status === "published" &&
              processedOrder.enrollList &&
              processedOrder.enrollList.length > 0
            ) {
              processedOrder.userStatusTip =
                UserStatusTips["expertSelected"] || "有达人报名，请查看并选择";
            }

            // 如果已选择达人但达人尚未出发，显示等待达人出发的提示
            if (
              processedOrder.status === "expertSelected" &&
              (!processedOrder.expertStatus ||
                processedOrder.expertStatus === "chosen")
            ) {
              processedOrder.userStatusTip =
                UserStatusTips["expertChosenWaiting"] || "等待达人出发";
            }

            // 根据达人状态更新提示信息
            if (processedOrder.expertStatus) {
              if (
                processedOrder.expertStatus === "departureConfirmed" &&
                UserStatusTips["departureConfirmed"]
              ) {
                processedOrder.userStatusTip =
                  UserStatusTips["departureConfirmed"];
              } else if (
                processedOrder.expertStatus === "arrivalConfirmed" &&
                UserStatusTips["arrivalConfirmed"]
              ) {
                processedOrder.userStatusTip =
                  UserStatusTips["arrivalConfirmed"];
              } else if (
                processedOrder.expertStatus === "serviceCompleted" &&
                UserStatusTips["serviceCompleted"]
              ) {
                processedOrder.userStatusTip =
                  UserStatusTips["serviceCompleted"];
              }
            }
          } catch (e) {
            console.error("设置用户状态提示时出错:", e);
            processedOrder.userStatusTip = "订单状态未知";
          }
        }



        // 活动时间
        // 处理日期和时间
        let dateStr = "";
        let timeStr = "";
        // 使用startTime和endTime格式化活动时间
        if (processedOrder.startTime && processedOrder.endTime) {
          const formattedTime = this.formatActivityTime(processedOrder.startTime, processedOrder.endTime);
          const timeParts = formattedTime.split(" ");
          if (formattedTime.includes(" - ")) {
            // 分离日期和时间
            if (timeParts.length >= 3) {
              dateStr = timeParts[0];
              timeStr = timeParts.slice(1).join(" ");
            } else {
              timeStr = formattedTime;
            }
          } else {
            timeStr = formattedTime;
          }
          
          // 计算活动时长
          const durationInfo = this.calculateDuration(processedOrder.startTime, processedOrder.endTime);
          // 保存小时和分钟信息
          processedOrder.durationHours = durationInfo.hours;
          processedOrder.durationMinutes = durationInfo.minutes;
          // 格式化时长文本
          processedOrder.duration = durationInfo.hours !== undefined && durationInfo.minutes !== undefined 
            ? `${durationInfo.hours}小时${durationInfo.minutes > 0 ? durationInfo.minutes + '分钟' : ''}` 
            : "";

          processedOrder.date = dateStr;
          processedOrder.time = timeStr;
        }
        // 判断当前订单状态
        switch (processedOrder.status) {
          case 0:
            processedOrder.statusTxt = '待审核';
            break;
          case 1:
            processedOrder.statusTxt = '报名中';
            break;
          case 2:
            processedOrder.statusTxt = '待选择达人';
            break;
          case 3:
            processedOrder.statusTxt = '进行中';
            break;
          case 4:
            processedOrder.statusTxt = '已完成';
            break;
          case 5:
            processedOrder.statusTxt = '已取消';
            break;
          default:
          processedOrder.statusTxt = '--';
            break;
          }
        return processedOrder;
      });

      // 应用筛选条件
      let filteredOrders = processedOrders;
      console.log("filteredOrders", filteredOrders);

      try {
        // 根据用户角色应用不同的筛选逻辑
        if (userRole === "闲时达人") {
          // 专家用户筛选逻辑
          switch (this.currentFilter) {
            case "all":
              // 所有公开订单和已报名订单
              break;

            case "applied":
              // 只显示已报名的订单
              filteredOrders = processedOrders.filter(
                (order) => order.isApplied === true
              );
              break;

            case "selected":
              // 只显示被选中的订单
              filteredOrders = processedOrders.filter(
                (order) =>
                  order.selectedExpertId === userId || order.isSelected === true
              );
              break;

            case "ongoing":
              // 显示进行中的订单（已被选中且服务尚未完成的订单）
              filteredOrders = processedOrders.filter(
                (order) =>
                  (order.selectedExpertId === userId ||
                    order.isSelected === true) &&
                  order.status !== "completed" &&
                  order.status !== "evaluated" &&
                  order.status !== "cancelled"
              );
              break;

            case "completed":
              // 显示已完成的订单
              filteredOrders = processedOrders.filter(
                (order) =>
                  (order.selectedExpertId === userId ||
                    order.isSelected === true) &&
                  (order.status === "completed" || order.status === "evaluated")
              );
              break;

            default:
              // 默认显示所有订单
              break;
          }
        } else {
          // 普通用户筛选逻辑
          switch (this.currentFilter) {
            case "all":
              // 显示所有由当前用户创建的订单
              // filteredOrders = processedOrders.filter(
              //   (order) => order.creatorId === userId || order.userId === userId
              // );
              break;

            case "pending":
              // 显示待处理的订单（已发布但尚未选择达人或尚未有达人报名）
              filteredOrders = processedOrders.filter(
                (order) =>
                  (order.creatorId === userId || order.userId === userId) &&
                  (order.status === "published" || order.status === "created")
              );
              break;

            case "ongoing":
              // 显示进行中的订单（已选择达人但服务尚未完成）
              filteredOrders = processedOrders.filter(
                (order) =>
                  (order.creatorId === userId || order.userId === userId) &&
                  order.status === "expertSelected" &&
                  order.status !== "completed" &&
                  order.status !== "evaluated" &&
                  order.status !== "cancelled"
              );
              break;

            case "completed":
              // 显示已完成的订单
              filteredOrders = processedOrders.filter(
                (order) =>
                  (order.creatorId === userId || order.userId === userId) &&
                  (order.status === "completed" || order.status === "evaluated")
              );
              break;

            case "cancelled":
              // 显示已取消的订单
              filteredOrders = processedOrders.filter(
                (order) =>
                  (order.creatorId === userId || order.userId === userId) &&
                  order.status === "cancelled"
              );
              break;

            default:
              // 默认只显示当前用户创建的订单
              filteredOrders = processedOrders.filter(
                (order) => order.creatorId === userId || order.userId === userId
              );
              break;
          }
        }
      } catch (e) {
        console.error("过滤订单时出错:", e);
        // 出错时返回所有订单
      }

      // 根据创建时间倒序排序（最新的订单显示在前）
      try {
        filteredOrders.sort((a, b) => {
          // 获取创建时间，如果不存在则使用当前时间
          const timeA = a.createTime ? new Date(a.createTime).getTime() : 0;
          const timeB = b.createTime ? new Date(b.createTime).getTime() : 0;
          // 降序排序
          return timeB - timeA;
        });
      } catch (e) {
        console.error("排序订单时出错:", e);
      }

      // 判断是否有新的达人报名（对于普通用户）
      if (userRole === "normal") {
        const hasNew = filteredOrders.some(
          (order) =>
            !order.viewedApplications &&
            order.enrollList &&
            order.enrollList.length > 0
        );

        if (hasNew !== this.hasNewApplications) {
          this.hasNewApplications = hasNew;
        }
      }

      console.log(`过滤后订单数量: ${filteredOrders.length}`);
      return filteredOrders;
    },

    // 格式化活动时间（开始时间和结束时间）
    formatActivityTime(startTime, endTime) {
      if (!startTime || !endTime) return "";
      
      try {
        const start = new Date(startTime);
        const end = new Date(endTime);
        
        // 格式化日期部分 (yyyy-MM-dd)
        const startYear = start.getFullYear();
        const startMonth = start.getMonth() + 1;
        const startDay = start.getDate();
        const startDateStr = `${startYear}-${startMonth}-${startDay}`;
        
        const endYear = end.getFullYear();
        const endMonth = end.getMonth() + 1;
        const endDay = end.getDate();
        const endDateStr = `${endYear}-${endMonth}-${endDay}`;
        
        // 格式化时间部分 (HH:mm)
        const startHours = String(start.getHours()).padStart(2, '0');
        const startMinutes = String(start.getMinutes()).padStart(2, '0');
        const startTimeStr = `${startHours}:${startMinutes}`;
        
        const endHours = String(end.getHours()).padStart(2, '0');
        const endMinutes = String(end.getMinutes()).padStart(2, '0');
        const endTimeStr = `${endHours}:${endMinutes}`;
        
        // 检查是否是同一天
        if (startYear === endYear && startMonth === endMonth && startDay === endDay) {
          // 同一天，只显示一个日期
          return `${startDateStr} ${startTimeStr} - ${endTimeStr}`;
        } else {
          // 不同天，显示完整的开始和结束时间
          return `${startDateStr} ${startTimeStr} - ${endDateStr} ${endTimeStr}`;
        }
      } catch (error) {
        console.error("格式化活动时间出错:", error);
        return startTime && endTime ? `${startTime} - ${endTime}` : "";
      }
    },
    // 计算活动时长（返回小时和分钟）
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return { hours: 0, minutes: 0, totalHours: 0 };
      
      try {
        const start = new Date(startTime);
        const end = new Date(endTime);
        
        // 计算时间差（毫秒）
        const diffMs = end.getTime() - start.getTime();
        
        // 总时间（毫秒）
        const totalMs = diffMs;
        
        // 计算小时（整数部分）
        const hours = Math.floor(totalMs / (1000 * 60 * 60));
        
        // 计算剩余的分钟
        const remainingMs = totalMs - (hours * 1000 * 60 * 60);
        const minutes = Math.floor(remainingMs / (1000 * 60));
        
        // 总小时数（带小数）
        const totalHours = totalMs / (1000 * 60 * 60);
        
        return {
          hours,
          minutes,
          totalHours
        };
      } catch (error) {
        console.error("计算活动时长出错:", error);
        return { hours: 0, minutes: 0, totalHours: 0 };
      }
    },

    // 显示订单流程弹窗
    showOrderFlowModal(e) {
      const orderId = e.currentTarget.dataset.id;
      console.log("显示订单流程 - 原始ID:", orderId);

      // 通过安全比较查找订单
      const safeCompare = (a, b) => {
        if (a === b) return true;
        if (a === undefined || b === undefined || a === null || b === null)
          return false;

        const strA = String(a || "");
        const strB = String(b || "");

        return strA === strB;
      };

      // 查找对应订单
      const order = this.orders.find(
        (o) => safeCompare(o.id, orderId) || safeCompare(o.orderId, orderId)
      );

      if (!order) {
        console.error("订单不存在", {
          orderId: orderId,
          当前订单数: this.orders.length,
        });

        uni.showToast({
          title: "订单不存在",
          icon: "none",
        });
        return;
      }

      // 处理订单日志中的时间显示
      if (order.stateLogs && Array.isArray(order.stateLogs)) {
        // 为每条日志添加格式化的时间和状态文本
        order.stateLogs = order.stateLogs.map((log) => {
          // 确保日志有有效的时间字段
          if (!log.time) {
            log.time = new Date().toISOString();
          }

          return {
            ...log,
            formattedTime: formatDateTime(log.time),
            stateText: StateDisplayText[log.state] || log.state,
          };
        });
      }

      // 设置当前选中的订单
      this.currentOrderFlow = order;
      this.showOrderFlowModal_flag = true;
    },

    // 隐藏订单流程弹窗
    hideOrderFlowModal() {
      this.showOrderFlowModal_flag = false;
      this.currentOrderFlow = null;
    },

    // 根据订单状态获取达人状态提示
    getExpertStatusTip: function (item) {
      // 检查订单是否存在
      if (!item) return "";

      // 处理已报名的情况
      if (item.isApplied && !item.selected) {
        return "您已报名该订单，等待用户选择";
      }

      // 检查订单状态和专家状态
      if (item.selected) {
        // 根据专家状态返回不同的提示
        const expertStatus = item.expertStatus || "";

        switch (expertStatus) {
          case OrderState.CHOSEN:
            return "您已被选中为该订单的达人，请确认出发";
          case OrderState.DEPARTURE_CONFIRMED:
            return '您已确认出发，请到达目的地后点击"确认到达"';
          case OrderState.ARRIVAL_CONFIRMED:
            return item.status === OrderState.ORDER_STARTED
              ? '订单已开始，服务完成后请点击"服务完成"'
              : "您已到达，等待用户开始订单";
          case OrderState.SERVICE_COMPLETED:
            return "您已完成服务，等待用户确认完成";
          default:
            if (item.status === OrderState.COMPLETED) {
              return "恭喜您！服务已完成，用户已确认完成！";
            } else if (item.status === OrderState.EVALUATED) {
              return "订单已评价，感谢您的服务！";
            } else if (
              item.selectedExpertId &&
              item.selectedExpertId === app.userId
            ) {
              return "您已被选为该订单的达人，请等待系统确认";
            }
        }
      }

      return "";
    },

    // 获取达人当前状态文本
    getExpertStatusText: function (item) {
      

      // 检查订单是否存在
      if (!item) return "";

      // 检查订单状态
      const status = item.status || "";

      // 根据订单状态返回不同的状态文本
      switch (status) {
        case app.orderState.ORDER_STATE_EXPERT_SELECTED:
          return "待出发";
        case app.orderState.ORDER_STATE_ORDER_STARTED:
          return "已出发";
        case app.orderState.ORDER_STATE_DEPARTURE_CONFIRMED:
          return "已出发";
        case app.orderState.ORDER_STATE_ARRIVAL_CONFIRMED:
          return "已到达";
        case app.orderState.ORDER_STATE_SERVICE_COMPLETED:
          return "服务已完成";
        case app.orderState.ORDER_STATE_COMPLETED:
          return "订单已完成";
        default:
          return "";
      }
    },

    // 迁移旧版专家订单数据到新的专用存储
    migrateExpertOrderData() {
      
      const currentUserId = app.currentUserId;

      if (app.userRole !== "expert") {
        return; // 非专家用户不需要迁移
      }

      console.log(`正在为达人 ${currentUserId} 迁移订单数据...`);

      // 检查专用存储是否已存在
      const expertSpecificKey = `expertAppliedOrders_${currentUserId}`;
      let hasSpecificStorage = false;

      try {
        const specificDataStr = uni.getStorageSync(expertSpecificKey);
        if (specificDataStr) {
          try {
            const specificData = JSON.parse(specificDataStr);
            if (Array.isArray(specificData) && specificData.length > 0) {
              console.log(
                `达人 ${currentUserId} 已有专用存储，包含 ${specificData.length} 个订单`
              );
              hasSpecificStorage = true;
            }
          } catch (e) {
            console.error(`解析专用存储失败:`, e);
          }
        }
      } catch (e) {
        console.error(`检查专用存储失败:`, e);
      }

      // 如果已有专用存储，则不需要迁移
      if (hasSpecificStorage) {
        console.log(`达人 ${currentUserId} 已有专用存储，跳过迁移`);
        return;
      }

      // 检查旧的共享存储
      let oldSharedKeys = ["expertAppliedOrders"];
      if (currentUserId === "003") {
        oldSharedKeys.push("expertAppliedOrders_003");
      }

      // 收集属于当前达人的订单
      const expertOrders = [];

      oldSharedKeys.forEach((sharedKey) => {
        try {
          const sharedDataStr = uni.getStorageSync(sharedKey);
          if (sharedDataStr) {
            try {
              const sharedData = JSON.parse(sharedDataStr);
              if (Array.isArray(sharedData) && sharedData.length > 0) {
                console.log(
                  `从 ${sharedKey} 中找到 ${sharedData.length} 个订单`
                );

                // 筛选属于当前达人的订单
                const filtered = sharedData.filter((order) => {
                  return (
                    order.expertId === currentUserId ||
                    (order.enrollList &&
                      order.enrollList.some(
                        (exp) => exp.id === currentUserId
                      ))
                  );
                });

                if (filtered.length > 0) {
                  console.log(
                    `从 ${sharedKey} 中筛选出 ${filtered.length} 个属于达人 ${currentUserId} 的订单`
                  );
                  // 确保每个订单都有expertId字段
                  const markedOrders = filtered.map((order) => ({
                    ...order,
                    expertId: currentUserId,
                  }));
                  expertOrders.push(...markedOrders);
                }
              }
            } catch (e) {
              console.error(`解析 ${sharedKey} 失败:`, e);
            }
          }
        } catch (e) {
          console.error(`读取 ${sharedKey} 失败:`, e);
        }
      });

      // 保存到专用存储
      if (expertOrders.length > 0) {
        try {
          const uniqueOrders = Array.from(
            new Map(
              expertOrders.map((item) => [item.id || item.orderId, item])
            ).values()
          );
          console.log(
            `为达人 ${currentUserId} 保存 ${uniqueOrders.length} 个订单到专用存储`
          );
          uni.setStorageSync(expertSpecificKey, JSON.stringify(uniqueOrders));
          console.log(`达人 ${currentUserId} 订单数据迁移成功`);
        } catch (e) {
          console.error(`保存专用存储失败:`, e);
        }
      } else {
        console.log(`未找到属于达人 ${currentUserId} 的订单，初始化空专用存储`);
        try {
          uni.setStorageSync(expertSpecificKey, JSON.stringify([]));
        } catch (e) {
          console.error(`初始化空专用存储失败:`, e);
        }
      }
    },

    // 初始化筛选状态
    initFilter: function () {
      
      const userRole =
        app.userRole || this.userRole || "normal";

      console.log("初始化订单筛选条件，用户角色:", userRole);

      // 根据用户角色设置不同的默认筛选条件
      let defaultFilter = "all";

      if (userRole === "expert") {
        // 专家用户默认查看"全部"订单
        defaultFilter = "all";
      } else {
        // 普通用户默认查看"全部"订单
        defaultFilter = "all";
      }
      this.currentFilter = defaultFilter;

      return defaultFilter;
    },

    // 格式化日期时间
    formatDateTime: function (dateTimeStr) {
      return formatDateTime(dateTimeStr);
    },
  },
};
</script>

<style scoped>
.container {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

.status-bar {
  display: flex;
  background: #fff;
  padding: 0 20rpx;
  position: sticky;
  top: 0;
  z-index: 100;
}

.status-item {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.status-item.active {
  color: #ff6b6b;
  font-weight: 500;
}

.status-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #ff6b6b;
  border-radius: 2rpx;
}

.order-list {
  padding: 20rpx;
}

.order-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.order-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.order-number {
  font-size: 26rpx;
  color: #999;
  width: calc(100% - 100rpx);
}

.order-status {
  font-size: 26rpx;
  color: #ff6b6b;
}

.order-status.published {
  color: #ff9800;
}

.order-status.expertSelected {
  color: #2196f3;
}

.order-status.orderStarted {
  color: #4caf50;
}

.order-status.completed {
  color: #9c27b0;
}

.order-status.evaluated {
  color: #607d8b;
}

.order-status.selected {
  background-color: #2196f3;
  color: #fff;
}

.order-status.applied {
  background-color: #ff9800;
  color: #fff;
  width: 100rpx;
  text-align: center;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.order-info {
  flex: 1;
  margin-right: 20rpx;
}

.order-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.order-time {
  font-size: 26rpx;
  color: #999;
}

.order-price {
  font-size: 34rpx;
  color: #ff6b6b;
  font-weight: 500;
}

/* 订单详情样式 */
.order-detail {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.detail-label {
  color: #666;
  min-width: 140rpx;
}

.detail-value {
  color: #333;
  flex: 1;
}

/* 加载动画样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 进度条样式 */
.order-progress,
.expert-progress {
  margin: 20rpx 0;
  padding: 16rpx;
  background: #fff;
  border-radius: 8rpx;
  border: 1rpx solid #f5f5f5;
}

.progress-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.progress-bar {
  display: flex;
  align-items: center;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.progress-step text {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  text-align: center;
  max-width: 120rpx;
}

.step-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ddd;
}

.progress-line {
  flex: 1;
  height: 2rpx;
  background-color: #ddd;
  position: relative;
  top: -8rpx;
  z-index: 1;
}

.progress-step.completed text {
  color: #333;
}

.progress-step.completed .step-dot {
  background-color: #ff6b6b;
}

.progress-line.completed {
  background-color: #ff6b6b;
}

/* 操作区样式 */
.action-area {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-top: 16rpx;
  color: #ffffff;
}

.action-btn.view-applicants {
  background-color: #3498db;
}

.action-btn.start-order {
  background-color: #2ecc71;
}

.action-btn.complete {
  background-color: #e67e22;
}

.action-btn.evaluate {
  background-color: #9b59b6;
}

/* 达人专用按钮 */
.action-btn.departure {
  background-color: #3498db; /* 蓝色 */
}

.action-btn.arrival {
  background-color: #2980b9; /* 深蓝色 */
}

.action-btn.complete-service {
  background-color: #e67e22; /* 橙色 */
}

.action-btn.apply {
  background-color: #2ecc71; /* 绿色 */
}

/* 达人状态显示 */
.expert-status {
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 0;
}

.select-expert {
  background-color: #2196f3;
  color: #fff;
}

.start-order {
  background-color: #4caf50;
  color: #fff;
}

.confirm-complete {
  background-color: #ff9800;
  color: #fff;
}

.evaluate {
  background-color: #9c27b0;
  color: #fff;
}

.view-experts {
  background-color: #607d8b;
  color: #fff;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 达人选择弹窗 */
.expert-selection-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  box-sizing: border-box;
  max-height: 70%;
  overflow-y: auto;
}

.modal-title {
  font-size: 32rpx;
  text-align: center;
  margin-bottom: 30rpx;
  font-weight: 500;
}

.expert-list {
  max-height: 60vh;
  overflow-y: auto;
}

.expert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.expert-info {
  display: flex;
  align-items: center;
}

.expert-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.expert-name {
  font-size: 28rpx;
  color: #333;
}

.select-btn {
  padding: 8rpx 24rpx;
  font-size: 24rpx;
  background-color: #ff6b6b;
  color: #fff;
  border-radius: 8rpx;
  margin: 0;
}

.modal-footer {
  margin-top: 30rpx;
  text-align: center;
}

.cancel-btn {
  width: 80%;
  padding: 16rpx 0;
  background-color: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  border-radius: 8rpx;
}

/* 新增达人状态栏样式 */
.expert-status-bar {
  display: flex;
  align-items: center;
  padding: 16rpx;
  margin: 12rpx 0;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.expert-status-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
}

.expert-status-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 通知红点样式 */
.notification-dot {
  position: relative;
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ff0000;
  margin-right: 8rpx;
}

/* 新增达人按钮样式 */
.action-btn.view-experts {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2196f3;
  color: #fff;
}

/* 等待达人到达提示 */
.waiting-tip {
  font-size: 26rpx;
  color: #ff9800;
  background-color: #fff8e1;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  border: 1rpx dashed #ffb74d;
  margin: 10rpx 0;
  text-align: center;
  display: inline-block;
}

/* 查看流程详情按钮 */
.view-flow-btn {
  font-size: 24rpx;
  color: #1890ff;
  float: right;
  font-weight: normal;
}

.order-progress,
.expert-progress {
  position: relative;
  cursor: pointer;
}

.order-progress:active,
.expert-progress:active {
  opacity: 0.8;
}

/* 订单流程弹窗 */
.order-flow-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.flow-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.flow-modal-content {
  position: relative;
  width: 90%;
  max-height: 80%;
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  z-index: 1002;
  overflow-y: auto;
}

.flow-modal-title {
  font-size: 36rpx;
  color: #333;
  text-align: center;
  font-weight: 500;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}

.flow-entry {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 30rpx;
}

.flow-entry::before {
  content: "";
  position: absolute;
  left: 10rpx;
  top: 0;
  bottom: 0;
  width: 2rpx;
  background: #e8e8e8;
}

.flow-entry:last-child::before {
  bottom: 50%;
}

.flow-entry::after {
  content: "";
  position: absolute;
  left: 6rpx;
  top: 16rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background: #e8e8e8;
}

.flow-entry.completed::after {
  background: #52c41a;
}

.flow-entry.current::after {
  background: #1890ff;
  width: 14rpx;
  height: 14rpx;
  left: 4rpx;
}

.flow-time {
  font-size: 24rpx;
  color: #999;
  min-width: 200rpx;
}

.flow-info {
  flex: 1;
  padding-left: 20rpx;
}

.flow-state {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.flow-remark {
  font-size: 26rpx;
  color: #666;
}

.flow-role {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.flow-role.user {
  color: #1890ff;
}

.flow-role.expert {
  color: #52c41a;
}

.flow-role.system {
  color: #faad14;
}

.flow-modal-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

/* 达人状态提示样式 */
.expert-status-tip {
  margin: 15rpx 0;
  padding: 15rpx 20rpx;
  background-color: #f0f8ff;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  border-left: 6rpx solid #1890ff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 已完成订单的特殊样式 */
.expert-status-tip.completed {
  background-color: #f0fff0;
  border-left-color: #52c41a;
}

.expert-status-tip.completed .tip-icon {
  color: #52c41a;
}

/* 为完成状态添加动画效果 */
@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.complete-animation {
  animation: pulse 2s infinite;
}

.tip-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
  color: #1890ff;
}

.tip-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

/* 特殊状态下的文本样式 */
.expert-status-tip.completed .tip-text {
  color: #389e0d;
  font-weight: 500;
}

/* 达人状态头部样式 */
.expert-status-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 10rpx;
  background-color: #f0f9ff;
  border-radius: 6rpx;
}

.expert-status-label {
  font-size: 28rpx;
  color: #666;
}

.expert-status-value {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: bold;
}

.action-btn.view-evaluation {
  background-color: #9c27b0; /* 紫色 */
}

/* 用户状态提示样式 */
.user-status-tip {
  border-left-color: #1890ff;
  background-color: #f0f8ff;
}

.user-status-tip .tip-icon {
  color: #1890ff;
}

.user-status-tip.completed {
  background-color: #f0fff0;
  border-left-color: #52c41a;
}

.user-status-tip.completed .tip-icon {
  color: #52c41a;
}

/* 订单卡片主要样式 */
.order-card {
  position: relative;
  border-radius: 24rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 32rpx;
  overflow: hidden;
  border-left: 8rpx solid #ff9800;
}

/* 灰色订单卡片样式 - 用于接单失败的订单 */
.order-card.order-failed {
  border-left: 8rpx solid #cccccc;
  background-color: #f9f9f9;
  opacity: 0.8;
}

/* 高亮订单卡片样式 - 用于已被选中的订单 */
.order-card.order-selected {
  border-left: 8rpx solid #4caf50;
  background-color: #f8fff8;
  box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.15);
}

/* 添加被选中达人信息的样式 */
.selected-expert-info {
  margin: 15rpx 0;
  padding: 15rpx 20rpx;
  background-color: #f8f9ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #4a90e2;
}

.selected-expert-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.selected-expert-content {
  display: flex;
  align-items: center;
}

.selected-expert-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  border: 2rpx solid #eee;
}

.selected-expert-details {
  flex: 1;
}

.selected-expert-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.selected-expert-id {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-top: 6rpx;
}

.expert-selection-time {
  font-size: 24rpx;
  color: #999;
  margin-left: auto;
  text-align: right;
  flex-shrink: 0;
}

/* 订单发布者信息的样式 */
.order-creator-info {
  margin: 15rpx 0;
  padding: 15rpx 20rpx;
  background-color: #fff8f0;
  border-radius: 12rpx;
  border-left: 4rpx solid #ff9800;
}

.order-creator-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.order-creator-content {
  display: flex;
  align-items: center;
}

.order-creator-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  border: 2rpx solid #eee;
}

.order-creator-details {
  flex: 1;
}

.order-creator-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.order-creator-id {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-top: 6rpx;
}

.order-selection-time {
  font-size: 24rpx;
  color: #999;
  margin-left: auto;
  text-align: right;
  flex-shrink: 0;
}
</style>
