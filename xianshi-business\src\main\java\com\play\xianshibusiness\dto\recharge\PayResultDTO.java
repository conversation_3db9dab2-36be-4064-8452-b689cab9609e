package com.play.xianshibusiness.dto.recharge;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支付结果DTO
 */
@Data
@ApiModel("支付结果DTO")
public class PayResultDTO {

    @ApiModelProperty(value = "订单号")
    private String orderNo;
    
    @ApiModelProperty(value = "支付链接")
    private String payUrl;
    
    @ApiModelProperty(value = "支付二维码")
    private String qrCode;
    
    @ApiModelProperty(value = "支付方式")
    private String payType;
    
    @ApiModelProperty(value = "支付状态")
    private String status;
    
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;
} 