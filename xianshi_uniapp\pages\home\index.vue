<template>
  <view class="container">
    <!-- 引入字体图标 -->
    <view class="icon-import">
      <text class="fas fa-home"></text>
      <text class="fas fa-user-tie"></text>
      <text class="fas fa-plus"></text>
      <text class="far fa-comments"></text>
      <text class="fas fa-user"></text>
    </view>
    <!-- 顶部背景 -->
    <view class="header">
      <image
        class="header-bg"
        src="../../static/images/item.png"
        mode="aspectFill"
      />
      <view class="header-content">
        <text class="title-small">闲时有伴 2025新版本上线！</text>
        <text class="title-large">2025与你同行 不再孤单！</text>
      </view>
    </view>

    <!-- 热门活动推荐 -->
    <view class="section-title">
      <text class="title-text">热门活动推荐</text>
    </view>
    <scroll-view class="activity-list" scroll-x enable-flex>
      <view class="activity-item">
        <view class="activity-image-container">
          <image
            src="https://ai-public.mastergo.com/ai/img_res/f9f96db8c75bb9a3796f89b787483315.jpg"
            mode="aspectFill"
          />
        </view>
        <view class="activity-info">
          <view class="activity-title">冬季温泉之旅</view>
          <view class="activity-desc">享受温泉浴，放松身心</view>
        </view>
      </view>
      <view class="activity-item">
        <view class="activity-image-container">
          <image
            src="https://ai-public.mastergo.com/ai/img_res/d9ceb72046b725e7fac5c92a88a5d16f.jpg"
            mode="aspectFill"
          />
        </view>
        <view class="activity-info">
          <view class="activity-title">茶艺品鉴会</view>
          <view class="activity-desc">感受传统茶道文化</view>
        </view>
      </view>
    </scroll-view>

    <!-- 达人推荐 -->
    <view class="expert">
      <view class="section-title-row">
        <view class="section-title">已发布的订单</view>
        <view class="clear-orders" @tap="clearPublishedOrders">清除订单</view>
      </view>
      <view class="section-subtitle">您发布的订单将在这里显示</view>

      <!-- 城市选择 -->
      <view class="filter-header">
        <scroll-view class="city-list" scroll-x enable-flex>
          <view
            v-for="(item, index) in orderFilter"
            :key="index"
            :class="['city-item', currentCity === index ? 'active' : '']"
            @tap="switchCity"
            :data-index="index"
            :data-city="item"
          >
            {{ item }}
          </view>
        </scroll-view>
        <view
          :class="['filter-icon', showFilter ? 'active' : '']"
          @tap="toggleFilter"
        >
          <text>筛选</text>
          <text class="filter-count" v-if="filterCount > 0">{{
            filterCount
          }}</text>
        </view>
      </view>

      <!-- 筛选面板 -->
      <view class="filter-panel" v-if="showFilter">
        <view class="filter-section">
          <view class="filter-title">性别要求</view>
          <view class="filter-options">
            <view
              :class="[
                'filter-option',
                selectedGender === 'all' ? 'selected' : '',
              ]"
              @tap="selectGender"
              data-gender="all"
            >
              不限
            </view>
            <view
              :class="[
                'filter-option',
                selectedGender === 'male' ? 'selected' : '',
              ]"
              @tap="selectGender"
              data-gender="male"
            >
              男
            </view>
            <view
              :class="[
                'filter-option',
                selectedGender === 'female' ? 'selected' : '',
              ]"
              @tap="selectGender"
              data-gender="female"
            >
              女
            </view>
          </view>
        </view>

        <view class="filter-section">
          <view class="filter-title">活动类型</view>
          <view class="filter-options">
            <view
              :class="[
                'filter-option',
                selectedActivity === 'all' ? 'selected' : '',
              ]"
              @tap="selectActivity"
              data-activity="all"
            >
              全部
            </view>
            <view
              :class="[
                'filter-option',
                selectedActivity === '休闲娱乐' ? 'selected' : '',
              ]"
              @tap="selectActivity"
              data-activity="休闲娱乐"
            >
              休闲娱乐
            </view>
            <view
              :class="[
                'filter-option',
                selectedActivity === '运动' ? 'selected' : '',
              ]"
              @tap="selectActivity"
              data-activity="运动"
            >
              运动
            </view>
            <view
              :class="[
                'filter-option',
                selectedActivity === '游戏' ? 'selected' : '',
              ]"
              @tap="selectActivity"
              data-activity="游戏"
            >
              游戏
            </view>
            <view
              :class="[
                'filter-option',
                selectedActivity === '商务' ? 'selected' : '',
              ]"
              @tap="selectActivity"
              data-activity="商务"
            >
              商务
            </view>
          </view>
        </view>

        <view class="filter-buttons">
          <view class="filter-reset" @tap="resetFilter">重置</view>
          <view class="filter-apply" @tap="applyFilter">应用筛选</view>
        </view>
      </view>

      <!-- 有订单时显示 -->
      <block v-if="currentOrders.length > 0">
        <view class="order-list">
          <view
            class="order-item"
            v-for="item in currentOrders"
            :key="item.id"
            @tap="goToOrderDetail"
            :data-id="item.id || item.orderId"
          >
            <!-- 订单状态标签 -->
            <view class="order-status-tags">
              <!-- 过期标签 -->
              <view class="expired-tag" v-if="item.isExpired">
                <text class="expired-icon">⚠️</text>
                <text class="expired-text">已过期</text>
              </view>
              
              <!-- 即时/预约订单标签 -->
              <view class="instant-order-tag" :class="{'appointment-order-tag': item.orderType === '预约订单', 'expired-order': item.isExpired}" v-if="!item.isExpired">
                <text class="instant-icon">{{ item.orderType === '即时订单' ? '⚡' : '📅' }}</text>
                <text class="instant-text">{{ item.orderType }}</text>
              </view>

              <!-- 倒计时标签 -->
              <view class="countdown-tag" v-if="item.countdown && !item.isExpired">
                <text class="countdown-icon">⏰</text>
                <text class="countdown-text">{{ item.countdown }}</text>
              </view>

               <!-- 已报名标签 -->
              <view class="enrolled-tag" v-if="item.hasEnrolled && userRole === '闲时达人' && !item.isExpired">
                <text class="enrolled-text">已报名</text>
              </view>
            </view>

            <!-- 订单头部 - 发布者信息和订单类型 -->
            <view class="order-header-row">
              <view class="order-type-info">
                <text class="order-tag">{{ item.typeName }}</text>
                <text class="order-connector" v-if="item.subType">></text>
                <text class="order-subtype" v-if="item.subType">{{
                  item.subType
                }}</text>
              </view>
            </view>

            <!-- 订单详情 -->
            <view class="order-content-row" :class="{'expired-content': item.isExpired}">
              <!-- 活动时间 -->
              <view class="order-time">
                <text class="time-label">活动时间：</text>
                <text class="time-value">{{ formatActivityTime(item.startTime, item.endTime) }}</text>
              </view>

              <!-- 活动时长 -->
              <view class="order-duration-row">
                <text class="duration-label">活动时长：</text>
                <text class="duration-value">{{ formatDuration(item.duration) }}</text>
              </view>

              <!-- 参与人数和小时费用并排 -->
              <view class="order-flex-row">
                <view class="order-people-row">
                  <text class="people-label">参与人数：</text>
                  <text class="people-value">{{ item.personCount || '待定' }}{{ item.personCount ? '人' : '' }}</text>
                </view>

                <!-- 小时价格 (右侧显示) -->
                <view class="hourly-rate">
                  <text class="rate-value">¥{{ item.hourlyRate }}</text>
                  <text class="rate-unit">/小时</text>
                </view>
              </view>

              <!-- 性别要求 -->
              <view class="order-gender-row">
                <text class="gender-label">性别要求：</text>
                <text class="gender-value">{{ formatGender(item.sexRequire) }}</text>
              </view>

              <!-- 发布者信息 -->
              <view class="publisher-row">
                <view class="publisher-info-container">
                  <image
                    class="publisher-avatar"
                    :src="item?.memberAvatar"
                    mode="aspectFill"
                  ></image>
                  <view class="publisher-name-beside">{{
                    item.memberName
                  }}</view>
                </view>

                <!-- 添加时间和数据统计标签 -->
                <view class="order-stats">
                  <view class="publish-time">
                    <text class="time-icon">🕒</text>
                    <text>{{ item.publishTime }}</text>
                  </view>
                  <view class="stats-divider">|</view>
                  <view class="applicants-display">
                    <!-- 报名人员头像轮播 -->
                    <view class="applicants-avatars" v-if="item.enrollList && item.enrollList.length > 0">
                      <!-- 显示前3个报名人员头像 -->
                      <view class="avatar-container" v-for="(enroll, index) in item.enrollList.slice(0, 3)" :key="index">
                        <image 
                          class="applicant-avatar" 
                          :src="enroll.talentAvatar || enroll.avatar || '/static/images/default-avatar.png'" 
                          mode="aspectFill"
                        ></image>
                      </view>
                      <!-- 超过3人显示+N -->
                      <view class="more-count" v-if="item.enrollList.length > 3">
                        <text class="more-text">+{{ item.enrollList.length - 3 }}</text>
                      </view>
                      <!-- 报名人数文字 -->
                      <text class="enroll-count-text">{{ item.enrollList.length }}人报名</text>
                    </view>
                    <!-- 无人报名时显示 -->
                    <view class="no-applicants" v-else>
                      <text class="applicants-icon">👤</text>
                      <text>{{ item.enrollCount || 0 }}人报名</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 订单地址 -->
            <view class="order-address">
              <text class="address-icon">📍</text>
              <text class="address-text">{{ item.address }}</text>
              <view class="check-detail-btn">查看详情</view>
            </view>
          </view>
        </view>
      </block>

      <!-- 无订单时显示 -->
      <block v-else>
        <view class="no-data">
          <text>暂无已发布的订单</text>
          <text class="tip">发布订单后将在这里显示</text>
        </view>
      </block>
    </view>
  </view>

  <!-- 时间选择器蒙层 -->
  <view class="picker-mask" v-if="showTimePicker_flag" @tap="hideTimePicker"></view>

  <!-- 时间选择器 -->
  <view class="picker-container" v-if="showTimePicker_flag">
    <view class="picker-header">
      <view class="picker-title">选择时长</view>
      <view class="picker-close" @tap="hideTimePicker">完成</view>
    </view>
    <picker-view
      class="time-picker-view"
      :value="timeValue"
      @change="onTimeChange"
      :indicator-style="indicatorStyle"
      :mask-style="maskStyle"
    >
      <picker-view-column>
        <view
          v-for="(item, index) in timeRange[0].values"
          :key="index"
          class="picker-column-item"
        >
          {{ item }}
        </view>
      </picker-view-column>
    </picker-view>
  </view>

  <!-- 日期选择器弹窗 -->
  <view class="picker-mask" v-if="showDatePicker_flag" @tap="hideDatePicker"></view>
  <view class="picker-container" v-if="showDatePicker_flag">
    <view class="picker-header">
      <view class="picker-title">选择日期</view>
      <view class="picker-close" @tap="hideDatePicker">完成</view>
    </view>
    <picker-view
      class="date-picker-view"
      :value="dateValue"
      @change="onDateChange"
      indicator-style="height: 88rpx;"
    >
      <picker-view-column>
        <view
          v-for="(item, index) in dateRange"
          :key="index"
          class="picker-column-item"
        >
          {{ item }}
        </view>
      </picker-view-column>
    </picker-view>
  </view>

  <!-- 时间段选择器蒙层 -->
  <view
    class="picker-mask"
    v-if="showTimeSlotPicker_flag"
    @tap="hideTimeSlotPicker"
  ></view>

  <!-- 时间段选择器 -->
  <view class="picker-container" v-if="showTimeSlotPicker_flag">
    <view class="picker-header">
      <view class="picker-title">选择时间段</view>
      <view class="picker-close" @tap="hideTimeSlotPicker">完成</view>
    </view>
    <picker-view
      class="time-slot-picker"
      :value="timeSlotValue"
      @change="onTimeSlotChange"
      indicator-style="height: 88rpx;"
    >
      <picker-view-column>
        <view
          v-for="(item, index) in timeSlotRange"
          :key="index"
          class="picker-column-item"
        >
          {{ item }}
        </view>
      </picker-view-column>
    </picker-view>
  </view>
<Tabbar currentPath="/pages/home/<USER>"></Tabbar>
</template>

<script>
import { getCurrentMemberInfo } from '../../api/index.js';
const app = getApp().globalData;

export default {
  data() {
    return {
      duration: 3,
      showTimePicker_flag: false,
      showTimeSlotPicker_flag: false,
      timeValue: [0], // 默认选中3小时
      timeSlotValue: [0],
      timeSlotRange: [],
      timeRange: [
        {
          values: ["3小时", "4小时", "5小时", "6小时", "7小时", "8小时"],
        },
      ],
    
    // 检查订单是否过期（结束时间已过且订单未开始进行）
    isOrderExpired(startTime, endTime, status) {
      if (!endTime) return false;
      
      try {
        const now = new Date();
        const end = new Date(endTime);
        const start = new Date(startTime);
        
        // 如果当前时间已超过结束时间，且订单还未开始（状态为发布中），则视为过期
        return now > end && (status === 2 || status === 'PUBLISHED'); // 2对应PUBLISHED状态
      } catch (error) {
        console.error("检查订单过期出错:", error);
        return false;
      }
    },
    
    // 获取订单倒计时
    getOrderCountdown(startTime) {
      if (!startTime) return null;
      
      try {
        const start = new Date(startTime);
        const now = new Date();
        const timeDiff = start.getTime() - now.getTime();
        
        if (timeDiff <= 0) {
          return "已开始";
        }
        
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        
        if (days > 0) {
          return `${days}天${hours}小时${minutes}分钟`;
        } else if (hours > 0) {
          return `${hours}小时${minutes}分钟`;
        } else {
          return `${minutes}分钟`;
        }
      } catch (error) {
        console.error("计算倒计时出错:", error);
        return null;
      }
    },
      currentCity: 0,
      orderFilter: ["全部", "同城", "多人", "推荐"],
      userLocation: "成都", // 用户当前所在城市，实际应通过定位获取
      // 筛选相关
      showFilter: false,
      filterCount: 0,
      selectedGender: "all",
      selectedActivity: "all",
      filterOptions: {
        gender: "all",
        activity: "all",
      },
      expertsData: {
        成都: [
          {
            id: 1,
            avatar:
              "https://ai-public.mastergo.com/ai/img_res/2a6010a1d0a7f23005dc9ae15fdd6f66.jpg",
            name: "安琪",
            gender: "女",
            age: "26",
            constellation: "天蝎座",
            height: "168cm",
            weight: "48kg",
            tags: ["旅行达人", "美食博主", "摄影达人"],
          },
        ],
        北京: [
          {
            id: 2,
            avatar:
              "https://ai-public.mastergo.com/ai/img_res/2a6010a1d0a7f23005dc9ae15fdd6f66.jpg",
            name: "小明",
            gender: "男",
            age: "28",
            constellation: "双子座",
            height: "180cm",
            weight: "70kg",
            tags: ["摄影达人", "户外运动", "咖啡达人"],
          },
        ],
        // ... 其他城市数据
      },
      currentExperts: [],
      indicatorStyle: "height: 88rpx;", // 添加指示器样式
      maskStyle: "background-color: transparent;", // 设置蒙版透明
      selectedAddress: "", // 添加选择的地址
      locationArea: "", // 省市区
      locationAddress: "", // 详细地址
      showDatePicker_flag: false,
      dateValue: [0],
      dateRange: [], // 日期范围
      publishedOrders: [], // 添加订单数组
      filteredOrders: [], // 添加过滤后的订单数组
      currentOrders: [], // 当前显示的订单列表
      demoOrders: [], // 保存原始数据用于筛选
      date: "",
      time: "14:00 - 17:00",
      startHour: 14,
      startMinute: 0,
      pagination: {
        total: 0,
        pages: 0,
        current: 1,
        size: 10
      },
      userRole: '', // 用户角色
      reverseGeocodingTimer: null // 防抖定时器
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function (options) {
    // 更新tabbar激活状态
    uni.$emit('updateTabbar', '/pages/home/<USER>');
    
    // 获取用户角色
    await this.getUserRole();
    
    // 初始化日期范围
    this.initDateRange();
    // 初始化时间段选项
    this.generateTimeSlots();
    // 初始化时间范围显示
    this.updateTimeRange();
    // 初始化加载成都数据
    this.loadCityExperts(0, "成都");
    this.loadPublishedOrders(); // 加载订单数据

    // 设置数据已加载标志
    this.ordersLoaded = true;
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 确保每次显示页面时tabbar状态正确
    uni.$emit('updateTabbar', '/pages/home/<USER>');
    
    if (typeof this.getTabBar === "function" && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0,
      });
    }

    // 检查是否有订单更新标志
    const orderUpdated = uni.getStorageSync("orderUpdated");
    if (orderUpdated === true) {
      this.loadPublishedOrders(); // 重新加载订单数据
      uni.removeStorageSync("orderUpdated"); // 清除更新标志
    } else if (!this.ordersLoaded) {
      this.loadPublishedOrders(); // 如果数据未加载，则加载订单数据
      this.ordersLoaded = true;
    }
    
    // 处理从地图选择页面返回的位置数据
    const selectedLocation = uni.getStorageSync('selectedLocation');
    if (selectedLocation) {
      console.log('从地图选择页面获取到位置数据:', selectedLocation);
      
      // 更新位置信息
      this.locationArea = selectedLocation.district || selectedLocation.city || '';
      this.locationAddress = selectedLocation.address || '';
      this.currentCity = selectedLocation.city || '';
      this.selectedAddress = selectedLocation;
      
      // 清除存储的位置数据
      uni.removeStorageSync('selectedLocation');
      
      uni.showToast({
        title: '位置选择成功',
        icon: 'success'
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 页面隐藏时重置加载标志，确保返回时可以刷新
    this.ordersLoaded = false;
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {}
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */,
  onPullDownRefresh() {
    // 下拉刷新时重新加载订单数据
    this.loadPublishedOrders();
    uni.stopPullDownRefresh();
  },
  methods: {
    // 清除已发布的订单数据
    clearPublishedOrders: function () {
      uni.showModal({
        title: "确认清除",
        content: "确定要清除所有已发布的订单吗？",
        success: (res) => {
          if (res.confirm) {
            // 清除本地存储
            uni.removeStorageSync("publishedOrders");

            // 清除全局数据
            const app = getApp();
            app.globalData.publishedOrders = [];

            // 重新初始化示例订单
            app.initSampleOrders();

            // 刷新页面数据
            this.loadPublishedOrders();

            uni.showToast({
              title: "已重置订单数据",
              icon: "success",
            });
          }
        },
      });
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
      // 上拉加载更多
      this.loadMoreOrders();
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {},

    // 显示时间选择器
    showTimePicker() {
      const initialIndex = this.duration - 3;
      this.timeValue = [initialIndex];
      this.showTimePicker_flag = true;
    },

    // 隐藏时间选择器
    hideTimePicker() {
      this.showTimePicker_flag = false;
    },

    // 时间选择器值变化处理
    onTimeChange(e) {
      const val = e.detail.value[0];
      const hourText = this.timeRange[0].values[val];
      const duration = parseInt(hourText);

      this.duration = duration;
      this.timeValue = [val];
      this.showTimePicker_flag = false;

      // 时长改变后，重新生成时间段选项并更新当前时间段
      this.generateTimeSlots();
      this.updateCurrentTimeSlot();
    },

    // 更新时间范围显示
    updateTimeRange() {
      const selectedDate = this.dateRange[this.dateValue[0]];
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();

      // 设置默认开始时间为当前时间的下一个半小时
      let startHour = currentHour;
      let startMinute = 0;

      if (currentMinute < 30) {
        startMinute = 30;
      } else {
        startHour = currentHour + 1;
        startMinute = 0;
      }

      // 计算结束时间
      const endTime = new Date();
      endTime.setHours(startHour + this.duration);
      endTime.setMinutes(startMinute);
      const endHour = endTime.getHours();
      const endMinute = endTime.getMinutes();

      // 格式化显示的时间
      const startTimeDisplay = `${startHour
        .toString()
        .padStart(2, "0")}:${startMinute.toString().padStart(2, "0")}`;
      const endTimeDisplay = `${endHour.toString().padStart(2, "0")}:${endMinute
        .toString()
        .padStart(2, "0")}`;

      this.timeDisplay = `${selectedDate} | ${startTimeDisplay} - ${endTimeDisplay}`;
      this.startHour = startHour;
      this.startMinute = startMinute;
    },

    // 切换筛选
    switchCity: function (e) {
      const index = e.currentTarget.dataset.index;
      const category = e.currentTarget.dataset.city;

      console.log("index", index, category);

      if (this.currentCity === index) {
        return; // 如果点击当前分类，不做处理
      }

      uni.showLoading({
        title: "切换中...",
      });

      this.loadCityOrders(index, category);
    },

    // 根据城市加载订单
    loadCityOrders: function (index, category) {
      setTimeout(() => {
        // 获取所有订单
        let orders = this.publishedOrders || [];

        // 根据不同分类筛选订单
        switch (category) {
          case "同城":
            // 筛选与用户同城的订单
            orders = orders.filter((order) => {
              // 检查地址中是否包含用户所在城市
              return order.address && order.address.includes(this.userLocation);
            });
            break;
          case "多人":
            // 筛选参与人数大于1的订单
            orders = orders.filter((order) => order.personCount > 1);
            break;
          case "推荐":
            // 根据某些条件推荐订单（这里简单地按发布时间排序）
            orders = [...orders].sort((a, b) => {
              return new Date(b.createTime) - new Date(a.createTime);
            });
            break;
          default:
            // 全部订单，不做筛选
            break;
        }

        this.currentCity = index;
        this.filteredOrders = orders;
        this.currentOrders = orders;

        // 如果有筛选条件，则应用筛选
        if (this.filterCount > 0) {
          this.applyFilterToCurrentOrders();
        }

        uni.hideLoading();
      }, 300);
    },

    // 切换筛选面板显示
    toggleFilter: function () {
      this.showFilter = !this.showFilter;
    },

    // 选择性别
    selectGender: function (e) {
      const gender = e.currentTarget.dataset.gender;
      this.selectedGender = gender;
    },

    // 选择活动类型
    selectActivity: function (e) {
      const activity = e.currentTarget.dataset.activity;
      this.selectedActivity = activity;
    },

    // 重置筛选
    resetFilter: function () {
      this.selectedGender = "all";
      this.selectedActivity = "all";
    },

    // 应用筛选
    applyFilter: function () {
      // 计算选中的筛选选项数量
      let count = 0;
      if (this.selectedGender !== "all") count++;
      if (this.selectedActivity !== "all") count++;

      // 保存筛选选项
      const filterOptions = {
        gender: this.selectedGender,
        activity: this.selectedActivity,
      };

      this.filterCount = count;
      this.filterOptions = filterOptions;
      this.showFilter = false;

      // 应用筛选条件到当前订单列表
      this.applyFilterToCurrentOrders();
    },

    // 将筛选条件应用到当前订单列表
    applyFilterToCurrentOrders: function () {
      let orders = this.filteredOrders || [];
      const { gender, activity } = this.filterOptions;

      // 应用性别筛选
      if (gender !== "all") {
        const genderValue = gender === "male" ? "男" : "女";
        orders = orders.filter((order) => order.sexRequire === genderValue);
      }

      // 应用活动类型筛选
      if (activity !== "all") {
        orders = orders.filter((order) => order.typeName === activity);
      }

      // 更新显示的订单列表
      this.currentOrders = orders;

      // 如果筛选后没有数据，提示用户
      if (orders.length === 0) {
        uni.showToast({
          title: "没有符合条件的订单",
          icon: "none",
        });
      }
    },

    // 加载城市达人数据
    loadCityExperts: function (index, city) {
      setTimeout(() => {
        this.currentCity = index;
        this.currentExperts = this.expertsData[city] || [];
        uni.hideLoading();
      }, 300);
    },

    // 选择地址
    chooseLocation() {
      // 跳转到地图选择页面
      uni.navigateTo({
        url: '/pages/map-selector/map-selector',
        success: () => {
          console.log('跳转到地图选择页面成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          // 如果跳转失败，回退到原生方法
          this.chooseLocationFallback();
        }
      });
    },
    
    // 原生地址选择方法（作为备用）
    chooseLocationFallback() {
      // 先尝试获取用户位置权限
      uni.getSetting({
        success: (res) => {
          // 检查是否有位置权限
          if (!res.authSetting["scope.userLocation"]) {
            // 没有权限，请求授权
            uni.authorize({
              scope: "scope.userLocation",
              success: () => {
                // 授权成功后调用位置选择
                this.openLocationChooser();
              },
              fail: (err) => {
                // 用户拒绝授权，弹出提示
                uni.showModal({
                  title: "提示",
                  content:
                    "需要您的位置权限才能使用此功能，是否前往设置开启权限？",
                  confirmText: "去设置",
                  cancelText: "取消",
                  success: (res) => {
                    if (res.confirm) {
                      uni.openSetting();
                    } else {
                      // 用户取消，使用默认位置
                      this.locationArea = "成都市龙泉驿区";
                      this.locationAddress = "默认位置";
                    }
                  },
                });
              },
            });
          } else {
            // 已有权限，直接调用位置选择
            this.openLocationChooser();
          }
        },
        fail: () => {
          // 获取设置失败，使用默认位置
          this.locationArea = "成都市龙泉驿区";
          this.locationAddress = "默认位置";
        },
      });
    },

    // 打开位置选择器
    openLocationChooser() {
      uni.chooseLocation({
        success: (res) => {
          const address = res.address;
          const name = res.name;

          // 处理地址显示
          let area = "";
          let detail = "";
          let city = "";

          // 解析地址
          if (address) {
            // 尝试提取城市信息
            const cityMatch = address.match(/[^省]+市/);
            if (cityMatch && cityMatch[0]) {
              city = cityMatch[0].replace("市", "");
            }

            // 假设地址格式为"省份市区详细地址"
            const addressParts = address.split(/[省市区县]/);
            // 提取省市区
            area = address.slice(
              0,
              address.length - addressParts[addressParts.length - 1].length
            );
            // 提取详细地址
            detail = (
              addressParts[addressParts.length - 1] + (name ? ` · ${name}` : "")
            ).trim();
          }

          this.selectedAddress = address + name;
          this.locationArea = area || "成都市龙泉驿区";
          this.locationAddress = detail || name;
          this.currentCity = this.getCityIndex(city || "成都"); // 根据城市名称设置当前选中的城市索引
        },
        fail: (err) => {
          console.log("选择位置失败", err);
          // 选择位置失败时，尝试获取当前位置
          this.getCurrentLocation();
        },
      });
    },

    // 根据城市名获取索引
    getCityIndex(cityName) {
      const index = this.orderFilter.findIndex((city) => city === cityName);
      return index > 0 ? index : 0; // 如果没找到，默认返回"全部"(索引0)
    },

    // 获取当前位置（备选方案）
    getCurrentLocation() {
      uni.getLocation({
        type: "gcj02",
        success: (res) => {
          // 逆地址解析（坐标转地址）
          this.reverseGeocoding(res.latitude, res.longitude);
        },
        fail: (err) => {
          console.log("获取位置失败", err);
          uni.showToast({
            title: "位置获取失败，请检查权限设置",
            icon: "none",
            duration: 2000,
          });

          // 使用默认位置
          this.locationArea = "成都市龙泉驿区";
          this.locationAddress = "默认位置";
          this.currentCity = this.getCityIndex("成都");
        },
      });
    },

    // 逆地址解析
    reverseGeocoding(latitude, longitude) {
      // H5环境下使用JSONP方式调用腾讯地图API避免CORS问题
      // 从manifest.json配置中获取API Key
      const platform = uni.getSystemInfoSync().platform;
      const key = platform === 'h5' ? 
        (window.__uniConfig?.h5?.sdkConfigs?.maps?.qqmap?.key || 'LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP') :
        'LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP';
      
      const callbackName = 'reverseGeocodingCallback' + Date.now();
      
      // 添加防抖机制，避免频繁调用
      if (this.reverseGeocodingTimer) {
        clearTimeout(this.reverseGeocodingTimer);
      }
      
      this.reverseGeocodingTimer = setTimeout(() => {
      
      // 创建全局回调函数
      window[callbackName] = (data) => {
        if (data && data.status === 0 && data.result) {
          const result = data.result;
          const address = result.address;
          const addressComponent = result.address_component;
          
          // 提取城市信息
          const city = addressComponent.city || '成都市';
          const district = addressComponent.district || '';
          const street = addressComponent.street || '';
          const streetNumber = addressComponent.street_number || '';
          
          // 设置位置信息
          this.locationArea = city + district;
          this.locationAddress = street + streetNumber || '当前位置';
          this.currentCity = this.getCityIndex(city.replace('市', ''));
          this.selectedAddress = address;
          
          uni.showToast({
            title: '位置获取成功',
            icon: 'success',
            duration: 2000,
          });
        } else {
          this.useDefaultLocation();
        }
        
        // 清理回调函数和script标签
        delete window[callbackName];
        const script = document.getElementById(callbackName);
        if (script) {
          document.head.removeChild(script);
        }
      };
      
      // 创建JSONP请求
      const script = document.createElement('script');
      script.id = callbackName;
      script.src = `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${key}&get_poi=1&output=jsonp&callback=${callbackName}`;
      script.onerror = () => {
        this.useDefaultLocation();
        delete window[callbackName];
        document.head.removeChild(script);
      };
      
      document.head.appendChild(script);
      }, 300); // 300ms防抖延迟
    },
    
    // 使用默认位置
    useDefaultLocation() {
      this.locationArea = "成都市龙泉驿区";
      this.locationAddress = "默认位置";
      this.currentCity = this.getCityIndex("成都");
      
      uni.showToast({
        title: "已使用默认位置",
        icon: "none",
        duration: 2000,
      });
    },

    // 修改发布订单方法
    publishOrder() {
      // 构建完整地址信息
      let fullLocation = "";
      if (this.selectedAddress) {
        fullLocation = this.selectedAddress;
      } else if (this.locationArea) {
        fullLocation = this.locationArea;
        if (this.locationAddress) {
          fullLocation += this.locationAddress;
        } else {
          fullLocation += "默认位置";
        }
      } else {
        fullLocation = "成都市龙泉驿区默认位置";
      }

      console.log("即将发布订单，信息：", {
        location: fullLocation,
        date: this.date,
        time: this.time,
        duration: this.duration,
        startHour: this.startHour,
        startMinute: this.startMinute,
      });

      // 使用完整URL编码确保参数正确传递
      const url =
        `/pages/publish-demand/publish-demand?` +
        `location=${encodeURIComponent(fullLocation)}` +
        `&date=${encodeURIComponent(this.date)}` +
        `&time=${encodeURIComponent(this.time)}` +
        `&duration=${this.duration}` +
        `&startHour=${this.startHour}` +
        `&startMinute=${this.startMinute}`;

      console.log("跳转URL:", url);

      uni.navigateTo({
        url: url,
      });
    },

    // 初始化日期范围
    initDateRange() {
      const dates = [];
      const today = new Date();

      // 生成未来30天的日期
      for (let i = 0; i < 30; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const month = date.getMonth() + 1;
        const day = date.getDate();
        dates.push(`${month}月${day}日`);
      }

      // 设置默认日期为今天
      const defaultDate = `${today.getMonth() + 1}月${today.getDate()}日`;

      this.dateRange = dates;
      this.date = defaultDate;
      this.dateValue = [0];

      // 初始化时间显示并设置为最早可选时间
      this.initEarliestTimeSlot();
    },

    // 初始化最早可选时间段
    initEarliestTimeSlot() {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();

      // 设置默认时间为当前时间的下一个整点或半点
      let startHour = currentHour;
      let startMinute = 0;

      if (currentMinute < 30) {
        startMinute = 30;
      } else {
        startHour = currentHour + 1;
        startMinute = 0;
      }

      this.startHour = startHour;
      this.startMinute = startMinute;

      // 计算结束时间
      const endTime = new Date();
      endTime.setHours(startHour + this.duration);
      endTime.setMinutes(startMinute);
      let endHour = endTime.getHours();
      const endMinute = endTime.getMinutes();

      // 格式化时间显示
      const startTimeStr = `${startHour
        .toString()
        .padStart(2, "0")}:${startMinute.toString().padStart(2, "0")}`;

      // 处理跨天的情况
      let timeStr;
      if (endHour >= 24) {
        endHour = endHour % 24;
        timeStr = `${startTimeStr} - 次日${endHour
          .toString()
          .padStart(2, "0")}:${endMinute.toString().padStart(2, "0")}`;
      } else {
        timeStr = `${startTimeStr} - ${endHour
          .toString()
          .padStart(2, "0")}:${endMinute.toString().padStart(2, "0")}`;
      }

      this.time = timeStr;

      // 生成时间段选项
      this.generateTimeSlots();
    },

    // 显示日期选择器
    showDatePicker() {
      this.showDatePicker_flag = true;
    },

    // 隐藏日期选择器
    hideDatePicker() {
      this.showDatePicker_flag = false;
    },

    // 日期改变时的处理
    onDateChange(e) {
      const val = e.detail.value[0];
      this.date = this.dateRange[val];
      this.dateValue = [val];
      this.showDatePicker_flag = false;

      // 日期改变后，重新生成时间段选项
      this.generateTimeSlots();
    },

    // 加载已发布的订单
    async loadPublishedOrders(params = {}) {
      console.log("开始从API加载订单数据...");
      
      // 显示加载提示
      uni.showLoading({
        title: '加载订单列表中...'
      });
      
      // 构建查询参数
      const queryParams = {
        pageNum: params.pageNum || 1,
        pageSize: params.pageSize || 10,
        orderDirection: 'desc', // 默认降序排序，最新的在前面
        ...params // 合并其他传入的参数
      };
      console.log("构建查询参数", queryParams);
      
      console.log("Zzz", this.selectedGender, this.selectedActivity);
      // 根据筛选条件添加参数
      // 性别要求
      if (this.selectedGender && this.selectedGender !== 'all') {
        queryParams.gender = this.selectedGender;
      }
      
      if (this.selectedActivity && this.selectedActivity !== 'all') {
        queryParams.activityTypeId = this.selectedActivity;
      }
      
      // 如果是同城筛选，添加地理位置参数
      if (this.currentCity === 1) { // 同城
        queryParams.city = this.userLocation;
      }
      
      console.log("｜请求参数｜:", queryParams);
      
      try {
        // 使用this.$requestHttp调用接口
        let res = await this.$requestHttp.get(app.commonApi.getOrderLIst, {
          data: queryParams
        });
        
        console.log("API请求成功:", res);
        
        if (res.code === 200) {
          // 获取分页信息
          const { records, total, pages, current, size } = res.data || {};
          let publishedOrders = records || [];
          
          // 更新分页信息
          this.pagination = {
            total: total || 0,
            pages: pages || 1,
            current: current || 1,
            size: size || 10
          };
          
          console.log("API返回的订单数量:", publishedOrders.length, "总数:", total);
          
          // 过滤掉标记为hideFromHome的订单和已经选择达人的订单
          // publishedOrders = publishedOrders.filter((order) => {
          //   // 过滤掉hideFromHome标记为true的订单
          //   if (order.hideFromHome === true) {
          //     return false;
          //   }

          //   // 过滤掉状态为expertSelected或更高级别的订单
          //   if (
          //     order.status === "expertSelected" ||
          //     order.status === "orderStarted" ||
          //     order.status === "completed" ||
          //     order.status === "evaluated"
          //   ) {
          //     return false;
          //   }

          //   return true;
          // });

          console.log("过滤后的订单数量:", publishedOrders.length);

          // 格式化订单信息
          const formattedOrders = publishedOrders.map((order) => {
            // 处理日期和时间
            let dateStr = "";
            let timeStr = "";

            // 使用startTime和endTime格式化活动时间
            if (order.startTime && order.endTime) {
              const formattedTime = this.formatActivityTime(order.startTime, order.endTime);
              const timeParts = formattedTime.split(" ");
              if (formattedTime.includes(" - ")) {
                // 分离日期和时间
                if (timeParts.length >= 3) {
                  dateStr = timeParts[0];
                  timeStr = timeParts.slice(1).join(" ");
                } else {
                  timeStr = formattedTime;
                }
              } else {
                timeStr = formattedTime;
              }
              
              // 计算活动时长
              const durationInfo = this.calculateDuration(order.startTime, order.endTime);
              // 保存小时和分钟信息
              order.durationHours = durationInfo.hours;
              order.durationMinutes = durationInfo.minutes;
              // 格式化时长文本
              order.duration = durationInfo.hours !== undefined && durationInfo.minutes !== undefined 
                ? `${durationInfo.hours}小时${durationInfo.minutes > 0 ? durationInfo.minutes + '分钟' : ''}` 
                : "";
                
              // 判断订单类型（即时订单或预约订单）
              order.isInstant = this.isInstantOrder(order.startTime);
              order.orderType = order.isInstant ? "即时订单" : "预约订单";
            }

            // 活动类型
            const type = order.activityTypeName || "";
            const subType = order.serviceSubType || "";

            // 处理发布时间
            let publishTime = "刚刚";
            if (order.createTime) {
              publishTime = this.formatTimeAgo(order.createTime);
            }

            console.log("order.memberAvatar", order.memberAvatar);

            // 检查订单是否过期（结束时间已过且订单未开始）
            const isExpired = this.isOrderExpired(order.startTime, order.endTime, order.status);
            
            // 格式化返回数据，使用后端标准字段名
            return {
              id: order.id,
              orderId: order.orderId,
              typeName: order.typeName || order.activityTypeName || type, // 使用后端的typeName字段
              subType,
              startTime: order.startTime, // 使用后端的startTime字段
              endTime: order.endTime, // 使用后端的endTime字段
              duration: order.duration || "", // 已格式化的时长文本，如"23小时1分钟"
              personCount: order.personCount || 0, // 使用后端的personCount字段
              hourlyRate: order.hourlyRate || 0,
              sexRequire: order.sexRequire, // 使用后端的sexRequire字段
              address: order.address || order.location || "未指定地点",
              
              memberName: order.memberName || "小闲", // 使用后端的memberName字段
              memberAvatar: order.memberAvatar || "/static/images/default-avatar.png", // 使用后端的memberAvatar字段
              enrollCount: order.enrollCount || 0, // 使用后端的enrollCount字段
              publishTime, // 添加发布时间（刚刚、x分钟前等）
              createTime: order.createTime, // 使用后端的createTime字段
              status: order.status, // 保存原始状态码
              statusDesc: order.statusDesc || this.getStatusText(order.status), // 使用后端的statusDesc字段
              
              // 订单类型（即时订单或预约订单）
              isInstant: order.isInstant || false,
              orderType: order.orderType || "预约订单",
              isExpired: isExpired, // 是否过期
              countdown: this.getOrderCountdown(order.startTime), // 倒计时
              
              // 传递enrollList和selectedList字段给格式化后的订单
              enrollList: order.enrollList || [],
              selectedList: order.selectedList || [],
              
              // 检查当前达人用户是否已报名
              hasEnrolled: this.checkUserEnrolled(order)
            };
          });

          // 按发布时间排序，最新的排在前面
          formattedOrders.sort((a, b) => {
            const orderA = publishedOrders.find((o) => o.id === a.id);
            const orderB = publishedOrders.find((o) => o.id === b.id);

            const timeA =
              orderA && orderA.createTime
                ? new Date(orderA.createTime)
                : new Date(0);
            const timeB =
              orderB && orderB.createTime
                ? new Date(orderB.createTime)
                : new Date(0);

            return timeB - timeA;
          });

          console.log("格式化后的订单数量:", formattedOrders.length);

          // 更新数据
          if (queryParams.pageNum > 1) {
            // 如果是加载更多，则追加数据
            this.publishedOrders = [...this.publishedOrders, ...formattedOrders];
            this.filteredOrders = [...this.filteredOrders, ...formattedOrders];
          } else {
            // 如果是首次加载或刷新，则替换数据
            this.publishedOrders = formattedOrders;
            this.filteredOrders = formattedOrders;
          }
          
          // 应用当前筛选条件
          this.applyFilters();
          
          uni.hideLoading(); // 隐藏加载提示
        } else {
          console.error("API请求返回错误状态码:", res);
          uni.hideLoading();
          uni.showToast({
            title: res.message || '获取订单列表失败',
            icon: 'none'
          });
          this.loadPublishedOrdersFromLocal(); // 接口错误时从本地读取
        }
      } catch (error) {
        console.error("API请求失败:", error);
        uni.hideLoading();
        uni.showToast({
          title: '获取订单列表失败',
          icon: 'none'
        });
        this.loadPublishedOrdersFromLocal(); // 请求失败时从本地读取
      }
    },
    
    // 获取订单状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '报名中',
        2: '待选择达人',
        3: '进行中',
        4: '已完成',
        5: '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    
    // 检查当前用户是否已报名
    checkUserEnrolled(order) {
      if (this.userRole !== '闲时达人') {
        return false;
      }
      
      const currentUserId = app.currentUserId;
      if (!currentUserId) {
        return false;
      }
      
      // 检查enrollList中是否包含当前用户
      if (order.enrollList && Array.isArray(order.enrollList)) {
        const isEnrolled = order.enrollList.some(enroll => 
          enroll.memberId === currentUserId
        );
        if (isEnrolled) {
          return true;
        }
      }
      
      // 检查selectedList中是否包含当前用户
      if (order.selectedList && Array.isArray(order.selectedList)) {
        const isSelected = order.selectedList.some(selected => 
          selected.memberId === currentUserId
        );
        if (isSelected) {
          return true;
        }
      }
      
      return false;
     },
     
     // 获取用户角色
     async getUserRole() {
       try {
         // 调用统一的API获取最新用户信息
         const res = await getCurrentMemberInfo();
         if (res && res.code === 200 && res.data) {
           const memberInfo = res.data;
           
           // 获取角色信息
           const currentRoleId = memberInfo.currentRoleId;
           const hasExpertRole = memberInfo.hasExpertRole;
           
           // 根据角色ID设置角色名称
           let roleName = '普通用户';
           if (currentRoleId === '2' || hasExpertRole) {
             roleName = '闲时达人';
           } else if (currentRoleId === '1') {
             roleName = '普通用户';
           }
           
           this.userRole = roleName;
           
           // 更新本地存储和全局数据
           uni.setStorageSync('userRole', roleName);
           uni.setStorageSync('currentRoleId', currentRoleId);
           uni.setStorageSync('hasExpertRole', hasExpertRole);
           
           const app = getApp();
           app.globalData.userRole = roleName;
           app.globalData.currentRoleId = currentRoleId;
           app.globalData.hasExpertRole = hasExpertRole;
           
           console.log('通过API获取用户角色:', this.userRole, 'currentRoleId:', currentRoleId, 'hasExpertRole:', hasExpertRole);
         } else {
           // API调用失败时，尝试从本地存储获取
           this.loadRoleFromStorage();
         }
       } catch (error) {
         console.error('API获取用户角色失败:', error);
         // API调用失败时，尝试从本地存储获取
         this.loadRoleFromStorage();
       }
     },
     
     // 从本地存储加载角色信息
     loadRoleFromStorage() {
       try {
         // 优先从本地存储获取
         const userRole = uni.getStorageSync('userRole');
         if (userRole) {
           this.userRole = userRole;
           console.log('从本地存储获取用户角色:', this.userRole);
           return;
         }
         
         // 从userInfo获取
         const userInfo = uni.getStorageSync('userInfo');
         if (userInfo) {
           const userInfoObj = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo;
           if (userInfoObj.currentRole) {
             this.userRole = userInfoObj.currentRole;
           } else if (userInfoObj.currentRoleName) {
             this.userRole = userInfoObj.currentRoleName;
           }
         }
         
         // 从全局数据获取
         if (!this.userRole) {
           const app = getApp();
           if (app.globalData && app.globalData.userRole) {
             this.userRole = app.globalData.userRole;
           }
         }
         
         // 默认值
         if (!this.userRole) {
           this.userRole = '普通用户';
         }
         
         console.log('从本地存储获取用户角色:', this.userRole);
       } catch (error) {
         console.error('从本地存储获取用户角色失败:', error);
         this.userRole = '普通用户';
       }
     },
     
     // 加载更多订单
    loadMoreOrders() {
      if (this.pagination && this.pagination.current < this.pagination.pages) {
        this.loadPublishedOrders({
          pageNum: this.pagination.current + 1,
          pageSize: this.pagination.size
        });
      }
    },
    
    // 应用筛选条件
    applyFilters() {
      // 根据当前筛选条件过滤订单
      let filtered = [...this.filteredOrders];
      
      // 应用性别筛选
      if (this.selectedGender && this.selectedGender !== 'all') {
        filtered = filtered.filter(order => {
          // 使用后端标准字段名sexRequire
          return order.sexRequire === this.selectedGender;
        });
      }
      
      // 应用活动类型筛选
      if (this.selectedActivity && this.selectedActivity !== 'all') {
        filtered = filtered.filter(order => 
          order.typeName === this.selectedActivity || order.subType === this.selectedActivity
        );
      }
      
      // 更新当前显示的订单
      this.currentOrders = filtered;
    },

    // 从本接口返回数据加载订单数据的备用方法
    loadPublishedOrdersFromLocal: function() {
      console.log("开始从本地存储加载publishedOrders...");
      // 从本地存储获取已发布的订单
      let publishedOrders = uni.getStorageSync("publishedOrders");

      // 确保publishedOrders是数组
      if (!publishedOrders) {
        publishedOrders = [];
      } else if (typeof publishedOrders === "string") {
        try {
          publishedOrders = JSON.parse(publishedOrders);
          console.log("解析后的publishedOrders:", publishedOrders);
        } catch (e) {
          console.error("解析publishedOrders失败:", e);
          publishedOrders = [];
        }
      }

      // 确保是数组类型
      if (!Array.isArray(publishedOrders)) {
        publishedOrders = [];
      }

      // 从全局数据获取订单信息作为备选
      const app = getApp();
      if (publishedOrders.length === 0 && app.globalData.publishedOrders) {
        publishedOrders = app.globalData.publishedOrders;
      }

      console.log("处理前的订单数量:", publishedOrders.length);

      // 过滤掉标记为hideFromHome的订单和已经选择达人的订单
      publishedOrders = publishedOrders.filter((order) => {
        // 过滤掉hideFromHome标记为true的订单
        if (order.hideFromHome === true) {
          return false;
        }

        // 过滤掉状态为expertSelected或更高级别的订单
        if (
          order.status === "expertSelected" ||
          order.status === "orderStarted" ||
          order.status === "completed" ||
          order.status === "evaluated"
        ) {
          return false;
        }

        return true;
      });

      console.log("过滤后的订单数量:", publishedOrders.length);

      // 格式化订单信息
      const formattedOrders = publishedOrders.map((order) => {
        // 处理日期和时间
        let dateStr = "";
        let timeStr = "";

        if (order.activityTime) {
          const timeArr = order.activityTime.split(" ");
          if (timeArr.length > 1) {
            dateStr = timeArr[0];
            timeStr = timeArr.slice(1).join(" ");
          } else {
            dateStr = order.activityTime;
          }
        } else {
          dateStr = order.date || "";
          timeStr = order.time || "";
        }

        // 性别要求直接使用order.sexRequire字段

        // 活动类型
        const type = order.service || "";
        const subType = order.serviceSubType || "";

        // 处理发布时间
        let publishTime = "刚刚";
        if (order.createTime) {
          publishTime = this.formatTimeAgo(order.createTime);
        }

        // 检查订单是否过期（结束时间已过且订单未开始）
        const isExpired = this.isOrderExpired(order.startTime, order.endTime, order.status);
        
        // 格式化返回数据，使用后端标准字段名
        return {
          id: order.id,
          orderId: order.orderId,
          typeName: order.typeName || type, // 使用后端的typeName字段
          subType,
          startTime: order.startTime, // 使用后端的startTime字段
          endTime: order.endTime, // 使用后端的endTime字段
          duration: order.duration || 3,
          personCount: order.personCount || 0, // 使用后端的personCount字段
          sexRequire: order.sexRequire, // 使用后端的sexRequire字段
          address: order.address || order.location || "未指定地点",
          hourlyRate: order.hourlyRate || order.price || 0,
          memberName: order.memberName || order.publisherName || "小闲", // 使用后端的memberName字段
          memberAvatar: order.memberAvatar || order.publisherAvatar || "/static/images/default-avatar.png", // 使用后端的memberAvatar字段
          enrollCount: order.enrollCount || (order.enrollList ? order.enrollList.length : 0), // 使用后端的enrollCount字段
          createTime: order.createTime, // 使用后端的createTime字段
          statusDesc: order.statusDesc || order.statusText, // 使用后端的statusDesc字段
          // 添加订单类型
          orderType: "预约订单", // 默认为预约订单
          isExpired: isExpired, // 是否过期
          countdown: this.getOrderCountdown(order.startTime) // 倒计时
        };
      });

      // 按发布时间排序，最新的排在前面
      formattedOrders.sort((a, b) => {
        const orderA = publishedOrders.find((o) => o.id === a.id);
        const orderB = publishedOrders.find((o) => o.id === b.id);

        const timeA =
          orderA && orderA.createTime
            ? new Date(orderA.createTime)
            : new Date(0);
        const timeB =
          orderB && orderB.createTime
            ? new Date(orderB.createTime)
            : new Date(0);

        return timeB - timeA;
      });

      console.log("格式化后的订单数量:", formattedOrders.length);

      this.publishedOrders = formattedOrders;
      this.currentOrders = formattedOrders;
      this.filteredOrders = formattedOrders;
    },
    
    // 添加formatTimeAgo方法
    formatTimeAgo: function(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      const diff = Math.floor((now - date) / 1000); // 秒数
      
      if (diff < 60) {
        return '刚刚';
      } else if (diff < 3600) {
        return Math.floor(diff / 60) + '分钟前';
      } else if (diff < 86400) {
        return Math.floor(diff / 3600) + '小时前';
      } else {
        return Math.floor(diff / 86400) + '天前';
      }
    },

    // 跳转到订单详情
    navigateToOrderDetail(e) {
      const orderId = e.currentTarget.dataset.orderId;

      // 检查订单ID是否有效
      if (!orderId || orderId === "undefined" || orderId === "null") {
        console.error("订单ID无效:", orderId);
        uni.showToast({
          title: "订单数据不完整",
          icon: "none",
        });
        return;
      }

      // 使用当前页面的formattedOrders查找对应订单，这是首页上显示的订单数据
      const order = this.publishedOrders.find((o) => o.id === orderId);

      if (order) {
        // 确保订单有id或orderId
        if (!order.id && !order.orderId) {
          console.error("订单数据缺少ID", order);
          order.id = orderId; // 使用点击时的ID赋值
        }

        // 保存当前订单到本地缓存，确保订单详情页能访问到
        uni.setStorageSync("currentOrderDetail", order);

        // 导航到订单详情页，使用有效的订单ID
        uni.navigateTo({
          url: `/pages/order-detail/order-detail?id=${
            order.id || order.orderId
          }`,
        });
      } else {
        // 查看全局数据中是否有此订单
        const app = getApp();
        const globalOrders = app.globalData.publishedOrders || [];
        const globalOrder = globalOrders.find(
          (o) => o.id === orderId || o.orderId === orderId
        );

        if (globalOrder) {
          // 确保订单有id或orderId
          if (!globalOrder.id && !globalOrder.orderId) {
            console.error("全局订单数据缺少ID", globalOrder);
            globalOrder.id = orderId; // 使用点击时的ID赋值
          }

          // 找到了全局数据中的订单，保存并导航
          uni.setStorageSync("currentOrderDetail", globalOrder);
          uni.navigateTo({
            url: `/pages/order-detail/order-detail?id=${
              globalOrder.id || globalOrder.orderId
            }`,
          });
          return;
        } else {
          // 都没找到，提示用户
          uni.showToast({
            title: "无法找到此订单",
            icon: "none",
          });
        }
      }
    },

    /**
     * 初始化示例订单数据 - 已移除假数据，仅保留空数组初始化
     */
    initDemoOrders: function () {
      // 移除所有假数据，仅初始化空数组
      this.demoOrders = [];
      this.currentOrders = [];
    },

    /**
     * 跳转到订单详情页
     */
    goToOrderDetail: function (e) {
      const orderId = e.currentTarget.dataset.id;

      // 检查订单ID是否有效
      if (!orderId || orderId === "undefined" || orderId === "null") {
        console.error("订单ID无效:", orderId);
        uni.showToast({
          title: "无效的订单ID",
          icon: "none",
        });
        return;
      }

      // 打印日志，便于调试
      console.log("即将跳转到订单详情页", {
        orderId: orderId,
        idType: typeof orderId,
      });

      // 直接跳转到订单详情页，让详情页通过API获取数据
          uni.navigateTo({
        url: `/pages/order-detail/order-detail?id=${orderId}`,
      });
    },

    // 显示时间段选择器
    showTimeSlotPicker() {
      // 根据当前选择的时长生成时间段选项
      this.generateTimeSlots();
      this.showTimeSlotPicker_flag = true;
    },

    // 隐藏时间段选择器
    hideTimeSlotPicker() {
      this.showTimeSlotPicker_flag = false;
    },

    // 时间段改变时的处理
    onTimeSlotChange(e) {
      const val = e.detail.value[0];
      this.timeSlotValue = [val];
      this.tempTimeSlotValue = val;
    },

    // 生成时间段选项
    generateTimeSlots() {
      const slots = [];
      const duration = this.duration;

      // 生成24小时的时间段
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const startTime = new Date();
          startTime.setHours(hour);
          startTime.setMinutes(minute);

          const endTime = new Date(startTime);
          endTime.setHours(hour + Math.floor(duration));
          endTime.setMinutes(minute + (duration % 1) * 60);

          // 格式化时间显示
          const startTimeStr = `${hour.toString().padStart(2, "0")}:${minute
            .toString()
            .padStart(2, "0")}`;
          let endHour = endTime.getHours();
          let endMinute = endTime.getMinutes();

          // 处理跨天的情况
          if (endHour >= 24) {
            endHour = endHour % 24;
            endTime.setHours(endHour);
          }

          const endTimeStr = `${endHour.toString().padStart(2, "0")}:${endMinute
            .toString()
            .padStart(2, "0")}`;

          // 如果结束时间小于开始时间，说明跨天了
          if (endTime < startTime) {
            slots.push(`${startTimeStr} - 次日${endTimeStr}`);
          } else {
            slots.push(`${startTimeStr} - ${endTimeStr}`);
          }
        }
      }

      this.timeSlotRange = slots;
    },

    // 确认时间段选择
    hideTimeSlotPicker() {
      const val = this.tempTimeSlotValue || this.timeSlotValue[0];
      const timeSlot = this.timeSlotRange[val];

      if (timeSlot) {
        // 从时间段中提取开始时间
        const startTime = timeSlot.split(" - ")[0];
        const [startHour, startMinute] = startTime.split(":").map(Number);

        this.time = timeSlot;
        this.startHour = startHour;
        this.startMinute = startMinute;
        this.showTimeSlotPicker_flag = false;
      } else {
        this.showTimeSlotPicker_flag = false;
      }
    },

    // 更新当前时间段显示
    updateCurrentTimeSlot() {
      const currentStartTime = `${this.startHour
        .toString()
        .padStart(2, "0")}:${this.startMinute.toString().padStart(2, "0")}`;
      const endTime = new Date();
      endTime.setHours(this.startHour + this.duration);
      endTime.setMinutes(this.startMinute);
      let endHour = endTime.getHours();
      const endMinute = endTime.getMinutes();

      // 处理跨天的情况
      let timeStr;
      if (endHour >= 24) {
        endHour = endHour % 24;
        timeStr = `${currentStartTime} - 次日${endHour
          .toString()
          .padStart(2, "0")}:${endMinute.toString().padStart(2, "0")}`;
      } else {
        timeStr = `${currentStartTime} - ${endHour
          .toString()
          .padStart(2, "0")}:${endMinute.toString().padStart(2, "0")}`;
      }

      this.time = timeStr;
    },

    // 格式化活动时间（开始时间和结束时间）
    formatActivityTime(startTime, endTime) {
      if (!startTime || !endTime) return "";
      
      try {
        const start = new Date(startTime);
        const end = new Date(endTime);
        
        // 格式化日期部分 (yyyy-MM-dd)
        const startYear = start.getFullYear();
        const startMonth = start.getMonth() + 1;
        const startDay = start.getDate();
        const startDateStr = `${startYear}-${startMonth}-${startDay}`;
        
        const endYear = end.getFullYear();
        const endMonth = end.getMonth() + 1;
        const endDay = end.getDate();
        const endDateStr = `${endYear}-${endMonth}-${endDay}`;
        
        // 格式化时间部分 (HH:mm)
        const startHours = String(start.getHours()).padStart(2, '0');
        const startMinutes = String(start.getMinutes()).padStart(2, '0');
        const startTimeStr = `${startHours}:${startMinutes}`;
        
        const endHours = String(end.getHours()).padStart(2, '0');
        const endMinutes = String(end.getMinutes()).padStart(2, '0');
        const endTimeStr = `${endHours}:${endMinutes}`;
        
        // 检查是否是同一天
        if (startYear === endYear && startMonth === endMonth && startDay === endDay) {
          // 同一天，只显示一个日期
          return `${startDateStr} ${startTimeStr} - ${endTimeStr}`;
        } else {
          // 不同天，显示完整的开始和结束时间
          return `${startDateStr} ${startTimeStr} - ${endDateStr} ${endTimeStr}`;
        }
      } catch (error) {
        console.error("格式化活动时间出错:", error);
        return startTime && endTime ? `${startTime} - ${endTime}` : "";
      }
    },

    // 计算活动时长（返回小时和分钟）
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return { hours: 0, minutes: 0, totalHours: 0 };
      
      try {
        const start = new Date(startTime);
        const end = new Date(endTime);
        
        // 计算时间差（毫秒）
        const diffMs = end.getTime() - start.getTime();
        
        // 总时间（毫秒）
        const totalMs = diffMs;
        
        // 计算小时（整数部分）
        const hours = Math.floor(totalMs / (1000 * 60 * 60));
        
        // 计算剩余的分钟
        const remainingMs = totalMs - (hours * 1000 * 60 * 60);
        const minutes = Math.floor(remainingMs / (1000 * 60));
        
        // 总小时数（带小数）
        const totalHours = totalMs / (1000 * 60 * 60);
        
        return {
          hours,
          minutes,
          totalHours
        };
      } catch (error) {
        console.error("计算活动时长出错:", error);
        return { hours: 0, minutes: 0, totalHours: 0 };
      }
    },

    // 检查订单是否为即时订单（开始时间在24小时内）
    isInstantOrder(startTime) {
      if (!startTime) return false;
      
      try {
        const start = new Date(startTime);
        const now = new Date();
        
        // 计算时间差（毫秒）
        const timeDiff = start.getTime() - now.getTime();
        // 转换为小时
        const hoursDiff = timeDiff / (1000 * 60 * 60);
        
        // 如果开始时间在24小时内，则为即时订单
        return hoursDiff >= 0 && hoursDiff <= 24;
      } catch (error) {
        console.error("检查即时订单出错:", error);
        return false;
      }
    },

    // 格式化活动时间
    formatActivityTime(startTime, endTime) {
      if (!startTime) return '';
      
      try {
        const start = new Date(startTime);
        const end = endTime ? new Date(endTime) : null;
        
        const formatDate = (date) => {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        };
        
        const formatTime = (date) => {
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          return `${hours}:${minutes}`;
        };
        
        if (end) {
          const startDate = formatDate(start);
          const endDate = formatDate(end);
          const startTimeStr = formatTime(start);
          const endTimeStr = formatTime(end);
          
          if (startDate === endDate) {
            return `${startDate} ${startTimeStr}-${endTimeStr}`;
          } else {
            return `${startDate} ${startTimeStr} - ${endDate} ${endTimeStr}`;
          }
        } else {
          return `${formatDate(start)} ${formatTime(start)}`;
        }
      } catch (error) {
        console.error('格式化活动时间失败:', error);
        return startTime;
      }
    },

    // 格式化持续时间
    formatDuration(duration) {
      if (!duration && duration !== 0) return '待定';
      
      // 如果duration是数字，假设单位是小时
      if (typeof duration === 'number') {
        if (duration < 1) {
          return `${duration * 60}分钟`;
        } else if (duration === 1) {
          return '1小时';
        } else {
          return `${duration}小时`;
        }
      }
      
      // 如果duration是字符串，直接返回
      return duration;
    },

    // 格式化性别要求
    formatGender(sexRequire) {
      if (!sexRequire && sexRequire !== 0) return '不限';
      
      switch (sexRequire) {
        case 0:
        case '0':
          return '不限';
        case 1:
        case '1':
          return '男';
        case 2:
        case '2':
          return '女';
        default:
          return '不限';
      }
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: 0;
  position: relative;
  z-index: 1;
}

/* 顶部区域 - 2025新年背景图 */
.header {
  position: relative;
  width: 100%;
  height: 420rpx;
  overflow: hidden;
}

/* 顶部背景图片 - 需要更换为2025新年主题图 */
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 40rpx 30rpx;
  color: #fff;
}

.title-small {
  display: block;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.title-large {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.btn-rule {
  width: fit-content;
  font-size: 24rpx;
  padding: 12rpx 30rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 999rpx;
  color: #fff;
  position: relative;
  z-index: 1;
}

/* 自由行卡片 - 白色悬浮卡片 */
.free-card-wrapper {
  position: relative;
  margin-top: -120rpx;
  padding: 0 30rpx;
  z-index: 10;
}

.free-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
}

.card-subtitle {
  font-size: 24rpx;
  color: #999;
}

.location-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  min-height: 88rpx;
}

.location-detail {
  flex: 1;
  margin-right: 20rpx;
  overflow: hidden;
}

.location-area {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
  white-space: nowrap;
}

.location-address {
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
}

.location-current {
  color: #ff8c00;
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
  padding-top: 4rpx;
  font-size: 24rpx;
  width: 120rpx;
  text-align: right;
}

.location-current .iconfont {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.time-info {
  padding: 20rpx 0;
  border-top: 1rpx solid #f5f5f5;
  border-bottom: 1rpx solid #f5f5f5;
}

.time-main {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.time-note {
  font-size: 24rpx;
  color: #999;
}

.btn-publish {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 28rpx;
  border-radius: 999rpx;
  margin-top: 30rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.btn-publish::after {
  border: none;
}

.btn-publish:active {
  opacity: 0.8;
}

/* 热门活动推荐区域 */
.recommend {
  margin-top: 40rpx;
  padding: 0 30rpx;
}

/* section 标题 */
.section-title {
  padding: 20rpx 0;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.title-text {
  position: relative;
  padding-left: 24rpx;
}

.title-text::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: #ff8c00;
  border-radius: 4rpx;
}

.section-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0rpx;
  padding-bottom: 0rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.clear-orders {
  font-size: 28rpx;
  color: #ff4d4f;
  background-color: #fff1f0;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
}

/* 活动卡片列表容器 */
.activity-list {
  white-space: nowrap;
  margin: 0 -30rpx;
  padding: 0 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 活动卡片 - 冬季温泉和茶艺品鉴 */
.activity-item {
  display: inline-block;
  width: 340rpx;
  margin: 0 12rpx;
  background: #fff;
  border-radius: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

/* 活动图片容器 */
.activity-image-container {
  width: 100%;
  height: 200rpx;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
}

/* 活动图片 - 需要高清无水印图片 */
.activity-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.activity-info {
  padding: 24rpx;
  background: #fff;
  border-radius: 0 0 30rpx 30rpx;
}

.activity-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  color: #333;
}

.activity-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 达人推荐区域 */
.expert {
  margin-top: 0rpx;
  padding: 0 30rpx;
  padding-bottom: 140rpx;
}

/* 城市选择列表 - 横向滚动 */
.city-list {
  display: flex;
  align-items: center;
  flex: 1;
  height: 64rpx;
  overflow: visible;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.filter-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 24rpx;
  background: #fff;
  border: 1rpx solid #eee;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  flex-shrink: 0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  margin-left: 15rpx;
}

.filter-icon.active {
  background: #ffecdb;
  color: #ff8c00;
  border: 1rpx solid #ff8c00;
}

.filter-icon:active {
  opacity: 0.7;
}

.filter-count {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

/* 城市选择项 - 圆角胶囊按钮 */
.city-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #666;
  background: #f7f7f7;
  border-radius: 999rpx;
  transition: all 0.3s ease;
  height: 40rpx;
  box-sizing: content-box;
}

.city-item.active {
  background: #ff8c00;
  color: #fff;
  font-weight: 500;
}

.expert-list {
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.expert-item {
  display: flex;
  padding: 30rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.expert-item:last-child {
  margin-bottom: 0;
}

/* 达人头像 - 圆形头像，建议尺寸240x240px */
.expert-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 999rpx;
  margin-right: 24rpx;
}

.expert-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.expert-header {
  margin-bottom: 0;
}

.expert-name {
  display: flex;
  align-items: flex-start;
  font-size: 32rpx;
  font-weight: bold;
}

.gender {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #999;
  line-height: 1;
  transform: translateY(4rpx);
}

.gender.female {
  color: #ff6b9c;
}

.gender.male {
  color: #3498db;
}

.expert-age {
  font-size: 24rpx;
  color: #999;
  margin-left: 12rpx;
}

/* 身高体重信息 */
.expert-body {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 0;
}

.expert-body .separator {
  margin: 0 12rpx;
  color: #ddd;
}

/* 技能标签 */
.expert-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: -4rpx;
}

/* 达人技能标签 - 彩色背景标签 */
.tag {
  padding: 0 32rpx;
  height: 48rpx;
  font-size: 24rpx;
  border-radius: 999rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  box-sizing: border-box;
  min-width: 140rpx;
  text-align: center;
  font-weight: 500;
  transform: translateY(4rpx);
  position: relative;
  top: -4rpx;
  padding-bottom: 2rpx;
}

/* 标签颜色 - 橙色系 */
.tag:nth-child(3n) {
  background: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
}

/* 标签颜色 - 蓝色系 */
.tag:nth-child(3n + 1) {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

/* 标签颜色 - 紫色系 */
.tag:nth-child(3n + 2) {
  background: rgba(114, 46, 209, 0.1);
  color: #722ed1;
}

/* 时间选择相关样式 */
.time-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.time-select {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.time-duration {
  color: #ff8c00;
  padding: 4rpx 12rpx;
  background: #fff5e6;
  border-radius: 999rpx;
}

.time-picker {
  display: flex;
  align-items: center;
  color: #666;
  padding: 4rpx 12rpx;
  background: #f5f5f5;
  border-radius: 999rpx;
}

.time-picker .iconfont {
  font-size: 24rpx;
  margin-right: 4rpx;
}

/* 添加原生选择器样式 */
.time-picker-view,
.time-slot-picker {
  width: 100%;
  height: 400rpx;
  position: relative;
}

.picker-column-item {
  line-height: 88rpx;
  text-align: center;
}

/* 时间选择器样式 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

.picker-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  z-index: 999;
  border-radius: 24rpx 24rpx 0 0;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
}

.picker-close {
  color: #ff8c00;
  font-size: 28rpx;
}

.picker-column-item {
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.publish-button {
  width: 100rpx;
  height: 100rpx;
  background: #ff8c00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: -30rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 140, 0, 0.3);
}

.publish-button text {
  color: #ffffff;
  font-size: 48rpx;
}

/* 无数据提示 */
.no-data {
  text-align: center;
  color: #999;
  padding: 40rpx 0;
}

/* 添加选中项的指示器 */
.time-picker-view:before,
.time-slot-picker:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 88rpx;
  transform: translateY(-44%);
  background: rgba(0, 0, 0, 0.02);
  pointer-events: none;
  border-top: 1rpx solid #eee;
  border-bottom: 1rpx solid #eee;
}

/* 调整选择器视图样式 */
picker-view-column {
  height: 400rpx !important;
}

/* 发布按钮 - 主题色按钮 */
.publish-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 999rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 订单列表样式 */
.order-list {
  padding: 0;
}

.order-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 14rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-id {
  font-size: 26rpx;
  color: #666;
}

.order-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 999rpx;
}

.order-status.pending {
  background: #fff5e6;
  color: #ff8c00;
}

.order-status.completed {
  background: #f0f9eb;
  color: #67c23a;
}

.order-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.order-row {
  display: flex;
  font-size: 28rpx;
  align-items: flex-start;
  min-height: 40rpx;
}

.order-row .label {
  width: 140rpx;
  color: #666;
  flex-shrink: 0;
  line-height: 40rpx;
}

.order-row .value {
  flex: 1;
  color: #333;
  line-height: 40rpx;
}

.order-row .location {
  display: flex;
  flex-direction: column;
}

.location-detail {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.price {
  color: #ff8c00;
  font-weight: 500;
}

.no-data {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.no-data .tip {
  font-size: 24rpx;
  color: #bbb;
}

/* 费用信息样式 */
.fee-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.price {
  color: #ff8c00;
  font-size: 36rpx;
  font-weight: bold;
}

.unit {
  color: #999;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.total-fee {
  color: #ff8c00;
  font-size: 26rpx;
  font-weight: 500;
  background: #fff5e6;
  padding: 4rpx 12rpx;
  border-radius: 999rpx;
}

/* 订单内容中的时间信息样式 */
.order-row .time-info {
  display: flex;
  align-items: center;
  padding: 0;
  border: none;
}

.order-row .time-info text {
  display: inline-block;
  vertical-align: middle;
}

.order-row .duration {
  color: #666;
  font-size: 24rpx;
  margin-left: 8rpx;
  line-height: 1.4;
}

/* 订单状态组样式 */
.status-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.applicants-count {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 999rpx;
}

/* 调整原有订单状态样式 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

/* 订单底部样式 */
.order-footer {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}

/* 调整已报名人数样式 */
.applicants-count {
  font-size: 24rpx;
  color: #666;
  background: #f8f8f8;
  padding: 8rpx 24rpx;
  border-radius: 999rpx;
  display: inline-block;
}

/* 移除原有的订单头部边框 */
.order-header {
  margin-bottom: 20rpx;
  padding-bottom: 0;
  border-bottom: none;
}

/* 调整订单内容间距 */
.order-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 高亮文字样式 */
.highlight {
  color: #ff8c00 !important;
  font-weight: 500;
}

/* 订单项目分类标签 */
.order-type {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.order-tag {
  color: #444;
  background-color: #f5f5f5;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.order-subtype {
  color: #ff5500;
  font-weight: bold;
  font-size: 30rpx;
  background-color: #fff0e6;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 85, 0, 0.15);
}

.order-details {
  padding-left: 10rpx;
}

/* 订单价格标签 */
.order-price-tag {
  display: flex;
  align-items: baseline;
}

.order-price-tag .price {
  font-size: 32rpx;
  color: #ff8c00;
  font-weight: bold;
}

.order-price-tag .unit {
  font-size: 22rpx;
  color: #999;
}

/* 总价格样式 */
.total-price-row {
  margin-top: 12rpx;
}

.total-fee-container {
  display: flex;
  align-items: baseline;
}

.total-price {
  font-size: 38rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.price-person {
  font-size: 22rpx;
  color: #999;
  margin-left: 4rpx;
}

/* 发布者信息 */
.publisher-info {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.publisher-name {
  font-size: 24rpx;
  color: #666;
}

/* 订单底部布局 */
.order-footer {
  margin-top: 20rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 订单状态标签容器 */
.order-status-tags {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8rpx;
  margin: 0rpx 0rpx 10rpx -14rpx;
  flex-wrap: wrap;
  min-height: 40rpx;
}

/* 过期订单标签 */
.expired-tag {
  display: flex;
  align-items: center;
  background-color: #ff4757;
  color: #fff;
  padding: 6rpx 16rpx;
  border-radius: 10rpx;
  font-weight: bold;
  height: 32rpx;
  box-sizing: border-box;
}

.expired-icon {
  font-size: 20rpx;
  margin-right: 6rpx;
}

.expired-text {
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}

/* 即时订单标签 */
.instant-order-tag {
  display: flex;
  align-items: center;
  background-color: #ffc300;
  color: #333;
  padding: 6rpx 16rpx;
  border-radius: 10rpx;
  font-weight: bold;
  height: 32rpx;
  box-sizing: border-box;
}

/* 预约订单标签样式 */
.appointment-order-tag {
  background-color: #4CAF50;
  color: #fff;
}

/* 过期订单的标签样式调整 */
.expired-order {
  opacity: 0.6;
  background-color: #ccc !important;
  color: #666 !important;
}

.instant-icon {
  font-size: 20rpx;
  margin-right: 6rpx;
}

.instant-text {
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}

/* 倒计时标签 */
.countdown-tag {
  display: flex;
  align-items: center;
  background-color: #ff6b35;
  color: #fff;
  padding: 6rpx 16rpx;
  border-radius: 10rpx;
  font-weight: bold;
  height: 32rpx;
  box-sizing: border-box;
  animation: pulse 2s infinite;
  margin: 0rpx 0rpx 20rpx 20rpx;
}

.countdown-icon {
  font-size: 20rpx;
  margin-right: 6rpx;
}

.countdown-text {
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}

/* 倒计时动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 过期订单内容样式 */
.expired-content {
  opacity: 0.7;
  filter: grayscale(0.3);
}

.publisher-name {
  margin-left: auto;
  font-size: 24rpx;
  color: #333;
}

/* 订单内容布局 */
.order-content-row {
  display: flex;
  flex-direction: column;
  padding: 0 6rpx;
  transition: all 0.3s ease;
}

/* 订单类型和发布者信息 */
.order-type-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx dashed #f0f0f0;
}

.publisher-info-container {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #eee;
  margin-right: 6rpx;
}

.publisher-name-beside {
  font-size: 24rpx;
  color: #333;
  max-width: 500rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 已报名标签样式 */
.enrolled-tag {
  display: flex;
  align-items: center;
  background-color: #4CAF50;
  color: #fff;
  padding: 6rpx 16rpx;
  border-radius: 10rpx;
  font-weight: bold;
  height: 32rpx;
  box-sizing: border-box;
  margin: 0rpx 0rpx 20rpx 120rpx;
}

.enrolled-text {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}

.order-type-info {
  display: flex;
  align-items: center;
  padding: 6rpx 0;
  gap: 10rpx;
}

.order-tag {
  color: #444;
  background-color: #f5f5f5;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.order-subtype {
  color: #ff5500;
  font-weight: bold;
  font-size: 30rpx;
  background-color: #fff0e6;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 85, 0, 0.15);
}

/* 订单时间 */
.order-time,
.order-duration-row,
.order-price-row,
.order-people-row,
.order-gender-row,
.order-description-row {
  font-size: 24rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: flex-start;
  min-height: 32rpx;
}

/* 需求描述样式 */
.order-description-row {
  margin-top: 8rpx;
  padding: 8rpx 0;
  border-top: 1rpx dashed #f0f0f0;
}

.description-label {
  color: #666;
  width: 150rpx;
  font-size: 24rpx;
  flex-shrink: 0;
}

.description-value {
  color: #333;
  font-size: 24rpx;
  flex: 1;
  line-height: 1.4;
  word-break: break-all;
}

/* 并排显示的行 */
.order-flex-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  height: 32rpx;
}

.order-people-row,
.order-gender-row {
  display: flex;
  font-size: 24rpx;
  align-items: center;
}

.hourly-rate {
  display: flex;
  align-items: baseline;
  font-size: 36rpx;
  color: #ff8c00;
  font-weight: bold;
  padding: 0 4rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 140, 0, 0.1);
}

.rate-value {
  font-size: 42rpx;
  color: #ff8c00;
  font-weight: bold;
}

.rate-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.time-label,
.price-label,
.duration-label,
.people-label,
.gender-label {
  color: #666;
  width: 150rpx;
  font-size: 24rpx;
}

.time-value,
.duration-value,
.people-value,
.gender-value {
  color: #333;
  font-size: 24rpx;
}

/* 性别要求特殊样式 */
.gender-value {
  color: #ff8c00;
  font-weight: 500;
  font-size: 24rpx;
}

/* 订单总价 */
.order-total-price {
  margin-top: 10rpx;
  margin-bottom: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: baseline;
}

.total-price-value {
  color: #ff8c00;
  font-weight: bold;
}

.per-person {
  font-size: 22rpx;
  color: #999;
  margin-left: 4rpx;
}

/* 订单地址 */
.order-address {
  display: flex;
  align-items: center;
  padding-top: 8rpx;
  padding-bottom: 2rpx;
  border-top: 1rpx solid #f0f0f0;
  font-size: 26rpx;
}

.address-icon {
  margin-right: 12rpx;
  color: #ff8c00;
}

.address-text {
  flex: 1;
  color: #666;
  letter-spacing: 1rpx;
}

.check-detail-btn {
  padding: 4rpx 14rpx;
  background-color: #ffc300;
  color: #fff;
  border-radius: 30rpx;
  font-size: 22rpx;
}

/* 订单卡片整体样式 */
.order-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 14rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 订单头部 - 发布者信息和订单类型 */
.order-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
  padding-bottom: 8rpx;
  border-bottom: 1rpx dashed #f0f0f0;
}

/* 发布者信息 */
.publisher-info-container {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  border: 1rpx solid #eee;
  margin-right: 8rpx;
}

.publisher-name-beside {
  font-size: 28rpx;
  color: #333;
  max-width: 110rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 订单类型信息 */
.order-type-info {
  display: flex;
  align-items: center;
  padding: 6rpx 0;
  gap: 10rpx;
}

.order-tag {
  color: #444;
  background-color: #f5f5f5;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.order-subtype {
  color: #ff5500;
  font-weight: bold;
  font-size: 30rpx;
  background-color: #fff0e6;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 85, 0, 0.15);
}

/* 订单内容 */
.order-content-row {
  display: flex;
  flex-direction: column;
  padding: 0 6rpx;
}

/* 发布者信息行 */
.publisher-row {
  display: flex;
  margin-top: 10rpx;
  padding: 8rpx 0;
  border-top: 1rpx dashed #f0f0f0;
  justify-content: space-between;
  align-items: center;
}

.publisher-info-container {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1rpx solid #eee;
  margin-right: 10rpx;
}

.publisher-name-beside {
  font-size: 26rpx;
  color: #666;
  max-width: 150rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 1rpx;
}

/* 订单统计信息 */
.order-stats {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #999;
}

.publish-time,
.applicants-count-tag {
  display: flex;
  align-items: center;
}

.time-icon,
.applicants-icon {
  font-size: 20rpx;
  margin-right: 4rpx;
}

/* 头像轮播动画 */
@keyframes avatarFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2rpx);
  }
}

/* 头像个别动画延迟 */
.avatar-container:nth-child(1) .applicant-avatar {
  animation: avatarBounce 2s ease-in-out infinite;
  animation-delay: 0s;
}

.avatar-container:nth-child(2) .applicant-avatar {
  animation: avatarBounce 2s ease-in-out infinite;
  animation-delay: 0.3s;
}

.avatar-container:nth-child(3) .applicant-avatar {
  animation: avatarBounce 2s ease-in-out infinite;
  animation-delay: 0.6s;
}

@keyframes avatarBounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.stats-divider {
  margin: 0 8rpx;
  color: #eee;
  font-size: 18rpx;
}

/* 报名人员显示区域 */
.applicants-display {
  display: flex;
  align-items: center;
}

.applicants-avatars {
  display: flex;
  align-items: center;
  gap: 4rpx;
  animation: avatarFloat 3s ease-in-out infinite;
}

.avatar-container {
  position: relative;
  margin-left: -8rpx;
}

.avatar-container:first-child {
  margin-left: 0;
}

.applicant-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.more-count {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: #ff8c00;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -8rpx;
  border: 2rpx solid #fff;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.more-text {
  font-size: 18rpx;
  color: #fff;
  font-weight: bold;
}

.enroll-count-text {
  font-size: 22rpx;
  color: #ff8c00;
  margin-left: 8rpx;
}

.no-applicants {
  display: flex;
  align-items: center;
  color: #ff8c00;
}

.applicants-icon {
  font-size: 20rpx;
  margin-right: 4rpx;
}

.check-detail-btn {
  padding: 4rpx 14rpx;
  background-color: #ffc300;
  color: #fff;
  border-radius: 30rpx;
  font-size: 22rpx;
}

.order-connector {
  color: #999;
  font-size: 24rpx;
  margin: 0 6rpx;
  font-weight: bold;
}

.filter-panel {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-section {
  margin-bottom: 24rpx;
}

.filter-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 10rpx 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
}

.filter-option.selected {
  background-color: #fff0e6;
  color: #ff8c00;
}

.filter-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.filter-reset {
  width: 220rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #ddd;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #666;
}

.filter-apply {
  width: 380rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff8c00;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #fff;
}

/* 即时订单标签 */
.instant-order-tag {
  display: flex;
  align-items: center;
  background-color: #ffc300;
  color: #333;
  padding: 4rpx 16rpx;
  border-radius: 10rpx 10rpx 0 0;
  margin: -14rpx -14rpx 10rpx -14rpx;
}

/* 预约订单标签样式 */
.appointment-order-tag {
  background-color: #4CAF50;
  color: #fff;
}
</style>
