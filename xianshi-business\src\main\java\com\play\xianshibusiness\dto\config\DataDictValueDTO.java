package com.play.xianshibusiness.dto.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 数据字典值DTO
 */
@Data
@ApiModel("数据字典值DTO")
public class DataDictValueDTO {
    
    @ApiModelProperty(value = "类型ID", required = true)
    @NotBlank(message = "类型ID不能为空")
    private String typeId;
    
    @ApiModelProperty(value = "值编码", required = true)
    @NotBlank(message = "值编码不能为空")
    private String valueCode;
    
    @ApiModelProperty(value = "值名称", required = true)
    @NotBlank(message = "值名称不能为空")
    private String valueName;
    
    @ApiModelProperty(value = "值描述")
    private String description;
    
    @ApiModelProperty(value = "附加数据（JSON格式）")
    private String extraData;
    
    @ApiModelProperty(value = "排序", example = "0")
    @NotNull(message = "排序不能为空")
    private Integer sort = 0;
} 