package com.play.xianshiapp.controller.member;

import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.member.CMemberDto;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.CMemberService;
import com.play.xianshibusiness.service.MemberViewHistoryService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 会员资料查询控制器
 * 用于查询其他会员的公开信息
 */
@RestController
@RequestMapping("/app/profile")
@Api(tags = "会员资料查询-API")
public class MemberProfileController {
    
    @Resource
    private CMemberService cMemberService;
    
    @Resource
    private MemberViewHistoryService memberViewHistoryService;

    /**
     * 根据会员ID获取会员公开信息
     */
    @ApiOperation(value = "根据会员ID获取会员公开信息")
    @GetMapping("/{memberId}")
    public Result<CMemberDto> getMemberInfo(
            @ApiParam(value = "会员ID", required = true) 
            @PathVariable("memberId") String memberId,
            @ApiParam(value = "是否包含附加信息（如关注/粉丝数）") 
            @RequestParam(value = "withExtra", required = false, defaultValue = "true") Boolean withExtra,
            @ApiParam(value = "是否包含隐私信息") 
            @RequestParam(value = "withPrivacy", required = false, defaultValue = "false") Boolean withPrivacy) {
        // 查询其他会员信息时不返回隐私数据
        return ResultUtils.success(cMemberService.getMemberInfo(memberId, withExtra, withPrivacy));
    }
    
    /**
     * 查看会员资料并记录浏览历史
     */
    @ApiOperation(value = "查看会员资料并记录浏览历史")
    @GetMapping("/view/{memberId}")
    @RequireLogin
    public Result<CMemberDto> viewMemberProfile(
            @ApiParam(value = "会员ID", required = true) 
            @PathVariable("memberId") String memberId) {
        // 获取当前登录用户ID
        String currentMemberId = PrincipalUtil.getMemberId();
        // 记录浏览历史
        memberViewHistoryService.recordViewHistory(currentMemberId, memberId);
        // 查询其他会员信息
        return ResultUtils.success(cMemberService.getMemberInfo(memberId, true, false));
    }
} 