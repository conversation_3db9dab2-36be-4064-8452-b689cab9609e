package com.play.xianshibusiness.dto.member;

import com.play.xianshibusiness.pojo.CRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员DTO
 */
@Data
@ApiModel("会员DTO")
public class CMemberDto {
    
    @ApiModelProperty(value = "会员ID")
    private String id;
    
    @ApiModelProperty(value = "系统ID")
    private String sysId;
    
    @ApiModelProperty(value = "昵称")
    private String nickname;
    
    @ApiModelProperty(value = "头像")
    private String avatar;
    
    @ApiModelProperty(value = "性别：1-男，2-女")
    private Integer gender;
    
    @ApiModelProperty(value = "年龄")
    private Integer age;
    
    @ApiModelProperty(value = "星座")
    private String constellation;
    
    @ApiModelProperty(value = "身高")
    private String height;
    
    @ApiModelProperty(value = "简介")
    private String context;
    
    @ApiModelProperty(value = "视频")
    private String video;
    
    @ApiModelProperty(value = "照片集合")
    private String images;
    
    @ApiModelProperty(value = "身份标签")
    private String identify;
    
    @ApiModelProperty(value = "风格标签")
    private String styleIds;
    
    @ApiModelProperty(value = "金币余额")
    private Integer gold;
    
    @ApiModelProperty(value = "人气值")
    private String popularity;
    
    @ApiModelProperty(value = "是否VIP")
    private Boolean isVip;
    
    @ApiModelProperty(value = "当前角色ID")
    private String currentRoleId;
    
    @ApiModelProperty(value = "当前角色信息")
    private CRole currentRole;
    
    @ApiModelProperty(value = "会员拥有的所有角色")
    private List<CRole> roles;
    
    @ApiModelProperty(value = "是否为达人")
    private Boolean isTalent;
    
    @ApiModelProperty(value = "是否拥有达人角色权限")
    private Boolean hasExpertRole;
    
    @ApiModelProperty(value = "是否拥有公会角色权限")
    private Boolean hasGuildRole;
    
    @ApiModelProperty(value = "当前角色类型：1-普通用户，2-闲时达人，3-闲时公会")
    private Integer roleType;
    
    @ApiModelProperty(value = "当前角色名称")
    private String currentRoleName;
    
    @ApiModelProperty(value = "手机号")
    private String tel;
    
    @ApiModelProperty(value = "微信名称")
    private String wxName;
    
    @ApiModelProperty(value = "微信头像")
    private String wxAvatar;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "审核状态")
    private String applyStatus;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    
    @ApiModelProperty(value = "最后登录时间")
    private LocalDateTime lastLoginTime;
    
    @ApiModelProperty(value = "是否删除")
    private Boolean deleted;
    
    @ApiModelProperty(value = "是否可用")
    private Boolean available;
    
    @ApiModelProperty(value = "达人项目列表")
    private List<String> projectList;
    
    @ApiModelProperty(value = "达人城市列表")
    private List<String> cityList;
    
    @ApiModelProperty(value = "接单总数")
    private Integer orderCount;
    
    @ApiModelProperty(value = "评分")
    private BigDecimal rating;
    
    @ApiModelProperty(value = "好评率")
    private BigDecimal goodRatePercent;

    @ApiModelProperty(value = "是否关注")
    public boolean following;


    @ApiModelProperty(value = "粉丝数")
    private String fansCount;

    @ApiModelProperty(value = "关注数")
    private String followCount;

    @ApiModelProperty(value = "浏览量")
    private Integer viewCount;
    
    @ApiModelProperty(value = "我的浏览数（浏览其他人的次数）")
    private Integer myViewCount;

    @ApiModelProperty(value = "体重")
    private String weight;

    @ApiModelProperty(value = "照片数组")
    private List<String> imagesList;

    @ApiModelProperty(value = "风格标签数组")
    private List<String> styleList;
    
    @ApiModelProperty(value = "实名认证状态：0-未认证，1-已认证")
    private Integer realNameStatus;
    
    @ApiModelProperty(value = "真实姓名")
    private String realName;
    
    @ApiModelProperty(value = "身份证号码（隐私信息）")
    private String idCardNumber;
    
    @ApiModelProperty(value = "实名认证时间")
    private LocalDateTime realNameVerifyTime;
    
    @ApiModelProperty(value = "微信CODE")
    private String wxCode;
    
    @ApiModelProperty(value = "微信OPEN_ID")
    private String wxOpenId;

    //城市
    @ApiModelProperty("城市")
    private String city;
    
    @ApiModelProperty(value = "是否已认证")
    private Boolean isVerified;
    
    @ApiModelProperty(value = "金币余额（BigDecimal类型）")
    private BigDecimal goldBalance;

}
