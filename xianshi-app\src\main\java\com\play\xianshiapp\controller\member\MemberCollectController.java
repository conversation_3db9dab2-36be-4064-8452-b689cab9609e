package com.play.xianshiapp.controller.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.member.MemberCollectDTO;
import com.play.xianshibusiness.enums.CollectType;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.CMemberCollectService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 会员收藏控制器
 */
@RestController
@RequestMapping("/app/collect")
@Api(tags = "会员收藏-API")
public class MemberCollectController {
    
    @Resource
    private CMemberCollectService memberCollectService;
    
    /**
     * 添加收藏
     */
    @ApiOperation("添加收藏")
    @PostMapping("/add")
    @RequireLogin
    public Result<Boolean> addCollect(
            @ApiParam(value = "目标ID", required = true) 
            @RequestParam("targetId") String targetId,
            @ApiParam(value = "收藏类型", required = true) 
            @RequestParam("type") String type,
            @ApiParam(value = "备注") 
            @RequestParam(value = "remark", required = false) String remark) {
        
        String memberId = PrincipalUtil.getMemberId();
        CollectType collectType = CollectType.valueOf(type);
        
        return ResultUtils.success(memberCollectService.addCollect(memberId, targetId, collectType, remark));
    }
    
    /**
     * 取消收藏
     */
    @ApiOperation("取消收藏")
    @PostMapping("/cancel")
    @RequireLogin
    public Result<Boolean> cancelCollect(
            @ApiParam(value = "目标ID", required = true) 
            @RequestParam("targetId") String targetId,
            @ApiParam(value = "收藏类型", required = true) 
            @RequestParam("type") String type) {
        
        String memberId = PrincipalUtil.getMemberId();
        CollectType collectType = CollectType.valueOf(type);
        
        return ResultUtils.success(memberCollectService.cancelCollect(memberId, targetId, collectType));
    }
    
    /**
     * 查询是否已收藏
     */
    @ApiOperation("查询是否已收藏")
    @GetMapping("/check")
    @RequireLogin
    public Result<Boolean> isCollected(
            @ApiParam(value = "目标ID", required = true) 
            @RequestParam("targetId") String targetId,
            @ApiParam(value = "收藏类型", required = true) 
            @RequestParam("type") String type) {
        
        String memberId = PrincipalUtil.getMemberId();
        CollectType collectType = CollectType.valueOf(type);
        
        return ResultUtils.success(memberCollectService.isCollected(memberId, targetId, collectType));
    }
    
    /**
     * 获取收藏列表
     */
    @ApiOperation("获取收藏列表")
    @GetMapping("/list")
    @RequireLogin
    public Result<Page<MemberCollectDTO>> getCollectList(
            @ApiParam(value = "收藏类型", required = true) 
            @RequestParam("type") String type,
            @ApiParam(value = "页码", defaultValue = "1") 
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", defaultValue = "10") 
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        
        String memberId = PrincipalUtil.getMemberId();
        CollectType collectType = CollectType.valueOf(type);
        
        return ResultUtils.success(memberCollectService.getCollectList(memberId, collectType, pageNum, pageSize));
    }
} 