/**
 * WebSocket客户端工具类
 */
class WebSocketClient {
    constructor(options = {}) {
        this.options = Object.assign({
            // 修改端口从9012改为9019
            url: 'ws://127.0.0.1:9019/ws',
            reconnectInterval: 5000,
            heartbeatInterval: 30000,
            maxReconnectAttempts: 5, // 添加最大重连次数
            token: null,
            userId: null,
            onOpen: () => {},
            onMessage: () => {},
            onClose: () => {},
            onError: () => {},
            debug: false
        }, options);
        
        this.connected = false;
        this.socket = null;
        this.reconnectTimer = null;
        this.heartbeatTimer = null;
        this.reconnectAttempts = 0; // 添加重连计数器
        
        // 消息处理函数映射
        this.messageHandlers = {
            'CHAT': this._handleChatMessage.bind(this),
            'SYSTEM': this._handleSystemMessage.bind(this),
            'CONNECT': this._handleConnectMessage.bind(this),
            'PING': this._handlePingMessage.bind(this),
            'ACK': this._handleAckMessage.bind(this)  // 添加ACK消息处理
        };
        
        // 消息回调函数
        this.messageCallbacks = {};
    }
    
    /**
     * 连接WebSocket服务器
     */
    connect() {
        if (this.socket) {
            this.disconnect();
        }
        
        if (!this.options.token || !this.options.userId) {
            this._log('Token or userId not provided');
            return;
        }
        
        // 确保token有Bearer前缀
        let token = this.options.token;
        if (!token.startsWith('Bearer ')) {
            token = 'Bearer ' + token;
        }
        
        const url = `${this.options.url}?token=${encodeURIComponent(token)}&userId=${this.options.userId}`;
        this._log('连接URL:', url);
        
        this.socket = uni.connectSocket({
            url: url,
            success: () => {
                this._log('WebSocket连接中...');
            },
            fail: (err) => {
                this._log('WebSocket连接失败', err);
                this._handleConnectionError(err);
            }
        });
        
        this.socket.onOpen(this._onOpen.bind(this));
        this.socket.onMessage(this._onMessage.bind(this));
        this.socket.onClose(this._onClose.bind(this));
        this.socket.onError(this._onError.bind(this));
    }
    
    /**
     * 处理连接错误
     */
    _handleConnectionError(error) {
        this.reconnectAttempts++;
        
        if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
            this._log('达到最大重连次数，停止重连');
            // 通知上层应用token可能无效，需要重新登录
            if (this.options.onError) {
                this.options.onError({
                    type: 'MAX_RECONNECT_REACHED',
                    message: 'Token可能已过期，请重新登录'
                });
            }
            return;
        }
        
        this._scheduleReconnect();
    }
    
    /**
     * WebSocket连接打开事件处理
     */
    _onOpen(event) {
        this._log('WebSocket连接已建立');
        this.connected = true;
        this.reconnectAttempts = 0; // 重置重连计数器
        
        // 清除重连定时器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        // 启动心跳
        this._startHeartbeat();
        
        // 调用回调
        if (this.options.onOpen) {
            this.options.onOpen(event);
        }
    }
    
    /**
     * WebSocket消息事件处理
     */
    _onMessage(event) {
        try {
            const message = JSON.parse(event.data);
            this._log('收到消息', message);
            
            // 根据消息类型处理
            const handler = this.messageHandlers[message.type];
            if (handler) {
                handler(message);
            }
            
            // 调用通用消息回调
            if (this.options.onMessage) {
                this.options.onMessage(message);
            }
            
            // 调用特定消息回调
            if (message.id && this.messageCallbacks[message.id]) {
                this.messageCallbacks[message.id](null, message);
                delete this.messageCallbacks[message.id];
            }
            
        } catch (e) {
            this._log('解析消息失败', e);
        }
    }
    
    /**
     * WebSocket连接关闭事件处理
     */
    _onClose(event) {
        this._log('WebSocket连接已关闭', event);
        this.connected = false;
        
        // 停止心跳
        this._stopHeartbeat();
        
        // 调用回调
        if (this.options.onClose) {
            this.options.onClose(event);
        }
        
        // 检查关闭原因，如果是认证失败则不重连
        if (event.code === 1002 || event.code === 1003) {
            this._log('认证失败，停止重连');
            if (this.options.onError) {
                this.options.onError({
                    type: 'AUTH_FAILED',
                    message: 'Token验证失败，请重新登录'
                });
            }
            return;
        }
        
        // 自动重连
        this._handleConnectionError(event);
    }
    
    /**
     * 重置重连状态
     */
    resetReconnectState() {
        this.reconnectAttempts = 0;
    }
    
    /**
     * 断开WebSocket连接
     */
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        
        this._clearTimers();
        this.connected = false;
    }
    
    /**
     * 发送消息
     */
    send(message, callback) {
        if (!this.connected) {
            this._log('Not connected');
            return false;
        }
        
        // 生成消息ID
        if (!message.id) {
            message.id = this._generateId();
        }
        
        // 设置发送时间
        if (!message.timestamp) {
            message.timestamp = new Date();
        }
        
        // 设置发送者ID
        if (!message.fromUserId) {
            message.fromUserId = this.options.userId;
        }
        
        // 存储回调函数
        if (callback) {
            this.messageCallbacks[message.id] = callback;
        }
        
        this.socket.send({
            data: JSON.stringify(message),
            success: () => {
                this._log('消息发送成功', message);
            },
            fail: (err) => {
                this._log('消息发送失败', err);
                if (callback) {
                    callback(err, null);
                }
            }
        });
        
        return true;
    }
    
    /**
     * 发送聊天消息
     */
    sendChatMessage(toUserId, content, callback) {
        const message = {
            type: 'CHAT',
            toUserId: toUserId,
            content: content
        };
        
        return this.send(message, callback);
    }
    
    /**
     * WebSocket连接打开事件处理
     */
    _onOpen(event) {
        this._log('WebSocket连接已建立');
        this.connected = true;
        
        // 清除重连定时器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        // 启动心跳
        this._startHeartbeat();
        
        // 调用回调
        if (this.options.onOpen) {
            this.options.onOpen(event);
        }
    }
    
    /**
     * WebSocket消息事件处理
     */
    _onMessage(event) {
        try {
            const message = JSON.parse(event.data);
            this._log('收到消息', message);
            
            // 根据消息类型处理
            const handler = this.messageHandlers[message.type];
            if (handler) {
                handler(message);
            }
            
            // 调用通用消息回调
            if (this.options.onMessage) {
                this.options.onMessage(message);
            }
            
            // 调用特定消息回调
            if (message.id && this.messageCallbacks[message.id]) {
                this.messageCallbacks[message.id](null, message);
                delete this.messageCallbacks[message.id];
            }
            
        } catch (e) {
            this._log('解析消息失败', e);
        }
    }
    
    /**
     * WebSocket连接关闭事件处理
     */
    _onClose(event) {
        this._log('WebSocket连接已关闭', event);
        this.connected = false;
        
        // 停止心跳
        this._stopHeartbeat();
        
        // 调用回调
        if (this.options.onClose) {
            this.options.onClose(event);
        }
        
        // 自动重连
        this._scheduleReconnect();
    }
    
    /**
     * WebSocket错误事件处理
     */
    _onError(event) {
        this._log('WebSocket错误', event);
        
        // 调用回调
        if (this.options.onError) {
            this.options.onError(event);
        }
    }
    
    /**
     * 处理聊天消息
     */
    _handleChatMessage(message) {
        // 更新消息状态
        if (message.fromUserId === this.options.userId) {
            if (message.status === 'DELIVERED') {
                this._log('消息已送达', message);
            }
        }
    }
    
    /**
     * 处理系统消息
     */
    _handleSystemMessage(message) {
        this._log('收到系统消息', message);
    }
    
    /**
     * 处理连接消息
     */
    _handleConnectMessage(message) {
        this._log('连接成功', message);
    }
    
    /**
     * 处理心跳消息
     */
    _handlePingMessage(message) {
        this._log('收到心跳响应', message);
    }
    
    /**
     * 处理ACK确认消息
     */
    _handleAckMessage(message) {
        this._log('收到消息确认', message);
        
        // 调用消息回调，更新消息状态
        if (message.id && this.messageCallbacks[message.id]) {
            this.messageCallbacks[message.id](null, message);
            delete this.messageCallbacks[message.id];
        }
    }
    
    /**
     * 启动心跳
     */
    _startHeartbeat() {
        this._stopHeartbeat();
        
        this.heartbeatTimer = setInterval(() => {
            if (this.connected) {
                this.send({
                    type: 'PING',
                    content: 'ping'
                });
            }
        }, this.options.heartbeatInterval);
    }
    
    /**
     * 停止心跳
     */
    _stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
    
    /**
     * 安排重连
     */
    _scheduleReconnect() {
        if (this.reconnectTimer) {
            return;
        }
        
        this.reconnectTimer = setTimeout(() => {
            this._log('尝试重连...');
            this.connect();
        }, this.options.reconnectInterval);
    }
    
    /**
     * 清除定时器
     */
    _clearTimers() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        this._stopHeartbeat();
    }
    
    /**
     * 生成唯一ID
     */
    _generateId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 日志输出
     */
    _log(message, data) {
        if (this.options.debug) {
            console.log('[WebSocketClient]', message, data || '');
        }
    }
}

export default WebSocketClient;