package com.play.xianshibusiness.enums;

import lombok.Getter;

/**
 * 金币操作类型枚举
 */
@Getter
public enum GoldOperationTypeEnum {
    
    RECHARGE(1, "金币充值"),
    CONSUMPTION(2, "金币消费"),
    SYSTEM_ADD(3, "系统调整-增加"),
    SYSTEM_DEDUCT(4, "系统调整-减少"),
    TASK_REWARD(5, "任务奖励"),
    REFUND(6, "退款"),
    WELCOME_GIFT(7, "新用户礼包"),
    ACTIVITY_REWARD(8, "活动奖励");
    
    private final Integer code;
    private final String desc;
    
    GoldOperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * 根据code获取枚举
     * 
     * @param code 编码
     * @return 对应的枚举值，如果找不到返回null
     */
    public static GoldOperationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (GoldOperationTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        
        return null;
    }
} 