package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.mapper.CMemberRoleMapper;
import com.play.xianshibusiness.pojo.CMemberRole;
import com.play.xianshibusiness.pojo.CRole;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (CMemberRole)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:17
 */
@Service
public class CMemberRoleService {
    
    @Resource
    private CMemberRoleMapper memberRoleMapper;
    
    @Resource
    private CRoleService roleService;
    
    /**
     * 获取会员拥有的所有角色ID
     * 
     * @param memberId 会员ID
     * @return 角色ID列表
     */
    public List<String> getMemberRoleIds(String memberId) {
        LambdaQueryWrapper<CMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMemberRole::getMemberId, memberId);
        
        List<CMemberRole> memberRoles = memberRoleMapper.selectList(queryWrapper);
        return memberRoles.stream()
                .map(CMemberRole::getRoleId)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取会员拥有的所有角色信息
     * 
     * @param memberId 会员ID
     * @return 角色信息列表
     */
    public List<CRole> getMemberRoles(String memberId) {
        List<String> roleIds = getMemberRoleIds(memberId);
        if (roleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        return roleService.getRolesByIds(roleIds);
    }
    
    /**
     * 添加会员角色
     * 
     * @param memberId 会员ID
     * @param roleId 角色ID
     * @return 是否成功
     */
    public boolean addMemberRole(String memberId, String roleId) {
        // 检查是否已有该角色
        LambdaQueryWrapper<CMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMemberRole::getMemberId, memberId)
                .eq(CMemberRole::getRoleId, roleId);
        
        if (memberRoleMapper.selectCount(queryWrapper) > 0) {
            return true; // 已有该角色，视为添加成功
        }
        
        // 添加新角色
        CMemberRole memberRole = new CMemberRole();
        memberRole.setMemberId(memberId);
        memberRole.setRoleId(roleId);
        
        return memberRoleMapper.insert(memberRole) > 0;
    }
    
    /**
     * 删除会员角色
     * 
     * @param memberId 会员ID
     * @param roleId 角色ID
     * @return 是否成功
     */
    public boolean removeMemberRole(String memberId, String roleId) {
        LambdaQueryWrapper<CMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMemberRole::getMemberId, memberId)
                .eq(CMemberRole::getRoleId, roleId);
        
        return memberRoleMapper.delete(queryWrapper) > 0;
    }
    
    /**
     * 检查会员是否拥有指定角色
     * 
     * @param memberId 会员ID
     * @param roleId 角色ID
     * @return 是否拥有该角色
     */
    public boolean hasMemberRole(String memberId, String roleId) {
        LambdaQueryWrapper<CMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMemberRole::getMemberId, memberId)
                .eq(CMemberRole::getRoleId, roleId);
        
        return memberRoleMapper.selectCount(queryWrapper) > 0;
    }
}

