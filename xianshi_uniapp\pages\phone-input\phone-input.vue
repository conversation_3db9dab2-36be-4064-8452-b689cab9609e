<template>
  <view class="container">
    <video
        class="bg-video"
        src="../../static/videos/login-bg.mp4"
        object-fit="cover"
        autoplay
        loop
        muted
        :controls="false"
        show-center-play-btn="false"
        show-fullscreen-btn="false"
        show-play-btn="false"
        enable-progress-gesture="false"
        disable-progress="true"
    />
    <view class="content">
      <view class="close" @tap="goBack">
        <text class="iconfont icon-close"></text>
      </view>
      <view class="welcome">
        <view class="logo">
          <text class="logo-text">FREE TIME</text>
        </view>
        <text class="subtitle">闲时有伴</text>
      </view>
      <view class="input-box">
        <view class="phone-input">
          <text class="prefix">+86</text>
          <input
            type="number"
            maxlength="11"
            placeholder="请输入手机号"
            placeholder-class="placeholder"
            @input="onPhoneInput"
            :value="phone"
          />
        </view>
        <button
          class="next-btn"
          :class="{ active: phone.length === 11 }"
          @tap="onNext"
          :disabled="phone.length !== 11"
        >
          下一步
        </button>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp().globalData;
export default {
  data() {
    return {
      phone: "15693409989",
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    onPhoneInput(e) {
      this.phone = e.detail.value;
    },

    // 获取验证码方法
    sendCaptcha(phoneNumber) {
      return new Promise((resolve, reject) => {
		  let res = this.$requestHttp.get(app.commonApi.getLoginRole, {
			data: {
			  phone: phoneNumber,
			  purpose: 'LOGIN'
			},
		  })
		  console.log("Res", res);
		  if (res.data && res.data.code === 200) {
			resolve(res.data.data);
		  } else {
			reject(res.data.msg || '获取验证码失败');
		  }
      });
    },

    // 调用登录接口方法 - 已移除，登录逻辑在verify-code页面处理

    onNext() {
      if (this.phone.length === 11) {
        // 跳转到验证码页面
        uni.navigateTo({
          url: `/pages/verify-code/verify-code?phone=${this.phone}`,
        });
      }
    }
  },
  onLoad() {
    // 页面加载完成
  }
};
</script>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.bg-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 100rpx 40rpx;
  box-sizing: border-box;
}

.close {
  position: absolute;
  top: 100rpx;
  left: 40rpx;
  color: #fff;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close .iconfont {
  font-size: 40rpx;
}

.welcome {
  text-align: center;
  color: #fff;
  margin-top: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: bold;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.logo-text {
  font-size: 48rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 32rpx;
  margin-top: 10rpx;
  font-weight: bold;
}

.input-box {
  margin-top: 100rpx;
  width: 100%;
}

.phone-input {
  width: 670rpx;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 44rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 0 40rpx;
  box-sizing: border-box;
  margin-bottom: 40rpx;
}

.prefix {
  color: #fff;
  font-size: 32rpx;
  margin-right: 20rpx;
}

input {
  flex: 1;
  color: #fff;
  font-size: 32rpx;
}

.placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.next-btn {
  width: 670rpx;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  opacity: 0.5;
}
.bg-video {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  object-fit: cover;
}
.next-btn.active {
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  opacity: 1;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
</style>
