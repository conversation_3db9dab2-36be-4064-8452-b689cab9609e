<template>
  <div class="app-container clay-container">
    <div class="filter-container clay-card">
      <el-form :inline="true" :model="listQuery" class="filter-form">
        <el-form-item label="订单号">
          <el-input v-model="listQuery.orderId" placeholder="请输入订单号" clearable class="clay-input" />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="listQuery.status" placeholder="请选择订单状态" clearable class="clay-select">
            <el-option v-for="item in orderStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户昵称">
          <el-input v-model="listQuery.memberName" placeholder="请输入用户昵称" clearable class="clay-input" />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            class="clay-date-picker"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" class="clay-button">查询</el-button>
          <el-button @click="resetQuery" class="clay-button clay-button-secondary">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      class="clay-table"
    >
      <el-table-column label="订单ID" align="center" min-width="120">
        <template slot-scope="{row}">
          <span>{{ row.orderId }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="陪玩项目" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.typeName }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="发单用户" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.memberName }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="订单状态" min-width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getOrderStatusType(row.status)" class="clay-tag">{{ row.statusDesc }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="地点" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.location }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="小时费用" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.hourlyRate }} 元/时</span>
        </template>
      </el-table-column>
      
      <el-table-column label="参与人数" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.personCount }} 人</span>
        </template>
      </el-table-column>
      
      <el-table-column label="金币消耗" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.goldCost || 0 }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createTime }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" min-width="200" class-name="fixed-width">
        <template slot-scope="{row}">
          <el-button 
            type="info" 
            size="mini" 
            @click="handleDetail(row)" 
            style="background-color: #909399; color: #fff; border-color: #909399;"
          >查看</el-button>
          
          <!-- 审核按钮只在待审核状态显示 -->
          <el-button 
            v-if="row.status === 1" 
            type="success" 
            size="mini" 
            @click="handleAudit(row, true)" 
            style="background-color: #67c23a; color: #fff; border-color: #67c23a;"
          >通过</el-button>
          
          <el-button 
            v-if="row.status === 1" 
            type="danger" 
            size="mini" 
            @click="handleAudit(row, false)" 
            style="background-color: #f56c6c; color: #fff; border-color: #f56c6c;"
          >拒绝</el-button>
          
          <!-- 取消按钮在报名中、待选择达人、进行中状态显示 -->
          <el-button 
            v-if="[2, 3, 4].includes(row.status)" 
            type="warning" 
            size="mini" 
            @click="handleCancel(row)" 
            style="background-color: #e6a23c; color: #fff; border-color: #e6a23c;"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
      class="clay-pagination"
    />
    
    <!-- 审核对话框 -->
    <el-dialog :title="auditForm.pass ? '审核通过' : '审核拒绝'" :visible.sync="auditDialogVisible" width="500px" class="clay-dialog">
      <el-form ref="auditForm" :model="auditForm" label-width="100px">
        <el-form-item label="审核意见" prop="reason" :rules="{ required: !auditForm.pass, message: '请填写拒绝原因', trigger: 'blur' }">
          <el-input 
            v-model="auditForm.reason" 
            type="textarea" 
            :placeholder="auditForm.pass ? '审核意见（选填）' : '请填写拒绝原因'"
            :rows="4"
            class="clay-textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false" class="clay-button clay-button-secondary">取消</el-button>
        <el-button type="primary" @click="submitAudit" class="clay-button">确认</el-button>
      </div>
    </el-dialog>
    
    <!-- 取消订单对话框 -->
    <el-dialog title="取消订单" :visible.sync="cancelDialogVisible" width="500px" class="clay-dialog">
      <el-form ref="cancelForm" :model="cancelForm" label-width="100px">
        <el-form-item label="取消原因" prop="reason" :rules="{ required: true, message: '请填写取消原因', trigger: 'blur' }">
          <el-input 
            v-model="cancelForm.reason" 
            type="textarea" 
            placeholder="请填写取消原因"
            :rows="4"
            class="clay-textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialogVisible = false" class="clay-button clay-button-secondary">取消</el-button>
        <el-button type="primary" @click="submitCancel" class="clay-button">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOrderList, auditOrder, cancelOrder } from '@/api/order';
import Pagination from '@/components/Pagination';

export default {
  name: 'OrderList',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        orderId: '',
        status: '',
        memberName: '',
        createTimeStart: '',
        createTimeEnd: ''
      },
      dateRange: [],
      orderStatusOptions: [
        { value: 0, label: '待发布' },
        { value: 1, label: '待审核' },
        { value: 2, label: '发布中' },
        { value: 3, label: '待选择达人' },
        { value: 4, label: '进行中' },
        { value: 5, label: '已完成' },
        { value: 6, label: '已取消' }
      ],
      auditDialogVisible: false,
      auditForm: {
        orderId: '',
        pass: true,
        reason: ''
      },
      cancelDialogVisible: false,
      cancelForm: {
        orderId: '',
        reason: ''
      }
    };
  },
  watch: {
    dateRange(val) {
      if (val) {
        this.listQuery.createTimeStart = val[0];
        this.listQuery.createTimeEnd = val[1];
      } else {
        this.listQuery.createTimeStart = '';
        this.listQuery.createTimeEnd = '';
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      getOrderList(this.listQuery).then(response => {
        this.list = response.data.records || [];
        this.total = response.data.total || 0;
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      });
    },
    handleSearch() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        orderId: '',
        status: '',
        memberName: '',
        createTimeStart: '',
        createTimeEnd: ''
      };
      this.dateRange = [];
      this.getList();
    },
    handleDetail(row) {
      this.$router.push({ path: `/activity/order/detail/${row.orderId}` });
    },
    handleAudit(row, pass) {
      this.auditForm = {
        orderId: row.orderId,
        pass: pass,
        reason: ''
      };
      this.auditDialogVisible = true;
    },
    submitAudit() {
      this.$refs.auditForm.validate(valid => {
        if (valid) {
          const data = {
            orderId: this.auditForm.orderId,
            approved: this.auditForm.pass,
            remark: this.auditForm.reason || ''
          };
          
          auditOrder(data).then(response => {
            this.$message({
              type: 'success',
              message: this.auditForm.pass ? '审核通过成功' : '审核拒绝成功'
            });
            this.auditDialogVisible = false;
            this.getList();
          });
        }
      });
    },
    handleCancel(row) {
      this.cancelForm = {
        orderId: row.orderId,
        reason: ''
      };
      this.cancelDialogVisible = true;
    },
    submitCancel() {
      this.$refs.cancelForm.validate(valid => {
        if (valid) {
          cancelOrder(this.cancelForm.orderId, this.cancelForm.reason).then(response => {
            this.$message({
              type: 'success',
              message: '取消订单成功'
            });
            this.cancelDialogVisible = false;
            this.getList();
          });
        }
      });
    },
    getOrderStatusType(status) {
      const statusMap = {
        0: 'info',    // 待发布 - 灰蓝色
        1: 'warning', // 待审核 - 黄色
        2: 'primary', // 发布中 - 蓝色
        3: 'success', // 待选择达人 - 绿色
        4: 'info',    // 进行中 - 灰蓝色
        5: 'success', // 已完成 - 绿色
        6: 'danger'   // 已取消 - 红色
      };
      return statusMap[status] || 'info';
    }
  }
};
</script>

<style lang="scss" scoped>
.clay-container {
  background-color: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
}

.clay-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.clay-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  
  ::v-deep .el-table__header-wrapper {
    th {
      background-color: #f7f9fc;
      color: #606266;
      font-weight: 600;
    }
  }
  
  ::v-deep .el-table__body-wrapper {
    td {
      padding: 16px 0;
    }
  }
}

.clay-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  &.clay-button-sm {
    padding: 7px 12px;
  }
  
  &.clay-button-secondary {
    background-color: #f5f7fa;
    color: #606266;
    border-color: #dcdfe6;
    
    &:hover {
      background-color: #e9ecf2;
    }
  }
  
  &.clay-button-success {
    background-color: #67c23a;
    border-color: #67c23a;
    color: #fff;
    
    &:hover {
      background-color: #85ce61;
      border-color: #85ce61;
    }
  }
  
  &.clay-button-danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #fff;
    
    &:hover {
      background-color: #f78989;
      border-color: #f78989;
    }
  }
  
  &.clay-button-warning {
    background-color: #e6a23c;
    border-color: #e6a23c;
    color: #fff;
    
    &:hover {
      background-color: #ebb563;
      border-color: #ebb563;
    }
  }
  
  &.clay-button-info {
    background-color: #909399;
    border-color: #909399;
    color: #fff;
    
    &:hover {
      background-color: #a6a9ad;
      border-color: #a6a9ad;
    }
  }
}

.clay-input, .clay-select, .clay-date-picker {
  ::v-deep .el-input__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    transition: all 0.3s;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-textarea {
  ::v-deep .el-textarea__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-tag {
  border-radius: 16px;
  padding: 2px 10px;
  font-weight: 500;
}

.clay-dialog {
  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
    
    .el-dialog__header {
      background-color: #f7f9fc;
      padding: 16px 20px;
      
      .el-dialog__title {
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}

.clay-pagination {
  margin-top: 24px;
  text-align: right;
  
  ::v-deep .el-pagination {
    padding: 10px 0;
    
    button, li {
      background-color: #f9fafc;
      border-radius: 6px;
      margin: 0 3px;
      
      &.active {
        background-color: #409eff;
        color: white;
      }
      
      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}
</style> 