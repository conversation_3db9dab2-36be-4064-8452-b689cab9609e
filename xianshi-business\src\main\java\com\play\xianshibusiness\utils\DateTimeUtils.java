package com.play.xianshibusiness.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日期时间工具类，提供灵活的日期时间解析功能
 */
public class DateTimeUtils {
    
    private static final String DEFAULT_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final Pattern DATE_PATTERN = Pattern.compile("(\\d+)-(\\d+)-(\\d+)");
    
    /**
     * 解析日期时间字符串，支持灵活格式（单数月日）
     * 
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象
     */
    public static LocalDateTime parseFlexibleDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        
        // 尝试直接解析标准格式
        try {
            return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_PATTERN));
        } catch (Exception e) {
            // 尝试修复单数月日
            try {
                Matcher matcher = DATE_PATTERN.matcher(dateTimeStr);
                if (matcher.find()) {
                    String year = matcher.group(1);
                    String month = matcher.group(2);
                    String day = matcher.group(3);
                    
                    // 补零
                    if (month.length() == 1) {
                        month = "0" + month;
                    }
                    if (day.length() == 1) {
                        day = "0" + day;
                    }
                    
                    // 替换日期部分
                    String fixedDate = year + "-" + month + "-" + day;
                    String fixedDateTime = dateTimeStr.replace(matcher.group(0), fixedDate);
                    
                    // 尝试用标准格式解析
                    return LocalDateTime.parse(fixedDateTime, DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_PATTERN));
                }
            } catch (Exception ignored) {
                // 继续尝试其他格式
            }
            
            // 如果所有尝试都失败，抛出异常
            throw new IllegalArgumentException("无法解析日期时间: " + dateTimeStr);
        }
    }
    
    /**
     * 将LocalDateTime格式化为标准格式字符串
     * 
     * @param dateTime LocalDateTime对象
     * @return 格式化后的字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_PATTERN));
    }
} 