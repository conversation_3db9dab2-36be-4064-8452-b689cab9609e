package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.member.MemberCollectDTO;
import com.play.xianshibusiness.enums.CollectType;
import com.play.xianshibusiness.mapper.CMemberCollectMapper;
import com.play.xianshibusiness.mapper.CMemberMapper;
import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.pojo.CMemberCollect;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 会员收藏服务
 */
@Service
@Slf4j
public class CMemberCollectService {
    
    @Resource
    private CMemberCollectMapper memberCollectMapper;
    
    @Resource
    private CMemberMapper memberMapper;
    
    /**
     * 添加收藏
     * 
     * @param memberId 会员ID
     * @param targetId 目标ID（可能是会员ID、订单ID等）
     * @param type 收藏类型
     * @param remark 备注
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addCollect(String memberId, String targetId, CollectType type, String remark) {
        // 检查是否已收藏
        LambdaQueryWrapper<CMemberCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMemberCollect::getMemberId, memberId)
                   .eq(CMemberCollect::getTargetId, targetId)
                   .eq(CMemberCollect::getType, type.getCode())
                   .eq(CMemberCollect::getDeleted, false);
        
        CMemberCollect existCollect = memberCollectMapper.selectOne(queryWrapper);
        if(existCollect != null) {
            // 已收藏，返回成功
            return true;
        }
        
        // 创建收藏记录
        CMemberCollect collect = new CMemberCollect();
        collect.setId(UUID.randomUUID().toString());
        collect.setMemberId(memberId);
        collect.setTargetId(targetId);
        collect.setType(type.getCode());
        collect.setRemark(remark);
        collect.setCreateTime(LocalDateTime.now());
        collect.setUpdateTime(LocalDateTime.now());
        collect.setDeleted(false);
        collect.setAvailable(true);
        
        memberCollectMapper.insert(collect);
        
        return true;
    }
    
    /**
     * 取消收藏
     * 
     * @param memberId 会员ID
     * @param targetId 目标ID
     * @param type 收藏类型
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelCollect(String memberId, String targetId, CollectType type) {
        // 查询收藏记录
        LambdaQueryWrapper<CMemberCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMemberCollect::getMemberId, memberId)
                   .eq(CMemberCollect::getTargetId, targetId)
                   .eq(CMemberCollect::getType, type.getCode())
                   .eq(CMemberCollect::getDeleted, false);
        
        CMemberCollect collect = memberCollectMapper.selectOne(queryWrapper);
        if(collect == null) {
            // 未收藏，无需操作
            return false;
        }
        
        // 逻辑删除收藏记录
        collect.setDeleted(true);
        collect.setUpdateTime(LocalDateTime.now());
        memberCollectMapper.updateById(collect);
        
        return true;
    }
    
    /**
     * 查询是否已收藏
     * 
     * @param memberId 会员ID
     * @param targetId 目标ID
     * @param type 收藏类型
     * @return 是否已收藏
     */
    public boolean isCollected(String memberId, String targetId, CollectType type) {
        LambdaQueryWrapper<CMemberCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMemberCollect::getMemberId, memberId)
                   .eq(CMemberCollect::getTargetId, targetId)
                   .eq(CMemberCollect::getType, type.getCode())
                   .eq(CMemberCollect::getDeleted, false);
        
        return memberCollectMapper.selectCount(queryWrapper) > 0;
    }
    
    /**
     * 获取会员收藏列表
     * 
     * @param memberId 会员ID
     * @param type 收藏类型
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 收藏列表
     */
    public Page<MemberCollectDTO> getCollectList(String memberId, CollectType type, Integer pageNum, Integer pageSize) {
        // 创建分页对象
        Page<CMemberCollect> page = new Page<>(pageNum, pageSize);
        
        // 构建查询条件
        LambdaQueryWrapper<CMemberCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMemberCollect::getMemberId, memberId)
                   .eq(CMemberCollect::getType, type.getCode())
                   .eq(CMemberCollect::getDeleted, false)
                   .orderByDesc(CMemberCollect::getCreateTime);
        
        // 执行查询
        Page<CMemberCollect> collectPage = memberCollectMapper.selectPage(page, queryWrapper);
        
        // 转换为DTO
        Page<MemberCollectDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(collectPage, dtoPage, "records");
        
        List<MemberCollectDTO> dtoList = collectPage.getRecords().stream().map(collect -> {
            MemberCollectDTO dto = new MemberCollectDTO();
            BeanUtils.copyProperties(collect, dto);
            
            // 如果是收藏会员，填充会员信息
            if (CollectType.MEMBER.getCode().equals(collect.getType())) {
                CMember targetMember = memberMapper.selectById(collect.getTargetId());
                if(targetMember != null) {
                    dto.setTargetName(targetMember.getNickname());
                    dto.setTargetAvatar(targetMember.getAvatar());
                }
            }
            // 其他类型的收藏可以在这里扩展...
            
            return dto;
        }).collect(Collectors.toList());
        
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }
}

