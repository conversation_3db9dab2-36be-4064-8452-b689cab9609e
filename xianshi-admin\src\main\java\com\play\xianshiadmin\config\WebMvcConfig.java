package com.play.xianshiadmin.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

/**
 * Web MVC 配置
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    
    private static final Logger logger = LoggerFactory.getLogger(WebMvcConfig.class);
    
    @Value("${file.upload.path:./upload}")
    private String uploadPath;
    
    @Value("${file.access.path:/upload}")
    private String accessPath;
    
    /**
     * 添加静态资源映射
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 确保上传路径存在
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            boolean mkdirResult = uploadDir.mkdirs();
            logger.info("创建上传目录: {}, 结果: {}", uploadPath, mkdirResult);
        }
        
        // 配置上传文件的访问路径
        String filePath = "file:" + uploadPath + File.separator;
        logger.info("配置静态资源映射: {} -> {}", accessPath + "/**", filePath);
        registry.addResourceHandler(accessPath + "/**")
                .addResourceLocations(filePath);
    }
    
    /**
     * 配置跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
} 