<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @tap="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="nav-title">选择位置</view>
      <view class="nav-right"></view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <text class="search-icon">🔍</text>
        <input 
          class="search-input" 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索地点、商圈、地址" 
          @input="onSearchInput"
          @focus="onSearchFocus"
          @blur="onSearchBlur"
        />
        <text v-if="searchKeyword" class="clear-icon" @tap="clearSearch">✕</text>
      </view>
    </view>

    <!-- 搜索建议列表 -->
    <view v-if="showSuggestions && suggestions.length > 0" class="suggestions-container">
      <scroll-view class="suggestions-list" scroll-y>
        <view 
          v-for="(item, index) in suggestions" 
          :key="index" 
          class="suggestion-item"
          @tap="selectSuggestion(item)"
        >
          <view class="suggestion-icon">📍</view>
          <view class="suggestion-content">
            <view class="suggestion-title">{{ item.title }}</view>
            <view class="suggestion-address">{{ item.address }}</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container" v-show="!showSuggestions">
      <map
        id="map"
        class="map"
        :longitude="longitude"
        :latitude="latitude"
        :scale="scale"
        :markers="markers"
        :show-location="true"
        @regionchange="onRegionChange"
        @tap="onMapTap"
        @markertap="onMarkerTap"
      >
        <!-- 中心点标记 -->
        <cover-view class="center-marker">
          <cover-image class="marker-icon" src="/static/images/location-pin.png" mode="aspectFit"></cover-image>
        </cover-view>
        
        <!-- 地图控制按钮 -->
        <cover-view class="map-controls">
          <cover-view class="control-btn zoom-in" @tap="zoomIn">+</cover-view>
          <cover-view class="control-btn zoom-out" @tap="zoomOut">-</cover-view>
          <cover-view class="control-btn location-btn" @tap="getCurrentLocation">📍</cover-view>
        </cover-view>
      </map>
    </view>

    <!-- 底部信息面板 -->
    <view class="bottom-panel" v-show="!showSuggestions">
      <!-- 当前位置信息 -->
      <view class="current-location">
        <view class="location-header">
          <text class="location-title">当前选择位置</text>
          <text v-if="isLoading" class="loading-text">定位中...</text>
        </view>
        <view class="location-info">
          <view class="location-name">{{ currentLocation.name || '未知位置' }}</view>
          <view class="location-address">{{ currentLocation.address || '获取地址中...' }}</view>
        </view>
      </view>

      <!-- 附近地点列表 -->
      <view class="nearby-places" v-if="nearbyPlaces.length > 0">
        <view class="section-title">附近地点</view>
        <scroll-view class="places-list" scroll-y>
          <view 
            v-for="(place, index) in nearbyPlaces" 
            :key="index"
            class="place-item"
            :class="{ selected: selectedPlace && selectedPlace.id === place.id }"
            @tap="selectPlace(place)"
          >
            <view class="place-icon">{{ getPlaceIcon(place.category) }}</view>
            <view class="place-content">
              <view class="place-name">{{ place.title }}</view>
              <view class="place-address">{{ place.address }}</view>
              <view class="place-distance" v-if="place.distance">{{ formatDistance(place.distance) }}</view>
            </view>
            <view class="place-select" v-if="selectedPlace && selectedPlace.id === place.id">✓</view>
          </view>
          
          <!-- 加载更多 -->
          <view v-if="hasMorePlaces" class="load-more" @tap="loadMorePlaces">
            <text v-if="loadingMore">加载中...</text>
            <text v-else>加载更多</text>
          </view>
        </scroll-view>
      </view>

      <!-- 确认按钮 -->
      <view class="confirm-container">
        <button class="confirm-btn" @tap="confirmSelection" :disabled="!currentLocation.name">
          确认选择
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 地图相关
      longitude: 104.066803, // 默认经度（成都）
      latitude: 30.572816,   // 默认纬度（成都）
      scale: 16,             // 地图缩放级别
      markers: [],           // 地图标记点
      
      // 搜索相关
      searchKeyword: '',     // 搜索关键词
      showSuggestions: false, // 是否显示搜索建议
      suggestions: [],       // 搜索建议列表
      searchTimer: null,     // 搜索防抖定时器
      
      // 位置信息
      currentLocation: {     // 当前选择的位置
        name: '',
        address: '',
        latitude: 0,
        longitude: 0
      },
      selectedPlace: null,   // 选中的附近地点
      
      // 附近地点
      nearbyPlaces: [],      // 附近地点列表
      hasMorePlaces: false,  // 是否有更多地点
      loadingMore: false,    // 是否正在加载更多
      currentPage: 1,        // 当前页码
      
      // 状态
      isLoading: false,      // 是否正在加载
      reverseGeocodingTimer: null, // 逆地理编码防抖定时器
      
      // API配置
      apiKey: 'LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP'
    }
  },
  
  onLoad(options) {
    console.log('地图选择器页面加载，参数:', options);
    
    // 如果有传入的位置参数，使用传入的位置
    if (options.longitude && options.latitude) {
      this.longitude = parseFloat(options.longitude);
      this.latitude = parseFloat(options.latitude);
      this.currentLocation.longitude = this.longitude;
      this.currentLocation.latitude = this.latitude;
      
      if (options.title) {
        this.currentLocation.name = decodeURIComponent(options.title);
      }
      if (options.address) {
        this.currentLocation.address = decodeURIComponent(options.address);
      }
    }
    
    // 初始化地图
    this.initMap();
  },
  
  onReady() {
    // 页面渲染完成后获取当前位置
    this.getCurrentLocation();
  },
  
  methods: {
    // 初始化地图
    initMap() {
      // 设置地图中心点
      this.updateMapCenter(this.longitude, this.latitude);
      
      // 获取当前位置的详细信息
      if (!this.currentLocation.name) {
        this.reverseGeocoding(this.latitude, this.longitude);
      }
      
      // 获取附近地点
      this.getNearbyPlaces();
    },
    
    // 更新地图中心点
    updateMapCenter(longitude, latitude) {
      this.longitude = longitude;
      this.latitude = latitude;
      this.currentLocation.longitude = longitude;
      this.currentLocation.latitude = latitude;
    },

    // 获取当前位置
    getCurrentLocation() {
      this.isLoading = true;

      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          console.log('获取当前位置成功:', res);
          this.updateMapCenter(res.longitude, res.latitude);
          this.reverseGeocoding(res.latitude, res.longitude);
          this.getNearbyPlaces();
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          uni.showToast({
            title: '获取位置失败，请检查定位权限',
            icon: 'none'
          });
          // 使用默认位置
          this.reverseGeocoding(this.latitude, this.longitude);
          this.getNearbyPlaces();
        },
        complete: () => {
          this.isLoading = false;
        }
      });
    },

    // 逆地理编码（坐标转地址）
    reverseGeocoding(latitude, longitude) {
      // 防抖处理
      if (this.reverseGeocodingTimer) {
        clearTimeout(this.reverseGeocodingTimer);
      }

      this.reverseGeocodingTimer = setTimeout(() => {
        this.callReverseGeocodingAPI(latitude, longitude);
      }, 300);
    },

    // 调用逆地理编码API
    callReverseGeocodingAPI(latitude, longitude) {
      const callbackName = 'reverseGeocodingCallback' + Date.now();

      // 创建全局回调函数
      // #ifdef H5
      window[callbackName] = (data) => {
        if (data && data.status === 0 && data.result) {
          const result = data.result;
          const addressComponent = result.address_component;

          // 更新当前位置信息
          this.currentLocation.address = result.address || '';
          this.currentLocation.name = result.formatted_addresses?.recommend ||
                                     result.address ||
                                     `${addressComponent.province}${addressComponent.city}${addressComponent.district}`;

          // 如果有POI信息，优先使用POI名称
          if (result.pois && result.pois.length > 0) {
            this.currentLocation.name = result.pois[0].title;
          }
        }

        // 清理回调函数和script标签
        delete window[callbackName];
        const script = document.getElementById(callbackName);
        if (script) {
          document.head.removeChild(script);
        }
      };

      // 创建JSONP请求
      const script = document.createElement('script');
      script.id = callbackName;
      script.src = `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${this.apiKey}&get_poi=1&output=jsonp&callback=${callbackName}`;
      script.onerror = () => {
        this.currentLocation.name = '未知位置';
        this.currentLocation.address = '获取地址失败';
        delete window[callbackName];
        if (document.getElementById(callbackName)) {
          document.head.removeChild(document.getElementById(callbackName));
        }
      };

      document.head.appendChild(script);
      // #endif

      // #ifndef H5
      // 非H5环境使用uni.request
      uni.request({
        url: `https://apis.map.qq.com/ws/geocoder/v1/`,
        data: {
          location: `${latitude},${longitude}`,
          key: this.apiKey,
          get_poi: 1
        },
        success: (res) => {
          if (res.data && res.data.status === 0 && res.data.result) {
            const result = res.data.result;
            const addressComponent = result.address_component;

            this.currentLocation.address = result.address || '';
            this.currentLocation.name = result.formatted_addresses?.recommend ||
                                       result.address ||
                                       `${addressComponent.province}${addressComponent.city}${addressComponent.district}`;

            if (result.pois && result.pois.length > 0) {
              this.currentLocation.name = result.pois[0].title;
            }
          }
        },
        fail: (err) => {
          console.error('逆地理编码失败:', err);
          this.currentLocation.name = '未知位置';
          this.currentLocation.address = '获取地址失败';
        }
      });
      // #endif
    },

    // 获取附近地点
    getNearbyPlaces() {
      this.currentPage = 1;
      this.nearbyPlaces = [];
      this.loadNearbyPlaces();
    },

    // 加载附近地点
    loadNearbyPlaces() {
      const callbackName = 'nearbyPlacesCallback' + Date.now();

      // #ifdef H5
      window[callbackName] = (data) => {
        if (data && data.status === 0 && data.data) {
          const places = data.data.map(item => ({
            id: item.id,
            title: item.title,
            address: item.address,
            category: item.category,
            location: item.location,
            distance: this.calculateDistance(
              this.latitude, this.longitude,
              item.location.lat, item.location.lng
            )
          }));

          if (this.currentPage === 1) {
            this.nearbyPlaces = places;
          } else {
            this.nearbyPlaces = [...this.nearbyPlaces, ...places];
          }

          this.hasMorePlaces = places.length >= 20; // 假设每页20条
        }

        // 清理
        delete window[callbackName];
        const script = document.getElementById(callbackName);
        if (script) {
          document.head.removeChild(script);
        }
        this.loadingMore = false;
      };

      const script = document.createElement('script');
      script.id = callbackName;
      script.src = `https://apis.map.qq.com/ws/place/v1/search?boundary=nearby(${this.latitude},${this.longitude},1000)&orderby=_distance&page_size=20&page_index=${this.currentPage}&key=${this.apiKey}&output=jsonp&callback=${callbackName}`;
      script.onerror = () => {
        this.loadingMore = false;
        delete window[callbackName];
        if (document.getElementById(callbackName)) {
          document.head.removeChild(document.getElementById(callbackName));
        }
      };

      document.head.appendChild(script);
      // #endif

      // #ifndef H5
      uni.request({
        url: 'https://apis.map.qq.com/ws/place/v1/search',
        data: {
          boundary: `nearby(${this.latitude},${this.longitude},1000)`,
          orderby: '_distance',
          page_size: 20,
          page_index: this.currentPage,
          key: this.apiKey
        },
        success: (res) => {
          if (res.data && res.data.status === 0 && res.data.data) {
            const places = res.data.data.map(item => ({
              id: item.id,
              title: item.title,
              address: item.address,
              category: item.category,
              location: item.location,
              distance: this.calculateDistance(
                this.latitude, this.longitude,
                item.location.lat, item.location.lng
              )
            }));

            if (this.currentPage === 1) {
              this.nearbyPlaces = places;
            } else {
              this.nearbyPlaces = [...this.nearbyPlaces, ...places];
            }

            this.hasMorePlaces = places.length >= 20;
          }
        },
        fail: (err) => {
          console.error('获取附近地点失败:', err);
        },
        complete: () => {
          this.loadingMore = false;
        }
      });
      // #endif
    },

    // 搜索输入处理
    onSearchInput() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      this.searchTimer = setTimeout(() => {
        if (this.searchKeyword.trim()) {
          this.searchSuggestions();
        } else {
          this.suggestions = [];
          this.showSuggestions = false;
        }
      }, 300);
    },

    // 搜索获得焦点
    onSearchFocus() {
      if (this.searchKeyword.trim()) {
        this.showSuggestions = true;
      }
    },

    // 搜索失去焦点
    onSearchBlur() {
      // 延迟隐藏，以便点击建议项
      setTimeout(() => {
        this.showSuggestions = false;
      }, 200);
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
      this.suggestions = [];
      this.showSuggestions = false;
    },

    // 搜索建议
    searchSuggestions() {
      const callbackName = 'suggestionsCallback' + Date.now();

      // #ifdef H5
      window[callbackName] = (data) => {
        if (data && data.status === 0 && data.data) {
          this.suggestions = data.data.map(item => ({
            title: item.title,
            address: item.address,
            location: item.location,
            id: item.id
          }));
          this.showSuggestions = true;
        }

        // 清理
        delete window[callbackName];
        const script = document.getElementById(callbackName);
        if (script) {
          document.head.removeChild(script);
        }
      };

      const script = document.createElement('script');
      script.id = callbackName;
      script.src = `https://apis.map.qq.com/ws/place/v1/suggestion?keyword=${encodeURIComponent(this.searchKeyword)}&region=全国&key=${this.apiKey}&output=jsonp&callback=${callbackName}`;
      script.onerror = () => {
        delete window[callbackName];
        if (document.getElementById(callbackName)) {
          document.head.removeChild(document.getElementById(callbackName));
        }
      };

      document.head.appendChild(script);
      // #endif

      // #ifndef H5
      uni.request({
        url: 'https://apis.map.qq.com/ws/place/v1/suggestion',
        data: {
          keyword: this.searchKeyword,
          region: '全国',
          key: this.apiKey
        },
        success: (res) => {
          if (res.data && res.data.status === 0 && res.data.data) {
            this.suggestions = res.data.data.map(item => ({
              title: item.title,
              address: item.address,
              location: item.location,
              id: item.id
            }));
            this.showSuggestions = true;
          }
        },
        fail: (err) => {
          console.error('搜索建议失败:', err);
        }
      });
      // #endif
    },

    // 选择搜索建议
    selectSuggestion(item) {
      this.searchKeyword = item.title;
      this.showSuggestions = false;

      if (item.location) {
        this.updateMapCenter(item.location.lng, item.location.lat);
        this.currentLocation.name = item.title;
        this.currentLocation.address = item.address;
        this.reverseGeocoding(item.location.lat, item.location.lng);
        this.getNearbyPlaces();
      }
    },

    // 地图区域变化
    onRegionChange(e) {
      if (e.type === 'end') {
        this.updateMapCenter(e.detail.centerLocation.longitude, e.detail.centerLocation.latitude);
        this.reverseGeocoding(e.detail.centerLocation.latitude, e.detail.centerLocation.longitude);
        this.getNearbyPlaces();
      }
    },

    // 地图点击
    onMapTap(e) {
      console.log('地图点击:', e);
    },

    // 标记点击
    onMarkerTap(e) {
      console.log('标记点击:', e);
    },

    // 放大地图
    zoomIn() {
      if (this.scale < 20) {
        this.scale++;
      }
    },

    // 缩小地图
    zoomOut() {
      if (this.scale > 5) {
        this.scale--;
      }
    },

    // 选择附近地点
    selectPlace(place) {
      this.selectedPlace = place;
      this.currentLocation.name = place.title;
      this.currentLocation.address = place.address;
      this.currentLocation.longitude = place.location.lng;
      this.currentLocation.latitude = place.location.lat;

      // 更新地图中心
      this.updateMapCenter(place.location.lng, place.location.lat);
    },

    // 加载更多地点
    loadMorePlaces() {
      if (this.loadingMore || !this.hasMorePlaces) return;

      this.loadingMore = true;
      this.currentPage++;
      this.loadNearbyPlaces();
    },

    // 计算距离
    calculateDistance(lat1, lng1, lat2, lng2) {
      const R = 6371; // 地球半径（公里）
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLng = (lng2 - lng1) * Math.PI / 180;
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng / 2) * Math.sin(dLng / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c * 1000; // 返回米
    },

    // 格式化距离
    formatDistance(distance) {
      if (distance < 1000) {
        return `${Math.round(distance)}m`;
      } else {
        return `${(distance / 1000).toFixed(1)}km`;
      }
    },

    // 获取地点图标
    getPlaceIcon(category) {
      const iconMap = {
        '美食': '🍽️',
        '酒店': '🏨',
        '购物': '🛍️',
        '生活服务': '🏪',
        '丽人': '💄',
        '旅游景点': '🏛️',
        '休闲娱乐': '🎮',
        '运动健身': '💪',
        '教育培训': '📚',
        '医疗': '🏥',
        '汽车服务': '🚗',
        '交通设施': '🚇',
        '金融': '🏦',
        '房地产': '🏠',
        '公司企业': '🏢'
      };

      return iconMap[category] || '📍';
    },

    // 确认选择
    confirmSelection() {
      if (!this.currentLocation.name) {
        uni.showToast({
          title: '请选择一个位置',
          icon: 'none'
        });
        return;
      }

      // 保存选择的位置到本地存储
      const selectedLocation = {
        name: this.currentLocation.name,
        address: this.currentLocation.address,
        latitude: this.currentLocation.latitude,
        longitude: this.currentLocation.longitude
      };

      uni.setStorageSync('selectedLocation', selectedLocation);

      // 返回上一页
      uni.navigateBack({
        success: () => {
          uni.showToast({
            title: '位置选择成功',
            icon: 'success'
          });
        }
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style scoped>
.container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 44rpx);
  z-index: 1000;
  border-bottom: 1rpx solid #eee;
}

.nav-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.nav-right {
  width: 60rpx;
}

/* 搜索框样式 */
.search-container {
  position: fixed;
  top: calc(88rpx + var(--status-bar-height, 44rpx));
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  z-index: 999;
  border-bottom: 1rpx solid #eee;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 30rpx;
  height: 80rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx;
}

/* 搜索建议样式 */
.suggestions-container {
  position: fixed;
  top: calc(168rpx + var(--status-bar-height, 44rpx));
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 998;
}

.suggestions-list {
  height: 100%;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.suggestion-item:active {
  background: #f8f8f8;
}

.suggestion-icon {
  font-size: 32rpx;
  margin-right: 24rpx;
  color: #ff8c00;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.suggestion-address {
  font-size: 24rpx;
  color: #999;
}

/* 地图容器样式 */
.map-container {
  flex: 1;
  position: relative;
  margin-top: calc(168rpx + var(--status-bar-height, 44rpx));
}

.map {
  width: 100%;
  height: 100%;
}

/* 中心点标记 */
.center-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -100%);
  z-index: 10;
}

.marker-icon {
  width: 60rpx;
  height: 60rpx;
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

/* 地图控制按钮 */
.map-controls {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.control-btn {
  width: 80rpx;
  height: 80rpx;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.control-btn:active {
  background: #f0f0f0;
}

.zoom-in {
  font-weight: bold;
}

.zoom-out {
  font-weight: bold;
}

.location-btn {
  color: #ff8c00;
}

/* 底部面板样式 */
.bottom-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 当前位置信息 */
.current-location {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.location-title {
  font-size: 28rpx;
  color: #666;
}

.loading-text {
  font-size: 24rpx;
  color: #ff8c00;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.location-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.location-address {
  font-size: 26rpx;
  color: #999;
}

/* 附近地点列表 */
.nearby-places {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.section-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  background: #fff;
}

.places-list {
  flex: 1;
  min-height: 0;
}

.place-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  position: relative;
}

.place-item:active {
  background: #f8f8f8;
}

.place-item.selected {
  background: #fff7e6;
  border-left: 6rpx solid #ff8c00;
}

.place-icon {
  font-size: 32rpx;
  margin-right: 24rpx;
  width: 40rpx;
  text-align: center;
}

.place-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.place-name {
  font-size: 30rpx;
  color: #333;
}

.place-address {
  font-size: 24rpx;
  color: #999;
}

.place-distance {
  font-size: 22rpx;
  color: #ff8c00;
}

.place-select {
  font-size: 32rpx;
  color: #ff8c00;
  font-weight: bold;
}

.load-more {
  padding: 30rpx;
  text-align: center;
  font-size: 26rpx;
  color: #999;
}

.load-more:active {
  background: #f8f8f8;
}

/* 确认按钮 */
.confirm-container {
  padding: 30rpx;
  background: #fff;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.confirm-btn:disabled {
  background: #ccc;
  color: #999;
}

.confirm-btn:not(:disabled):active {
  background: #e67c00;
}
</style>
