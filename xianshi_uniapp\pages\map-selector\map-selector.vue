<template>
  <view class="map-selector">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <uni-icons type="left" size="18" color="#fff"></uni-icons>
      </view>
      <view class="nav-title">选择位置</view>
      <view class="nav-right"></view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <input 
          class="search-input" 
          placeholder="搜索地点" 
          v-model="searchKeyword" 
          @input="onSearchInput"
          @confirm="searchLocation"
        />
        <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <uni-icons type="clear" size="14" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <!-- 使用uni-app原生地图组件 -->
      <map 
        id="map"
        class="map"
        :longitude="longitude"
        :latitude="latitude"
        :scale="scale"
        :markers="markers"
        :show-location="showLocation"
        :enable-3D="enable3D"
        :show-compass="showCompass"
        :enable-overlooking="enableOverlooking"
        :enable-zoom="enableZoom"
        :enable-scroll="enableScroll"
        :enable-rotate="enableRotate"
        :subkey="mapKey"
        @tap="onMapTap"
        @markertap="onMarkerTap"
        @regionchange="onRegionChange"
        @updated="onMapUpdated"
        @error="onMapError"
      ></map>
      
      <!-- 中心点标记 -->
      <view class="center-marker">
        <image class="marker-icon" src="/static/images/location-pin.svg" mode="aspectFit"></image>
      </view>
      
      <!-- 定位按钮 -->
      <view class="location-btn" @click="getCurrentLocation">
        <uni-icons type="location" size="20" color="#007aff"></uni-icons>
      </view>
      
      <!-- 加载提示 -->
      <view v-if="isLoading" class="loading-overlay">
        <view class="loading-spinner"></view>
        <view class="loading-text">正在获取位置...</view>
      </view>
    </view>

    <!-- 搜索结果列表 -->
    <view v-if="showSearchResults" class="search-results">
      <scroll-view class="results-list" scroll-y>
        <view 
          v-for="(item, index) in searchResults" 
          :key="index" 
          class="result-item"
          @click="selectSearchResult(item)"
        >
          <view class="result-icon">
            <uni-icons type="location" size="16" color="#ff8c00"></uni-icons>
          </view>
          <view class="result-content">
            <view class="result-title">{{ item.title }}</view>
            <view class="result-address">{{ item.address }}</view>
            <view class="result-distance" v-if="item.distance">{{ item.distance }}m</view>
          </view>
        </view>
        <view v-if="searchResults.length === 0 && searchKeyword && !searchLoading" class="no-results">
          <uni-icons type="info" size="24" color="#ccc"></uni-icons>
          <text>未找到相关地点</text>
        </view>
        <view v-if="searchLoading" class="search-loading">
          <view class="loading-spinner small"></view>
          <text>搜索中...</text>
        </view>
      </scroll-view>
    </view>

    <!-- 地址信息 -->
    <view class="address-info">
      <view class="current-address">
        <view class="address-icon">
          <uni-icons type="location-filled" size="18" color="#ff8c00"></uni-icons>
        </view>
        <view class="address-content">
          <view class="address-title">{{ currentAddress.title || '当前位置' }}</view>
          <view class="address-detail">{{ currentAddress.address || '正在获取地址...' }}</view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button class="confirm-btn" @click="confirmSelection" :disabled="!currentAddress.latitude">
        确认选择
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 地图基础配置
      longitude: 116.397428, // 默认北京坐标
      latitude: 39.90923,
      scale: 16,
      showLocation: true,
      enable3D: true,
      showCompass: false,
      enableOverlooking: false,
      enableZoom: true,
      enableScroll: true,
      enableRotate: false,
      
      // 地图标记
      markers: [],
      
      // 搜索相关
      searchKeyword: '',
      searchResults: [],
      showSearchResults: false,
      searchLoading: false,
      searchDebounceTimer: null,
      
      // 当前地址信息
      currentAddress: {
        title: '',
        address: '',
        longitude: null,
        latitude: null
      },
      
      // 状态控制
      isLoading: true,
      mapContext: null,
      
      // 腾讯地图API密钥
      mapKey: 'LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP'
    }
  },
  
  onLoad(options) {
    console.log('地图选择页面加载，参数:', options)
    
    // 获取传入的初始位置
    if (options.longitude && options.latitude) {
      this.longitude = parseFloat(options.longitude)
      this.latitude = parseFloat(options.latitude)
      this.currentAddress.longitude = this.longitude
      this.currentAddress.latitude = this.latitude
      console.log('使用传入的位置:', this.longitude, this.latitude)
    }
  },
  
  onReady() {
    // 创建地图上下文
    this.mapContext = uni.createMapContext('map', this)
    
    // 如果没有传入位置，获取当前位置
    if (!this.currentAddress.longitude || !this.currentAddress.latitude) {
      this.getCurrentLocation()
    } else {
      this.isLoading = false
      this.updateMarkers()
      this.reverseGeocoding(this.latitude, this.longitude)
    }
  },
  
  onUnload() {
    // 清理定时器
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer)
    }
  },
  
  methods: {
    // 获取当前位置
    getCurrentLocation() {
      this.isLoading = true
      console.log('开始获取当前位置')
      
      uni.getLocation({
        type: 'gcj02', // 使用国测局坐标系
        altitude: false,
        geocode: false,
        isHighAccuracy: true,
        highAccuracyExpireTime: 4000,
        success: (res) => {
          console.log('获取位置成功:', res)
          
          this.longitude = res.longitude
          this.latitude = res.latitude
          this.currentAddress.longitude = res.longitude
          this.currentAddress.latitude = res.latitude
          
          this.updateMarkers()
          this.reverseGeocoding(res.latitude, res.longitude)
          this.isLoading = false
          
          // 移动地图中心到当前位置
          if (this.mapContext) {
            this.mapContext.moveToLocation({
              longitude: res.longitude,
              latitude: res.latitude
            })
          }
        },
        fail: (err) => {
          console.error('获取位置失败:', err)
          this.isLoading = false
          
          let errorMsg = '获取位置失败'
          if (err.errMsg) {
            if (err.errMsg.includes('auth deny')) {
              errorMsg = '定位权限被拒绝，请在设置中开启定位权限'
            } else if (err.errMsg.includes('timeout')) {
              errorMsg = '定位超时，请检查网络连接'
            }
          }
          
          uni.showModal({
            title: '定位失败',
            content: errorMsg,
            showCancel: false,
            confirmText: '知道了'
          })
          
          // 使用默认位置并获取地址
          this.updateMarkers()
          this.reverseGeocoding(this.latitude, this.longitude)
        }
      })
    },
    
    // 更新地图标记
    updateMarkers() {
      this.markers = [{
        id: 1,
        longitude: this.longitude,
        latitude: this.latitude,
        iconPath: '/static/images/location-pin.svg',
        width: 30,
        height: 30,
        anchor: {
          x: 0.5,
          y: 1
        }
      }]
    },
    
    // 地图点击事件
    onMapTap(e) {
      const { longitude, latitude } = e.detail
      console.log('地图点击:', longitude, latitude)
      
      this.updateLocation(latitude, longitude)
    },
    
    // 标记点击事件
    onMarkerTap(e) {
      console.log('标记点击:', e.detail)
    },
    
    // 地图区域变化
    onRegionChange(e) {
      if (e.type === 'end' && e.causedBy === 'drag') {
        // 拖拽结束时获取中心点位置
        this.mapContext.getCenterLocation({
          success: (res) => {
            this.updateLocation(res.latitude, res.longitude)
          }
        })
      }
    },
    
    // 地图更新完成
    onMapUpdated(e) {
      console.log('地图更新完成:', e)
    },
    
    // 地图错误
    onMapError(e) {
      console.error('地图错误:', e)
      uni.showToast({
        title: '地图加载失败',
        icon: 'none'
      })
    },
    
    // 更新位置
    updateLocation(lat, lng) {
      this.latitude = lat
      this.longitude = lng
      this.currentAddress.latitude = lat
      this.currentAddress.longitude = lng
      
      this.updateMarkers()
      this.reverseGeocoding(lat, lng)
    },
    
    // 逆地理编码
    reverseGeocoding(lat, lng) {
      const location = `${lat},${lng}`
      
      uni.request({
        url: 'https://apis.map.qq.com/ws/geocoder/v1/',
        data: {
          location: location,
          key: this.mapKey,
          get_poi: 1,
          output: 'json'
        },
        success: (res) => {
          if (res.data.status === 0) {
            const result = res.data.result
            this.currentAddress.title = result.formatted_addresses?.recommend || result.address
            this.currentAddress.address = result.address
            console.log('逆地理编码成功:', result)
          } else {
            console.error('逆地理编码失败:', res.data.message)
            this.currentAddress.title = '未知位置'
            this.currentAddress.address = `经度:${lng.toFixed(6)}, 纬度:${lat.toFixed(6)}`
          }
        },
        fail: (err) => {
          console.error('逆地理编码请求失败:', err)
          this.currentAddress.title = '获取地址失败'
          this.currentAddress.address = `经度:${lng.toFixed(6)}, 纬度:${lat.toFixed(6)}`
        }
      })
    },
    
    // 搜索输入处理
    onSearchInput() {
      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer)
      }
      
      this.searchDebounceTimer = setTimeout(() => {
        if (this.searchKeyword.trim()) {
          this.searchLocation()
        } else {
          this.showSearchResults = false
          this.searchResults = []
        }
      }, 500)
    },
    
    // 搜索地点
    searchLocation() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = []
        this.showSearchResults = false
        return
      }
      
      this.searchLoading = true
      this.showSearchResults = true
      
      // 使用腾讯位置服务地点搜索API
      uni.request({
        url: 'https://apis.map.qq.com/ws/place/v1/search',
        data: {
          boundary: `nearby(${this.latitude},${this.longitude},10000)`,
          keyword: this.searchKeyword,
          page_size: 20,
          page_index: 1,
          orderby: '_distance',
          key: this.mapKey
        },
        success: (res) => {
          this.searchLoading = false
          
          if (res.data.status === 0) {
            this.searchResults = res.data.data.map(item => ({
              title: item.title,
              address: item.address,
              latitude: item.location.lat,
              longitude: item.location.lng,
              distance: item._distance || 0
            })) || []
            console.log('搜索成功:', this.searchResults)
          } else {
            console.error('搜索失败:', res.data.message)
            this.searchResults = []
          }
        },
        fail: (err) => {
          console.error('搜索请求失败:', err)
          this.searchLoading = false
          this.searchResults = []
          
          uni.showToast({
            title: '搜索失败，请检查网络',
            icon: 'none'
          })
        }
      })
    },
    
    // 选择搜索结果
    selectSearchResult(item) {
      console.log('选择搜索结果:', item)
      
      this.updateLocation(item.latitude, item.longitude)
      this.currentAddress.title = item.title
      this.currentAddress.address = item.address
      
      // 移动地图到选中位置
      if (this.mapContext) {
        this.mapContext.moveToLocation({
          longitude: item.longitude,
          latitude: item.latitude
        })
      }
      
      this.showSearchResults = false
      this.searchKeyword = ''
    },
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.showSearchResults = false
      this.searchResults = []
    },
    
    // 确认选择
    confirmSelection() {
      if (!this.currentAddress.latitude || !this.currentAddress.longitude) {
        uni.showToast({
          title: '请选择一个位置',
          icon: 'none'
        })
        return
      }
      
      const selectedLocation = {
        latitude: this.currentAddress.latitude,
        longitude: this.currentAddress.longitude,
        address: this.currentAddress.title || this.currentAddress.address,
        fullAddress: this.currentAddress
      }
      
      console.log('确认选择位置:', selectedLocation)
      
      // 将选择的位置信息存储到本地
      uni.setStorageSync('selectedLocation', selectedLocation)
      
      // 通过事件总线传递数据
      uni.$emit('locationSelected', selectedLocation)
      
      // 返回上一页
      uni.navigateBack({
        success: () => {
          uni.showToast({
            title: '位置选择成功',
            icon: 'success'
          })
        }
      })
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.map-selector {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 45px;
  background: #ff8c00;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: var(--status-bar-height, 20px);
  z-index: 1000;
  color: #fff;
}

.nav-left {
  position: absolute;
  left: 15px;
  top: calc(var(--status-bar-height, 20px) + 12px);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

.nav-right {
  width: 30px;
  visibility: hidden;
}

/* 搜索框 */
.search-container {
  margin-top: calc(45px + var(--status-bar-height, 20px));
  padding: 12px 15px;
  background: #fff;
  border-bottom: 1px solid #eee;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 20px;
  padding: 10px 15px;
  gap: 10px;
}

.search-input {
  flex: 1;
  font-size: 14px;
  border: none;
  background: transparent;
  outline: none;
}

.clear-btn {
  padding: 2px;
  border-radius: 50%;
  background: #ddd;
}

/* 地图容器 */
.map-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.map {
  width: 100%;
  height: 100%;
}

.center-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -100%);
  z-index: 10;
  pointer-events: none;
}

.marker-icon {
  width: 30px;
  height: 30px;
}

.location-btn {
  position: absolute;
  right: 15px;
  bottom: 140px;
  width: 44px;
  height: 44px;
  background: #fff;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.location-btn:active {
  background: #f0f0f0;
}

/* 加载提示 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
  margin-bottom: 5px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 搜索结果 */
.search-results {
  position: absolute;
  top: calc(69px + var(--status-bar-height, 20px));
  left: 0;
  right: 0;
  bottom: 140px;
  background: #fff;
  z-index: 30;
  border-top: 1px solid #eee;
}

.results-list {
  height: 100%;
}

.result-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.result-item:active {
  background: #f8f8f8;
}

.result-icon {
  margin-top: 2px;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
  font-weight: 500;
}

.result-address {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  margin-bottom: 2px;
}

.result-distance {
  font-size: 11px;
  color: #ff8c00;
}

.no-results, .search-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
  font-size: 14px;
  gap: 10px;
}

/* 地址信息 */
.address-info {
  background: #fff;
  padding: 15px;
  border-top: 1px solid #eee;
}

.current-address {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.address-icon {
  margin-top: 2px;
}

.address-content {
  flex: 1;
  min-width: 0;
}

.address-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
}

.address-detail {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

/* 底部按钮 */
.bottom-actions {
  padding: 15px;
  background: #fff;
  border-top: 1px solid #eee;
}

.confirm-btn {
  width: 100%;
  height: 44px;
  background: #ff8c00;
  color: #fff;
  font-size: 16px;
  border-radius: 22px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.confirm-btn:active {
  opacity: 0.8;
}

.confirm-btn:disabled {
  background: #ccc;
  opacity: 0.6;
}
</style>