<template>
  <view class="container">
    <!-- 视频展示/上传区域 -->
    <view class="video-container">
      <video
        v-if="videoUrl && showVideo"
        class="video-player"
        :src="videoUrl"
        object-fit="cover"
        controls
      ></video>
      <uni-file-picker 
        v-else
        limit="1" 
        file-mediatype="video" 
        title="添加形象视频,支持MP4格式，最大60秒" 
        @select="selectVideo"
        @progress="uploadProgress"
        @success="uploadSuccess"
        ref="videoPicker"
      ></uni-file-picker>
    </view>

    <!-- 上传进度 -->
    <view class="progress-container" v-if="uploadProgressPercent > 0 && uploadProgressPercent < 100">
      <text class="progress-text">上传进度: {{uploadProgressPercent}}%</text>
      <progress :percent="uploadProgressPercent" stroke-width="4" />
    </view>

    <!-- 删除按钮 -->
    <view class="delete-btn" v-if="videoUrl" @tap="removeVideo">
      删除视频
    </view>

    <!-- 保存按钮 -->
    <view class="save-btn-wrap">
      <button class="save-btn" @tap="saveVideo">保存</button>
    </view>
  </view>
</template>

<script>
const app = getApp().globalData;
export default {
  data() {
    return {
      videoUrl: "",
      showVideo: false,
      uploadProgressPercent: 0,
      tempVideoPath: "", // 临时视频路径
    };
  },
  onLoad(options) {
    if (options.video) {
      this.videoUrl = decodeURIComponent(options.video);
      console.log("videoUrl:{}",this.videoUrl)
      this.showVideo = true;
    }
  },
  methods: {
    // 选择视频文件后的回调
    selectVideo(e) {
      console.log('选择视频文件', e);
      // 保存临时视频路径
      if (e.tempFiles && e.tempFiles.length > 0) {
        this.tempVideoPath = e.tempFiles[0].path;
        console.log("tempVideoPath:{}",this.tempVideoPath)
        // 开始上传
        this.uploadVideo(e.tempFiles[0]);
      }
    },

    // 监听上传进度
    uploadProgress(e) {
      console.log('上传进度', e);
      if (e.progress) {
        this.uploadProgressPercent = Math.floor(e.progress);
      }
    },

    // 上传成功回调
    uploadSuccess(e) {
      console.log('上传成功', e);
      if (e.tempFiles && e.tempFiles.length > 0 && e.tempFiles[0].url) {
        this.videoUrl = e.tempFiles[0].url;
        this.showVideo = true;
        this.uploadProgressPercent = 100;
        
        // 2秒后隐藏进度条
        setTimeout(() => {
          this.uploadProgressPercent = 0;
        }, 2000);
      }
    },

    // 上传视频到OSS
    async uploadVideo(file) {
      try {
        const token = uni.getStorageSync('token');
        if (!token) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }

        console.log('准备上传视频文件:', file.path);
        
        // 显示上传中提示
        uni.showLoading({
          title: '视频上传中...',
          mask: true
        });

        // 开始上传
        uni.uploadFile({
          url: app.baseUrl + '/api/oss/upload',
          filePath: file.path,
          name: 'file',
          header: {
            'Authorization': token
          },
          formData: {
            folder: 'member/videos' // 指定存储文件夹
          },
          success: (uploadRes) => {
            console.log('OSS上传结果', uploadRes);
            
            try {
              const result = JSON.parse(uploadRes.data);
              console.log('解析后的响应数据:', result);
              
              if (uploadRes.statusCode === 200 && result.code === 200) {
                // 获取后端返回的视频URL
                let videoUrl = result.message;
                
                // 检查URL是否为null或空
                if (!videoUrl) {
                  console.warn('服务器返回的视频URL为空，使用本地视频');
                  // 使用临时视频路径作为URL
                  videoUrl = file.path;
                  
                  uni.showToast({
                    title: '使用本地视频预览',
                    icon: 'none'
                  });
                } else {
                  // 确保URL是完整的
                  if (!videoUrl.startsWith('http')) {
                    videoUrl = 'https:' + videoUrl;
                  }
                  console.log('完整的视频URL:', videoUrl);
                  
                  uni.showToast({
                    title: '上传成功',
                    icon: 'success'
                  });
                }
                
                this.videoUrl = videoUrl;
                this.showVideo = true;
              } else {
                console.error('上传视频失败:', result.msg);
                
                // 使用本地视频作为预览
                this.videoUrl = file.path;
                this.showVideo = true;
                
                uni.showToast({
                  title: result.msg || '上传失败，使用本地预览',
                  icon: 'none'
                });
              }
            } catch (error) {
              console.error('解析响应数据失败:', error);
              
              // 使用本地视频作为预览
              this.videoUrl = file.path;
              this.showVideo = true;
              
              uni.showToast({
                title: '处理响应数据失败，使用本地预览',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            console.error('上传视频失败', err);
            
            // 使用本地视频作为预览
            this.videoUrl = file.path;
            this.showVideo = true;
            
            uni.showToast({
              title: '上传失败，使用本地预览',
              icon: 'none'
            });
          },
          complete: () => {
            this.uploadProgressPercent = 0;
            uni.hideLoading();
          }
        });
      } catch (error) {
        console.error('上传视频错误', error);
        
        // 使用本地视频作为预览
        this.videoUrl = file.path;
        this.showVideo = true;
        
        uni.showToast({
          title: '上传出错，使用本地预览',
          icon: 'none'
        });
        
        uni.hideLoading();
      }
    },

    // 原有的选择视频方法，备用
    chooseVideo() {
      uni.chooseMedia({
        count: 1,
        mediaType: ["video"],
        sourceType: ["album", "camera"],
        maxDuration: 60,
        camera: "back",
        success: (res) => {
          this.tempVideoPath = res.tempFiles[0].tempFilePath;
          // 上传视频到OSS
          this.uploadVideo({
            path: this.tempVideoPath
          });
        },
      });
    },

    removeVideo() {
      this.videoUrl = "";
      this.showVideo = false;
      // 如果有ref实例，重置file-picker
      if (this.$refs.videoPicker) {
        this.$refs.videoPicker.clearFiles();
      }
    },

    saveVideo() {
      // 如果有视频URL，确保是完整的URL
      if (this.videoUrl) {
        if (!this.videoUrl.startsWith('http')) {
          // 本地视频路径，可能需要特殊处理
          console.log('保存的是本地视频:', this.videoUrl);
          // 对于本地视频，提示用户
          uni.showModal({
            title: '提示',
            content: '检测到您使用的是本地视频，这可能在其他设备上无法正常播放。是否继续保存？',
            success: (res) => {
              if (res.confirm) {
                this.updatePrevPageAndSave();
              }
            }
          });
          return;
        }
      }
      
      // 如果是网络URL或没有视频，直接保存
      this.updatePrevPageAndSave();
    },
    
    // 更新上一页数据并返回
    async updatePrevPageAndSave() {
      try {
        uni.showLoading({ title: '保存中...' });
        
        // 调用API保存视频URL
        const app = getApp().globalData;
        const res = await this.$requestHttp.put(app.commonApi.updateMemberInfo, {
          data: {
            videoUrl: this.videoUrl || ''
          }
        });
        
        if (res && res.code === 200) {
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];

          prevPage.videoUrl = this.videoUrl;

          const profileData = uni.getStorageSync("profileData") || {};
          profileData.videoUrl = this.videoUrl;
          uni.setStorageSync("profileData", profileData);

          // 触发上一页面的完整度更新
          if (prevPage.updateCompletion) {
            prevPage.updateCompletion();
          }
          
          uni.hideLoading();
          uni.showToast({ title: '保存成功', icon: 'success' });
          
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(res?.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存视频失败:', error);
        uni.hideLoading();
        uni.showToast({ title: error.message || '保存失败，请重试', icon: 'none' });
      }
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.video-container {
  width: 100%;
  height: 400rpx;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.plus-icon {
  font-size: 60rpx;
  color: #999;
  line-height: 1;
}

.placeholder-text {
  font-size: 28rpx;
  color: #666;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.progress-container {
  padding: 20rpx 0;
}

.progress-text {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.delete-btn {
  width: 100%;
  height: 88rpx;
  background: #f8f8f8;
  color: #ff4d4f;
  font-size: 32rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.save-btn-wrap {
  margin-top: auto;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
