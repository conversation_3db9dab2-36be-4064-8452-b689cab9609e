/**
 * 日期工具函数，提供兼容iOS的日期处理方法
 */

/**
 * 格式化日期字符串为iOS兼容格式
 * @param {string} dateString - 日期字符串
 * @returns {string} - 格式化后的日期字符串
 */
export const formatDateForIOS = (dateString) => {
  if (!dateString) return '';

  try {
    // 检查是否已经是ISO格式
    if (dateString.includes('T') && dateString.includes('Z')) {
      return dateString; // 已经是ISO格式，无需转换
    }

    // 将常见的日期格式转换为ISO格式
    let date;

    if (dateString.includes(' ')) {
      // 将 "yyyy-MM-dd HH:mm" 或 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy-MM-ddTHH:mm:ss"
      const parts = dateString.split(' ');
      const datePart = parts[0];
      // 确保时间部分有秒数
      const timePart = parts[1] + (parts[1].split(':').length === 2 ? ':00' : '');
      date = new Date(`${datePart}T${timePart}`);
    } else {
      // 尝试直接解析
      date = new Date(dateString);
    }

    // 检查解析是否成功
    if (isNaN(date.getTime())) {
      console.warn('无效的日期格式:', dateString);
      return dateString;
    }

    // 返回ISO标准格式
    return date.toISOString();
  } catch (e) {
    console.error('日期格式转换错误:', e);
    return dateString;
  }
}

/**
 * 格式化日期为友好显示格式
 * @param {string} dateString - 日期字符串
 * @param {string} format - 输出格式，默认为 'yyyy-MM-dd HH:mm'
 * @returns {string} - 格式化后的日期字符串
 */
export const formatDateForDisplay = (dateString, format = 'yyyy-MM-dd HH:mm') => {
  if (!dateString) return '';

  try {
    // 首先转换为iOS兼容格式
    const isoDateString = formatDateForIOS(dateString);
    const date = new Date(isoDateString);

    // 判断日期是否有效
    if (isNaN(date.getTime())) {
      return dateString;
    }

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    // 根据格式返回
    let result = format;
    result = result.replace('yyyy', year);
    result = result.replace('MM', month);
    result = result.replace('dd', day);
    result = result.replace('HH', hours);
    result = result.replace('mm', minutes);
    result = result.replace('ss', seconds);

    return result;
  } catch (e) {
    console.error('日期显示格式化错误:', e);
    return dateString;
  }
}

/**
 * 计算时间差并返回友好的文字描述
 * @param {string} dateString - 日期字符串
 * @returns {string} - 友好的时间差描述，如"10分钟前"、"2小时前"、"3天前"
 */
export const getTimeAgo = (dateString) => {
  if (!dateString) return '';

  try {
    // 转换为iOS兼容格式
    const isoDateString = formatDateForIOS(dateString);
    const date = new Date(isoDateString);
    const now = new Date();

    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);

    if (diffSec < 60) return '刚刚';

    const diffMin = Math.floor(diffSec / 60);
    if (diffMin < 60) return `${diffMin}分钟前`;

    const diffHour = Math.floor(diffMin / 60);
    if (diffHour < 24) return `${diffHour}小时前`;

    const diffDay = Math.floor(diffHour / 24);
    if (diffDay < 30) return `${diffDay}天前`;

    const diffMonth = Math.floor(diffDay / 30);
    if (diffMonth < 12) return `${diffMonth}个月前`;

    return `${Math.floor(diffMonth / 12)}年前`;
  } catch (e) {
    console.error('计算时间差错误:', e);
    return dateString;
  }
}

/**
 * 将日期格式化为相对时间（如"刚刚"、"5分钟前"等）
 * @param {string} timeString - 日期字符串
 * @returns {string} 相对时间字符串
 */
export const formatRelativeTime = (timeString) => {
  if (!timeString) return '';

  try {
    // 解析日期
    let date;
    if (timeString.includes(' ')) {
      const parts = timeString.split(' ');
      const datePart = parts[0];
      const timePart = parts[1] + (parts[1].split(':').length === 2 ? ':00' : '');
      date = new Date(`${datePart}T${timePart}`);
    } else {
      date = new Date(timeString);
    }

    if (isNaN(date.getTime())) {
      return timeString;
    }

    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);

    if (diffSec < 60) {
      return '刚刚';
    } else if (diffSec < 3600) {
      return `${Math.floor(diffSec / 60)}分钟前`;
    } else if (diffSec < 86400) {
      return `${Math.floor(diffSec / 3600)}小时前`;
    } else if (diffSec < 2592000) {
      return `${Math.floor(diffSec / 86400)}天前`;
    } else {
      // 超过30天显示具体日期
      return formatDateForDisplay(timeString);
    }
  } catch (e) {
    console.error('相对时间格式解析错误:', e, timeString);
    return timeString;
  }
}
