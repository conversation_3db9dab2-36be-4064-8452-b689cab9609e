package com.play.xianshiadmin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.dto.message.MessageBroadcastDTO;
import com.play.xianshibusiness.dto.message.MessageQueryDTO;
import com.play.xianshibusiness.dto.message.MessageVO;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.ISysMessageService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 管理端消息管理控制器
 */
@RestController
@RequestMapping("/admin/message")
@Api(tags = "管理端消息管理API")
public class MessageAdminController {

    @Resource
    private ISysMessageService sysMessageService;
    
    /**
     * 分页查询系统消息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页查询系统消息")
    @RequireAdmin
    public Result<Page<MessageVO>> pageMessages(MessageQueryDTO queryDTO) {
        return ResultUtils.success(sysMessageService.adminPageMessages(queryDTO));
    }
    
    /**
     * 获取消息详情
     */
    @GetMapping("/{messageId}")
    @ApiOperation(value = "获取消息详情")
    @RequireAdmin
    public Result<MessageVO> getMessageDetail(
            @ApiParam(value = "消息ID", required = true) @PathVariable String messageId) {
        return ResultUtils.success(sysMessageService.adminGetMessageDetail(messageId));
    }
    
    /**
     * 发送系统广播消息
     */
    @PostMapping("/broadcast")
    @ApiOperation(value = "发送系统广播消息")
    @RequireAdmin
    public Result<Boolean> sendBroadcastMessage(@RequestBody @Valid MessageBroadcastDTO broadcastDTO) {
        // 设置操作人ID为当前管理员
        String adminId = PrincipalUtil.getMemberIdOrNull();
        if (adminId != null) {
            broadcastDTO.setOperatorId(adminId);
        }
        
        return ResultUtils.success(sysMessageService.sendBroadcastMessage(broadcastDTO));
    }
    
    /**
     * 向指定用户发送消息
     */
    @PostMapping("/send/{memberId}")
    @ApiOperation(value = "向指定用户发送消息")
    @RequireAdmin
    public Result<Boolean> sendMessageToMember(
            @ApiParam(value = "会员ID", required = true) @PathVariable String memberId,
            @ApiParam(value = "消息内容", required = true) @RequestParam String content,
            @ApiParam(value = "消息类型：1-系统通知，2-订单通知，3-活动通知") @RequestParam(defaultValue = "1") Integer type) {
        return ResultUtils.success(sysMessageService.adminSendMessageToMember(memberId, content, type));
    }
    
    /**
     * 删除消息
     */
    @DeleteMapping("/{messageId}")
    @ApiOperation(value = "删除消息")
    @RequireAdmin
    public Result<Boolean> deleteMessage(
            @ApiParam(value = "消息ID", required = true) @PathVariable String messageId) {
        return ResultUtils.success(sysMessageService.adminDeleteMessage(messageId));
    }
    
    /**
     * 获取消息统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取消息统计信息")
    @RequireAdmin
    public Result<Map<String, Object>> getMessageStatistics() {
        return ResultUtils.success(sysMessageService.adminGetMessageStatistics());
    }
} 