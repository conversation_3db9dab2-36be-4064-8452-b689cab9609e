import Vue from 'vue';
import VueRouter from 'vue-router';
import Layout from '@/layout';

Vue.use(VueRouter);

// 公共路由
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/profile',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '',
        component: () => import('@/views/profile/index'),
        name: 'Profile',
        meta: { title: '个人资料', icon: 'el-icon-user' }
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: { title: '控制台', icon: 'el-icon-monitor', affix: true }
      }
    ]
  },
  {
    path: '/member',
    component: Layout,
    redirect: '/member/list',
    name: 'Member',
    meta: { title: '会员管理', icon: 'el-icon-user' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/member/list'),
        name: 'MemberList',
        meta: { title: '会员列表', icon: 'el-icon-user-solid' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/member/detail'),
        name: 'MemberDetail',
        meta: { title: '会员详情', icon: 'el-icon-document', activeMenu: '/member/list' },
        hidden: true
      },
      {
        path: 'talent-apply',
        component: () => import('@/views/member/talent-apply'),
        name: 'TalentApply',
        meta: { title: '达人申请', icon: 'el-icon-star-on' }
      }
    ]
  },
  {
    path: '/recharge',
    component: Layout,
    redirect: '/recharge/package',
    name: 'Recharge',
    meta: { title: '充值管理', icon: 'el-icon-money' },
    children: [
      {
        path: 'package',
        component: () => import('@/views/recharge/package'),
        name: 'RechargePackage',
        meta: { title: '充值套餐', icon: 'el-icon-coin' }
      },
      {
        path: 'records',
        component: () => import('@/views/recharge/records'),
        name: 'RechargeRecords',
        meta: { title: '充值记录', icon: 'el-icon-notebook-2' }
      },
      {
        path: 'member',
        component: () => import('@/views/recharge/member'),
        name: 'RechargeMember',
        meta: { title: '会员充值', icon: 'el-icon-wallet' }
      }
    ]
  },
  {
    path: '/activity',
    component: Layout,
    redirect: '/activity/order',
    name: 'Activity',
    meta: {
      title: '陪玩管理',
      icon: 'el-icon-s-help'
    },
    children: [
      {
        path: 'order',
        name: 'OrderList',
        component: () => import('@/views/order/list/index'),
        meta: { title: '订单管理' }
      },
      {
        path: 'order/detail/:id',
        name: 'OrderDetail',
        component: () => import('@/views/order/detail/index'),
        meta: { title: '订单详情', activeMenu: '/activity/order' },
        hidden: true
      },
      {
        path: 'type',
        name: 'ActivityType',
        component: () => import('@/views/activity-type/index'),
        meta: { title: '陪玩类型' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/config',
    name: 'System',
    meta: {
      title: '系统管理',
      icon: 'el-icon-setting'
    },
    children: [
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/config'),
        meta: { title: '系统配置', icon: 'el-icon-setting' }
      }
    ]
  },
  { path: '*', redirect: '/404', hidden: true }
];

const createRouter = () => new VueRouter({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
});

const router = createRouter();

// 重置路由
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher;
}

export default router; 