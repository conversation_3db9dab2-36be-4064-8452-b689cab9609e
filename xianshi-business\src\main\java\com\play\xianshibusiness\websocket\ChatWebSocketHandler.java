package com.play.xianshibusiness.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.play.xianshibusiness.dto.ChatMessage;
import com.play.xianshibusiness.enums.ChatMessageType;
import com.play.xianshibusiness.service.IMemberChatService;
import com.play.xianshibusiness.utils.JwtSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.net.URI;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket聊天处理器
 */
@Slf4j
@Component
public class ChatWebSocketHandler implements WebSocketHandler {

    @Autowired
    private IMemberChatService chatService;

    @Autowired
    private JwtSignUtils jwtSignUtils;

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 存储用户会话
    private final Map<String, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    
    // 存储会话对应的用户ID
    private final Map<String, String> sessionUserMap = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("WebSocket连接建立: {}", session.getId());
        
        // 从URL参数中获取token和userId
        String token = getQueryParam(session, "token");
        String userId = getQueryParam(session, "userId");
        
        if (token == null || userId == null) {
            log.warn("连接缺少必要参数，关闭连接: {}", session.getId());
            session.close(CloseStatus.BAD_DATA.withReason("Missing token or userId"));
            return;
        }
        
        // URL解码token
        try {
            token = java.net.URLDecoder.decode(token, "UTF-8");
        } catch (Exception e) {
            log.warn("Token解码失败: {}", e.getMessage());
        }
        
        // 确保token有Bearer前缀
        if (!token.startsWith("Bearer ")) {
            token = "Bearer " + token;
        }
        
        // 验证token
        try {
            String tokenUserId = jwtSignUtils.getMemberIdFromToken(token);
            
            if (tokenUserId == null) {
                log.warn("Token解析失败，无法获取用户ID，关闭连接: {}", session.getId());
                session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Invalid token format"));
                return;
            }
            
            if (!userId.equals(tokenUserId)) {
                log.warn("Token验证失败，用户ID不匹配，关闭连接: {} vs {}", userId, tokenUserId);
                session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Invalid token"));
                return;
            }
            
            // 验证token是否过期
            if (!jwtSignUtils.validateTokenExpiration(token)) {
                log.warn("Token已过期，关闭连接: {}", session.getId());
                session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Token expired"));
                return;
            }
            
        } catch (Exception e) {
            log.error("Token验证异常: {}", e.getMessage(), e);
            session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Token validation error"));
            return;
        }
        
        // 存储用户会话
        userSessions.put(userId, session);
        sessionUserMap.put(session.getId(), userId);
        
        // 发送连接成功消息
        ChatMessage connectMessage = ChatMessage.builder()
                .id(generateMessageId())
                .type(ChatMessageType.CONNECT.getType())
                .fromUserId("system")
                .toUserId(userId)
                .content("连接成功")
                .timestamp(new Date())
                .status("DELIVERED")
                .build();
        
        sendMessage(session, connectMessage);
        
        log.info("用户 {} 连接成功", userId);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String userId = sessionUserMap.get(session.getId());
        if (userId == null) {
            log.warn("未找到会话对应的用户ID: {}", session.getId());
            return;
        }
        
        try {
            ChatMessage chatMessage = objectMapper.readValue(message.getPayload().toString(), ChatMessage.class);
            log.info("收到消息: {}", chatMessage);
            
            // 设置发送者ID
            chatMessage.setFromUserId(userId);
            chatMessage.setTimestamp(new Date());
            
            // 根据消息类型处理
            switch (ChatMessageType.valueOf(chatMessage.getType())) {
                case CHAT:
                    handleChatMessage(chatMessage);
                    break;
                case PING:
                    handlePingMessage(session, chatMessage);
                    break;
                default:
                    log.warn("未知消息类型: {}", chatMessage.getType());
            }
            
        } catch (Exception e) {
            log.error("处理消息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket传输错误: {}", exception.getMessage(), exception);
        cleanupSession(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        log.info("WebSocket连接关闭: {}, 状态: {}", session.getId(), closeStatus);
        cleanupSession(session);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    /**
     * 处理聊天消息
     */
    private void handleChatMessage(ChatMessage chatMessage) {
        try {
            // 保存前端的原始消息ID
            String originalMessageId = chatMessage.getId();
            
            // 保存消息到数据库
            String dbMessageId = chatService.sendChatMessage(
                    chatMessage.getFromUserId(),
                    chatMessage.getToUserId(),
                    chatMessage.getContent()
            );
            
            // 为发送给接收者的消息使用数据库ID
            ChatMessage messageToReceiver = ChatMessage.builder()
                    .id(dbMessageId)
                    .type(chatMessage.getType())
                    .fromUserId(chatMessage.getFromUserId())
                    .toUserId(chatMessage.getToUserId())
                    .content(chatMessage.getContent())
                    .timestamp(chatMessage.getTimestamp())
                    .status("SENT")
                    .build();
            
            // 发送给接收者
            WebSocketSession targetSession = userSessions.get(chatMessage.getToUserId());
            if (targetSession != null && targetSession.isOpen()) {
                sendMessage(targetSession, messageToReceiver);
                messageToReceiver.setStatus("DELIVERED");
            }
            
            // 发送确认给发送者，使用原始消息ID
            WebSocketSession senderSession = userSessions.get(chatMessage.getFromUserId());
            if (senderSession != null && senderSession.isOpen()) {
                ChatMessage ackMessage = ChatMessage.builder()
                        .id(originalMessageId)  // 使用前端的原始ID
                        .type("ACK")
                        .fromUserId("system")
                        .toUserId(chatMessage.getFromUserId())
                        .content("消息已发送")
                        .timestamp(new Date())
                        .status(messageToReceiver.getStatus())
                        .build();
                sendMessage(senderSession, ackMessage);
            }
            
        } catch (Exception e) {
            log.error("处理聊天消息失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理心跳消息
     */
    private void handlePingMessage(WebSocketSession session, ChatMessage pingMessage) {
        ChatMessage pongMessage = ChatMessage.builder()
                .id(generateMessageId())
                .type(ChatMessageType.PING.getType())
                .fromUserId("system")
                .toUserId(pingMessage.getFromUserId())
                .content("pong")
                .timestamp(new Date())
                .status("DELIVERED")
                .build();
        
        sendMessage(session, pongMessage);
    }
    
    /**
     * 发送消息
     */
    private void sendMessage(WebSocketSession session, ChatMessage message) {
        try {
            if (session.isOpen()) {
                String messageJson = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(messageJson));
            }
        } catch (IOException e) {
            log.error("发送消息失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 清理会话
     */
    private void cleanupSession(WebSocketSession session) {
        String userId = sessionUserMap.remove(session.getId());
        if (userId != null) {
            userSessions.remove(userId);
            log.info("清理用户会话: {}", userId);
        }
    }
    
    /**
     * 获取URL查询参数
     */
    private String getQueryParam(WebSocketSession session, String paramName) {
        URI uri = session.getUri();
        if (uri != null && uri.getQuery() != null) {
            String[] params = uri.getQuery().split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 2 && paramName.equals(keyValue[0])) {
                    return keyValue[1];
                }
            }
        }
        return null;
    }
    
    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000000);
    }
    
    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return userSessions.size();
    }
    
    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(String userId) {
        WebSocketSession session = userSessions.get(userId);
        return session != null && session.isOpen();
    }
}