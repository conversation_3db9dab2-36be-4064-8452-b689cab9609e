<template>
	<view class="container">
		<!-- 互动消息 -->
		<view class="message-section" v-if="chatMessages.length > 0">
			<text class="section-title">互动消息</text>
			<view 
				v-for="chat in chatMessages" 
				:key="chat.id"
				class="message-item clickable"
				@click="onChatMessageClick(chat.targetId, chat.targetName, chat.targetAvatar)"
			>
				<image 
					class="user-avatar" 
					:src="chat.targetAvatar || '/static/images/default-avatar.png'" 
					mode="aspectFill"
				/>
				<view class="message-content">
					<view class="message-header">
						<text class="user-name">{{ chat.targetName }}</text>
						<text class="message-time">{{ formatTime(chat.lastMessageTime) }}</text>
					</view>
					<view class="message-desc">{{ chat.lastMessage }}</view>
				</view>
				<view v-if="chat.unreadCount > 0" class="unread-count">{{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}</view>
			</view>
		</view>
		
		<!-- 系统消息 -->
		<view class="message-section" >
			<view class="section-title">系统消息</view>
			<view class="message-list" :class="{ clickable: !!latestSysMessage }" @click="latestSysMessage && onSysMessageClick()">
				<view v-if="latestSysMessage" class="message-item system">
					<view class="red-dot" v-if="!latestSysMessage.hasRead"></view>
					<view class="message-icon">
						<text class="fas fa-bell"></text>
					</view>
					<view class="message-content">
						<view class="message-title">{{ latestSysMessage.title }}</view>
						<view class="message-desc">{{ latestSysMessage.content }}</view>
						<view class="message-row">
							<view class="message-time">{{ latestSysMessage.startTime ? (latestSysMessage.startTime.slice ? latestSysMessage.startTime.slice(0, 10) : latestSysMessage.startTime) : '' }}</view>
						</view>
					</view>
				</view>
				<view v-else class="message-item system">
					<view class="message-content">
						<view class="message-title">暂无系统消息</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 无消息提示 -->
		<view class="no-message" v-if="chatMessages.length === 0 && sysMessages.length === 0">
			<text>暂无消息</text>
		</view>
	</view>
	<Tabbar currentPath="/pages/message/message"></Tabbar>
</template>

<script>
import { getSysMessages, getOrderMessages, getChatList, getChatUnreadCount, getMemberInfo, getChatHistory } from '../../api/index.js';
import WebSocketClient from '@/utils/websocket-client.js';
import Tabbar from '@/custom-tab-bar/index.vue';

export default {
	components: {
		Tabbar
	},
	data() {
		return {
			currentUserId: null,
			webSocketClient: null,
			sysMessages: [],
			latestSysMessage: null,
			orderMessages: [],
			latestOrderMessage: null,
			chatMessages: [],
			latestChatMessage: null,
			totalUnreadCount: 0
		};
	},
	onLoad() {
		uni.$emit('updateTabbar', '/pages/message/message');
		this.initData();
	},
	onShow() {
		uni.$emit('updateTabbar', '/pages/message/message');
		console.log('=== 消息页面显示，开始加载数据 ===');
		this.loadSysMessages();
		this.loadOrderMessages();
		this.loadChatMessages();
	},
	onUnload() {
		if (this.webSocketClient) {
			this.webSocketClient.disconnect();
		}
	},
	methods: {
		async initData() {
			try {
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.currentUserId = userInfo.id;
					console.log('当前用户ID:', this.currentUserId);
					this.initWebSocket();
				}
			} catch (error) {
				console.error('初始化数据失败:', error);
			}
		},
		
		initWebSocket() {
			const token = uni.getStorageSync('token');
			if (!token || !this.currentUserId) return;
			
			this.webSocketClient = new WebSocketClient({
				token: token,
				userId: this.currentUserId,
				onMessage: (message) => {
					this.handleWebSocketMessage(message);
				}
			});
			
			this.webSocketClient.connect();
		},
		
		handleWebSocketMessage(message) {
			if (message.type === 'CHAT') {
				this.latestChatMessage = {
					name: message.fromUserName || '新消息',
					content: message.content,
					time: new Date(message.timestamp),
					avatar: message.fromUserAvatar || '/static/images/default-avatar.png'
				};
				this.totalUnreadCount++;
				
				uni.showToast({
					title: '收到新消息',
					icon: 'none'
				});
				
				// 重新加载聊天消息
				this.loadChatMessages();
			}
		},
		
		/**
		 * 加载聊天消息
		 */
		async loadChatMessages() {
			try {
				console.log('开始加载聊天消息...');
				
				// 获取聊天列表
				const chatListResponse = await getChatList();
				console.log('聊天列表响应:', chatListResponse);
				
				// 统一使用 code === 200 判断成功
				if (chatListResponse.code === 200 && chatListResponse.data) {
					const chatUserIds = chatListResponse.data;
					console.log('聊天用户ID列表:', chatUserIds);
					
					// 如果没有聊天记录，直接返回
					if (!Array.isArray(chatUserIds) || chatUserIds.length === 0) {
						console.log('没有聊天记录');
						this.chatMessages = [];
						return;
					}
					
					// 为每个聊天对象获取详细信息和最后一条消息
					const chatPromises = chatUserIds.map(async (userId) => {
						try {
							console.log('处理用户:', userId);
							
							// 获取用户信息
							const userInfo = await getMemberInfo(userId);
							console.log('用户信息响应:', userInfo);
							
							// 获取聊天历史记录
							const chatHistory = await getChatHistory(userId);
							console.log('聊天历史响应:', chatHistory);
							
							// 统一使用 code === 200 判断成功
							if (chatHistory.code === 200 && chatHistory.data && chatHistory.data.length > 0) {
								const lastMessage = chatHistory.data[chatHistory.data.length - 1];
								let contentText = '暂无消息';
								
								try {
									const contentObj = JSON.parse(lastMessage.contentJson || '{}');
									contentText = contentObj.text || contentObj.content || '图片消息';
								} catch (e) {
									// 如果不是JSON格式，直接使用原始内容
									contentText = lastMessage.contentJson || '消息内容解析失败';
								}
								
								// 获取未读消息数
								const unreadResponse = await getChatUnreadCount();
								const unreadCount = (unreadResponse.code === 200) ? unreadResponse.data || 0 : 0;
								
								return {
									id: userId,
									targetId: userId,
									targetName: (userInfo.code === 200 && userInfo.data) ? (userInfo.data.nickname || '用户' + userId.substr(-4)) : '用户' + userId.substr(-4),
									targetAvatar: (userInfo.code === 200 && userInfo.data) ? userInfo.data.avatar : '/static/images/default-avatar.png',
									lastMessage: contentText,
									lastMessageTime: lastMessage.createTime,
									unreadCount: unreadCount
								};
							} else {
								// 没有聊天记录的用户，也显示在列表中
								return {
									id: userId,
									targetId: userId,
									targetName: (userInfo.code === 200 && userInfo.data) ? (userInfo.data.nickname || '用户' + userId.substr(-4)) : '用户' + userId.substr(-4),
									targetAvatar: (userInfo.code === 200 && userInfo.data) ? userInfo.data.avatar : '/static/images/default-avatar.png',
									lastMessage: '点击开始聊天',
									lastMessageTime: new Date().toISOString(),
									unreadCount: 0
								};
							}
						} catch (error) {
							console.error('获取聊天信息失败:', error);
							return null;
						}
					});
					
					const chatResults = await Promise.all(chatPromises);
					this.chatMessages = chatResults.filter(chat => chat !== null);
					
					console.log('最终加载的聊天消息:', this.chatMessages);
				} else {
					console.log('获取聊天列表失败或无数据:', chatListResponse);
					this.chatMessages = [];
				}
			} catch (error) {
				console.error('加载聊天消息失败:', error);
				this.chatMessages = [];
			}
		},
		
		/**
		 * 点击聊天消息 - 跳转到聊天详情页
		 */
		onChatMessageClick(targetId, targetName, targetAvatar) {
			console.log('点击聊天消息:', targetId, targetName, targetAvatar);
			uni.navigateTo({ 
				url: `/pages/chat/chat-detail?id=${targetId}&name=${encodeURIComponent(targetName)}&avatar=${encodeURIComponent(targetAvatar || '/static/images/default-avatar.png')}` 
			});
		},
		
		onChatListClick() {
			uni.navigateTo({ url: '/pages/chat/chat' });
		},
		
		async loadSysMessages() {
			try {
				const res = await getSysMessages();
				if (res.code === 200 && Array.isArray(res.data)) {
					this.sysMessages = res.data;
					if (this.sysMessages.length > 0) {
						this.latestSysMessage = this.sysMessages[0];
					} else {
						this.latestSysMessage = null;
					}
				} else {
					this.sysMessages = [];
					this.latestSysMessage = null;
				}
			} catch (e) {
				console.error('获取系统消息失败', e);
				this.sysMessages = [];
				this.latestSysMessage = null;
			}
		},
		
		async loadOrderMessages() {
			try {
				const res = await getOrderMessages();
				if (res.code === 200 && Array.isArray(res.data)) {
					this.orderMessages = res.data;
					if (this.orderMessages.length > 0) {
						this.latestOrderMessage = this.orderMessages[0];
					} else {
						this.latestOrderMessage = null;
					}
				} else {
					this.orderMessages = [];
					this.latestOrderMessage = null;
				}
			} catch (e) {
				console.error('获取订单消息失败', e);
				this.orderMessages = [];
				this.latestOrderMessage = null;
			}
		},
		
		onSysMessageClick() {
			uni.navigateTo({ url: '/pages/message/sys-message-list?type=sys' });
		},
		
		onOrderMessageClick() {
			uni.navigateTo({ url: '/pages/message/sys-message-list?type=order' });
		},
		
		formatTime(time) {
			if (!time) return '';
			const now = new Date();
			const messageTime = new Date(time);
			const diff = now - messageTime;
			
			if (diff < 60000) {
				return '刚刚';
			} else if (diff < 3600000) {
				return Math.floor(diff / 60000) + '分钟前';
			} else if (messageTime.toDateString() === now.toDateString()) {
				return messageTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
			} else {
				return messageTime.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
			}
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background-color: #f7f7f7;
	padding-bottom: 120rpx;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 88rpx;
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 30rpx;
	border-bottom: 1rpx solid #eee;
	z-index: 100;
}

.nav-title {
	font-size: 32rpx;
	font-weight: 500;
}

/* 消息类型选项 */
.message-types {
	padding: 30rpx;
	background: #fff;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20rpx;
}

.type-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}

.type-icon {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 36rpx;
	color: #fff;
}

.type-icon.like {
	background: #ff6b9c;
}

.type-icon.order {
	background: #ff8c00;
}

.type-icon.chat {
	background: #1890ff;
}

.type-icon.phone {
	background: #52c41a;
}

.type-name {
	font-size: 24rpx;
	color: #666;
}

/* 消息列表 */
.message-section {
	margin-top: 24rpx;
	background: #fff;
	padding: 24rpx 30rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: 500;
	margin-bottom: 24rpx;
}

.message-item {
	position: relative;
	display: flex;
	align-items: flex-start;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.message-item:last-child {
	border-bottom: none;
}

.message-icon {
	width: 80rpx;
	height: 80rpx;
	background: #f0f7ff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #1890ff;
	font-size: 32rpx;
	margin-right: 24rpx;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 24rpx;
}

.message-content {
	flex: 1;
}

.message-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.user-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.message-time {
	font-size: 24rpx;
	color: #999;
}

.message-title {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.message-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

/* 无消息提示 */
.no-message {
	padding: 120rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #999;
	font-size: 28rpx;
}

.empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 24rpx;
}

.message-row {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.red-dot {
	position: absolute;
	left: 10rpx;
	top: 10rpx;
	width: 16rpx;
	height: 16rpx;
	background: #ff3b30;
	border-radius: 50%;
	border: 2rpx solid #fff;
	z-index: 2;
}

.clickable {
	cursor: pointer;
}

.message-icon.chat {
	background: #1890ff;
}

.red-dot {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	width: 16rpx;
	height: 16rpx;
	background: #ff4444;
	border-radius: 50%;
	z-index: 1;
}

.unread-count {
	background: #ff4444;
	color: #fff;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 20rpx;
	min-width: 32rpx;
	text-align: center;
}

.clickable {
	cursor: pointer;
}
</style>
