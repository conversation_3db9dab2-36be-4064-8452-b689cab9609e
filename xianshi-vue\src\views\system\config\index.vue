<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="基础配置" name="basic">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>基础配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="saveBasicConfig">保存配置</el-button>
          </div>
          
          <el-form ref="basicForm" :model="basicConfig" label-width="150px" v-loading="loading.basic">
            <el-form-item label="系统名称">
              <el-input v-model="basicConfig.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            <el-form-item label="系统Logo">
              <el-input v-model="basicConfig.systemLogo" placeholder="请输入Logo URL" />
              <div class="upload-demo">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :auto-upload="false"
                  :http-request="uploadLogoRequest"
                >
                  <i slot="default" class="el-icon-plus"></i>
                  <div slot="file" slot-scope="{file}">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
                <div class="el-upload__tip">建议尺寸：200px * 60px</div>
              </div>
            </el-form-item>
            <el-form-item label="客服电话">
              <el-input v-model="basicConfig.servicePhone" placeholder="请输入客服电话" />
            </el-form-item>
            <el-form-item label="客服邮箱">
              <el-input v-model="basicConfig.serviceEmail" placeholder="请输入客服邮箱" />
            </el-form-item>
            <el-form-item label="版权信息">
              <el-input v-model="basicConfig.copyright" placeholder="请输入版权信息" />
            </el-form-item>
            <el-form-item label="ICP备案号">
              <el-input v-model="basicConfig.icp" placeholder="请输入ICP备案号" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="会员配置" name="member">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>会员配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="saveMemberConfig">保存配置</el-button>
          </div>
          
          <el-form ref="memberForm" :model="memberConfig" label-width="150px" v-loading="loading.member">
            <el-form-item label="默认头像">
              <el-input v-model="memberConfig.defaultAvatar" placeholder="请输入默认头像URL" />
              <div class="upload-demo">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :auto-upload="false"
                  :http-request="uploadAvatarRequest"
                >
                  <i slot="default" class="el-icon-plus"></i>
                </el-upload>
                <div class="el-upload__tip">建议尺寸：200px * 200px</div>
              </div>
            </el-form-item>
            <el-form-item label="默认昵称前缀">
              <el-input v-model="memberConfig.defaultNicknamePrefix" placeholder="请输入默认昵称前缀" />
              <div class="form-tip">例如：闲时会员</div>
            </el-form-item>
            <el-form-item label="注册赠送金币">
              <el-input-number v-model="memberConfig.registerGold" :min="0" :step="10" />
            </el-form-item>
            <el-form-item label="每日签到金币">
              <el-input-number v-model="memberConfig.signInGold" :min="0" :step="1" />
            </el-form-item>
            <el-form-item label="达人认证资料完善度要求">
              <el-input-number v-model="memberConfig.talentProfileCompletionRequired" :min="0" :max="100" :step="5" />
              <span class="form-tip">单位：%</span>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="订单配置" name="order">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>订单配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="saveOrderConfig">保存配置</el-button>
          </div>
          
          <el-form ref="orderForm" :model="orderConfig" label-width="150px" v-loading="loading.order">
            <el-form-item label="订单发布金币消耗">
              <el-table
                :data="orderConfig.publishGoldTable"
                border
                style="width: 100%"
              >
                <el-table-column prop="activityType" label="陪玩类型" width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.activityType" placeholder="请输入陪玩类型" />
                  </template>
                </el-table-column>
                <el-table-column prop="gold" label="金币消耗" width="180">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.gold" :min="0" :step="10" />
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      @click="handleDeleteGoldItem(scope.$index)"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button type="primary" icon="el-icon-plus" @click="addGoldItem">添加陪玩类型</el-button>
              </div>
            </el-form-item>
            <el-form-item label="订单自动取消时间">
              <el-input-number v-model="orderConfig.autoCancelMinutes" :min="1" :step="5" />
              <span class="form-tip">单位：分钟，超过此时间未支付的订单将自动取消</span>
            </el-form-item>
            <el-form-item label="订单自动完成时间">
              <el-input-number v-model="orderConfig.autoCompleteHours" :min="1" :step="1" />
              <span class="form-tip">单位：小时，超过此时间未确认完成的订单将自动完成</span>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="支付配置" name="payment">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>支付配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="savePaymentConfig">保存配置</el-button>
          </div>
          
          <el-form ref="paymentForm" :model="paymentConfig" label-width="150px" v-loading="loading.payment">
            <el-form-item label="微信支付">
              <el-switch v-model="paymentConfig.enableWechatPay" />
            </el-form-item>
            <el-collapse-transition>
              <div v-show="paymentConfig.enableWechatPay">
                <el-form-item label="微信AppID">
                  <el-input v-model="paymentConfig.wechatAppId" placeholder="请输入微信AppID" />
                </el-form-item>
                <el-form-item label="微信商户号">
                  <el-input v-model="paymentConfig.wechatMchId" placeholder="请输入微信商户号" />
                </el-form-item>
                <el-form-item label="微信API密钥">
                  <el-input v-model="paymentConfig.wechatApiKey" placeholder="请输入微信API密钥" show-password />
                </el-form-item>
              </div>
            </el-collapse-transition>
            
            <el-form-item label="支付宝支付">
              <el-switch v-model="paymentConfig.enableAlipay" />
            </el-form-item>
            <el-collapse-transition>
              <div v-show="paymentConfig.enableAlipay">
                <el-form-item label="支付宝AppID">
                  <el-input v-model="paymentConfig.alipayAppId" placeholder="请输入支付宝AppID" />
                </el-form-item>
                <el-form-item label="支付宝应用私钥">
                  <el-input v-model="paymentConfig.alipayPrivateKey" type="textarea" placeholder="请输入支付宝应用私钥" />
                </el-form-item>
                <el-form-item label="支付宝公钥">
                  <el-input v-model="paymentConfig.alipayPublicKey" type="textarea" placeholder="请输入支付宝公钥" />
                </el-form-item>
              </div>
            </el-collapse-transition>
          </el-form>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  getBasicConfig, saveBasicConfig,
  getMemberConfig, saveMemberConfig,
  getOrderConfig, saveOrderConfig,
  getPaymentConfig, savePaymentConfig,
  uploadFile
} from '@/api/system';

export default {
  name: 'SystemConfig',
  data() {
    return {
      activeTab: 'basic',
      loading: {
        basic: false,
        member: false,
        order: false,
        payment: false
      },
      basicConfig: {
        systemName: '闲时有伴',
        systemLogo: '/static/images/logo.png',
        servicePhone: '************',
        serviceEmail: '<EMAIL>',
        copyright: '© 2025 闲时有伴 All Rights Reserved',
        icp: '粤ICP备12345678号'
      },
      memberConfig: {
        defaultAvatar: '/static/images/default-avatar.png',
        defaultNicknamePrefix: '闲时会员',
        registerGold: 100,
        signInGold: 5,
        talentProfileCompletionRequired: 50
      },
      orderConfig: {
        publishGoldTable: [
          { activityType: '休闲娱乐-KTV', gold: 100 },
          { activityType: '休闲娱乐-桌球', gold: 80 },
          { activityType: '运动-健身', gold: 120 },
          { activityType: '运动-篮球', gold: 100 }
        ],
        autoCancelMinutes: 30,
        autoCompleteHours: 24
      },
      paymentConfig: {
        enableWechatPay: true,
        wechatAppId: 'wx12345678',
        wechatMchId: '1234567890',
        wechatApiKey: '',
        enableAlipay: true,
        alipayAppId: '2021000000000000',
        alipayPrivateKey: '',
        alipayPublicKey: ''
      }
    };
  },
  created() {
    this.getConfigs();
  },
  methods: {
    // 获取所有配置
    getConfigs() {
      this.getBasicConfig();
      this.getMemberConfig();
      this.getOrderConfig();
      this.getPaymentConfig();
    },
    
    // 获取基础配置
    getBasicConfig() {
      this.loading.basic = true;
      getBasicConfig().then(response => {
        this.basicConfig = response.data || this.basicConfig;
        this.loading.basic = false;
      }).catch(() => {
        this.loading.basic = false;
      });
    },
    
    // 获取会员配置
    getMemberConfig() {
      this.loading.member = true;
      getMemberConfig().then(response => {
        this.memberConfig = response.data || this.memberConfig;
        this.loading.member = false;
      }).catch(() => {
        this.loading.member = false;
      });
    },
    
    // 获取订单配置
    getOrderConfig() {
      this.loading.order = true;
      getOrderConfig().then(response => {
        this.orderConfig = response.data || this.orderConfig;
        this.loading.order = false;
      }).catch(() => {
        this.loading.order = false;
      });
    },
    
    // 获取支付配置
    getPaymentConfig() {
      this.loading.payment = true;
      getPaymentConfig().then(response => {
        this.paymentConfig = response.data || this.paymentConfig;
        this.loading.payment = false;
      }).catch(() => {
        this.loading.payment = false;
      });
    },
    
    // 保存基础配置
    saveBasicConfig() {
      this.loading.basic = true;
      saveBasicConfig(this.basicConfig).then(() => {
        this.$message.success('基础配置保存成功');
        this.loading.basic = false;
      }).catch(() => {
        this.loading.basic = false;
      });
    },
    
    // 保存会员配置
    saveMemberConfig() {
      this.loading.member = true;
      saveMemberConfig(this.memberConfig).then(() => {
        this.$message.success('会员配置保存成功');
        this.loading.member = false;
      }).catch(() => {
        this.loading.member = false;
      });
    },
    
    // 保存订单配置
    saveOrderConfig() {
      this.loading.order = true;
      saveOrderConfig(this.orderConfig).then(() => {
        this.$message.success('订单配置保存成功');
        this.loading.order = false;
      }).catch(() => {
        this.loading.order = false;
      });
    },
    
    // 保存支付配置
    savePaymentConfig() {
      this.loading.payment = true;
      savePaymentConfig(this.paymentConfig).then(() => {
        this.$message.success('支付配置保存成功');
        this.loading.payment = false;
      }).catch(() => {
        this.loading.payment = false;
      });
    },
    
    // 添加金币项
    addGoldItem() {
      this.orderConfig.publishGoldTable.push({
        activityType: '',
        gold: 0
      });
    },
    
    // 删除金币项
    handleDeleteGoldItem(index) {
      this.orderConfig.publishGoldTable.splice(index, 1);
    },
    
    // 移除文件
    handleRemove(file) {
      console.log(file);
    },
    
    // 上传Logo
    uploadLogoRequest(options) {
      this.uploadFile(options, 'logo', url => {
        this.basicConfig.systemLogo = url;
      });
    },
    
    // 上传头像
    uploadAvatarRequest(options) {
      this.uploadFile(options, 'avatar', url => {
        this.memberConfig.defaultAvatar = url;
      });
    },
    
    // 通用上传方法
    uploadFile(options, type, callback) {
      const formData = new FormData();
      formData.append('file', options.file);
      formData.append('type', type);
      
      uploadFile(formData).then(response => {
        if (response.code === 200) {
          this.$message.success('上传成功');
          callback(response.data);
        } else {
          this.$message.error(response.msg || '上传失败');
        }
      }).catch(() => {
        this.$message.error('上传失败');
      });
    }
  }
};
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}
</style> 