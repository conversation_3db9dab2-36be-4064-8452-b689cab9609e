package com.play.xianshibusiness.dto.message;

import com.play.xianshibusiness.dto.PageQueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息查询DTO
 */
@Data
@ApiModel(value = "消息查询DTO")
@EqualsAndHashCode(callSuper = true)
public class MessageQueryDTO extends PageQueryParam {
    
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    
    @ApiModelProperty(value = "会员昵称(模糊查询)")
    private String memberName;
    
    @ApiModelProperty(value = "消息类型：1-系统通知，2-订单通知，3-活动通知")
    private Integer messageType;
    
    @ApiModelProperty(value = "是否已读：true-已读，false-未读")
    private Boolean isRead;
    
    @ApiModelProperty(value = "创建开始时间 yyyy-MM-dd HH:mm:ss")
    private String createTimeStart;
    
    @ApiModelProperty(value = "创建结束时间 yyyy-MM-dd HH:mm:ss")
    private String createTimeEnd;
    
    @ApiModelProperty(value = "排序字段")
    private String orderBy;
    
    @ApiModelProperty(value = "排序方式：asc-升序，desc-降序")
    private String orderDirection = "desc";
} 