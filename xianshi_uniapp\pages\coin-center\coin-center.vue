<template>
  <view class="container">
    <!-- 导航栏 -->


    <!-- 金币余额横幅 -->
    <view class="coin-banner">
      <view class="banner-content">
        <view class="balance-section">
          <view class="balance-icon">
            <text class="icon-text">M</text>
          </view>
          <view class="balance-info">
            <text class="balance-number">{{ coinBalance }}</text>
            <text class="balance-desc">小金币有大用途,多领一些屯起来</text>
          </view>
        </view>
        <view class="recharge-section">
          <button class="recharge-btn" @tap="goToRecharge">充值金币</button>
        </view>
      </view>
    </view>

    <!-- 标签页切换 -->
    <view class="tabs">
      <view
        :class="`tab ${activeTab === 0 ? 'active' : ''}`"
        @click="switchTab"
        data-tab="0"
      >
        <text class="tab-text">获得金币</text>
      </view>
      <view
        :class="`tab ${activeTab === 1 ? 'active' : ''}`"
        @click="switchTab"
        data-tab="1"
      >
        <text class="tab-text">金币用途</text>
      </view>
      <view
        :class="`tab ${activeTab === 2 ? 'active' : ''}`"
        @click="switchTab"
        data-tab="2"
      >
        <text class="tab-text">金币记录</text>
      </view>
    </view>

    <!-- 获得金币选项卡 -->
    <view class="tab-content" v-if="activeTab === 0">
      <!-- 日常任务 -->
      <view class="task-section">
        <view class="section-header">
          <view class="section-icon">✓</view>
          <text class="section-title">日常任务</text>
        </view>
        <view class="task-list">
          <view
            v-for="task in dailyTasks"
            :key="task.id"
            class="task-item"
          >
            <view class="task-info">
              <view class="task-main">
                <text class="task-title">{{ task.title }}</text>
                <view class="task-reward">
                  <text class="reward-icon">🪙</text>
                  <text class="reward-text">{{ task.reward }}</text>
                </view>
              </view>
              <text class="task-desc">{{ task.description }}</text>
            </view>
            <view class="task-action">
              <text v-if="task.completed" class="status-text">{{ task.status }}</text>
              <button v-else class="action-btn" @click="handleTaskAction" :data-task="task.id">
                {{ task.actionText }}
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 奖励任务 -->
      <view class="task-section">
        <view class="section-header">
          <view class="section-icon">🏆</view>
          <text class="section-title">奖励任务</text>
        </view>
        <view class="task-list">
          <view
            v-for="task in rewardTasks"
            :key="task.id"
            class="task-item"
          >
            <view class="task-info">
              <view class="task-main">
                <text class="task-title">{{ task.title }}</text>
                <view class="task-reward">
                  <text class="reward-icon">🪙</text>
                  <text class="reward-text">{{ task.reward }}</text>
                </view>
              </view>
              <text class="task-desc">{{ task.description }}</text>
            </view>
            <view class="task-action">
              <text v-if="task.completed" class="status-text">{{ task.status }}</text>
              <button v-else class="action-btn" @click="handleTaskAction" :data-task="task.id">
                {{ task.actionText }}
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 金币用途选项卡 -->
    <view class="tab-content" v-if="activeTab === 1">
      <view class="usage-section">
        <view class="usage-item">
          <view class="usage-icon">📝</view>
          <view class="usage-info">
            <text class="usage-title">发布订单</text>
            <text class="usage-desc">每次发布订单消耗2金币</text>
          </view>
        </view>
        <view class="usage-item">
          <view class="usage-icon">💎</view>
          <view class="usage-info">
            <text class="usage-title">购买服务</text>
            <text class="usage-desc">可用于购买平台内的各种服务</text>
          </view>
        </view>
        <view class="usage-item">
          <view class="usage-icon">🎁</view>
          <view class="usage-info">
            <text class="usage-title">兑换礼品</text>
            <text class="usage-desc">金币可兑换实物礼品或优惠券</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 金币记录选项卡 -->
    <view class="tab-content" v-if="activeTab === 2">
      <view class="record-section">
        <view class="record-item" v-for="record in coinRecords" :key="record.id">
          <view class="record-info">
            <text class="record-title">{{ record.title }}</text>
            <text class="record-time">{{ record.time }}</text>
          </view>
          <view class="record-amount">
            <text :class="['amount-text', record.type]">{{ record.amount }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getGoldBalance, getGoldRecords } from '@/api/index.js';

export default {
  data() {
    return {
      coinBalance: 0, // 用户金币余额
      activeTab: 0, // 当前激活的标签页
      dailyTasks: [
        {
          id: 1,
          title: "每日签到",
          reward: "+1",
          description: "每日签到,可获得1金币",
          completed: true,
          status: "已签到",
          actionText: "去签到"
        },
        {
          id: 2,
          title: "邀请好友",
          reward: "+3/人",
          description: "每邀请一位好友,可获得3金币",
          completed: false,
          status: "",
          actionText: "去邀请"
        }
      ],
      rewardTasks: [
        {
          id: 3,
          title: "去美影云发布场地",
          reward: "+5",
          description: "发布场地,可获得美影积分,积分可兑换金币",
          completed: false,
          status: "",
          actionText: "去发布"
        },
        {
          id: 4,
          title: "完善联系方式",
          reward: "+5",
          description: "绑定联系方式,可获得5金币",
          completed: true,
          status: "已完成",
          actionText: "去完善"
        },
        {
          id: 5,
          title: "模卡获得≥5个粉丝",
          reward: "+5",
          description: "模卡主页获得5个粉丝,可获得5金币",
          completed: false,
          status: "",
          actionText: "去分享"
        }
      ],
      coinRecords: [
        {
          id: 1,
          title: "每日签到奖励",
          time: "2024-01-15 09:30",
          amount: "+1",
          type: "income"
        },
        {
          id: 2,
          title: "发布订单",
          time: "2024-01-14 15:20",
          amount: "-2",
          type: "expense"
        },
        {
          id: 3,
          title: "邀请好友奖励",
          time: "2024-01-13 10:15",
          amount: "+3",
          type: "income"
        },
        {
          id: 4,
          title: "完善联系方式",
          time: "2024-01-12 14:30",
          amount: "+5",
          type: "income"
        }
      ]
    };
  },
  onLoad() {
    // 获取用户金币余额和记录
    this.loadGoldBalance();
    this.loadGoldRecords();
  },
  onShow() {
    // 页面显示时刷新金币余额
    this.loadGoldBalance();
  },
  methods: {
     /**
      * 获取用户金币余额
      */
     async loadGoldBalance() {
       try {
         const res = await getGoldBalance();
         if (res.code === 200) {
           this.coinBalance = res.data || 0;
           console.log('获取金币余额成功:', this.coinBalance);
         } else {
           console.error('获取金币余额失败:', res.msg);
           // 使用缓存值作为备用
           this.coinBalance = uni.getStorageSync('coinBalance') || 0;
         }
       } catch (error) {
         console.error('获取金币余额异常:', error);
         // 使用缓存值作为备用
         this.coinBalance = uni.getStorageSync('coinBalance') || 0;
       }
     },

     /**
      * 获取金币记录
      */
     async loadGoldRecords() {
       try {
         const res = await getGoldRecords({
           pageNum: 1,
           pageSize: 20
         });
         if (res.code === 200) {
           const records = res.data?.records || res.data || [];
           // 转换后端数据格式为前端显示格式
           this.coinRecords = records.map(record => ({
             id: record.id,
             title: this.getRecordTitle(record),
             time: this.formatTime(record.createTime),
             amount: record.changeAmount > 0 ? `+${record.changeAmount}` : `${record.changeAmount}`,
             type: record.changeAmount > 0 ? 'income' : 'expense'
           }));
           console.log('获取金币记录成功:', this.coinRecords);
         } else {
           console.error('获取金币记录失败:', res.msg);
         }
       } catch (error) {
         console.error('获取金币记录异常:', error);
       }
     },

     /**
      * 根据记录类型获取标题
      */
     getRecordTitle(record) {
       const typeMap = {
         'RECHARGE': '充值金币',
         'ORDER_CONSUME': '发布订单',
         'ORDER_REFUND': '订单退款',
         'TASK_REWARD': '任务奖励',
         'SIGN_IN': '签到奖励',
         'INVITE_REWARD': '邀请奖励'
       };
       return typeMap[record.type] || record.remark || '金币变动';
     },

     /**
      * 格式化时间
      */
     formatTime(timeStr) {
       if (!timeStr) return '';
       const date = new Date(timeStr);
       const year = date.getFullYear();
       const month = String(date.getMonth() + 1).padStart(2, '0');
       const day = String(date.getDate()).padStart(2, '0');
       const hour = String(date.getHours()).padStart(2, '0');
       const minute = String(date.getMinutes()).padStart(2, '0');
       return `${year}-${month}-${day} ${hour}:${minute}`;
     },

     // 切换标签页
     switchTab(e) {
       const tab = parseInt(e.currentTarget.dataset.tab);
       this.activeTab = tab;
     },

    // 处理任务操作
    handleTaskAction(e) {
      const taskId = e.currentTarget.dataset.task;
      const task = [...this.dailyTasks, ...this.rewardTasks].find(t => t.id === taskId);
      
      if (!task) return;

      // 根据任务类型执行不同操作
      switch (taskId) {
        case 1: // 每日签到
          this.handleDailySignIn();
          break;
        case 2: // 邀请好友
          this.handleInviteFriends();
          break;
        case 3: // 去美影云发布场地
          this.handlePublishVenue();
          break;
        case 4: // 完善联系方式
          this.handleCompleteContact();
          break;
        case 5: // 模卡获得粉丝
          this.handleShareProfile();
          break;
      }
    },

    // 处理每日签到
    handleDailySignIn() {
      // 检查是否已经签到
      const today = new Date().toDateString();
      const lastSignIn = uni.getStorageSync("lastSignIn");
      
      if (lastSignIn === today) {
        uni.showToast({
          title: "今日已签到",
          icon: "none"
        });
        return;
      }

      // 执行签到
      uni.showLoading({ title: "签到中..." });
      
      setTimeout(() => {
        // 更新金币余额
        const newBalance = this.coinBalance + 1;
        this.coinBalance = newBalance;
        uni.setStorageSync("coinBalance", newBalance);
        uni.setStorageSync("lastSignIn", today);

        // 更新任务状态
        const taskIndex = this.dailyTasks.findIndex(t => t.id === 1);
        if (taskIndex !== -1) {
          this.dailyTasks[taskIndex].completed = true;
          this.dailyTasks[taskIndex].status = "已签到";
        }

        uni.hideLoading();
        uni.showToast({
          title: "签到成功，获得1金币",
          icon: "success"
        });
      }, 1000);
    },

    // 处理邀请好友
    handleInviteFriends() {
      uni.showModal({
        title: "邀请好友",
        content: "分享邀请链接给好友，好友注册后您将获得3金币奖励",
        confirmText: "去分享",
        success: (res) => {
          if (res.confirm) {
            // 这里可以调用分享功能
            uni.showToast({
              title: "分享功能开发中",
              icon: "none"
            });
          }
        }
      });
    },

    // 处理发布场地
    handlePublishVenue() {
      uni.showModal({
        title: "发布场地",
        content: "跳转到美影云发布场地页面",
        confirmText: "去发布",
        success: (res) => {
          if (res.confirm) {
            // 这里可以跳转到发布页面
            uni.showToast({
              title: "跳转功能开发中",
              icon: "none"
            });
          }
        }
      });
    },

    // 处理完善联系方式
    handleCompleteContact() {
      uni.showModal({
        title: "完善联系方式",
        content: "跳转到个人资料页面完善联系方式",
        confirmText: "去完善",
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: "/pages/edit-profile/edit-profile"
            });
          }
        }
      });
    },

    // 处理分享个人主页
    handleShareProfile() {
      uni.showModal({
        title: "分享个人主页",
        content: "分享您的个人主页给好友，获得更多粉丝",
        confirmText: "去分享",
        success: (res) => {
          if (res.confirm) {
            // 这里可以调用分享功能
            uni.showToast({
              title: "分享功能开发中",
              icon: "none"
            });
          }
        }
      });
    },

    // 跳转到充值页面
    goToRecharge() {
      uni.navigateTo({
        url: "/pages/coin-recharge/coin-recharge",
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: function () {
          // 如果返回失败（没有上一页），则跳转到首页
          uni.navigateTo({
            url: "/pages/index/index",
          });
        },
      });
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.nav-back {
  width: 60rpx;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 15rpx;
}

.nav-icon {
  font-size: 24rpx;
  color: #666;
  width: 30rpx;
  text-align: center;
}

/* 金币余额横幅 */
.coin-banner {
  background-color: #ffd700;
  padding: 40rpx 30rpx;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.balance-icon {
  width: 60rpx;
  height: 60rpx;
  background-color: #ffb347;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #fff;
}

.balance-info {
  display: flex;
  flex-direction: column;
}

.balance-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.balance-desc {
  font-size: 24rpx;
  color: #666;
}

.recharge-section {
  margin-left: 20rpx;
}

.recharge-btn {
  background-color: #333;
  color: #fff;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  border: none;
  line-height: 1.5;
}

/* 标签页 */
.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  margin: 0 30rpx;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #ffd700;
  font-weight: 500;
}

.tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ffd700;
  border-radius: 2rpx;
}

.tab-text {
  display: block;
}

/* 选项卡内容 */
.tab-content {
  padding: 30rpx;
}

/* 任务区域 */
.task-section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.task-list {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.task-item:last-child {
  border-bottom: none;
}

.task-info {
  flex: 1;
  margin-right: 20rpx;
}

.task-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.task-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.task-reward {
  display: flex;
  align-items: center;
}

.reward-icon {
  font-size: 20rpx;
  margin-right: 6rpx;
}

.reward-text {
  font-size: 24rpx;
  color: #ffd700;
  font-weight: 500;
}

.task-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.task-action {
  flex-shrink: 0;
}

.status-text {
  font-size: 24rpx;
  color: #999;
}

.action-btn {
  background-color: #ffd700;
  color: #fff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: none;
  line-height: 1.5;
}

/* 金币用途 */
.usage-section {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.usage-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.usage-item:last-child {
  border-bottom: none;
}

.usage-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.usage-info {
  flex: 1;
}

.usage-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.usage-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

/* 金币记录 */
.record-section {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.record-time {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.record-amount {
  flex-shrink: 0;
}

.amount-text {
  font-size: 28rpx;
  font-weight: 500;
}

.amount-text.income {
  color: #52c41a;
}

.amount-text.expense {
  color: #ff4d4f;
}
</style>
