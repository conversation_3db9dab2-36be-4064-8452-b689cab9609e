package com.play.xianshibusiness.aspect;

import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.enums.ResultCode;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.utils.JwtSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.Optional;

/**
 * 身份验证切面
 * 拦截标记了@RequireLogin注解的方法，进行JWT令牌验证
 */
@Aspect
@Component
@Slf4j
public class AuthenticationAspect {

    private static final String AUTHORIZATION_HEADER = "Authorization";

    /**
     * 环绕通知，拦截标记了@RequireLogin注解的方法
     */
    @Around("@annotation(com.play.xianshibusiness.annotation.RequireLogin) || @within(com.play.xianshibusiness.annotation.RequireLogin)")
    public Object checkAuth(ProceedingJoinPoint joinPoint) throws Throwable {
        log.debug("进入身份验证切面");
        
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取注解信息（优先从方法上获取，如果没有则从类上获取）
        RequireLogin requireLogin = method.getAnnotation(RequireLogin.class);
        if (requireLogin == null) {
            requireLogin = method.getDeclaringClass().getAnnotation(RequireLogin.class);
        }
        
        if (requireLogin == null) {
            // 如果没有注解（理论上不会发生，因为切点表达式已经过滤），直接执行原方法
            return joinPoint.proceed();
        }
        
        // 从请求中获取令牌
        HttpServletRequest request = getRequest();
        if (request == null) {
            throw new GlobalException(ResultCode.INVALID_TOKEN);
        }

        String token = request.getHeader(AUTHORIZATION_HEADER);
        if (token == null || token.isEmpty()) {
            throw new GlobalException(ResultCode.INVALID_TOKEN);
        }
        
        // 验证令牌
        Authentication authentication = JwtSignUtils.validateToken(token);
        if (authentication == null) {
            throw new GlobalException(ResultCode.INVALID_TOKEN);
        }


        // 设置认证信息到安全上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 如果需要检查角色
        if (requireLogin.checkRole() && requireLogin.roles().length > 0) {
            boolean hasRequiredRole = false;
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            
            for (String requiredRole : requireLogin.roles()) {
                // 检查是否有要求的任一角色
                if (authorities.stream().anyMatch(auth -> auth.getAuthority().equals(requiredRole) || 
                        auth.getAuthority().equals("ROLE_" + requiredRole))) {
                    hasRequiredRole = true;
                    break;
                }
            }
            
            if (!hasRequiredRole) {
                log.warn("用户 {} 没有所需角色: {}", authentication.getName(), Arrays.toString(requireLogin.roles()));
                throw new GlobalException(ResultCode.Login_No_Role);
            }
        }
        
        // 验证通过，执行原方法
        return joinPoint.proceed();
    }
    
    /**
     * 获取当前请求
     */
    private HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return Optional.ofNullable(attributes)
            .map(ServletRequestAttributes::getRequest)
            .orElse(null);
    }
} 