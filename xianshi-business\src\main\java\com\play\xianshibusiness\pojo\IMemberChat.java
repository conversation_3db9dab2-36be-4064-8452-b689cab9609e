package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * (IMemberChat)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("i_member_chat")
@ApiModel("会员聊天记录表")
public class IMemberChat extends BaseObjPo {

    //会员ID
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    //目标会员ID
    @ApiModelProperty(value = "目标会员ID")
    private String targetId;
    //内容JSON
    @ApiModelProperty(value = "内容JSON")
    private String contentJson;

}

