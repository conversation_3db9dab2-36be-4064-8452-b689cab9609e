//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.play.xianshibusiness.vailad;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import com.play.xianshibusiness.annotation.LoginRequestValid;
import com.play.xianshibusiness.dto.member.LoginRequest;
import com.play.xianshibusiness.enums.ResultCode;
import com.play.xianshibusiness.exception.GlobalException;
import org.springframework.util.StringUtils;

import java.util.Objects;

public class LoginRequestValidator implements ConstraintValidator<LoginRequestValid, LoginRequest> {
    public LoginRequestValidator() {
    }

    public boolean isValid(LoginRequest request, ConstraintValidatorContext context) {
        if (Objects.isNull(request)) {
            throw new GlobalException(ResultCode.InValid_Param);
        } else {
            boolean valid = true;
            context.disableDefaultConstraintViolation();
            if (StringUtils.isEmpty(request.getPhone())) {
                this.buildConstraint(context, "手机号不能为空", "phone");
                valid = false;
            }

            // 检查登录方式是否为空
            if (Objects.isNull(request.getMethod())) {
                this.buildConstraint(context, "登录方式不能为空", "method");
                valid = false;
            } else {
                // 根据登录方式验证相应字段
                switch (request.getMethod()) {
                    case PHONE_CODE:
                        if (StringUtils.isEmpty(request.getCode())) {
                            this.buildConstraint(context, "验证码不能为空", "code");
                            valid = false;
                        }
                        break;
                    case CURRENT_PHONE:
                        if (StringUtils.isEmpty(request.getCode())) {
                            this.buildConstraint(context, "一键登录token不能为空", "code");
                            valid = false;
                        }
                        break;
                    default:
                        this.buildConstraint(context, "不支持的登录方式", "method");
                        valid = false;
                        break;
                }
            }

            return valid;
        }
    }

    /**
     * 构建约束验证信息
     *
     * @param context 约束验证上下文
     * @param message 验证失败时显示的消息模板
     * @param node    要添加约束验证的属性节点名称
     */
    public void buildConstraint(ConstraintValidatorContext context, String message, String node) {
        context.buildConstraintViolationWithTemplate(message).addPropertyNode(node).addConstraintViolation();
    }

}
