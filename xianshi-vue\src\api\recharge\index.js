import request from '@/utils/request';

/**
 * 获取所有充值套餐
 * @returns {Promise}
 */
export function getAllPackages() {
  return request({
    url: '/recharge/packages',
    method: 'get'
  });
}

/**
 * 获取充值套餐详情
 * @param {String} id 套餐ID
 * @returns {Promise}
 */
export function getPackageDetail(id) {
  return request({
    url: `/recharge/package/${id}`,
    method: 'get'
  });
}

/**
 * 创建充值套餐
 * @param {Object} data 套餐数据
 * @returns {Promise}
 */
export function createPackage(data) {
  return request({
    url: '/recharge/package',
    method: 'post',
    data
  });
}

/**
 * 更新充值套餐
 * @param {String} id 套餐ID
 * @param {Object} data 套餐数据
 * @returns {Promise}
 */
export function updatePackage(id, data) {
  return request({
    url: `/recharge/package/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除充值套餐
 * @param {String} id 套餐ID
 * @returns {Promise}
 */
export function deletePackage(id) {
  return request({
    url: `/recharge/package/${id}`,
    method: 'delete'
  });
}

/**
 * 分页查询充值套餐
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function pagePackages(params) {
  return request({
    url: '/recharge/package/page',
    method: 'get',
    params
  });
}

/**
 * 获取充值记录
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getRechargeRecords(params) {
  return request({
    url: '/recharge/records',
    method: 'get',
    params
  });
}

/**
 * 获取充值记录详情
 * @param {String} id 充值记录ID
 * @returns {Promise}
 */
export function getRechargeDetail(id) {
  return request({
    url: `/recharge/record/${id}`,
    method: 'get'
  });
}

/**
 * 管理员给会员充值
 * @param {Object} data 充值数据
 * @returns {Promise}
 */
export function adminRecharge(data) {
  return request({
    url: '/recharge/member',
    method: 'post',
    data
  });
}

/**
 * 搜索会员
 * @param {String} keyword 搜索关键词
 * @returns {Promise}
 */
export function searchMembers(keyword) {
  return request({
    url: '/member/search',
    method: 'get',
    params: { keyword }
  });
} 