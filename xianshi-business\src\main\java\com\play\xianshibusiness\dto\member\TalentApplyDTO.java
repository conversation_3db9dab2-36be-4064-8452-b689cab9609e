package com.play.xianshibusiness.dto.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 达人申请DTO
 */
@Data
@ApiModel("达人申请DTO")
public class TalentApplyDTO {
    
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    
    @ApiModelProperty(value = "会员昵称")
    private String nickname;
    
    @ApiModelProperty(value = "会员头像")
    private String avatar;
    
    @ApiModelProperty(value = "性别：1-男，2-女")
    private Integer gender;
    
    @ApiModelProperty(value = "年龄")
    private Integer age;
    
    @ApiModelProperty(value = "手机号")
    private String phone;
    
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;
    
    @ApiModelProperty(value = "申请状态：0-待审核，1-已通过，2-已拒绝")
    private Integer status;
    
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;
    
    @ApiModelProperty(value = "审核人ID")
    private String auditorId;
    
    @ApiModelProperty(value = "审核意见")
    private String auditComment;
    
    @ApiModelProperty(value = "自我介绍")
    private String introduction;
    
    @ApiModelProperty(value = "技能特长")
    private String skills;
    
    @ApiModelProperty(value = "照片列表")
    private String[] photos;
    
    @ApiModelProperty(value = "视频")
    private String video;
} 