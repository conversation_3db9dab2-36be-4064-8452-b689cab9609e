package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.dto.config.BasicConfigDTO;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.TBasicConfigMapper;
import com.play.xianshibusiness.pojo.TBasicConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 基础配置服务接口
 */
public interface TBasicConfigService {
    
    /**
     * 获取所有基础配置
     *
     * @return 基础配置列表
     */
    List<TBasicConfig> getAllBasicConfig();
    
    /**
     * 更新基础配置
     *
     * @param configDTO 配置DTO
     * @return 是否成功
     */
    Boolean updateBasicConfig(BasicConfigDTO configDTO);
    
    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);
    
    /**
     * 根据配置键获取配置对象
     *
     * @param configKey 配置键
     * @return 配置对象
     */
    TBasicConfig getConfigByKey(String configKey);
}

