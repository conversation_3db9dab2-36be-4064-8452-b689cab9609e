package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.recharge.RechargePackageVO;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.CRechargePackageMapper;
import com.play.xianshibusiness.pojo.CRechargePackage;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 充值套餐服务
 */
@Service
public class CRechargePackageService {

    @Resource
    private CRechargePackageMapper rechargePackageMapper;

    /**
     * 获取所有充值套餐
     * @return 充值套餐列表
     */
    public List<RechargePackageVO> getAllPackages() {
        List<CRechargePackage> packages = rechargePackageMapper.selectList(
                new LambdaQueryWrapper<CRechargePackage>()
                        .eq(CRechargePackage::getDeleted, false)
                        .eq(CRechargePackage::getAvailable, true)
                        .orderByAsc(CRechargePackage::getSort)
        );
        
        return packages.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    /**
     * 获取推荐充值套餐
     * @return 推荐充值套餐列表
     */
    public List<RechargePackageVO> getRecommendPackages() {
        List<CRechargePackage> packages = rechargePackageMapper.selectList(
                new LambdaQueryWrapper<CRechargePackage>()
                        .eq(CRechargePackage::getDeleted, false)
                        .eq(CRechargePackage::getAvailable, true)
                        .eq(CRechargePackage::getIsRecommend, true)
                        .orderByAsc(CRechargePackage::getSort)
        );
        
        return packages.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    /**
     * 获取充值套餐详情
     * @param id 套餐ID
     * @return 充值套餐详情
     */
    public RechargePackageVO getPackageDetail(String id) {
        CRechargePackage rechargePackage = rechargePackageMapper.selectById(id);
        if (rechargePackage == null || rechargePackage.getDeleted() || !rechargePackage.getAvailable()) {
            throw new GlobalException(400, "充值套餐不存在");
        }
        
        return convertToVO(rechargePackage);
    }
    
    /**
     * 创建充值套餐
     * @param rechargePackage 充值套餐
     * @return 套餐ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String createPackage(CRechargePackage rechargePackage) {
        rechargePackage.setId(UUID.randomUUID().toString());
        rechargePackage.setCreateTime(LocalDateTime.now());
        rechargePackage.setUpdateTime(LocalDateTime.now());
        rechargePackage.setDeleted(false);
        rechargePackage.setAvailable(true);
        
        rechargePackageMapper.insert(rechargePackage);
        
        return rechargePackage.getId();
    }
    
    /**
     * 更新充值套餐
     * @param id 套餐ID
     * @param rechargePackage 充值套餐
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePackage(String id, CRechargePackage rechargePackage) {
        CRechargePackage existPackage = rechargePackageMapper.selectById(id);
        if (existPackage == null || existPackage.getDeleted()) {
            throw new GlobalException(400, "充值套餐不存在");
        }
        
        rechargePackage.setId(id);
        rechargePackage.setUpdateTime(LocalDateTime.now());
        
        rechargePackageMapper.updateById(rechargePackage);
        
        return true;
    }
    
    /**
     * 删除充值套餐
     * @param id 套餐ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePackage(String id) {
        CRechargePackage rechargePackage = rechargePackageMapper.selectById(id);
        if (rechargePackage == null || rechargePackage.getDeleted()) {
            throw new GlobalException(400, "充值套餐不存在");
        }
        
        rechargePackage.setDeleted(true);
        rechargePackage.setUpdateTime(LocalDateTime.now());
        
        rechargePackageMapper.updateById(rechargePackage);
        
        return true;
    }
    
    /**
     * 分页查询充值套餐
     * @param pageNum 页码
     * @param pageSize 每页条数
     * @param name 套餐名称
     * @return 分页结果
     */
    public Page<RechargePackageVO> pagePackages(Integer pageNum, Integer pageSize, String name) {
        LambdaQueryWrapper<CRechargePackage> queryWrapper = new LambdaQueryWrapper<CRechargePackage>()
                .eq(CRechargePackage::getDeleted, false);
        
        if (!StringUtils.isEmpty(name)) {
            queryWrapper.like(CRechargePackage::getName, name);
        }
        
        queryWrapper.orderByAsc(CRechargePackage::getSort);
        
        Page<CRechargePackage> page = new Page<>(pageNum, pageSize);
        Page<CRechargePackage> packagePage = rechargePackageMapper.selectPage(page, queryWrapper);
        
        Page<RechargePackageVO> resultPage = new Page<>(packagePage.getCurrent(), packagePage.getSize(), packagePage.getTotal());
        List<RechargePackageVO> records = packagePage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        resultPage.setRecords(records);
        
        return resultPage;
    }
    
    /**
     * 转换为VO
     * @param rechargePackage 充值套餐
     * @return 充值套餐VO
     */
    private RechargePackageVO convertToVO(CRechargePackage rechargePackage) {
        RechargePackageVO vo = new RechargePackageVO();
        BeanUtils.copyProperties(rechargePackage, vo);
        return vo;
    }
} 