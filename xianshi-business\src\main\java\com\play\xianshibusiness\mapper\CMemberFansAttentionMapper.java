package com.play.xianshibusiness.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.play.xianshibusiness.pojo.CMemberFansAttention;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * (CMemberFansAttentionDao)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:13
 */
public interface CMemberFansAttentionMapper extends BaseMapper<CMemberFansAttention> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<CMemberFansAttentionDao> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<CMemberFansAttention> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<CMemberFansAttentionDao> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<CMemberFansAttention> entities);

    /**
     * 查询有效的会员 关注数量
     * @return
     */
    @Select("select count(1) from c_member_fans_attention cf left join c_member cm on cm.id = cf.member_id  where cm.deleted = 0 and cm.available = 1 and cf.member_id = #{memberId}")
    String  getCMemberFansCount(@Param("memberId") String memberId);
}

