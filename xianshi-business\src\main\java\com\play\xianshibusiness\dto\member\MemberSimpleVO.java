package com.play.xianshibusiness.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员简单信息VO
 */
@Data
@ApiModel(value = "会员简单信息")
public class MemberSimpleVO {

    @ApiModelProperty(value = "会员ID")
    private String id;
    
    @ApiModelProperty(value = "会员昵称")
    private String nickname;
    
    @ApiModelProperty(value = "会员头像")
    private String avatar;
    
    @ApiModelProperty(value = "会员手机号")
    private String phone;
    
    @ApiModelProperty(value = "会员金币余额")
    private Integer goldBalance;
} 