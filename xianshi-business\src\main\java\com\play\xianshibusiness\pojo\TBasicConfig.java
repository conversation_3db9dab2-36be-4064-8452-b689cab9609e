package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 系统基础配置实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_basic_config")
@ApiModel("系统基础配置表")
public class TBasicConfig extends BaseObjPo {

    @ApiModelProperty("配置键")
    private String configKey;
    
    @ApiModelProperty("配置值")
    private String configValue;
    
    @ApiModelProperty("配置名称")
    private String configName;
    
    @ApiModelProperty("配置描述")
    @TableField("config_desc")
    private String configDesc;
    
    @ApiModelProperty("配置分组")
    private String configGroup;



}

