<template>
  <view class="container">
    <!-- 照片网格 -->
    <view class="photos-grid">
      <!-- 已上传的照片 -->
      <view class="photo-item" v-for="(item, index) in photos" :key="index">
        <view class="photo-image" :style="{ backgroundImage: `url(${item})` }"></view>
        <view class="delete-btn" @tap="deletePhoto" :data-index="index"
          >×</view
        >
      </view>
    </view>
    
    <!-- 图片上传组件 -->
    <view class="upload-container">
      <uni-file-picker 
        v-if="photos.length < 9"
        file-mediatype="image"
        mode="grid"
        limit="9"
        :limitType="['png', 'jpg', 'jpeg']"
        title="最多选择9张图片"
        @select="selectPhotos"
        @progress="uploadProgress"
        @success="uploadSuccess"
        ref="photoPicker"
      ></uni-file-picker>
      <view v-else class="limit-tip">最多上传9张照片</view>
    </view>
    
    <!-- 上传进度 -->
    <view class="progress-container" v-if="uploadProgressPercent > 0">
      <text class="progress-text">上传进度: {{uploadProgressPercent}}%</text>
      <progress :percent="uploadProgressPercent" stroke-width="4" />
    </view>

    <!-- 保存按钮 -->
    <view class="save-btn-wrap">
      <button class="save-btn" @tap="savePhotos" :disabled="uploading">保存</button>
    </view>
  </view>
</template>

<script>
const app = getApp().globalData;

export default {
  data() {
    return {
      photos: [],
      uploadProgressPercent: 0,
      uploading: false,
      tempPhotoPath: "",
      currentUploadIndex: 0,
      totalToUpload: 0,
      uploadQueue: []
    };
  },
  onLoad(options) {
    uni.setNavigationBarTitle({
      title: "编辑形象照",
    });

    if (options.photos) {
      let parsedPhotos = JSON.parse(decodeURIComponent(options.photos));
      
      // 检查并修正照片URL
      parsedPhotos = parsedPhotos.map(url => {
        if (url && !url.startsWith('http')) {
          return 'https:' + url;
        }
        return url;
      });
      
      console.log('加载的照片URLs:', parsedPhotos);
      this.photos = parsedPhotos;
    }
  },
  methods: {
    // 选择照片回调
    selectPhotos(e) {
      console.log('选择照片', e);
      if (e.tempFiles && e.tempFiles.length > 0) {
        // 检查总数是否超过9张
        const totalCount = this.photos.length + e.tempFiles.length;
        if (totalCount > 9) {
          uni.showToast({
            title: `最多上传9张照片，当前已有${this.photos.length}张`,
            icon: 'none'
          });
          return;
        }
        
        // 添加到上传队列
        this.uploadQueue = [...e.tempFiles];
        this.totalToUpload = e.tempFiles.length;
        this.currentUploadIndex = 0;
        this.uploading = true;
        
        // 直接开始上传
        this.uploadNextPhoto();
      }
    },
    
    // 上传队列中的下一张照片
    uploadNextPhoto() {
      if (this.currentUploadIndex < this.uploadQueue.length) {
        const file = this.uploadQueue[this.currentUploadIndex];
        this.uploadPhotoToOSS(file);
      } else {
        // 所有照片上传完成
        this.uploading = false;
        this.uploadQueue = [];
        
        // 2秒后隐藏进度条
        setTimeout(() => {
          this.uploadProgressPercent = 0;
        }, 2000);
        
        if (this.totalToUpload > 0) {
          uni.showToast({
            title: `成功上传${this.totalToUpload}张照片`,
            icon: 'success'
          });
        }
      }
    },

    // 监听上传进度
    uploadProgress(e) {
      console.log('上传进度', e);
      if (e.progress) {
        // 计算总体进度：(已完成图片数 + 当前图片进度比例) / 总图片数
        const singleProgress = e.progress / 100;
        const totalProgress = ((this.currentUploadIndex + singleProgress) / this.totalToUpload) * 100;
        this.uploadProgressPercent = Math.floor(totalProgress);
      }
    },

    // 上传成功回调
    uploadSuccess(e) {
      console.log('上传成功', e);
    },

    // 直接上传照片到阿里云OSS
    uploadPhotoToOSS(file) {
      try {
        const token = uni.getStorageSync('token');
        if (!token) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }
        
        console.log('准备上传文件:', file.path);
        
        // 显示上传中
        uni.showLoading({
          title: '上传中...',
          mask: true
        });
        
        // 使用后端接口上传图片
        const uploadTask = uni.uploadFile({
          url: app.baseUrl + '/api/oss/upload/image',
          filePath: file.path,
          name: 'file',
          formData: {
            folder: 'member/photos' // 指定存储文件夹
          },
          header: {
            'Authorization': token
          },
          success: (uploadRes) => {
            console.log('上传结果原始数据:', uploadRes);
            
            try {
              const response = JSON.parse(uploadRes.data);
              console.log('解析后的响应数据:', response);
              
              if (uploadRes.statusCode === 200) {
                console.log('HTTP状态码正确');
                if (response.code === 200) {
                  console.log('业务状态码正确');
                  // 获取后端返回的文件URL
                  let fileUrl = response.message;
                  console.log('返回的URL:', fileUrl);
                  
                  // 检查URL是否为null或空
                  if (!fileUrl) {
                    console.warn('服务器返回的URL为空，使用本地路径');
                    // 使用本地文件路径替代
                    fileUrl = file.path;
                    
                    uni.showToast({
                      title: '使用本地预览',
                      icon: 'none'
                    });
                  } else {
                    // 确保URL是完整的
                    if (!fileUrl.startsWith('http')) {
                      fileUrl = 'https:' + fileUrl; // 如果是相对URL，添加协议
                    }
                    console.log('处理后的URL:', fileUrl);
                  }
                  
                  console.log('最终图片URL:', fileUrl);
                  
                  // 添加到照片列表
                  this.photos = [...this.photos, fileUrl];
                  
                  // 更新进度
                  const progress = ((this.currentUploadIndex + 1) / this.totalToUpload) * 100;
                  this.uploadProgressPercent = Math.floor(progress);
                } else {
                  console.error('业务状态码错误:', response.code);
                  // 使用本地文件路径作为临时URL
                  const fileUrl = file.path;
                  console.log('使用本地文件路径:', fileUrl);
                  this.photos = [...this.photos, fileUrl];
                  
                  uni.showToast({
                    title: response?.msg || '上传失败，使用本地预览',
                    icon: 'none'
                  });
                }
              } else {
                console.error('HTTP状态码错误:', uploadRes.statusCode);
                // 使用本地文件路径作为临时URL
                const fileUrl = file.path;
                console.log('使用本地文件路径:', fileUrl);
                this.photos = [...this.photos, fileUrl];
                
                uni.showToast({
                  title: '网络错误，使用本地预览',
                  icon: 'none'
                });
              }
            } catch (error) {
              console.error('解析响应数据失败:', error, uploadRes.data);
              // 使用本地文件路径作为临时URL
              const fileUrl = file.path;
              console.log('解析错误，使用本地文件路径:', fileUrl);
              this.photos = [...this.photos, fileUrl];
              
              uni.showToast({
                title: '处理响应数据失败',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            console.error('上传照片失败', err);
            // 使用本地文件路径作为临时URL
            const fileUrl = file.path;
            console.log('上传失败，使用本地文件路径:', fileUrl);
            this.photos = [...this.photos, fileUrl];
            
            uni.showToast({
              title: '上传失败，使用本地预览',
              icon: 'none'
            });
          },
          complete: () => {
            uni.hideLoading();
            // 继续上传下一张
            this.currentUploadIndex++;
            this.uploadNextPhoto();
          }
        });
        
        // 监听上传进度
        uploadTask.onProgressUpdate((res) => {
          this.uploadProgress(res);
        });
      } catch (error) {
        console.error('上传照片错误', error);
        uni.hideLoading();
        uni.showToast({
          title: '上传出错，请重试',
          icon: 'none'
        });
        
        // 出错时也要继续下一张
        this.currentUploadIndex++;
        this.uploadNextPhoto();
      }
    },
    
    // 生成OSS文件名
    generateOSSFileName(filePath) {
      const ext = filePath.substring(filePath.lastIndexOf('.'));
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 10);
      return `${timestamp}_${randomString}${ext}`;
    },
    
    // 原有的选择照片方法，改为调用新的上传逻辑
    handlePhotoClick() {
      const remainCount = 9 - this.photos.length;
      if (remainCount <= 0) {
        uni.showToast({
          title: "最多上传9张照片",
          icon: "none",
        });
        return;
      }

      uni.chooseImage({
        count: remainCount,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          // 将选择的照片添加到上传队列
          const tempFiles = res.tempFilePaths.map(path => ({
            path: path
          }));
          
          this.uploadQueue = [...tempFiles];
          this.totalToUpload = tempFiles.length;
          this.currentUploadIndex = 0;
          this.uploading = true;
          
          // 先获取OSS凭证再开始上传
          this.getOSSCredentials();
        },
      });
    },

    deletePhoto(e) {
      const index = e.currentTarget.dataset.index;
      const photos = [...this.photos];
      photos.splice(index, 1);
      this.photos = photos;
      
      // 如果有组件实例，重置
      if (this.$refs.photoPicker) {
        this.$refs.photoPicker.clearFiles();
      }
    },

    savePhotos() {
      if (this.uploading) {
        uni.showToast({
          title: '正在上传中，请稍候',
          icon: 'none'
        });
        return;
      }
      
      // 调用goBack方法，它会处理自动保存和返回逻辑
      this.goBack();
    },

    // 返回上一页
    async goBack() {
      // 如果有上传的新照片，自动保存
      if (this.photos.length > 0) {
        try {
          const token = uni.getStorageSync('token');
          if (token) {
            // 显示加载提示
            uni.showLoading({ title: '保存中...' });
            
            // 确保所有URL都是完整的
            const validPhotos = this.photos.map(url => {
              if (url && !url.startsWith('http')) {
                return 'https:' + url;
              }
              return url;
            });
            
            console.log('保存到后端的照片URLs:', validPhotos);
            
            // 调用接口保存照片
            const res = await this.$requestHttp.put(app.commonApi.updateMemberInfo, {
              data: {
                images: validPhotos.join(',')
              }
            });
            
            uni.hideLoading();
            
            if (res && res.code === 200) {
              uni.showToast({ title: '照片已保存', icon: 'success' });
            } else {
              uni.showToast({ title: res?.msg || '保存失败', icon: 'none' });
            }
          }
        } catch (error) {
          console.error('自动保存照片失败', error);
          uni.hideLoading();
          uni.showToast({ title: '保存失败，请重试', icon: 'none' });
        }
      }
      
      // 更新上一页数据并返回
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (prevPage) {
        prevPage.photos = this.photos;
        if (prevPage.updateCompletion) {
          prevPage.updateCompletion();
        }
      }

      uni.navigateBack();
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.photos-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.photo-item {
  width: calc((100% - 40rpx) / 3);
  aspect-ratio: 9/16;
  background: #f7f7f7;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.photo-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.upload-container {
  margin-bottom: 30rpx;
}

.limit-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 20rpx 0;
}

.progress-container {
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.save-btn-wrap {
  margin-top: auto;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn[disabled] {
  opacity: 0.6;
}

.delete-btn {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}
</style>
