package com.play.xianshibusiness.service;

import com.play.xianshibusiness.dto.config.DataDictValueDTO;
import com.play.xianshibusiness.pojo.TDataDictValue;

import java.util.List;
import java.util.Map;

/**
 * 数据字典值服务接口
 */
public interface TDataDictValueService {
    
    /**
     * 根据类型编码获取字典值列表
     *
     * @param typeCode 类型编码
     * @return 字典值列表
     */
    List<TDataDictValue> getDictValuesByTypeCode(String typeCode);
    
    /**
     * 获取字典值详情
     *
     * @param id 字典值ID
     * @return 字典值详情
     */
    TDataDictValue getDictValueDetail(String id);
    
    /**
     * 创建字典值
     *
     * @param valueDTO 字典值DTO
     * @return 字典值ID
     */
    String createDictValue(DataDictValueDTO valueDTO);
    
    /**
     * 更新字典值
     *
     * @param id 字典值ID
     * @param valueDTO 字典值DTO
     * @return 是否成功
     */
    Boolean updateDictValue(String id, DataDictValueDTO valueDTO);
    
    /**
     * 删除字典值
     *
     * @param id 字典值ID
     * @return 是否成功
     */
    Boolean deleteDictValue(String id);
    
    /**
     * 批量更新排序
     *
     * @param sortMap ID与排序值的映射
     * @return 是否成功
     */
    Boolean batchUpdateSort(Map<String, Integer> sortMap);
}

