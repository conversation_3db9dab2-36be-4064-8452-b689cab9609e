package com.play.xianshibusiness.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单视图对象
 */
@Data
@ApiModel(value = "订单视图对象")
public class OrderVO {
    
    @ApiModelProperty(value = "订单ID")
    private String orderId;
    
    @ApiModelProperty(value = "订单标题")
    private String title;
    
    @ApiModelProperty(value = "陪玩类型名称")
    private String typeName;
    
    @ApiModelProperty(value = "陪玩类型ID")
    private String typeId;
    
    @ApiModelProperty(value = "用户ID")
    private String memberId;
    
    @ApiModelProperty(value = "用户昵称")
    private String memberName;
    
    @ApiModelProperty(value = "用户头像")
    private String memberAvatar;
    
    @ApiModelProperty(value = "陪玩时长(分钟)")
    private Integer duration;
    
    @ApiModelProperty(value = "小时费用")
    private BigDecimal hourlyRate;
    
    @ApiModelProperty(value = "总费用")
    private BigDecimal totalFee;
    
    @ApiModelProperty(value = "性别要求：0-不限，1-男，2-女")
    private String sexRequire;
    
    @ApiModelProperty(value = "参与人数")
    private Integer personCount;
    
    @ApiModelProperty(value = "活动地点")
    private String location;
    
    @ApiModelProperty(value = "活动地址详情")
    private String address;
    
    @ApiModelProperty(value = "已报名人数")
    private Integer enrollCount;
    
    @ApiModelProperty(value = "订单状态：0-待审核，1-报名中，2-待选择达人，3-进行中，4-已完成，5-已取消")
    private Integer status;
    
    @ApiModelProperty(value = "状态描述")
    private String statusDesc;
    
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "是否为即时订单（开始时间在24小时内）")
    private Boolean isInstant;
}