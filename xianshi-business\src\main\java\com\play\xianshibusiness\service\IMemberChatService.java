package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.mapper.IMemberChatMapper;
import com.play.xianshibusiness.pojo.IMemberChat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * (IMemberChat)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:18
 */
@Service
@Slf4j
public class IMemberChatService {
    
    @Resource
    private IMemberChatMapper iMemberChatMapper;
    
    /**
     * 发送聊天消息
     * @param memberId 发送者ID
     * @param targetId 接收者ID
     * @param content 消息内容（JSON格式，包含消息类型、文本内容、图片URL等）
     * @return 消息ID
     */
    @Transactional
    public String sendChatMessage(String memberId, String targetId, String content) {
        IMemberChat chat = new IMemberChat();
        chat.setMemberId(memberId);
        chat.setTargetId(targetId);
        chat.setContentJson(content);
        
        iMemberChatMapper.insert(chat);
        return chat.getId();
    }
    
    /**
     * 获取与指定用户的聊天记录
     * @param memberId 当前用户ID
     * @param targetId 聊天对象ID
     * @return 聊天记录列表
     */
    public List<IMemberChat> getChatHistory(String memberId, String targetId) {
        LambdaQueryWrapper<IMemberChat> queryWrapper = new LambdaQueryWrapper<>();
        
        // 双向查询：查询自己发送的和接收到的消息
//        queryWrapper.and(wrapper ->
//            // 自己发送的消息
//            wrapper.and(w -> w.eq(IMemberChat::getMemberId, memberId).eq(IMemberChat::getTargetId, targetId))
//                   .or()
//                   // 接收到的消息
//                   .and(w -> w.eq(IMemberChat::getMemberId, targetId).eq(IMemberChat::getTargetId, memberId))
//        );

        queryWrapper
                .nested(wrapper ->
                        wrapper
                                .and(w -> w.eq(IMemberChat::getMemberId, memberId)
                                        .eq(IMemberChat::getTargetId, targetId))
                                .or(w -> w.eq(IMemberChat::getMemberId, targetId)
                                        .eq(IMemberChat::getTargetId, memberId))
                )
                .eq(IMemberChat::getDeleted, false); // 加上deleted = false条件


        // 按创建时间排序
        queryWrapper.orderByAsc(IMemberChat::getCreateTime);
        
        return iMemberChatMapper.selectList(queryWrapper);
    }
    
    /**
     * 获取聊天列表（会话列表）
     * @param memberId 当前用户ID
     * @return 聊天会话列表
     */
    public List<String> getChatList(String memberId) {
        // 这里需要自定义SQL，查询与memberId有聊天记录的所有用户ID
        return iMemberChatMapper.getChatList(memberId);
    }
    
    /**
     * 删除聊天记录
     * @param memberId 当前用户ID
     * @param targetId 聊天对象ID
     * @return 删除结果
     */
    @Transactional
    public boolean deleteChatHistory(String memberId, String targetId) {
        LambdaQueryWrapper<IMemberChat> queryWrapper = new LambdaQueryWrapper<>();
        
        // 删除双向聊天记录
        queryWrapper.and(wrapper -> 
            // 自己发送的消息
            wrapper.and(w -> w.eq(IMemberChat::getMemberId, memberId).eq(IMemberChat::getTargetId, targetId))
                   .or()
                   // 接收到的消息
                   .and(w -> w.eq(IMemberChat::getMemberId, targetId).eq(IMemberChat::getTargetId, memberId))
        );
        
        // 只删除未被软删除的记录
        queryWrapper.eq(IMemberChat::getDeleted, false);
        
        int deletedCount = iMemberChatMapper.delete(queryWrapper);
        log.info("删除聊天记录，用户: {}, 目标: {}, 删除数量: {}", memberId, targetId, deletedCount);
        
        return deletedCount > 0;
    }
    
    /**
     * 获取未读消息数
     * @param memberId 当前用户ID
     * @return 未读消息数
     */
    public Integer getUnreadCount(String memberId) {
        // 这里可以通过WebSocket会话管理或添加消息状态字段来实现
        // 临时实现：返回0，实际需要根据业务逻辑完善
        return 0;
    }


}

