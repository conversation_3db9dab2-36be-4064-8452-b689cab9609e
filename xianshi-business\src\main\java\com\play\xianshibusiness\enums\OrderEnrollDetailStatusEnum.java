package com.play.xianshibusiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 子订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum OrderEnrollDetailStatusEnum {

    ENROLLED("ENROLLED", "达人已报名"),
    SELECTED("SELECTED", "被选中"),
    DEPARTED("DEPARTED", "已出发"),
    ARRIVED("ARRIVED", "已到达"),
    SERVICING("SERVICING", "服务中"),
    SERVICE_COMPLETED("SERVICE_COMPLETED", "服务完成"),
    EVALUATED("EVALUATED", "已评价"),
    CANCELED("CANCELED", "已取消");

    private final String code;
    private final String desc;
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果找不到返回null
     */
    public static OrderEnrollDetailStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        switch (code) {
            case 0:
                return ENROLLED;
            case 1:
                return SELECTED;
            case 2:
                return DEPARTED;
            case 3:
                return ARRIVED;
            case 4:
                return SERVICING;
            case 5:
                return SERVICE_COMPLETED;
            case 6:
                return EVALUATED;
            case 7:
                return CANCELED;
            default:
                return null;
        }
    }
} 