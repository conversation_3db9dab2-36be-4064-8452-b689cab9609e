package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.config.DataDictQueryDTO;
import com.play.xianshibusiness.dto.config.DataDictTypeDTO;
import com.play.xianshibusiness.pojo.TDataDictType;

/**
 * 数据字典类型服务接口
 */
public interface TDataDictTypeService {
    
    /**
     * 分页查询数据字典类型
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<TDataDictType> pageDictTypes(DataDictQueryDTO queryDTO);
    
    /**
     * 获取数据字典类型详情
     *
     * @param id 类型ID
     * @return 类型详情
     */
    TDataDictType getDictTypeDetail(String id);
    
    /**
     * 创建数据字典类型
     *
     * @param typeDTO 类型DTO
     * @return 类型ID
     */
    String createDictType(DataDictTypeDTO typeDTO);
    
    /**
     * 更新数据字典类型
     *
     * @param id 类型ID
     * @param typeDTO 类型DTO
     * @return 是否成功
     */
    Boolean updateDictType(String id, DataDictTypeDTO typeDTO);
    
    /**
     * 删除数据字典类型
     *
     * @param id 类型ID
     * @return 是否成功
     */
    Boolean deleteDictType(String id);
}

