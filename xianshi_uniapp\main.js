import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false;


import requestHttp from '@/api/index'
Vue.prototype.$requestHttp = requestHttp;

App.mpType = 'app'
const app = new Vue({
  ...App
})
console.log("app||", app);
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import requestHttp from '@/api/index'
import Tabbar from './custom-tab-bar/index.vue'

export function createApp() {
  const app = createSSRApp(App)
  app.component('Tabbar', Tabbar) // 全局注册TabBar组件
  app.config.globalProperties.$requestHttp = requestHttp;
  return {
    app
  }
}
// #endif