<template>
	<view class="chat-list-container">
		<!-- 导航栏 -->
		<view class="nav-bar">
			<text class="nav-title">聊天</text>
		</view>
		
		<!-- 聊天列表 -->
		<view class="chat-list">
			<view 
				v-for="chat in chatList" 
				:key="chat.userId"
				class="chat-item"
				@click="openChat(chat)"
			>
				<view class="chat-avatar">
					<image :src="chat.avatar || '/static/images/default-avatar.png'" mode="aspectFill" />
					<view v-if="chat.unreadCount > 0" class="unread-badge">{{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}</view>
				</view>
				<view class="chat-content">
					<view class="chat-header">
						<text class="chat-name">{{ chat.name }}</text>
						<text class="chat-time">{{ formatTime(chat.lastMessageTime) }}</text>
					</view>
					<view class="chat-message">
						<text class="message-preview" :class="{ 'unread': chat.unreadCount > 0 }">{{ chat.lastMessage }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view v-if="chatList.length === 0" class="empty-state">
			<text class="empty-text">暂无聊天记录</text>
		</view>
	</view>
</template>

<script>
import { getChatList, getCurrentMemberInfo } from '@/api/index.js';
import WebSocketClient from '@/utils/websocket-client.js';

export default {
	data() {
		return {
			chatList: [],
			webSocketClient: null,
			currentUserId: ''
		}
	},
	
	onLoad() {
		this.initData();
		this.initWebSocket();
	},
	
	onShow() {
		this.loadChatList();
	},
	
	onUnload() {
		if (this.webSocketClient) {
			this.webSocketClient.disconnect();
		}
	},
	
	methods: {
		async initData() {
			try {
				const userInfo = await getCurrentMemberInfo();
				if (userInfo.status === 1) {
					this.currentUserId = userInfo.data.id;
				}
			} catch (error) {
				console.error('获取用户信息失败:', error);
			}
		},
		
		async loadChatList() {
			try {
				const response = await getChatList();
				if (response.status === 1 && Array.isArray(response.data)) {
					// 获取聊天对象的详细信息
					this.chatList = await Promise.all(
						response.data.map(async (userId) => {
							// 这里需要调用获取用户信息的API
							return {
								userId: userId,
								name: '用户' + userId.substr(-4), // 临时显示
								avatar: '/static/images/default-avatar.png',
								lastMessage: '点击开始聊天',
								lastMessageTime: new Date(),
								unreadCount: 0
							};
						})
					);
				}
			} catch (error) {
				console.error('获取聊天列表失败:', error);
			}
		},
		
		initWebSocket() {
			const token = uni.getStorageSync('token');
			if (!token || !this.currentUserId) return;
			
			this.webSocketClient = new WebSocketClient({
				token: token,
				userId: this.currentUserId,
				onMessage: (message) => {
					this.handleWebSocketMessage(message);
				}
			});
			
			this.webSocketClient.connect();
		},
		
		handleWebSocketMessage(message) {
			if (message.type === 'CHAT') {
				// 更新聊天列表中的最新消息
				const chatIndex = this.chatList.findIndex(chat => chat.userId === message.fromUserId);
				if (chatIndex !== -1) {
					this.chatList[chatIndex].lastMessage = message.content;
					this.chatList[chatIndex].lastMessageTime = new Date(message.timestamp);
					this.chatList[chatIndex].unreadCount++;
					
					// 将该聊天移到列表顶部
					const chat = this.chatList.splice(chatIndex, 1)[0];
					this.chatList.unshift(chat);
				} else {
					// 新的聊天会话
					this.chatList.unshift({
						userId: message.fromUserId,
						name: message.fromUserName || '用户' + message.fromUserId.substr(-4),
						avatar: '/static/images/default-avatar.png',
						lastMessage: message.content,
						lastMessageTime: new Date(message.timestamp),
						unreadCount: 1
					});
				}
			}
		},
		
		openChat(chat) {
			// 清除未读数
			chat.unreadCount = 0;
			
			// 跳转到聊天详情页
			uni.navigateTo({
				url: `/pages/chat/chat-detail?id=${chat.userId}&name=${chat.name}`
			});
		},
		
		formatTime(time) {
			const now = new Date();
			const messageTime = new Date(time);
			const diff = now - messageTime;
			
			if (diff < 60000) { // 1分钟内
				return '刚刚';
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前';
			} else if (messageTime.toDateString() === now.toDateString()) { // 今天
				return messageTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
			} else { // 其他
				return messageTime.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
			}
		}
	}
}
</script>

<style scoped>
.chat-list-container {
	height: 100vh;
	background-color: #f5f5f5;
}

.nav-bar {
	height: 88rpx;
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 1rpx solid #eee;
}

.nav-title {
	font-size: 32rpx;
	font-weight: 600;
}

.chat-list {
	background: #fff;
}

.chat-item {
	display: flex;
	align-items: center;
	padding: 24rpx 30rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.chat-avatar {
	position: relative;
	margin-right: 24rpx;
}

.chat-avatar image {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
}

.unread-badge {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	background: #ff4444;
	color: #fff;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 20rpx;
	min-width: 32rpx;
	text-align: center;
}

.chat-content {
	flex: 1;
}

.chat-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.chat-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.chat-time {
	font-size: 24rpx;
	color: #999;
}

.message-preview {
	font-size: 26rpx;
	color: #666;
}

.message-preview.unread {
	color: #333;
	font-weight: 500;
}

.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}
</style>
