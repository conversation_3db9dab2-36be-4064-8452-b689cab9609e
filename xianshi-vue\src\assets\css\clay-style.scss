/* Clay风格全局样式 */

:root {
  --primary-color: #F8D010;
  --primary-light: #F8E068;
  --dark-color: #303030;
  --gray-color: #909090;
  --light-gray: #f8f8f8;
  --text-primary: #303030;
  --text-secondary: #909090;
  --border-radius: 12px;
  --box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Clay卡片样式 */
.clay-card {
  background: #ffffff;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
  border: none !important;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-3px) scale(1.01);
    box-shadow: var(--box-shadow);
  }
}

/* Clay按钮样式 */
.clay-button {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: var(--dark-color) !important;
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 20px;
  transition: var(--transition);
  
  &:hover, &:focus {
    background-color: var(--primary-light) !important;
    border-color: var(--primary-light) !important;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 5px 15px rgba(248, 208, 16, 0.3);
  }
  
  &:active {
    transform: scale(0.98);
  }
}

/* Clay标题样式 */
.clay-title {
  color: var(--dark-color);
  font-weight: 600;
  position: relative;
  margin-bottom: 25px;
  
  &:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
  }
}

/* 输入框样式增强 */
.el-input {
  .el-input__inner {
    border-radius: 8px;
    padding: 12px 15px;
    height: 48px;
    transition: var(--transition);
    
    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(248, 208, 16, 0.1);
      transform: scale(1.005);
    }
  }
}

/* 表格样式增强 */
.el-table {
  border-radius: var(--border-radius);
  overflow: hidden;
  
  th {
    background-color: var(--light-gray) !important;
    color: var(--dark-color);
    font-weight: 600;
    padding: 12px 0;
  }
  
  td {
    padding: 12px 0;
  }
  
  .el-table__row {
    transition: var(--transition);
    
    &:hover {
      background-color: rgba(248, 208, 16, 0.05) !important;
      transform: translateY(-1px);
    }
  }
}

/* 分页样式增强 */
.el-pagination {
  .el-pagination__total,
  .el-pagination__jump {
    font-size: 14px;
  }
  
  .btn-prev,
  .btn-next {
    background-color: var(--light-gray);
    border-radius: 4px;
    transition: var(--transition);
    
    &:hover {
      background-color: rgba(248, 208, 16, 0.2);
      transform: scale(1.03);
    }
  }
  
  .el-pager li {
    border-radius: 4px;
    transition: var(--transition);
    
    &:hover {
      transform: scale(1.05);
    }
    
    &.active {
      background-color: var(--primary-color);
      color: var(--dark-color);
      font-weight: 600;
    }
  }
}

/* 表单样式增强 */
.el-form-item {
  margin-bottom: 25px;
  
  .el-form-item__label {
    font-weight: 500;
    color: var(--text-primary);
  }
}

/* 下拉菜单样式增强 */
.el-dropdown-menu {
  border-radius: 8px;
  padding: 8px 0;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  
  .el-dropdown-menu__item {
    padding: 10px 20px;
    transition: var(--transition);
    
    &:hover {
      background-color: rgba(248, 208, 16, 0.1);
      transform: translateX(3px);
    }
  }
}

/* 弹窗样式增强 */
.el-dialog {
  border-radius: var(--border-radius);
  overflow: hidden;
  
  .el-dialog__header {
    padding: 20px;
    background-color: var(--light-gray);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    
    .el-dialog__title {
      color: var(--dark-color);
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 30px 20px;
  }
  
  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }
}

/* 标签样式增强 */
.el-tag {
  border-radius: 4px;
  padding: 6px 10px;
  transition: var(--transition);
  
  &:hover {
    transform: scale(1.03);
  }
}

/* 卡通风格动画 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* 文字溢出处理 */
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 特定组件不需要悬停效果 */
.login-form-container:hover,
.navbar.clay-card:hover {
  transform: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
} 