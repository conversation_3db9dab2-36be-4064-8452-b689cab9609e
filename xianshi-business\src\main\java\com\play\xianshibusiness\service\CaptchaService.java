package com.play.xianshibusiness.service;

import cn.zhxu.toys.captcha.CaptchaException;
import cn.zhxu.toys.captcha.MsgCaptchaManager;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.play.xianshibusiness.enums.ResultCode;
import com.play.xianshibusiness.exception.GlobalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

//* @Description: 验证码服务
@Service
@Slf4j
public class CaptchaService {
    @Resource
    @Lazy
    private MsgCaptchaManager msgCaptchaManager;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // 阿里云短信配置
    @Value("${aliyun.sms.access-key-id:LTAI5tEeaW8Hw5j43v9siDS3}")
    private String accessKeyId;

    @Value("${aliyun.sms.access-key-secret:******************************}")
    private String accessKeySecret;

    @Value("${aliyun.sms.sign-name:威客字节}")
    private String signName;

    // 模板编码映射
    private static final Map<String, String> TEMPLATE_CODES = new ConcurrentHashMap<>();

    static {
        // 初始化短信模板编码
        TEMPLATE_CODES.put("LOGIN", "SMS_225366344"); // 登录验证码模板
        TEMPLATE_CODES.put("REGISTER", "SMS_123456788"); // 注册验证码模板
        TEMPLATE_CODES.put("RESET_PASSWORD", "SMS_123456787"); // 重置密码验证码模板
    }

    // Redis key 前缀，用于存储手机验证码
    private static final String SMS_CODE_PREFIX = "sms:code:";
    // 验证码有效期（分钟）
    private static final int CODE_EXPIRE_MINUTES = 5;
    // 同一手机号发送间隔（秒）
    private static final int SEND_INTERVAL_SECONDS = 60;

    /**
     * 验证手机验证码
     *
     * @param phone 手机号码
     * @param code  验证码
     * @throws GlobalException 如果验证码验证失败，则抛出全局异常
     */
    public void verifyPhoneCode(String phone, String code) {
        String key = SMS_CODE_PREFIX + phone;
        String savedCode = stringRedisTemplate.opsForValue().get(key);

        // 开发环境：支持万能验证码和实际验证码
        boolean isTestCode = "123456".equals(code);
        boolean isValidCode = savedCode != null && savedCode.equals(code);

        if (isTestCode || isValidCode) {
            // 验证成功，删除验证码防止重复使用（测试验证码除外）
            if (isValidCode) {
                stringRedisTemplate.delete(key);
            }
            log.info("手机验证码验证成功！手机: {}, 使用{}验证码", phone, isTestCode ? "测试" : "实际");
        } else {
            log.error("手机验证码验证失败！手机: {}, 输入验证码: {}, 实际验证码: {}", phone, code, savedCode);
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "验证码错误或已过期");
        }
    }

    /**
     * 生成并发送手机验证码
     *
     * @param phone   手机号码
     * @param purpose 用途，如LOGIN、REGISTER、RESET_PASSWORD等
     * @return 验证码发送成功后的提示信息
     * @throws GlobalException 如果验证码发送失败，则抛出全局异常
     */
    public String phoneCaptchaCode(String phone, String purpose) {
        // 检查是否可以发送验证码（防止频繁发送）
        String lockKey = SMS_CODE_PREFIX + "lock:" + phone;
        Boolean notExist = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", SEND_INTERVAL_SECONDS,
                TimeUnit.SECONDS);

        if (Boolean.FALSE.equals(notExist)) {
            // 发送过于频繁
            long ttl = stringRedisTemplate.getExpire(lockKey, TimeUnit.SECONDS);
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "验证码发送过于频繁，请" + ttl + "秒后再试");
        }

        try {
            // 生成6位随机验证码
            String code = generateRandomCode(6);

            // 获取对应用途的短信模板
            String templateCode = TEMPLATE_CODES.getOrDefault(purpose, TEMPLATE_CODES.get("LOGIN"));

            // 在实际环境中，这里会调用阿里云短信服务发送验证码
            sendSmsViaAliyun(phone, code, templateCode);
            log.info("向手机号 {} 发送验证码: {}, 用途: {}, 模板: {}", phone, code, purpose, templateCode);

            // 模拟发送短信，将验证码存入Redis
            String codeKey = SMS_CODE_PREFIX + phone;
            stringRedisTemplate.opsForValue().set(codeKey, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

            return "验证码已经发送至您的手机，" + CODE_EXPIRE_MINUTES + "分钟内有效";
        } catch (Exception e) {
            // 发送失败时删除锁，允许重试
            stringRedisTemplate.delete(lockKey);
            log.error("短信验证码发送失败！" + phone, e);
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "短信验证码发送失败！");
        }
    }

    /**
     * 通过阿里云发送短信
     * 此方法保留为接口，由您自行补充实现
     *
     * @param phone        手机号
     * @param code         验证码
     * @param templateCode 短信模板编码
     */
    private void sendSmsViaAliyun(String phone, String code, String templateCode) {
        // 阿里云短信发送逻辑
        // 此处保留为接口，由您自行根据阿里云文档补充实现
        // 参考：https://help.aliyun.com/document_detail/101414.html

        try {
            // 创建DefaultAcsClient实例并初始化
            DefaultProfile profile = DefaultProfile.getProfile(
                    "cn-hangzhou", // 地域ID
                    accessKeyId, // RAM账号的AccessKey ID
                    accessKeySecret // RAM账号AccessKey Secret
            );
            IAcsClient client = new DefaultAcsClient(profile);

            // 创建API请求并设置参数
            CommonRequest request = new CommonRequest();
            request.setSysMethod(MethodType.POST);
            request.setSysDomain("dysmsapi.aliyuncs.com");
            request.setSysVersion("2017-05-25");
            request.setSysAction("SendSms");

            // 设置发送短信的参数
            request.putQueryParameter("PhoneNumbers", phone);
            request.putQueryParameter("SignName", signName);
            request.putQueryParameter("TemplateCode", templateCode);
            request.putQueryParameter("TemplateParam", "{\"code\":\"" + code + "\"}");

            // 发起API请求并处理响应
            CommonResponse response = client.getCommonResponse(request);
            if (response.getHttpStatus() == 200) {
                log.info("短信发送成功: {}", response.getData());
            } else {
                log.error("短信发送失败: {}", response.getData());
                throw new GlobalException(ResultCode.PROGRAM_ERROR, "短信发送失败");
            }
        } catch (Exception e) {
            log.error("发送短信时发生异常", e);
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "短信发送异常");
        }
    }

    /**
     * 生成指定长度的随机数字验证码
     *
     * @param length 验证码长度
     * @return 随机数字验证码
     */
    private String generateRandomCode(int length) {
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }
}
