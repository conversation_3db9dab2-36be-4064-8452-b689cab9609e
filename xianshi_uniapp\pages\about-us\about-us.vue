<template>
  <view class="container">
    <!-- 应用信息 -->
    <view class="app-info">
      <image class="app-logo" src="/static/images/default-avatar.png" mode="aspectFit"></image>
      <text class="app-name">闲时</text>
      <text class="app-version">当前版本 v4.3.3</text>
    </view>
    
    <!-- 信息列表 -->
    <view class="info-list">
      <view class="info-item" @tap="handleTap('联系客服')">
        <text class="item-name">联系客服</text>
        <text class="item-arrow">></text>
      </view>
      <view class="info-item" @tap="handleTap('关注微博')">
        <text class="item-name">关注微博</text>
        <text class="item-arrow">></text>
      </view>
      <view class="info-item" @tap="handleTap('关注公众号')">
        <text class="item-name">关注公众号</text>
        <text class="item-arrow">></text>
      </view>
    </view>
    
    <!-- 公司信息 -->
    <view class="company-info">
      <text>© 2023 闲时科技（北京）有限公司</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },
  methods: {
    handleTap(item) {
      uni.showToast({
        title: item + '功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style>
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.app-info {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 60rpx 40rpx;
  margin-top: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.app-logo {
  width: 150rpx;
  height: 150rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

.app-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.app-version {
  color: #999;
  font-size: 26rpx;
}

.info-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.company-info {
  text-align: center;
  color: #999;
  font-size: 26rpx;
  padding: 30rpx 0;
}
</style> 