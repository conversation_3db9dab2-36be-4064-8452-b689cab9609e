package com.play.xianshibusiness.mapper;

import java.util.List;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.play.xianshibusiness.pojo.CMemberGoldRecord;
import org.apache.ibatis.annotations.Select;

/**
 * (CMemberGoldRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:10
 */
public interface CMemberGoldRecordMapper extends BaseMapper<CMemberGoldRecord> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<CMemberGoldRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<CMemberGoldRecord> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<CMemberGoldRecord> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<CMemberGoldRecord> entities);

    /**
     * 查询今日增加金币总量
     *
     * @param today 今天的起始时间
     * @return 金币总量
     */
    @Select("SELECT IFNULL(SUM(amount), 0) FROM c_member_gold_record WHERE create_time >= #{today} AND operation_type IN (1, 3, 5, 7, 8)")
    Integer selectTodayAddTotal(@Param("today") LocalDateTime today);

    /**
     * 查询今日减少金币总量
     *
     * @param today 今天的起始时间
     * @return 金币总量
     */
    @Select("SELECT IFNULL(SUM(amount), 0) FROM c_member_gold_record WHERE create_time >= #{today} AND operation_type IN (2, 4, 6)")
    Integer selectTodayReduceTotal(@Param("today") LocalDateTime today);

    /**
     * 查询系统金币总量
     *
     * @return 金币总量
     */
    @Select("SELECT IFNULL(SUM(after_balance), 0) FROM (SELECT member_id, MAX(create_time) AS latest_time FROM c_member_gold_record GROUP BY member_id) AS latest JOIN c_member_gold_record cgr ON latest.member_id = cgr.member_id AND latest.latest_time = cgr.create_time")
    Integer selectSystemTotal();

    /**
     * 查询某个操作类型的金币总量
     *
     * @param type 操作类型
     * @return 金币总量
     */
    @Select("SELECT IFNULL(SUM(amount), 0) FROM c_member_gold_record WHERE operation_type = #{type}")
    Integer selectTypeTotal(@Param("type") Integer type);

}

