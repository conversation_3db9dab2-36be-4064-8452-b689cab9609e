package com.play.xianshiapp.controller.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.order.*;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.BOrderCommentService;
import com.play.xianshibusiness.service.BOrderEnrollDetailService;
import com.play.xianshibusiness.service.BOrderService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 订单控制器
 */
@RestController
@RequestMapping("/api/order")
@Api(tags = "订单模块API")
public class OrderController {

    @Resource
    private BOrderService orderService;

    @Resource
    private BOrderEnrollDetailService orderEnrollDetailService;
    
    @Resource
    private BOrderCommentService orderCommentService;
    
    @PostMapping("/create")
    @ApiOperation(value = "创建订单（草稿状态）")
    @RequireLogin
    public Result<String> createOrder(@RequestBody @Valid OrderCreateDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.createOrder(dto, memberId));
    }

    @PostMapping("/submit/{orderId}")
    @ApiOperation(value = "提交审核")
    @RequireLogin
    public Result<Boolean> submitForAudit(@PathVariable String orderId) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.submitForAudit(orderId, memberId));
    }

    @PostMapping("/select-talents/{orderId}")
    @ApiOperation(value = "选择达人")
    @RequireLogin
    public Result<Boolean> selectTalents(
            @PathVariable String orderId, 
            @RequestBody @ApiParam(value = "达人ID列表", required = true) String[] memberIds) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.selectTalents(orderId, memberIds, memberId));
    }

    @GetMapping("/page")
    @ApiOperation(value = "分页查询订单（不需要登录，查看所有公开订单）")
    public Result<Page<OrderDetailVO>> pageOrder(OrderQueryDTO dto) {
        return ResultUtils.success(orderService.pageOrder(dto));
    }
    
    @GetMapping("/my-orders")
    @ApiOperation(value = "根据当前用户角色获取订单列表")
    @RequireLogin
    public Result<Page<OrderDetailVO>> pageOrderByRole(OrderQueryDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.pageOrderByRole(dto, memberId));
    }

    @GetMapping("/detail/{orderId}")
    @ApiOperation(value = "获取订单详情")
    public Result<OrderDetailVO> getOrderDetail(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId) {
        return ResultUtils.success(orderService.getOrderDetail(orderId));
    }

    @GetMapping("/enroll-details/{orderId}")
    @ApiOperation(value = "获取订单下的所有子订单")
    public Result<List<OrderEnrollDetailVO>> listByOrderId(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId) {
        return ResultUtils.success(orderEnrollDetailService.listByOrderId(orderId));
    }
    
    @PostMapping("/cancel/{orderId}")
    @ApiOperation(value = "用户取消订单")
    @RequireLogin
    public Result<Boolean> cancelOrder(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId,
            @ApiParam(value = "取消原因") @RequestParam(required = false) String reason) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.cancelOrder(orderId, memberId, reason));
    }
    
    @GetMapping("/statistics")
    @ApiOperation(value = "获取用户订单统计数据")
    @RequireLogin
    public Result<Map<String, Object>> getOrderStatistics() {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.getUserOrderStatistics(memberId));
    }
    
    @GetMapping("/comments/{orderId}")
    @ApiOperation(value = "获取订单评价")
    public Result<List<Map<String, Object>>> getOrderComments(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId) {
        return ResultUtils.success(orderCommentService.getOrderComments(orderId));
    }
}