package com.play.xianshiapp.controller.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.member.MemberViewHistoryDTO;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.MemberViewHistoryService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 会员浏览历史控制器
 */
@RestController
@RequestMapping("/app/history")
@Api(tags = "会员浏览历史-API")
public class MemberHistoryController {
    
    @Resource
    private MemberViewHistoryService memberViewHistoryService;
    
    /**
     * 获取浏览历史列表
     */
    @ApiOperation("获取浏览历史列表")
    @GetMapping("/list")
    @RequireLogin
    public Result<Page<MemberViewHistoryDTO>> getViewHistory(
            @ApiParam(value = "页码", defaultValue = "1") 
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", defaultValue = "10") 
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberViewHistoryService.getMemberViewHistory(memberId, pageNum, pageSize));
    }
    
    /**
     * 删除指定浏览历史
     */
    @ApiOperation("删除指定浏览历史")
    @PostMapping("/delete/{historyId}")
    @RequireLogin
    public Result<Boolean> deleteHistory(
            @ApiParam(value = "历史记录ID", required = true) 
            @PathVariable("historyId") String historyId) {
        
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberViewHistoryService.deleteViewHistory(memberId, historyId));
    }
    
    /**
     * 清空浏览历史
     */
    @ApiOperation("清空浏览历史")
    @PostMapping("/clear")
    @RequireLogin
    public Result<Boolean> clearAllHistory() {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberViewHistoryService.clearAllViewHistory(memberId));
    }
} 