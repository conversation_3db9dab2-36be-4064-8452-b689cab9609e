package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会员收藏实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_member_collect")
@ApiModel("会员收藏")
public class CMemberCollect extends BaseObjPo {
    
    @ApiModelProperty("会员ID")
    private String memberId;
    
    @ApiModelProperty("目标ID（可能是会员ID、订单ID等）")
    private String targetId;
    
    @ApiModelProperty("收藏类型")
    private String type;
    
    @ApiModelProperty("备注")
    private String remark;
}

