<template>
  <view class="container">
    <!-- 财务概览卡片 -->
    <view class="overview-card">
      <view class="total-revenue">
        <text class="label">本月收入(元)</text>
        <text class="value">{{ financeData.monthlyRevenue }}</text>
      </view>
      
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-value">{{ financeData.orderCount }}</text>
          <text class="stat-label">订单数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ financeData.pendingWithdrawal }}</text>
          <text class="stat-label">待提现(元)</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ financeData.totalRevenue }}</text>
          <text class="stat-label">总收入(元)</text>
        </view>
      </view>
      
      <view class="action-row">
        <button class="action-btn withdraw-btn" @tap="showWithdrawDialog">提现</button>
        <button class="action-btn detail-btn" @tap="showRevenueDetail">收入明细</button>
      </view>
    </view>
    
    <!-- 财务图表 -->
    <view class="chart-card">
      <view class="card-header">
        <text class="card-title">收入趋势</text>
        <view class="period-selector">
          <text 
            v-for="(period, index) in periods" 
            :key="index" 
            :class="['period-item', { active: currentPeriod === index }]"
            @tap="switchPeriod(index)"
          >
            {{ period }}
          </text>
        </view>
      </view>
      
      <view class="chart-placeholder">
        <!-- 实际项目中应引入图表组件 -->
        <text class="placeholder-text">此处显示收入趋势图表</text>
      </view>
    </view>
    
    <!-- 近期财务记录 -->
    <view class="records-card">
      <view class="card-header">
        <text class="card-title">近期财务记录</text>
        <text class="view-all" @tap="viewAllRecords">查看全部</text>
      </view>
      
      <view class="record-list">
        <view class="record-item" v-for="(record, index) in records" :key="index">
          <view class="record-left">
            <text class="record-title">{{ record.title }}</text>
            <text class="record-time">{{ record.time }}</text>
          </view>
          
          <view class="record-right">
            <text :class="['record-amount', record.type === 'income' ? 'income' : 'expense']">
              {{ record.type === 'income' ? '+' : '-' }}{{ record.amount }}
            </text>
            <text class="record-status" v-if="record.status">{{ record.status }}</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="records.length === 0">
        <image class="empty-icon" src="/static/images/empty-finance.png"></image>
        <text class="empty-text">暂无财务记录</text>
      </view>
    </view>
    
    <!-- 收益分配规则卡片 -->
    <view class="rules-card">
      <view class="card-header">
        <text class="card-title">收益分配规则</text>
        <text class="edit-rules" @tap="editRules">编辑</text>
      </view>
      
      <view class="rule-item">
        <text class="rule-title">公会抽成比例</text>
        <text class="rule-value">{{ rules.guildCommission }}%</text>
      </view>
      
      <view class="rule-item">
        <text class="rule-title">达人收益比例</text>
        <text class="rule-value">{{ rules.talentCommission }}%</text>
      </view>
      
      <view class="rule-item">
        <text class="rule-title">平台服务费</text>
        <text class="rule-value">{{ rules.platformFee }}%</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 财务数据
      financeData: {
        monthlyRevenue: '8,560.00',
        orderCount: 42,
        pendingWithdrawal: '6,320.00',
        totalRevenue: '53,890.00'
      },
      
      // 时间周期
      periods: ['周', '月', '季', '年'],
      currentPeriod: 1,
      
      // 财务记录
      records: [
        {
          title: '订单收入 - 篮球陪练',
          time: '2023-07-28 14:30',
          amount: '320.00',
          type: 'income'
        },
        {
          title: '提现到微信钱包',
          time: '2023-07-27 10:15',
          amount: '1,000.00',
          type: 'expense',
          status: '已完成'
        },
        {
          title: '订单收入 - KTV欢唱',
          time: '2023-07-26 18:45',
          amount: '450.00',
          type: 'income'
        },
        {
          title: '提现到银行卡',
          time: '2023-07-25 09:20',
          amount: '2,000.00',
          type: 'expense',
          status: '处理中'
        }
      ],
      
      // 收益分配规则
      rules: {
        guildCommission: 20,
        talentCommission: 70,
        platformFee: 10
      }
    };
  },
  methods: {
    switchPeriod(index) {
      this.currentPeriod = index;
      // TODO: 根据时间周期更新图表数据
    },
    
    showWithdrawDialog() {
      uni.navigateTo({
        url: '/pages/guild-finance/withdraw'
      });
    },
    
    showRevenueDetail() {
      uni.navigateTo({
        url: '/pages/guild-finance/revenue-detail'
      });
    },
    
    viewAllRecords() {
      uni.navigateTo({
        url: '/pages/guild-finance/all-records'
      });
    },
    
    editRules() {
      uni.navigateTo({
        url: '/pages/guild-finance/commission-rules'
      });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

/* 卡片通用样式 */
.overview-card,
.chart-card,
.records-card,
.rules-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 财务概览卡片 */
.overview-card {
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  color: #ffffff;
}

.total-revenue {
  text-align: center;
  margin-bottom: 30rpx;
}

.total-revenue .label {
  font-size: 28rpx;
  opacity: 0.9;
}

.total-revenue .value {
  font-size: 60rpx;
  font-weight: bold;
  margin-top: 10rpx;
  display: block;
}

.stats-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

.action-row {
  display: flex;
  justify-content: space-between;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.withdraw-btn {
  background-color: #ffffff;
  color: #ff8c00;
  margin-right: 15rpx;
}

.detail-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  margin-left: 15rpx;
}

/* 图表卡片 */
.period-selector {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  overflow: hidden;
}

.period-item {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #666;
}

.period-item.active {
  background-color: #ff8c00;
  color: #ffffff;
}

.chart-placeholder {
  height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

/* 财务记录 */
.view-all,
.edit-rules {
  font-size: 28rpx;
  color: #ff8c00;
}

.record-list {
  margin-top: 20rpx;
}

.record-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  text-align: right;
}

.income {
  color: #ff8c00;
}

.expense {
  color: #409eff;
}

.record-status {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  display: block;
  margin-top: 8rpx;
}

/* 收益规则 */
.rule-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-title {
  font-size: 28rpx;
  color: #333;
}

.rule-value {
  font-size: 28rpx;
  color: #ff8c00;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 