<template>
	<view class="container">
  <view class="header">
    <text class="title">实名认证</text>
    <text class="subtitle">请填写真实的个人信息</text>
  </view>

  <view class="form-box">
    <view class="form-item">
      <text class="label">真实姓名</text>
      <input class="input" type="text" placeholder="请输入真实姓名" v-model="name" @input="onNameInput"/>
    </view>
    
    <view class="form-item">
      <text class="label">身份证号</text>
      <input class="input" type="idcard" placeholder="请输入身份证号码" v-model="idCard" @input="onIdCardInput"/>
    </view>

    <view class="form-item">
      <text class="label">手机号码</text>
      <input class="input" type="number" placeholder="请输入手机号码" v-model="phone" @input="onPhoneInput"/>
    </view>
  </view>

  <button class="submit-btn" 
          :class="{ active: canSubmit }" 
          @tap="submitVerify" 
          :disabled="!canSubmit">
    提交认证
  </button>
</view> 
</template>

<script>
	export default {
		data() {
			return {
				
    name: '',
    idCard: '',
    phone: '',
    canSubmit: false
			}
		},
  onShow() {
    this.checkCanSubmit()
  },
		methods: {
			onNameInput(e) {
				this.name = e.detail.value;
				this.checkCanSubmit();
			},

			onIdCardInput(e) {
				this.idCard = e.detail.value;
				this.checkCanSubmit();
			},

			onPhoneInput(e) {
				this.phone = e.detail.value;
				this.checkCanSubmit();
			},

			checkCanSubmit() {
				const { name, idCard, phone } = this;
				this.canSubmit = name && idCard && phone;
			},

  async submitVerify() {
    // 验证表单
    if (!this.name.trim()) {
      uni.showToast({
        title: '请输入真实姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!this.idCard.trim()) {
      uni.showToast({
        title: '请输入身份证号码',
        icon: 'none'
      });
      return;
    }
    
    if (!this.phone.trim()) {
      uni.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return;
    }
    
    // 简单的身份证号码格式验证
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(this.idCard)) {
      uni.showToast({
        title: '身份证号码格式不正确',
        icon: 'none'
      });
      return;
    }
    
    // 手机号码格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.phone)) {
      uni.showToast({
        title: '手机号码格式不正确',
        icon: 'none'
      });
      return;
    }
    
    uni.showLoading({
      title: '认证中...'
    });
    
    try {
      // 调用后端实名认证API
      const response = await this.$requestHttp.post('/app/member/verify-real-name', {
        data: {
          realName: this.name,
          idCardNumber: this.idCard,
          phone: this.phone
        }
      });
      
      console.log('实名认证API响应:', response);
      
      if (response.code === 200) {
        // 认证成功，先隐藏loading
        uni.hideLoading();
        
        // 更新本地存储（用try-catch包装，避免存储失败影响成功提示）
        try {
          let userInfo = uni.getStorageSync('userInfo') || {};
          userInfo.isVerified = true;
          userInfo.realNameStatus = 1;
          uni.setStorageSync('userInfo', userInfo);
          console.log('本地存储更新成功');
        } catch (storageError) {
          console.error('更新本地存储失败:', storageError);
        }
        
        // 更新申请达人页面的状态（用try-catch包装，避免页面操作失败影响成功提示）
        try {
          const pages = getCurrentPages();
          const applyPage = pages.find(p => p.route === 'pages/apply-expert/apply-expert');
          if (applyPage && applyPage.checkRequirements) {
            applyPage.checkRequirements();
          }
        } catch (pageError) {
          console.error('更新申请达人页面状态失败:', pageError);
        }
        
        // 显示成功提示
        uni.showToast({
          title: '实名完成',
          icon: 'success'
        });
        
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } else {
        // API返回失败
        uni.hideLoading();
        uni.showToast({
          title: response.message || '实名失败',
          icon: 'none'
        });
      }
    } catch (error) {
      // 网络请求失败
      uni.hideLoading();
      console.error('实名认证网络请求失败:', error);
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    }
  }
		}
	}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #f7f7f7;
  padding: 30rpx;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.form-box {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.input {
  height: 88rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 140, 0, 0.5);
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.active {
  background: #ff8c00;
} 
</style>
