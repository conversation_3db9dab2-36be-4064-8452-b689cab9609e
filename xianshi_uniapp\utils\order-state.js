// 订单状态枚举
export const OrderState = {
  // 用户侧状态
  PUBLISHED: 'published',         // 已发布订单
  EXPERT_SELECTED: 'expertSelected', // 已选择达人
  ORDER_STARTED: 'orderStarted',  // 订单已开始
  COMPLETED: 'completed',         // 已完成
  EVALUATED: 'evaluated',         // 已评价
  
  // 达人侧状态
  APPLIED: 'applied',             // 已报名
  CHOSEN: 'chosen',               // 已被选中 
  DEPARTURE_CONFIRMED: 'departureConfirmed', // 出发确认
  ARRIVAL_CONFIRMED: 'arrivalConfirmed',    // 到达确认
  SERVICE_COMPLETED: 'serviceCompleted'     // 服务完成
};

// 达人订单状态枚举
export const ExpertOrderStatus = {
  APPLIED: 'applied',             // 已报名
  CONFIRMED: 'confirmed',         // 已被选中
  FAILED: 'failed',               // 未被选中/失败
  DEPARTED: 'departed',           // 已出发
  ARRIVED: 'arrived',             // 已到达
  COMPLETED: 'completed',         // 已完成
  CANCELED: 'canceled'            // 已取消
};

// 状态转换映射（定义哪些状态可以转换到哪些状态）
export const StateTransitions = {
  // 用户侧转换
  [OrderState.PUBLISHED]: [OrderState.EXPERT_SELECTED],
  [OrderState.EXPERT_SELECTED]: [OrderState.ORDER_STARTED], // 这里逻辑上会做限制，需要等待达人确认到达
  [OrderState.ORDER_STARTED]: [OrderState.COMPLETED],
  [OrderState.COMPLETED]: [OrderState.EVALUATED],
  
  // 达人侧转换
  [OrderState.APPLIED]: [OrderState.CHOSEN],
  [OrderState.CHOSEN]: [OrderState.DEPARTURE_CONFIRMED],
  [OrderState.DEPARTURE_CONFIRMED]: [OrderState.ARRIVAL_CONFIRMED],
  [OrderState.ARRIVAL_CONFIRMED]: [OrderState.SERVICE_COMPLETED]
};

// 状态对应的显示文本
export const StateDisplayText = {
  [OrderState.PUBLISHED]: '已发布',
  [OrderState.EXPERT_SELECTED]: '已选择达人',
  [OrderState.ORDER_STARTED]: '进行中',
  [OrderState.COMPLETED]: '已完成',
  [OrderState.EVALUATED]: '已评价',
  
  [OrderState.APPLIED]: '已报名',
  [OrderState.CHOSEN]: '已被选中',
  [OrderState.DEPARTURE_CONFIRMED]: '已确认出发',
  [OrderState.ARRIVAL_CONFIRMED]: '已到达目的地',
  [OrderState.SERVICE_COMPLETED]: '服务已完成'
};

// 达人订单状态提示信息
export const ExpertStatusTips = {
  [OrderState.APPLIED]: '您已成功报名此订单，请等待用户选择',
  'tempSelected': '您已被暂时选中，等待用户确认选人结束后会收到正式通知',
  [OrderState.CHOSEN]: '恭喜！您已被选为此订单的达人，请确认出发',
  [OrderState.DEPARTURE_CONFIRMED]: '您已确认出发，请到达目的地后点击"确认到达"',
  [OrderState.ARRIVAL_CONFIRMED]: '您已到达目的地，等待用户开始订单',
  [OrderState.ORDER_STARTED]: '订单已开始，请提供优质服务',
  [OrderState.SERVICE_COMPLETED]: '您已完成服务，等待用户确认完成订单',
  [OrderState.COMPLETED]: '订单已完成，等待用户评价',
  [OrderState.EVALUATED]: '用户已评价，订单已结束'
};

// 用户订单状态提示信息
export const UserStatusTips = {
  [OrderState.PUBLISHED]: '您已成功发布此订单，请耐心等待达人报名',
  'expertMarked': '您已标记选择达人，请点击"确认选人结束"按钮完成选择并通知达人',
  [OrderState.EXPERT_SELECTED]: '您的订单已有达人报名，请查看并选择服务达人',
  'expertChosenWaiting': '您已成功选择达人，达人正在准备出发，出发后将及时更新状态',
  [OrderState.DEPARTURE_CONFIRMED]: '达人已确认出发，正在前往活动目的地',
  [OrderState.ARRIVAL_CONFIRMED]: '达人已到达目的地，请点击开始订单以确认服务开始',
  [OrderState.ORDER_STARTED]: '服务已开始，达人正在为您提供服务',
  [OrderState.SERVICE_COMPLETED]: '达人已完成服务，请确认完成订单',
  [OrderState.COMPLETED]: '订单已完成，为达人进行评价',
  [OrderState.EVALUATED]: '感谢您评价，订单已结束'
};

// 用户和达人状态对应关系
export const UserExpertStateMapping = {
  [OrderState.EXPERT_SELECTED]: OrderState.CHOSEN, // 用户选择达人 -> 达人被选中
  [OrderState.ORDER_STARTED]: OrderState.ARRIVAL_CONFIRMED, // 用户开始订单 -> 达人已到达
  [OrderState.COMPLETED]: OrderState.SERVICE_COMPLETED // 用户确认完成 -> 达人服务完成
};

// 获取用户进度值
export const getUserProgressValue = (status) => {
  const progressMap = {
    [OrderState.PUBLISHED]: 1,
    [OrderState.EXPERT_SELECTED]: 2,
    [OrderState.ORDER_STARTED]: 3,
    [OrderState.COMPLETED]: 4,
    [OrderState.EVALUATED]: 5
  };
  
  return progressMap[status] || 0;
}

// 获取达人进度值
export const getExpertProgressValue = (status) => {
  const progressMap = {
    [OrderState.APPLIED]: 1,
    [OrderState.CHOSEN]: 2,
    [OrderState.DEPARTURE_CONFIRMED]: 3,
    [OrderState.ARRIVAL_CONFIRMED]: 4,
    [OrderState.SERVICE_COMPLETED]: 5
  };
  
  return progressMap[status] || 0;
}