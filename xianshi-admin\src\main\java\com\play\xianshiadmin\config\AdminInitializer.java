package com.play.xianshiadmin.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.mapper.AdminMapper;
import com.play.xianshibusiness.pojo.Admin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 管理员账户初始化器
 * 在应用启动时自动创建默认管理员账号
 */
@Component
public class AdminInitializer implements ApplicationRunner {

    @Autowired
    private AdminMapper adminMapper;
    
    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Override
    public void run(ApplicationArguments args) {
        // 创建指定的管理员账号
        createAdmin("***********", "123456");
    }
    
    /**
     * 创建指定的管理员账号
     * 
     * @param username 用户名
     * @param password 密码
     */
    public void createAdmin(String username, String password) {
        try {
            // 检查账号是否已存在
            LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Admin::getUsername, username);
            Admin existingAdmin = adminMapper.selectOne(queryWrapper);
            
            if (existingAdmin != null) {
                return;
            }
            
            // 创建新的管理员账号
            Admin admin = new Admin();
            admin.setId(UUID.randomUUID().toString().replace("-", ""));
            admin.setUsername(username);
            admin.setPassword(passwordEncoder.encode(password));
            admin.setNickname("管理员");
            admin.setPhone(username);
            admin.setRoles("ADMIN");
            admin.setAvatar("/static/images/admin-avatar.png");
            admin.setAvailable(true);
            admin.setDeleted(false);
            admin.setCreateTime(LocalDateTime.now());
            admin.setUpdateTime(LocalDateTime.now());
            
            adminMapper.insert(admin);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    
    /**
     * 用于手动注册管理员的方法
     * 可以在控制台或通过API调用此方法
     * 
     * @param username 用户名
     * @param password 密码
     * @param nickname 昵称
     * @param phone 手机号
     * @return 是否创建成功
     */
    public boolean registerAdmin(String username, String password, String nickname, String phone) {
        try {
            // 检查账号是否已存在
            LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Admin::getUsername, username);
            Admin existingAdmin = adminMapper.selectOne(queryWrapper);
            
            if (existingAdmin != null) {
                return false;
            }
            
            // 创建新的管理员账号
            Admin admin = new Admin();
            admin.setId(UUID.randomUUID().toString().replace("-", ""));
            admin.setUsername(username);
            admin.setPassword(passwordEncoder.encode(password));
            admin.setNickname(nickname);
            admin.setPhone(phone);
            admin.setRoles("ADMIN");
            admin.setAvatar("/static/images/admin-avatar.png");
            admin.setAvailable(true);
            admin.setDeleted(false);
            admin.setCreateTime(LocalDateTime.now());
            admin.setUpdateTime(LocalDateTime.now());
            
            adminMapper.insert(admin);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
} 