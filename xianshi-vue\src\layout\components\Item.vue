<template>
  <div class="menu-item">
    <i v-if="icon" :class="icon"></i>
    <span v-if="title" slot="title" class="menu-title" style="border-bottom: none !important; text-decoration: none !important; border: none !important; box-shadow: none !important;">{{ title }}</span>
  </div>
</template>

<script>
export default {
  name: 'MenuItem',
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  mounted() {
    // 延迟执行，确保DOM已经渲染完成
    setTimeout(() => {
      this.removeUnderlines();
    }, 100);
  },
  methods: {
    removeUnderlines() {
      // 直接修改当前组件内的span元素
      if (this.$el) {
        const span = this.$el.querySelector('span');
        if (span) {
          span.style.setProperty('border-bottom', 'none', 'important');
          span.style.setProperty('text-decoration', 'none', 'important');
          span.style.setProperty('border', 'none', 'important');
          span.style.setProperty('box-shadow', 'none', 'important');
        }
      }
    }
  }
};
</script>

<style lang="scss">
/* 使用全局样式覆盖所有菜单标题的下划线 */
.sidebar-container .el-menu span[slot="title"],
.sidebar-container .el-submenu__title span,
.sidebar-container .el-menu-item span,
.sidebar-container span[data-v-10973580],
.sidebar-container [class*="data-v-"] span {
  border-bottom: none !important;
  text-decoration: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 直接针对带有data-v-10973580属性的元素 */
span[data-v-10973580] {
  border-bottom: none !important;
  text-decoration: none !important;
  border: none !important;
  box-shadow: none !important;
}
</style>

<style lang="scss" scoped>
.menu-item {
  display: flex;
  align-items: center;
  width: 100%;
  transition: all 0.3s;
  border: none !important;
  box-shadow: none !important;
  
  i {
    margin-right: 12px;
    font-size: 20px;
    transition: all 0.3s;
  }
  
  .menu-title {
    font-size: 16px;
    transition: all 0.3s;
    border-bottom: none !important;
    text-decoration: none !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  &:hover {
    i {
      transform: scale(1.1);
      color: var(--primary-color, #F8D010);
    }
    
    span {
      transform: translateX(2px);
    }
  }
}
</style> 