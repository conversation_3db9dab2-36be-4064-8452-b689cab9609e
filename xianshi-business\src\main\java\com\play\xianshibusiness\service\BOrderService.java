package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.order.*;
import com.play.xianshibusiness.enums.MessageType;
import com.play.xianshibusiness.enums.OrderEnrollDetailStatusEnum;
import com.play.xianshibusiness.enums.OrderStatusEnum;
import com.play.xianshibusiness.enums.OrderTypeEnum;
import com.play.xianshibusiness.enums.MemberRoleEnum;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.*;
import com.play.xianshibusiness.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单服务
 */
@Service
@Slf4j
public class BOrderService {

    @Resource
    private BOrderMapper orderMapper;

    @Resource
    private BOrderEnrollDetailMapper orderEnrollDetailMapper;

    @Resource
    private BOrderTypeMapper orderTypeMapper;

    @Resource
    private CMemberMapper memberMapper;

    @Resource
    private BOrderStatusService orderStatusService;

    @Resource
    private ISysMessageService sysMessageService;

    @Resource
    private CMemberService memberService;

    /**
     * 创建订单（草稿状态）
     *
     * @param dto      订单创建DTO
     * @param memberId 会员ID
     * @return 订单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String createOrder(OrderCreateDTO dto, String memberId) {
        // 1.校验活动时间
        if (dto.getStartTime().isBefore(LocalDateTime.now())) {
            throw new GlobalException(400, "活动开始时间不能早于当前时间");
        }
        if (dto.getEndTime().isBefore(dto.getStartTime())) {
            throw new GlobalException(400, "活动结束时间不能早于开始时间");
        }
        // 2.校验活动类型是否存在
        BOrderType orderType = orderTypeMapper.selectById(dto.getActivityTypeId());
        if (orderType == null) {
            throw new GlobalException(400, "活动类型不存在");
        }

        // 3.创建订单
        BOrder order = new BOrder();
        BeanUtils.copyProperties(dto, order);

        // 设置地点和地址信息
        order.setLocation(dto.getAddressText()); // 设置地点
        order.setAddress(dto.getAddressText()); // 设置详细地址

        // 解析经纬度信息
        try {
            if (StringUtils.hasText(dto.getAddressJson())) {
                // 假设addressJson格式为: {"lng": 123.456, "lat": 78.90, ...}
                if (dto.getAddressJson().contains("lng")) {
                    String lng = dto.getAddressJson().replaceAll(".*\"lng\"\\s*:\\s*([\\d.]+).*", "$1");
                    order.setLongitude(lng);
                }
                if (dto.getAddressJson().contains("lat")) {
                    String lat = dto.getAddressJson().replaceAll(".*\"lat\"\\s*:\\s*([\\d.]+).*", "$1");
                    order.setLatitude(lat);
                }
            }
        } catch (Exception e) {
            // 解析失败不影响主流程
        }

        // 计算时长（分钟）
        long minutes = ChronoUnit.MINUTES.between(dto.getStartTime(), dto.getEndTime());
        double hours = minutes / 60.0;
        order.setLongTime(hours);

        // 计算总费用
        BigDecimal totalFee = dto.getHourlyRate().multiply(BigDecimal.valueOf(hours));
        order.setTotalFee(totalFee);

        // 显式设置参与人数
        order.setPersonCount(dto.getPersonCount().intValue());
        
        // 计算金币消耗 = 小时费用 × 人数 × 活动时长
        BigDecimal goldCost = dto.getHourlyRate()
                .multiply(BigDecimal.valueOf(dto.getPersonCount()))
                .multiply(BigDecimal.valueOf(hours));
        order.setGoldCost(goldCost);

        // 设置标题
        String title = orderType.getName() + " - " + dto.getAddressText();
        if (title.length() > 50) {
            title = title.substring(0, 47) + "...";
        }
        order.setTitle(title);

        order.setMemberId(memberId);
        order.setSysId(getSysOrderId());
        order.setStatus(OrderStatusEnum.DRAFT);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        order.setDeleted(false);
        order.setAvailable(true);
        
        // 设置订单类型和目标达人ID
        if (dto.getOrderType() != null) {
            order.setOrderType(dto.getOrderType());
        } else {
            // 如果前端没有传递orderType，根据targetId自动判断
            if (StringUtils.hasText(dto.getTargetId())) {
                order.setOrderType(OrderTypeEnum.APPOINTMENT); // 约单模式
            } else {
                order.setOrderType(OrderTypeEnum.DISPATCH); // 派单模式
            }
        }
        
        // 设置目标达人ID（约单模式时使用）
        if (StringUtils.hasText(dto.getTargetId())) {
            order.setTargetId(dto.getTargetId());
        }

        orderMapper.insert(order);

        // 4.记录状态变更
        orderStatusService.recordOrderStatus(order.getId(), OrderStatusEnum.DRAFT);

        return order.getId();
    }

    /**
     * 生成订单系统ID
     *
     * @return
     */
    private String getSysOrderId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 12);
    }

    /**
     * 提交审核
     *
     * @param orderId  订单ID
     * @param memberId 会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitForAudit(String orderId, String memberId) {
        // 1.获取订单信息
        BOrder order = getOrderByIdAndMember(orderId, memberId);

        // 2.校验订单状态
        if (order.getStatus() != OrderStatusEnum.DRAFT) {
            throw new GlobalException(400, "只有草稿状态的订单才能提交审核");
        }

        // 3.获取订单类型
        BOrderType orderType = orderTypeMapper.selectById(order.getActivityTypeId());
        if (orderType == null) {
            throw new GlobalException(400, "活动类型不存在");
        }

        // 4.检查并扣减金币
        if (orderType.getNeedGold() != null && orderType.getNeedGold().compareTo(BigDecimal.ZERO) > 0) {
            // 先检查金币余额
            Integer currentGold = memberService.getMemberGoldBalance(memberId);
            if (currentGold < orderType.getNeedGold().intValue()) {
                throw new GlobalException(400,
                        String.format("金币余额不足，当前余额：%d金币，需要：%d金币，请先充值",
                                currentGold, orderType.getNeedGold().intValue()));
            }
            memberService.decreaseGold(memberId, orderType.getNeedGold(), "提交订单" + order.getSysId());
        }

        // 5.更新订单状态
        order.setStatus(OrderStatusEnum.AUDITING);
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 6.记录状态变更
        orderStatusService.recordOrderStatus(orderId, OrderStatusEnum.AUDITING);

        return true;
    }

    /**
     * 审核订单
     *
     * @param dto     审核DTO
     * @param adminId 管理员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditOrder(OrderAuditDTO dto, String adminId) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(dto.getOrderId());
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }

        // 2.校验订单状态
        if (order.getStatus() != OrderStatusEnum.AUDITING) {
            throw new GlobalException(400, "只有待审核状态的订单才能审核");
        }

        // 3.更新订单状态
        OrderStatusEnum newStatus = dto.getPassed() ? OrderStatusEnum.PUBLISHED : OrderStatusEnum.DRAFT;
        order.setStatus(newStatus);
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 4.记录状态变更
        orderStatusService.recordOrderStatus(dto.getOrderId(), newStatus);

        // 5.发送系统消息
        String message = dto.getPassed() ? "您的订单" + order.getSysId() + "已通过审核，现在可以接收达人报名了"
                : "您的订单" + order.getSysId() + "未通过审核，原因：" + (dto.getRemark() != null ? dto.getRemark() : "未通过审核");

        sysMessageService.sendMessage(order.getMemberId(), message, MessageType.ORDER_AUDIT);

        return true;
    }

    /**
     * 取消订单（带原因）
     *
     * @param orderId  订单ID
     * @param memberId 会员ID
     * @param reason   取消原因
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(String orderId, String memberId, String reason) {
        // 1.获取订单信息
        BOrder order = getOrderByIdAndMember(orderId, memberId);

        // 2.校验订单状态
        if (order.getStatus() == OrderStatusEnum.COMPLETED || order.getStatus() == OrderStatusEnum.CANCELED) {
            throw new GlobalException(400, "已完成或已取消的订单不能取消");
        }

        // 3.更新订单状态
        order.setStatus(OrderStatusEnum.CANCELED);
        order.setCancelReason(reason);
        order.setCancelTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 4.记录状态变更
        orderStatusService.recordOrderStatus(orderId, OrderStatusEnum.CANCELED);

        // 5.如果订单已经有达人报名，需要发送消息通知
        List<BOrderEnrollDetail> details = orderEnrollDetailMapper.selectList(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, orderId)
                        .eq(BOrderEnrollDetail::getDeleted, false));

        if (!CollectionUtils.isEmpty(details)) {
            for (BOrderEnrollDetail detail : details) {
                String message = "订单" + order.getSysId() + "已被发布者取消" +
                        (StringUtils.hasText(reason) ? "，原因：" + reason : "");
                sysMessageService.sendMessage(detail.getMemberId(), message, MessageType.ORDER_CANCEL);
            }
        }

        return true;
    }

    /**
     * 达人取消报名
     *
     * @param orderId  订单ID
     * @param memberId 达人会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelEnrollment(String orderId, String memberId) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }

        // 2.校验订单状态
        if (order.getStatus() != OrderStatusEnum.PUBLISHED) {
            throw new GlobalException(400, "只有发布中状态的订单才能取消报名");
        }

        // 3.查找报名记录
        BOrderEnrollDetail existDetail = orderEnrollDetailMapper.selectOne(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, orderId)
                        .eq(BOrderEnrollDetail::getMemberId, memberId)
                        .eq(BOrderEnrollDetail::getDeleted, false));

        if (existDetail == null) {
            throw new GlobalException(400, "您未报名该订单");
        }

        // 4.校验报名状态
        if (existDetail.getStatus() != OrderEnrollDetailStatusEnum.ENROLLED) {
            throw new GlobalException(400, "只有已报名状态才能取消报名");
        }

        // 5.删除报名记录（软删除）
        existDetail.setDeleted(true);
        existDetail.setStatus(OrderEnrollDetailStatusEnum.CANCELED);
        existDetail.setUpdateTime(LocalDateTime.now());
        orderEnrollDetailMapper.updateById(existDetail);

        // 6.记录状态变更
        orderStatusService.recordOrderDetailStatus(existDetail.getId(), OrderEnrollDetailStatusEnum.CANCELED);

        // 7.更新订单的报名人数
        int enrollCount = orderEnrollDetailMapper.selectCount(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, orderId)
                        .eq(BOrderEnrollDetail::getDeleted, false))
                .intValue();
        order.setEnrollCount(enrollCount);
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 8.通知订单发布者
        CMember member = memberMapper.selectById(memberId);
        String memberName = member != null ? member.getNickname() : "达人";
        String message = memberName + "取消了对订单" + order.getSysId() + "的报名";
        sysMessageService.sendMessage(order.getMemberId(), message, MessageType.ORDER_UPDATE);

        return true;
    }

    /**
     * 达人报名订单
     *
     * @param orderId  订单ID
     * @param memberId 达人会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean enrollOrder(String orderId, String memberId) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }

        // 2.校验订单状态
        if (order.getStatus() != OrderStatusEnum.PUBLISHED) {
            throw new GlobalException(400, "只有发布中状态的订单才能报名");
        }

        // 2.1.不能报名自己的订单
        if (order.getMemberId().equals(memberId)) {
            throw new GlobalException(400, "不能报名自己发布的订单");
        }

        // 3.校验是否已报名
        BOrderEnrollDetail existDetail = orderEnrollDetailMapper.selectOne(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, orderId)
                        .eq(BOrderEnrollDetail::getMemberId, memberId)
                        .eq(BOrderEnrollDetail::getDeleted, false));

        if (existDetail != null) {
            throw new GlobalException(400, "您已报名该订单");
        }

        // 4.校验性别要求
        CMember member = memberMapper.selectById(memberId);
        if (member == null) {
            throw new GlobalException(400, "用户不存在");
        }

        // 4.1.校验用户当前角色是否为达人
        if (!MemberRoleEnum.TALENT.getCode().equals(member.getCurrentRoleId())) {
            throw new GlobalException(400, "只有达人角色才能报名订单");
        }

        // 4.2.校验性别要求
        if (order.getSexRequire() != null && !order.getSexRequire().isEmpty() && !"0".equals(order.getSexRequire())) {
            Integer memberGender = member.getGender();
            if (memberGender == null) {
                throw new GlobalException(400, "请先完善个人资料中的性别信息");
            }
            
            // 性别要求校验：1-男性，2-女性，0或null-不限
            if (!memberGender.toString().equals(order.getSexRequire()) && !"0".equals(order.getSexRequire())) {
                String genderText = "1".equals(order.getSexRequire()) ? "男性" : "女性";
                throw new GlobalException(400, "该订单仅限" + genderText + "报名");
            }
        }

        // 5.创建报名记录
        BOrderEnrollDetail enrollDetail = new BOrderEnrollDetail();
        enrollDetail.setId(UUID.randomUUID().toString());
        enrollDetail.setOrderId(orderId);
        enrollDetail.setMemberId(memberId);
        enrollDetail.setIsSelect(false);
        enrollDetail.setStatus(OrderEnrollDetailStatusEnum.ENROLLED);
        enrollDetail.setCreateTime(LocalDateTime.now());
        enrollDetail.setUpdateTime(LocalDateTime.now());
        enrollDetail.setDeleted(false);
        enrollDetail.setAvailable(true);

        orderEnrollDetailMapper.insert(enrollDetail);

        // 6.记录状态变更
        orderStatusService.recordOrderDetailStatus(enrollDetail.getId(), OrderEnrollDetailStatusEnum.ENROLLED);

        // 7.更新订单的报名人数
        int enrollCount = orderEnrollDetailMapper.selectCount(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, orderId)
                        .eq(BOrderEnrollDetail::getDeleted, false))
                .intValue();
        order.setEnrollCount(enrollCount);
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 8.发送系统消息给订单所有者
        sysMessageService.sendMessage(order.getMemberId(),
                "有达人报名了您的订单" + order.getSysId(),
                MessageType.ORDER_ENROLL);

        // 9.通知订单所有者有新的报名
        // 移除报名人数限制检查，允许无限制报名
        // 发布者可以在任何时候从已报名的达人中选择合适的人选

        return true;
    }

    /**
     * 选择达人
     *
     * @param orderId   订单ID
     * @param memberIds 达人会员ID列表
     * @param memberId  会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean selectTalents(String orderId, String[] memberIds, String memberId) {
        // 1.获取订单信息
        BOrder order = getOrderByIdAndMember(orderId, memberId);

        // 2.校验订单状态
        if (order.getStatus() != OrderStatusEnum.PUBLISHED && order.getStatus() != OrderStatusEnum.WAITING_SELECT) {
            throw new GlobalException(400, "只有发布中或待选择达人状态的订单才能选择达人");
        }

        // 3.校验选择的达人是否都已报名
        List<BOrderEnrollDetail> enrollDetails = orderEnrollDetailMapper.selectList(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, orderId)
                        .eq(BOrderEnrollDetail::getDeleted, false)
                        .in(BOrderEnrollDetail::getMemberId, Arrays.asList(memberIds)));

        if (enrollDetails.size() != memberIds.length) {
            throw new GlobalException(400, "部分选择的达人未报名该订单");
        }

        // 4.校验选择人数是否合规
        if (memberIds.length > order.getPersonCount()) {
            throw new GlobalException(400, "选择的达人数量超过订单需求人数");
        }

        // 5.更新达人状态为已选择
        for (BOrderEnrollDetail detail : enrollDetails) {
            detail.setIsSelect(true);
            detail.setStatus(OrderEnrollDetailStatusEnum.SELECTED);
            detail.setUpdateTime(LocalDateTime.now());
            orderEnrollDetailMapper.updateById(detail);

            // 记录子订单状态变更
            orderStatusService.recordOrderDetailStatus(detail.getId(), OrderEnrollDetailStatusEnum.SELECTED);

            // 通知达人
            sysMessageService.sendMessage(detail.getMemberId(),
                    "恭喜您，您已被选中参与订单" + order.getSysId(),
                    MessageType.ORDER_SELECTED);
        }

        // 6.更新订单状态为进行中
        order.setStatus(OrderStatusEnum.PROCESSING);
        order.setUpdateTime(LocalDateTime.now());
        // 设置目标会员IDS
        order.setTargetId(String.join(",", memberIds));
        orderMapper.updateById(order);

        // 7.记录状态变更
        orderStatusService.recordOrderStatus(orderId, OrderStatusEnum.PROCESSING);

        return true;
    }

    /**
     * 根据当前用户角色获取订单列表
     *
     * @param dto      查询条件
     * @param memberId 当前会员ID
     * @return 订单分页结果
     */
    public Page<OrderDetailVO> pageOrderByRole(OrderQueryDTO dto, String memberId) {
        // 获取会员信息
        CMember member = memberMapper.selectById(memberId);
        if (member == null || member.getDeleted() || !member.getAvailable()) {
            throw new GlobalException(400, "会员不存在或已被禁用");
        }

        // 根据当前角色返回相应的订单列表
        if (MemberRoleEnum.TALENT.getCode().equals(member.getCurrentRoleId())) {
            // 达人角色, 返回已报名的订单或可报名的订单
            return pageOrderForTalent(dto, memberId);
        } else {
            // 普通用户，返回自己发布的订单
            return pageOrderForUser(dto, memberId);
        }
    }

    /**
     * 为普通用户角色获取订单列表（自己发布的订单）
     *
     * @param dto      查询条件
     * @param memberId 当前会员ID
     * @return 订单分页结果
     */
    private Page<OrderDetailVO> pageOrderForUser(OrderQueryDTO dto, String memberId) {
        // 构建查询条件
        LambdaQueryWrapper<BOrder> queryWrapper = buildBaseOrderQuery(dto);
        // 只查询用户自己发布的订单
        queryWrapper.eq(BOrder::getMemberId, memberId);

        return executeOrderQuery(queryWrapper, dto.getPageNum(), dto.getPageSize());
    }

    /**
     * 为达人角色获取订单列表（已报名或可报名的订单）
     *
     * @param dto      查询条件
     * @param memberId 当前会员ID
     * @return 订单分页结果
     */
    private Page<OrderDetailVO> pageOrderForTalent(OrderQueryDTO dto, String memberId) {
        // 达人可以看到：
        // 1. 自己已报名的订单（无论订单状态如何）
        // 2. 未报名的且处于"已发布"状态的订单（可以报名的）

        // 查询已报名的订单ID
        List<String> enrolledOrderIds = orderEnrollDetailMapper.selectList(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getMemberId, memberId)
                        .eq(BOrderEnrollDetail::getDeleted, false)
                        .select(BOrderEnrollDetail::getOrderId))
                .stream().map(BOrderEnrollDetail::getOrderId).collect(Collectors.toList());

        // 构建基础查询条件
        LambdaQueryWrapper<BOrder> queryWrapper = buildBaseOrderQuery(dto);

        // 如果dto中指定了"我的报名"，则只查询已报名的订单
        if (Boolean.TRUE.equals(dto.getMyEnrollment())) {
            if (enrolledOrderIds.isEmpty()) {
                // 没有报名记录，返回空结果
                return new Page<>(dto.getPageNum(), dto.getPageSize());
            }
            queryWrapper.in(BOrder::getId, enrolledOrderIds);
        } else {
            // 否则查询可报名的订单或已报名的订单
            queryWrapper.and(wrapper -> wrapper.eq(BOrder::getStatus, OrderStatusEnum.PUBLISHED)
                    .or()
                    .in(!enrolledOrderIds.isEmpty(), BOrder::getId, enrolledOrderIds));

            // 排除自己发的订单
            queryWrapper.ne(BOrder::getMemberId, memberId);
        }

        return executeOrderQuery(queryWrapper, dto.getPageNum(), dto.getPageSize());
    }

    /**
     * 构建基础订单查询条件
     */
    private LambdaQueryWrapper<BOrder> buildBaseOrderQuery(OrderQueryDTO dto) {
        LambdaQueryWrapper<BOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BOrder::getDeleted, false);

        // 订单ID
        if (!StringUtils.isEmpty(dto.getOrderId())) {
            queryWrapper.eq(BOrder::getId, dto.getOrderId())
                    .or()
                    .eq(BOrder::getSysId, dto.getOrderId());
        }

        // 会员ID
        if (!StringUtils.isEmpty(dto.getMemberId())) {
            queryWrapper.eq(BOrder::getMemberId, dto.getMemberId());
        }

        // 状态列表
        if (dto.getStatusList() != null && !dto.getStatusList().isEmpty()) {
            queryWrapper.in(BOrder::getStatus, dto.getStatusList());
        } else if (dto.getStatus() != null) {
            // 单个状态
            queryWrapper.eq(BOrder::getStatus, dto.getStatus());
        }

        // 活动类型
        if (!StringUtils.isEmpty(dto.getActivityTypeId())) {
            queryWrapper.eq(BOrder::getActivityTypeId, dto.getActivityTypeId());
        } else if (!StringUtils.isEmpty(dto.getTypeId())) {
            queryWrapper.eq(BOrder::getActivityTypeId, dto.getTypeId());
        }

        // 开始时间范围
        if (!StringUtils.isEmpty(dto.getStartTimeBegin())) {
            queryWrapper.ge(BOrder::getStartTime, dto.getStartTimeBegin());
        }

        if (!StringUtils.isEmpty(dto.getStartTimeEnd())) {
            queryWrapper.le(BOrder::getStartTime, dto.getStartTimeEnd());
        }

        // 创建时间范围
        if (!StringUtils.isEmpty(dto.getCreateTimeStart())) {
            queryWrapper.ge(BOrder::getCreateTime, dto.getCreateTimeStart());
        }

        if (!StringUtils.isEmpty(dto.getCreateTimeEnd())) {
            queryWrapper.le(BOrder::getCreateTime, dto.getCreateTimeEnd());
        }

        return queryWrapper;
    }

    /**
     * 执行订单查询并转换为VO
     *
     * @param queryWrapper 查询条件
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @return 订单分页结果
     */
    private Page<OrderDetailVO> executeOrderQuery(LambdaQueryWrapper<BOrder> queryWrapper, Integer pageNum,
            Integer pageSize) {
        // 分页查询
        Page<BOrder> page = new Page<>(pageNum, pageSize);
        queryWrapper.orderByDesc(BOrder::getCreateTime);
        Page<BOrder> orderPage = orderMapper.selectPage(page, queryWrapper);

        // 转换为VO
        Page<OrderDetailVO> voPage = new Page<>();
        BeanUtils.copyProperties(orderPage, voPage, "records");

        if (orderPage.getRecords().isEmpty()) {
            voPage.setRecords(Collections.emptyList());
            return voPage;
        }

        // 获取订单创建者ID
        Set<String> memberIds = orderPage.getRecords().stream()
                .map(BOrder::getMemberId)
                .collect(Collectors.toSet());

        // 获取订单类型ID
        Set<String> typeIds = orderPage.getRecords().stream()
                .map(BOrder::getActivityTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询会员信息
        Map<String, CMember> memberMap = memberMapper.selectList(
                new LambdaQueryWrapper<CMember>()
                        .in(CMember::getId, memberIds))
                .stream().collect(Collectors.toMap(CMember::getId, Function.identity(), (o1, o2) -> o1));

        // 批量查询订单类型
        Map<String, BOrderType> typeMap = new HashMap<>();
        if (!typeIds.isEmpty()) {
            typeMap = orderTypeMapper.selectList(
                    new LambdaQueryWrapper<BOrderType>()
                            .in(BOrderType::getId, typeIds))
                    .stream().collect(Collectors.toMap(BOrderType::getId, Function.identity(), (o1, o2) -> o1));
        }

        // 获取所有订单ID，用于批量查询报名信息
        Set<String> orderIds = orderPage.getRecords().stream()
                .map(BOrder::getId)
                .collect(Collectors.toSet());

        // 批量查询所有订单的报名信息
        Map<String, List<BOrderEnrollDetail>> orderEnrollMap = new HashMap<>();
        if (!orderIds.isEmpty()) {
            List<BOrderEnrollDetail> allEnrollDetails = orderEnrollDetailMapper.selectList(
                    new LambdaQueryWrapper<BOrderEnrollDetail>()
                            .in(BOrderEnrollDetail::getOrderId, orderIds)
                            .eq(BOrderEnrollDetail::getDeleted, false)
                            .orderByDesc(BOrderEnrollDetail::getCreateTime));
            
            orderEnrollMap = allEnrollDetails.stream()
                    .collect(Collectors.groupingBy(BOrderEnrollDetail::getOrderId));
        }

        // 获取所有报名达人的ID
        Set<String> talentIds = orderEnrollMap.values().stream()
                .flatMap(List::stream)
                .map(BOrderEnrollDetail::getMemberId)
                .collect(Collectors.toSet());

        // 批量查询达人信息
        Map<String, CMember> talentMap = new HashMap<>();
        if (!talentIds.isEmpty()) {
            talentMap = memberMapper.selectList(
                    new LambdaQueryWrapper<CMember>()
                            .in(CMember::getId, talentIds))
                    .stream()
                    .collect(Collectors.toMap(CMember::getId, Function.identity(), (o1, o2) -> o1));
        }

        // 转换为VO
        List<OrderDetailVO> voList = new ArrayList<>(orderPage.getRecords().size());
        for (BOrder order : orderPage.getRecords()) {
            OrderDetailVO vo = new OrderDetailVO();
            BeanUtils.copyProperties(order, vo);
            vo.setOrderId(order.getId());
            vo.setStatus(Integer.valueOf(order.getStatus().ordinal()));
            vo.setStatusDesc(order.getStatus().getDesc());

            // 设置会员信息
            CMember member = memberMap.get(order.getMemberId());
            if (member != null) {
                vo.setMemberName(member.getNickname());
                vo.setMemberAvatar(member.getAvatar());
                vo.setMemberNickname(member.getNickname());
            }

            // 设置活动类型名称
            BOrderType orderType = typeMap.get(order.getActivityTypeId());
            if (orderType != null) {
                vo.setTypeName(orderType.getName());
                vo.setActivityTypeName(orderType.getName());
            }

            // 获取该订单的报名信息
            List<BOrderEnrollDetail> enrollDetails = orderEnrollMap.getOrDefault(order.getId(), Collections.emptyList());
            
            // 设置报名人数
            vo.setEnrollCount(enrollDetails.size());
            
            // 构建报名VO列表
            List<OrderEnrollVO> enrollVOList = new ArrayList<>();
            List<OrderEnrollVO> selectedVOList = new ArrayList<>();
            
            for (BOrderEnrollDetail detail : enrollDetails) {
                OrderEnrollVO enrollVO = convertToEnrollVO(detail, talentMap.get(detail.getMemberId()));
                enrollVOList.add(enrollVO);
                
                if (detail.getSelected() != null && detail.getSelected()) {
                    selectedVOList.add(enrollVO);
                }
            }
            
            // 设置报名列表
            vo.setEnrollList(enrollVOList);
            vo.setSelectedList(selectedVOList);
            vo.setAppliedExperts(enrollVOList); // 兼容前端

            voList.add(vo);
        }

        voPage.setRecords(voList);
        return voPage;
    }

    /**
     * 获取订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    public OrderDetailVO getOrderDetail(String orderId) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }

        // 2.转换基本信息
        OrderDetailVO detailVO = new OrderDetailVO();
        BeanUtils.copyProperties(convertToOrderVO(order), detailVO);

        // 3.补充详情信息
        detailVO.setDescription(order.getDescription());
        detailVO.setLongitude(order.getLongitude());
        detailVO.setLatitude(order.getLatitude());
        detailVO.setAuditResult(order.getAuditResult());
        detailVO.setAuditComment(order.getAuditComment());
        detailVO.setAuditTime(order.getAuditTime());
        detailVO.setAuditorId(order.getAuditorId());
        detailVO.setCancelReason(order.getCancelReason());
        detailVO.setCancelTime(order.getCancelTime());
        detailVO.setGoldCost(order.getGoldCost() != null ? order.getGoldCost().intValue() : null);
        detailVO.setUpdateTime(order.getUpdateTime());

        // 4.获取会员信息
        CMember member = memberMapper.selectById(order.getMemberId());
        if (member != null) {
            detailVO.setMemberNickname(member.getNickname());
            detailVO.setMemberAvatar(member.getAvatar());
        }

        // 5.获取活动类型名称
        BOrderType orderType = orderTypeMapper.selectById(order.getActivityTypeId());
        if (orderType != null) {
            detailVO.setActivityTypeName(orderType.getName());
        }

        // 6.获取报名达人列表
        List<BOrderEnrollDetail> enrollDetails = orderEnrollDetailMapper.selectList(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, orderId)
                        .eq(BOrderEnrollDetail::getDeleted, false)
                        .orderByDesc(BOrderEnrollDetail::getCreateTime));

        // 7.获取达人会员信息
        if (!CollectionUtils.isEmpty(enrollDetails)) {
            // 获取达人ID列表
            List<String> talentIds = enrollDetails.stream()
                    .map(BOrderEnrollDetail::getMemberId)
                    .collect(Collectors.toList());

            // 批量查询达人信息
            Map<String, CMember> memberMap = memberMapper.selectList(
                    new LambdaQueryWrapper<CMember>()
                            .in(CMember::getId, talentIds))
                    .stream()
                    .collect(Collectors.toMap(CMember::getId, Function.identity(), (o1, o2) -> o1));

            // 8.构建子订单VO
            List<OrderEnrollDetailVO> enrollDetailVOs = enrollDetails.stream().map(detail -> {
                OrderEnrollDetailVO vo = new OrderEnrollDetailVO();
                BeanUtils.copyProperties(detail, vo);
                vo.setStatus(detail.getStatus());
                vo.setStatusDesc(detail.getStatus().getDesc());
                vo.setIsSelect(detail.getSelected());

                // 设置达人会员信息
                CMember talent = memberMap.get(detail.getMemberId());
                if (talent != null) {
                    vo.setMemberNickname(talent.getNickname());
                    vo.setMemberAvatar(talent.getAvatar());
                    vo.setGender(talent.getGender());
                    vo.setAge(talent.getAge());
                    vo.setConstellation(talent.getConstellation());
                }

                // 计算剩余服务时间
                if (detail.getServiceStartTime() != null
                        && detail.getStatus() == OrderEnrollDetailStatusEnum.SERVICING) {
                    LocalDateTime endTime = detail.getServiceStartTime().plusMinutes((long) (order.getLongTime() * 60));
                    long remainingMinutes = ChronoUnit.MINUTES.between(LocalDateTime.now(), endTime);
                    vo.setRemainingMinutes(remainingMinutes > 0 ? remainingMinutes : 0);
                }

                return vo;
            }).collect(Collectors.toList());

            detailVO.setEnrollDetails(enrollDetailVOs);

            // 9.构建报名VO和已选VO
            List<OrderEnrollVO> enrollVOList = new ArrayList<>();
            List<OrderEnrollVO> selectedVOList = new ArrayList<>();

            for (BOrderEnrollDetail detail : enrollDetails) {
                OrderEnrollVO enrollVO = convertToEnrollVO(detail, memberMap.get(detail.getMemberId()));
                enrollVOList.add(enrollVO);

                if (detail.getSelected() != null && detail.getSelected()) {
                    selectedVOList.add(enrollVO);
                }
            }

            detailVO.setEnrollList(enrollVOList);
            detailVO.setSelectedList(selectedVOList);
            
            // 同时设置appliedExperts字段，确保与前端兼容
            detailVO.setAppliedExperts(enrollVOList);
            
            // 重新设置enrollCount，确保与实际报名列表数量一致
            detailVO.setEnrollCount(enrollVOList.size());
        } else {
            // 如果没有报名记录，确保enrollCount为0
            detailVO.setEnrollCount(0);
        }

        return detailVO;
    }

    /**
     * 根据ID和会员ID获取订单
     *
     * @param orderId  订单ID
     * @param memberId 会员ID
     * @return 订单信息
     */
    private BOrder getOrderByIdAndMember(String orderId, String memberId) {
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }

        if (!order.getMemberId().equals(memberId)) {
            throw new GlobalException(400, "您无权操作此订单");
        }

        return order;
    }

    /**
     * 检查订单是否已完成
     *
     * @param orderId 订单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkOrderCompleted(String orderId) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            return;
        }

        // 2.检查订单状态
        if (order.getStatus() != OrderStatusEnum.PROCESSING) {
            return;
        }

        // 3.获取所有子订单
        List<BOrderEnrollDetail> enrollDetails = orderEnrollDetailMapper.selectList(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, orderId)
                        .eq(BOrderEnrollDetail::getIsSelect, true)
                        .eq(BOrderEnrollDetail::getDeleted, false));

        // 4.检查是否所有子订单都已完成
        boolean allCompleted = !CollectionUtils.isEmpty(enrollDetails) &&
                enrollDetails.stream()
                        .allMatch(detail -> detail.getStatus() == OrderEnrollDetailStatusEnum.SERVICE_COMPLETED ||
                                detail.getStatus() == OrderEnrollDetailStatusEnum.EVALUATED);

        if (allCompleted) {
            // 5.更新订单状态为已完成
            order.setStatus(OrderStatusEnum.COMPLETED);
            order.setUpdateTime(LocalDateTime.now());
            orderMapper.updateById(order);

            // 6.记录状态变更
            orderStatusService.recordOrderStatus(orderId, OrderStatusEnum.COMPLETED);

            // 7.通知订单所有者
            sysMessageService.sendMessage(order.getMemberId(),
                    "您的订单" + order.getSysId() + "已完成",
                    MessageType.ORDER_COMPLETED);
        }
    }

    /**
     * 分页查询订单（不区分角色，查询所有公开订单）
     *
     * @param dto 查询条件
     * @return 订单分页结果
     */
    public Page<OrderDetailVO> pageOrder(OrderQueryDTO dto) {
        // 构建基础查询条件
        LambdaQueryWrapper<BOrder> queryWrapper = buildBaseOrderQuery(dto);

        // 只查询已发布状态的订单
        queryWrapper.eq(BOrder::getStatus, OrderStatusEnum.PUBLISHED);
        
        // 只显示派单模式的订单，过滤掉约单模式的订单
        queryWrapper.eq(BOrder::getOrderType, OrderTypeEnum.DISPATCH);

        // 执行查询
        return executeOrderQuery(queryWrapper, dto.getPageNum(), dto.getPageSize());
    }

    /**
     * 管理端分页查询订单列表
     *
     * @param queryDTO 查询条件
     * @return 订单列表
     */
    public Page<OrderVO> adminPageOrders(OrderQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<BOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BOrder::getDeleted, false);

        // 条件过滤
        if (!StringUtils.isEmpty(queryDTO.getOrderId())) {
            queryWrapper.eq(BOrder::getId, queryDTO.getOrderId())
                    .or()
                    .eq(BOrder::getSysId, queryDTO.getOrderId());
        }

        if (!StringUtils.isEmpty(queryDTO.getMemberId())) {
            queryWrapper.eq(BOrder::getMemberId, queryDTO.getMemberId());
        }

        if (!StringUtils.isEmpty(queryDTO.getMemberName())) {
            List<String> memberIds = memberMapper.selectList(new LambdaQueryWrapper<CMember>()
                    .like(CMember::getNickname, queryDTO.getMemberName())
                    .select(CMember::getId))
                    .stream()
                    .map(CMember::getId)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(memberIds)) {
                queryWrapper.in(BOrder::getMemberId, memberIds);
            } else {
                return new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
            }
        }

        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(BOrder::getStatus, queryDTO.getStatus());
        }

        if (!StringUtils.isEmpty(queryDTO.getTypeId())) {
            queryWrapper.eq(BOrder::getActivityTypeId, queryDTO.getTypeId());
        }

        if (!StringUtils.isEmpty(queryDTO.getCreateTimeStart())) {
            queryWrapper.ge(BOrder::getCreateTime, queryDTO.getCreateTimeStart());
        }

        if (!StringUtils.isEmpty(queryDTO.getCreateTimeEnd())) {
            queryWrapper.le(BOrder::getCreateTime, queryDTO.getCreateTimeEnd());
        }

        // 排序
        if (!StringUtils.isEmpty(queryDTO.getOrderBy())) {
            queryWrapper.orderBy(true,
                    "asc".equalsIgnoreCase(queryDTO.getOrderDirection()),
                    getOrderByColumn(queryDTO.getOrderBy()));
        } else {
            queryWrapper.orderByDesc(BOrder::getCreateTime);
        }

        // 执行查询
        Page<BOrder> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<BOrder> resultPage = orderMapper.selectPage(page, queryWrapper);

        // 转换结果
        List<OrderVO> records = resultPage.getRecords().stream()
                .map(this::convertToOrderVO)
                .collect(Collectors.toList());

        Page<OrderVO> resultVOPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        resultVOPage.setRecords(records);

        return resultVOPage;
    }

    /**
     * 获取排序字段
     */
    private SFunction<BOrder, ?> getOrderByColumn(String orderBy) {
        switch (orderBy) {
            case "createTime":
                return BOrder::getCreateTime;
            case "startTime":
                return BOrder::getStartTime;
            case "hourlyRate":
                return BOrder::getHourlyRate;
            default:
                return BOrder::getCreateTime;
        }
    }

    /**
     * 转换为订单VO
     */
    private OrderVO convertToOrderVO(BOrder order) {
        if (order == null) {
            return null;
        }

        OrderVO vo = new OrderVO();

        // 设置基本字段
        vo.setOrderId(order.getId());
        vo.setTitle(order.getTitle());
        vo.setTypeId(order.getActivityTypeId());
        vo.setMemberId(order.getMemberId());
        vo.setDuration(order.getLongTime() != null ? (int) (order.getLongTime() * 60) : 0); // 转换为分钟
        vo.setHourlyRate(order.getHourlyRate());
        vo.setTotalFee(order.getTotalFee());
        vo.setSexRequire(order.getSexRequire());
        vo.setPersonCount(order.getPersonCount() != null ? order.getPersonCount().intValue() : 0);
        vo.setLocation(order.getLocation());
        vo.setAddress(order.getAddress());
        vo.setStatus(Integer.valueOf(order.getStatus().ordinal()));
        vo.setStatusDesc(order.getStatus().getDesc());
        vo.setStartTime(order.getStartTime());
        vo.setEndTime(order.getEndTime());
        vo.setCreateTime(order.getCreateTime());

        // 获取用户信息
        CMember member = memberMapper.selectById(order.getMemberId());
        if (member != null) {
            vo.setMemberName(member.getNickname());
            vo.setMemberAvatar(member.getAvatar());
        }

        // 获取陪玩类型信息
        BOrderType orderType = orderTypeMapper.selectById(order.getActivityTypeId());
        if (orderType != null) {
            vo.setTypeName(orderType.getName());
        }

        // 获取报名人数
        Integer enrollCount = orderEnrollDetailMapper.selectCount(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, order.getId())
                        .eq(BOrderEnrollDetail::getDeleted, false));
        vo.setEnrollCount(enrollCount);
        
        // 判断是否为即时订单（开始时间在24小时内）
        if (order.getStartTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = order.getStartTime();
            long hoursUntilStart = ChronoUnit.HOURS.between(now, startTime);
            vo.setIsInstant(hoursUntilStart <= 24 && hoursUntilStart >= 0);
        } else {
            vo.setIsInstant(false);
        }

        return vo;
    }
    
    /**
     * 检查并更新过期订单
     * 将结束时间已过且状态为PUBLISHED的订单标记为CANCELED
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkAndUpdateExpiredOrders() {
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // 查询所有已发布但结束时间已过的订单
            List<BOrder> expiredOrders = orderMapper.selectList(
                new LambdaQueryWrapper<BOrder>()
                    .eq(BOrder::getStatus, OrderStatusEnum.PUBLISHED)
                    .lt(BOrder::getEndTime, now)
                    .eq(BOrder::getDeleted, false)
            );
            
            if (!CollectionUtils.isEmpty(expiredOrders)) {
                for (BOrder order : expiredOrders) {
                    // 更新订单状态为已取消
                    order.setStatus(OrderStatusEnum.CANCELED);
                    order.setCancelReason("订单已过期（结束时间已过）");
                    order.setCancelTime(now);
                    order.setUpdateTime(now);
                    orderMapper.updateById(order);
                    
                    // 记录状态变更
                    orderStatusService.recordOrderStatus(order.getId(), OrderStatusEnum.CANCELED);
                    
                    // 通知订单发布者
                    sysMessageService.sendMessage(order.getMemberId(), 
                        "您的订单" + order.getSysId() + "已自动取消（订单已过期）", 
                        MessageType.ORDER_UPDATE);
                    
                    // 通知所有报名的达人
                    List<BOrderEnrollDetail> enrollDetails = orderEnrollDetailMapper.selectList(
                        new LambdaQueryWrapper<BOrderEnrollDetail>()
                            .eq(BOrderEnrollDetail::getOrderId, order.getId())
                            .eq(BOrderEnrollDetail::getDeleted, false)
                    );
                    
                    for (BOrderEnrollDetail detail : enrollDetails) {
                        if (detail.getStatus() != OrderEnrollDetailStatusEnum.CANCELED) {
                            // 更新报名详情状态
                            detail.setStatus(OrderEnrollDetailStatusEnum.CANCELED);
                            detail.setUpdateTime(now);
                            orderEnrollDetailMapper.updateById(detail);
                            
                            // 通知达人
                            sysMessageService.sendMessage(detail.getMemberId(),
                                "订单" + order.getSysId() + "已过期取消",
                                MessageType.ORDER_UPDATE);
                        }
                    }
                }
                
                log.info("检查并更新了{}个过期订单", expiredOrders.size());
            }
        } catch (Exception e) {
            log.error("检查更新过期订单失败", e);
        }
    }

    /**
     * 管理端获取订单详情
     * 
     * @param orderId 订单ID
     * @return 订单详情
     */
    public OrderDetailVO adminGetOrderDetail(String orderId) {
        // 获取订单信息
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }

        // 转换基本信息
        OrderDetailVO detailVO = new OrderDetailVO();
        BeanUtils.copyProperties(convertToOrderVO(order), detailVO);

        // 补充详情信息
        detailVO.setDescription(order.getDescription());
        detailVO.setLongitude(order.getLongitude());
        detailVO.setLatitude(order.getLatitude());
        detailVO.setAuditResult(order.getAuditResult());
        detailVO.setAuditComment(order.getAuditComment());
        detailVO.setAuditTime(order.getAuditTime());
        detailVO.setAuditorId(order.getAuditorId());
        detailVO.setCancelReason(order.getCancelReason());
        detailVO.setCancelTime(order.getCancelTime());
        detailVO.setGoldCost(order.getGoldCost() != null ? order.getGoldCost().intValue() : null);
        detailVO.setUpdateTime(order.getUpdateTime());

        // 获取会员信息
        CMember member = memberMapper.selectById(order.getMemberId());
        if (member != null) {
            detailVO.setMemberNickname(member.getNickname());
            detailVO.setMemberAvatar(member.getAvatar());
        }

        // 获取审核人信息
        if (order.getAuditorId() != null) {
            CMember auditor = memberMapper.selectById(order.getAuditorId());
            if (auditor != null) {
                detailVO.setAuditorName(auditor.getNickname());
            }
        }

        // 获取活动类型名称
        BOrderType orderType = orderTypeMapper.selectById(order.getActivityTypeId());
        if (orderType != null) {
            detailVO.setActivityTypeName(orderType.getName());
        }

        // 获取报名达人列表
        List<BOrderEnrollDetail> enrollDetails = orderEnrollDetailMapper.selectList(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getOrderId, orderId)
                        .eq(BOrderEnrollDetail::getDeleted, false));

        if (!CollectionUtils.isEmpty(enrollDetails)) {
            // 获取达人ID列表
            List<String> talentIds = enrollDetails.stream()
                    .map(BOrderEnrollDetail::getMemberId)
                    .collect(Collectors.toList());

            // 批量查询达人信息
            Map<String, CMember> memberMap = memberMapper.selectList(
                    new LambdaQueryWrapper<CMember>()
                            .in(CMember::getId, talentIds))
                    .stream()
                    .collect(Collectors.toMap(CMember::getId, Function.identity(), (o1, o2) -> o1));

            // 转换为VO
            List<OrderEnrollVO> enrollVOList = new ArrayList<>();
            List<OrderEnrollVO> selectedVOList = new ArrayList<>();

            // 转换为详情VO
            List<OrderEnrollDetailVO> enrollDetailVOList = new ArrayList<>();

            for (BOrderEnrollDetail detail : enrollDetails) {
                OrderEnrollVO enrollVO = convertToEnrollVO(detail, memberMap.get(detail.getMemberId()));
                enrollVOList.add(enrollVO);

                if (detail.getSelected() != null && detail.getSelected()) {
                    selectedVOList.add(enrollVO);
                }

                // 转换为详情VO
                OrderEnrollDetailVO detailVO2 = new OrderEnrollDetailVO();
                detailVO2.setId(detail.getId());
                detailVO2.setOrderId(detail.getOrderId());
                detailVO2.setMemberId(detail.getMemberId());

                CMember talentMember = memberMap.get(detail.getMemberId());
                if (talentMember != null) {
                    detailVO2.setMemberNickname(talentMember.getNickname());
                    detailVO2.setMemberAvatar(talentMember.getAvatar());
                    detailVO2.setGender(talentMember.getGender());
                    detailVO2.setAge(talentMember.getAge());
                    detailVO2.setConstellation(talentMember.getConstellation());
                }

                detailVO2.setIsSelect(detail.getIsSelect());
                detailVO2.setStatus(detail.getStatus());
                detailVO2.setStatusDesc(detail.getStatus() != null ? detail.getStatus().getDesc() : "");
                detailVO2.setCreateTime(detail.getCreateTime());
                detailVO2.setSelectTime(detail.getSelectTime());
                detailVO2.setDepartureTime(detail.getDepartureTime());
                detailVO2.setArrivalTime(detail.getArrivalTime());
                detailVO2.setServiceStartTime(detail.getServiceStartTime());
                detailVO2.setServiceEndTime(detail.getServiceEndTime());

                // 计算剩余服务时间
                if (detail.getServiceStartTime() != null && order.getLongTime() != null) {
                    long serviceMinutes = (long) (order.getLongTime() * 60);
                    long passedMinutes = 0;
                    if (detail.getServiceEndTime() != null) {
                        passedMinutes = ChronoUnit.MINUTES.between(detail.getServiceStartTime(),
                                detail.getServiceEndTime());
                    } else {
                        passedMinutes = ChronoUnit.MINUTES.between(detail.getServiceStartTime(), LocalDateTime.now());
                    }
                    long remainingMinutes = Math.max(0, serviceMinutes - passedMinutes);
                    detailVO2.setRemainingMinutes(remainingMinutes);
                }

                enrollDetailVOList.add(detailVO2);
            }

            // 更新到订单详情中
            detailVO.setEnrollList(enrollVOList);
            detailVO.setSelectedList(selectedVOList);
            detailVO.setEnrollDetails(enrollDetailVOList);
            
            // 同时设置appliedExperts字段，确保与前端兼容
            detailVO.setAppliedExperts(enrollVOList);
            
            // 重新设置enrollCount，确保与实际报名列表数量一致
            detailVO.setEnrollCount(enrollVOList.size());
        } else {
            // 如果没有报名记录，确保enrollCount为0
            detailVO.setEnrollCount(0);
        }

        return detailVO;
    }

    /**
     * 转换为报名VO
     */
    private OrderEnrollVO convertToEnrollVO(BOrderEnrollDetail detail, CMember member) {
        if (detail == null) {
            return null;
        }

        OrderEnrollVO vo = new OrderEnrollVO();
        vo.setEnrollId(detail.getId());
        vo.setOrderId(detail.getOrderId());
        vo.setTalentId(detail.getMemberId());
        vo.setMemberId(detail.getMemberId()); // 设置memberId，与talentId保持一致，便于前端统一判断
        vo.setIsSelected(detail.getSelected() != null && detail.getSelected());
        vo.setStatus(detail.getStatus() != null ? detail.getStatus().ordinal() : 0);
        vo.setStatusDesc(detail.getStatus() != null ? detail.getStatus().getDesc() : "");
        vo.setCreateTime(detail.getCreateTime());
        vo.setSelectTime(detail.getSelectTime());
        vo.setDepartureTime(detail.getDepartureTime());
        vo.setArrivalTime(detail.getArrivalTime());
        vo.setServiceStartTime(detail.getServiceStartTime());
        vo.setServiceEndTime(detail.getServiceEndTime());

        // 填充会员信息
        if (member != null) {
            vo.setTalentName(member.getNickname());
            vo.setTalentAvatar(member.getAvatar());
            vo.setGender(member.getGender());
            vo.setAge(member.getAge()); // 直接使用字符串
            vo.setConstellation(member.getConstellation());
        }

        return vo;
    }

    /**
     * 管理端审核订单
     *
     * @param auditDTO 审核参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditOrder(AuditOrderDTO auditDTO) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(auditDTO.getOrderId());
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }

        // 2.校验订单状态
        if (order.getStatus() != OrderStatusEnum.AUDITING) {
            throw new GlobalException(400, "只有待审核状态的订单才能审核");
        }

        // 3.更新订单状态
        OrderStatusEnum newStatus;
        if (auditDTO.getResult() == 1) {
            // 审核通过，更新为已发布状态
            newStatus = OrderStatusEnum.PUBLISHED;
        } else {
            // 审核拒绝，更新为草稿状态
            newStatus = OrderStatusEnum.DRAFT;
        }

        order.setStatus(newStatus);
        order.setUpdateTime(LocalDateTime.now());
        order.setAuditTime(LocalDateTime.now());
        order.setAuditComment(auditDTO.getComment());
        order.setAuditResult(auditDTO.getResult());
        order.setAuditorId(auditDTO.getAuditorId());

        if (auditDTO.getResult() == 1 && auditDTO.getGoldCost() != null) {
            order.setGoldCost(auditDTO.getGoldCost());
        }

        orderMapper.updateById(order);

        // 4.记录状态变更
        orderStatusService.recordOrderStatus(auditDTO.getOrderId(), newStatus);

        // 5.发送系统消息
        String message = auditDTO.getResult() == 1 ? "您的订单" + order.getSysId() + "已通过审核，现在可以接收达人报名了"
                : "您的订单" + order.getSysId() + "未通过审核，原因："
                        + (auditDTO.getComment() != null ? auditDTO.getComment() : "未通过审核");

        sysMessageService.sendMessage(order.getMemberId(), message, MessageType.ORDER_AUDIT);

        return true;
    }

    /**
     * 管理端取消订单
     *
     * @param orderId 订单ID
     * @param reason  取消原因
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean adminCancelOrder(String orderId, String reason) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }

        // 2.校验订单状态
        if (order.getStatus() == OrderStatusEnum.COMPLETED || order.getStatus() == OrderStatusEnum.CANCELED) {
            throw new GlobalException(400, "已完成或已取消的订单不能取消");
        }

        // 3.更新订单状态
        order.setStatus(OrderStatusEnum.CANCELED);
        order.setCancelTime(LocalDateTime.now());
        order.setCancelReason(reason != null ? reason : "管理员取消订单");
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 4.记录状态变更
        orderStatusService.recordOrderStatus(orderId, OrderStatusEnum.CANCELED);

        // 5.发送系统消息
        String message = "您的订单" + order.getSysId() + "已被管理员取消，原因：" + (reason != null ? reason : "管理员取消订单");
        sysMessageService.sendMessage(order.getMemberId(), message, MessageType.ORDER_CANCEL);

        return true;
    }

    /**
     * 获取用户订单统计数据
     *
     * @param memberId 会员ID
     * @return 统计数据
     */
    public Map<String, Object> getUserOrderStatistics(String memberId) {
        Map<String, Object> result = new HashMap<>();

        // 1.获取用户发布的订单统计
        long publishedCount = orderMapper.selectCount(
                new LambdaQueryWrapper<BOrder>()
                        .eq(BOrder::getMemberId, memberId)
                        .eq(BOrder::getDeleted, false));

        // 2.获取用户发布的进行中订单数量
        long ongoingCount = orderMapper.selectCount(
                new LambdaQueryWrapper<BOrder>()
                        .eq(BOrder::getMemberId, memberId)
                        .eq(BOrder::getStatus, OrderStatusEnum.PROCESSING)
                        .eq(BOrder::getDeleted, false));

        // 3.获取用户发布的已完成订单数量
        long completedCount = orderMapper.selectCount(
                new LambdaQueryWrapper<BOrder>()
                        .eq(BOrder::getMemberId, memberId)
                        .eq(BOrder::getStatus, OrderStatusEnum.COMPLETED)
                        .eq(BOrder::getDeleted, false));

        // 4.获取用户参与的订单数量（作为达人）
        long participatedCount = orderEnrollDetailMapper.selectCount(
                new LambdaQueryWrapper<BOrderEnrollDetail>()
                        .eq(BOrderEnrollDetail::getMemberId, memberId)
                        .eq(BOrderEnrollDetail::getDeleted, false));

        result.put("publishedCount", publishedCount);
        result.put("ongoingCount", ongoingCount);
        result.put("completedCount", completedCount);
        result.put("participatedCount", participatedCount);

        return result;
    }

    /**
     * 管理端获取订单评价
     *
     * @param orderId  订单ID
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return 评价列表
     */
    public Page<Object> adminGetOrderComments(String orderId, Integer pageNum, Integer pageSize) {
        // 此处需要根据具体的评价表结构实现
        // 假设有一个OrderComment表

        // 模拟返回
        Page<Object> page = new Page<>(pageNum, pageSize, 0);
        page.setRecords(new ArrayList<>());
        return page;

        // 实际实现应该类似如下：
        /*
         * Page<BOrderComment> commentPage = new Page<>(pageNum, pageSize);
         * LambdaQueryWrapper<BOrderComment> queryWrapper = new LambdaQueryWrapper<>();
         * queryWrapper.eq(BOrderComment::getOrderId, orderId);
         * queryWrapper.orderByDesc(BOrderComment::getCreateTime);
         * 
         * Page<BOrderComment> resultPage = orderCommentMapper.selectPage(commentPage,
         * queryWrapper);
         * 
         * // 转换为VO
         * List<OrderCommentVO> records = resultPage.getRecords().stream()
         * .map(this::convertToCommentVO)
         * .collect(Collectors.toList());
         * 
         * Page<OrderCommentVO> resultVOPage = new Page<>(resultPage.getCurrent(),
         * resultPage.getSize(), resultPage.getTotal());
         * resultVOPage.setRecords(records);
         * 
         * return resultVOPage;
         */
    }

    /**
     * 获取订单统计数据（管理端）
     *
     * @return 统计数据
     */
    public Map<String, Object> getOrderStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 总订单数
            long totalOrders = orderMapper.selectCount(
                    new LambdaQueryWrapper<BOrder>()
                            .eq(BOrder::getDeleted, false));
            statistics.put("totalOrders", totalOrders);

            // 进行中订单数
            long processingOrders = orderMapper.selectCount(
                    new LambdaQueryWrapper<BOrder>()
                            .eq(BOrder::getDeleted, false)
                            .eq(BOrder::getStatus, OrderStatusEnum.PROCESSING));
            statistics.put("processingOrders", processingOrders);

            // 已完成订单数
            long completedOrders = orderMapper.selectCount(
                    new LambdaQueryWrapper<BOrder>()
                            .eq(BOrder::getDeleted, false)
                            .eq(BOrder::getStatus, OrderStatusEnum.COMPLETED));
            statistics.put("completedOrders", completedOrders);

            // 用户总数
            long totalUsers = memberMapper.selectCount(
                    new LambdaQueryWrapper<CMember>()
                            .eq(CMember::getDeleted, false));
            statistics.put("totalUsers", totalUsers);

            // 今日订单数
            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            long todayOrders = orderMapper.selectCount(
                    new LambdaQueryWrapper<BOrder>()
                            .eq(BOrder::getDeleted, false)
                            .ge(BOrder::getCreateTime, today));
            statistics.put("todayOrders", todayOrders);

            // 今日新增用户数
            long todayNewUsers = memberMapper.selectCount(
                    new LambdaQueryWrapper<CMember>()
                            .eq(CMember::getDeleted, false)
                            .ge(CMember::getCreateTime, today));
            statistics.put("todayNewUsers", todayNewUsers);

            // 获取本月订单趋势数据
            Map<String, Object> orderTrend = getOrderTrendData("month", null, null);
            statistics.put("orderTrend", orderTrend);

            // 获取订单类型分布数据
            List<Map<String, Object>> orderTypeDistribution = getOrderTypeDistribution(null, null);
            statistics.put("orderTypeDistribution", orderTypeDistribution);

            // 打印日志，方便调试
            System.out.println("统计数据: " + statistics);
        } catch (Exception e) {
            e.printStackTrace();
            // 发生异常时返回默认值
            statistics.put("totalOrders", 0);
            statistics.put("processingOrders", 0);
            statistics.put("completedOrders", 0);
            statistics.put("totalUsers", 0);
            statistics.put("todayOrders", 0);
            statistics.put("todayNewUsers", 0);
        }

        return statistics;
    }

    /**
     * 获取订单趋势数据
     *
     * @param period    周期类型：week-本周, month-本月, year-本年, custom-自定义
     * @param startDate 开始日期 (yyyy-MM-dd)，仅当period为custom时有效
     * @param endDate   结束日期 (yyyy-MM-dd)，仅当period为custom时有效
     * @return 趋势数据
     */
    public Map<String, Object> getOrderTrendData(String period, String startDate, String endDate) {
        Map<String, Object> result = new HashMap<>();
        List<String> dateLabels = new ArrayList<>();
        List<Long> orderCounts = new ArrayList<>();

        // 确定开始和结束时间
        LocalDateTime startDateTime;
        LocalDateTime endDateTime = LocalDateTime.now();
        String groupBy = "day"; // 默认按天分组

        switch (period) {
            case "week":
                // 本周数据
                startDateTime = endDateTime.with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0).withNano(0);
                groupBy = "day";
                break;
            case "month":
                // 本月数据
                startDateTime = endDateTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                groupBy = "day";
                break;
            case "year":
                // 本年数据
                startDateTime = endDateTime.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                groupBy = "month";
                break;
            case "custom":
                // 自定义时间范围
                if (startDate != null && !startDate.isEmpty()) {
                    startDateTime = LocalDate.parse(startDate).atStartOfDay();
                } else {
                    // 默认最近7天
                    startDateTime = endDateTime.minusDays(7).withHour(0).withMinute(0).withSecond(0).withNano(0);
                }

                if (endDate != null && !endDate.isEmpty()) {
                    endDateTime = LocalDate.parse(endDate).plusDays(1).atStartOfDay();
                }

                // 根据时间跨度决定分组方式
                long days = ChronoUnit.DAYS.between(startDateTime, endDateTime);
                if (days > 60) {
                    groupBy = "month";
                } else {
                    groupBy = "day";
                }
                break;
            default:
                // 默认最近7天
                startDateTime = endDateTime.minusDays(7).withHour(0).withMinute(0).withSecond(0).withNano(0);
                groupBy = "day";
        }

        // 查询时间范围内的订单数据
        if ("month".equals(groupBy)) {
            // 按月分组
            Map<String, Long> monthlyData = orderMapper.selectList(
                    new LambdaQueryWrapper<BOrder>()
                            .eq(BOrder::getDeleted, false)
                            .between(BOrder::getCreateTime, startDateTime, endDateTime))
                    .stream()
                    .collect(Collectors.groupingBy(
                            order -> order.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM")),
                            Collectors.counting()));

            // 填充所有月份数据
            LocalDateTime current = startDateTime;
            while (current.isBefore(endDateTime)) {
                String monthKey = current.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                dateLabels.add(current.format(DateTimeFormatter.ofPattern("MM月")));
                orderCounts.add(monthlyData.getOrDefault(monthKey, 0L));
                current = current.plusMonths(1);
            }
        } else {
            // 按天分组
            Map<String, Long> dailyData = orderMapper.selectList(
                    new LambdaQueryWrapper<BOrder>()
                            .eq(BOrder::getDeleted, false)
                            .between(BOrder::getCreateTime, startDateTime, endDateTime))
                    .stream()
                    .collect(Collectors.groupingBy(
                            order -> order.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                            Collectors.counting()));

            // 填充所有日期数据
            LocalDateTime current = startDateTime;
            while (current.isBefore(endDateTime)) {
                String dateKey = current.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                dateLabels.add(current.format(DateTimeFormatter.ofPattern("MM-dd")));
                orderCounts.add(dailyData.getOrDefault(dateKey, 0L));
                current = current.plusDays(1);
            }
        }

        result.put("dateLabels", dateLabels);
        result.put("orderCounts", orderCounts);
        result.put("groupBy", groupBy);

        return result;
    }

    /**
     * 获取订单类型分布数据
     *
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return 订单类型分布数据
     */
    public List<Map<String, Object>> getOrderTypeDistribution(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        // 构建查询条件
        LambdaQueryWrapper<BOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BOrder::getDeleted, false);

        // 添加时间范围条件
        if (startDateTime != null) {
            queryWrapper.ge(BOrder::getCreateTime, startDateTime);
        }
        if (endDateTime != null) {
            queryWrapper.le(BOrder::getCreateTime, endDateTime);
        }

        // 查询订单数据
        List<BOrder> orders = orderMapper.selectList(queryWrapper);

        // 按活动类型分组并计数
        Map<String, Long> typeCountMap = orders.stream()
                .collect(Collectors.groupingBy(
                        BOrder::getActivityTypeId,
                        Collectors.counting()));

        // 查询所有活动类型
        List<BOrderType> activityTypes = orderTypeMapper.selectList(
                new LambdaQueryWrapper<BOrderType>()
                        .eq(BOrderType::getDeleted, false));

        // 构建结果
        List<Map<String, Object>> result = new ArrayList<>();

        // 将活动类型ID转换为名称，并添加到结果集
        for (Map.Entry<String, Long> entry : typeCountMap.entrySet()) {
            String typeId = entry.getKey();
            Long count = entry.getValue();

            // 查找活动类型名称
            String typeName = activityTypes.stream()
                    .filter(type -> type.getId().equals(typeId))
                    .map(BOrderType::getName)
                    .findFirst()
                    .orElse("未知类型");

            Map<String, Object> typeData = new HashMap<>();
            typeData.put("name", typeName);
            typeData.put("value", count);
            result.add(typeData);
        }

        // 如果没有数据，添加一个空类型
        if (result.isEmpty()) {
            Map<String, Object> emptyData = new HashMap<>();
            emptyData.put("name", "暂无数据");
            emptyData.put("value", 0);
            result.add(emptyData);
        }

        return result;
    }

    /**
     * 根据时间范围获取订单统计数据
     *
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate   结束日期 (yyyy-MM-dd)
     * @return 统计数据
     */
    public Map<String, Object> getOrderStatisticsByDateRange(String startDate, String endDate) {
        Map<String, Object> statistics = new HashMap<>();

        // 转换日期参数
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        if (startDate != null && !startDate.isEmpty()) {
            startDateTime = LocalDate.parse(startDate).atStartOfDay();
        }

        if (endDate != null && !endDate.isEmpty()) {
            // 结束日期加1天，以包含当天的数据
            endDateTime = LocalDate.parse(endDate).plusDays(1).atStartOfDay();
        } else {
            // 如果没有指定结束日期，则默认为当前时间
            endDateTime = LocalDateTime.now();
        }

        // 构建查询条件
        LambdaQueryWrapper<BOrder> orderQueryWrapper = new LambdaQueryWrapper<BOrder>()
                .eq(BOrder::getDeleted, false);

        // 添加时间范围条件
        if (startDateTime != null) {
            orderQueryWrapper.ge(BOrder::getCreateTime, startDateTime);
        }
        if (endDateTime != null) {
            orderQueryWrapper.le(BOrder::getCreateTime, endDateTime);
        }

        // 总订单数
        long totalOrders = orderMapper.selectCount(orderQueryWrapper);
        statistics.put("totalOrders", totalOrders);

        // 进行中订单数
        long processingOrders = orderMapper.selectCount(
                orderQueryWrapper.clone()
                        .eq(BOrder::getStatus, OrderStatusEnum.PROCESSING));
        statistics.put("processingOrders", processingOrders);

        // 已完成订单数
        long completedOrders = orderMapper.selectCount(
                orderQueryWrapper.clone()
                        .eq(BOrder::getStatus, OrderStatusEnum.COMPLETED));
        statistics.put("completedOrders", completedOrders);

        // 用户查询条件
        LambdaQueryWrapper<CMember> memberQueryWrapper = new LambdaQueryWrapper<CMember>()
                .eq(CMember::getDeleted, false);

        // 添加用户时间范围条件
        if (startDateTime != null) {
            memberQueryWrapper.ge(CMember::getCreateTime, startDateTime);
        }
        if (endDateTime != null) {
            memberQueryWrapper.le(CMember::getCreateTime, endDateTime);
        }

        // 用户总数 - 这里返回所有用户，不考虑时间范围
        long totalUsers = memberMapper.selectCount(
                new LambdaQueryWrapper<CMember>()
                        .eq(CMember::getDeleted, false));
        statistics.put("totalUsers", totalUsers);

        // 新增用户数 - 在指定时间范围内新增的用户
        long newUsers = 0;
        if (startDateTime != null || endDateTime != null) {
            newUsers = memberMapper.selectCount(memberQueryWrapper);
        }
        statistics.put("newUsers", newUsers);

        // 获取时间范围内的订单趋势数据
        statistics.put("orderTrend", getOrderTrendData("custom", startDate, endDate));

        // 获取时间范围内的订单类型分布
        statistics.put("orderTypeDistribution", getOrderTypeDistribution(startDateTime, endDateTime));

        return statistics;
    }

    /**
     * 管理员选择达人
     *
     * @param orderId   订单ID
     * @param memberIds 达人ID列表
     * @param adminId   管理员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean adminSelectTalents(String orderId, List<String> memberIds, String adminId) {
        // 1.获取订单信息
        BOrder order = orderMapper.selectById(orderId);
        if (order == null || order.getDeleted()) {
            throw new GlobalException(400, "订单不存在");
        }

        // 2.校验订单状态
        if (order.getStatus() != OrderStatusEnum.PUBLISHED && order.getStatus() != OrderStatusEnum.WAITING_SELECT) {
            throw new GlobalException(400, "只有发布中或待选择达人状态的订单才能选择达人");
        }

        // 3.校验达人是否已报名
        for (String memberId : memberIds) {
            BOrderEnrollDetail detail = orderEnrollDetailMapper.selectOne(
                    new LambdaQueryWrapper<BOrderEnrollDetail>()
                            .eq(BOrderEnrollDetail::getOrderId, orderId)
                            .eq(BOrderEnrollDetail::getMemberId, memberId)
                            .eq(BOrderEnrollDetail::getDeleted, false));

            if (detail == null) {
                throw new GlobalException(400, "部分达人未报名");
            }

            // 4.更新达人状态
            detail.setSelected(true);
            detail.setIsSelect(true);
            detail.setSelectTime(LocalDateTime.now());
            detail.setStatus(OrderEnrollDetailStatusEnum.SELECTED);
            detail.setUpdateTime(LocalDateTime.now());
            orderEnrollDetailMapper.updateById(detail);

            // 5.记录状态变更
            orderStatusService.recordOrderDetailStatus(detail.getId(), OrderEnrollDetailStatusEnum.SELECTED);

            // 6.通知达人
            sysMessageService.sendMessage(detail.getMemberId(),
                    "您的报名已被选中，请准备开始服务",
                    MessageType.ORDER_UPDATE);
        }

        // 7.更新订单状态为进行中
        order.setStatus(OrderStatusEnum.PROCESSING);
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 8.记录状态变更
        orderStatusService.recordOrderStatus(orderId, OrderStatusEnum.PROCESSING);

        return true;
    }
}
