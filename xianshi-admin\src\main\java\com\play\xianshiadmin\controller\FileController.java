package com.play.xianshiadmin.controller;

import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/admin")
@Api(tags = "文件上传API")
public class FileController {
    
    private static final Logger logger = LoggerFactory.getLogger(FileController.class);
    
    @Value("${file.upload.path:./upload}")
    private String uploadPath;
    
    @Value("${file.access.path:/upload}")
    private String accessPath;
    
    /**
     * 上传文件
     * @param file 文件
     * @param type 文件类型
     * @return 文件访问路径
     */
    @PostMapping("/upload")
    @ApiOperation(value = "上传文件")
    @RequireAdmin
    public Result<String> uploadFile(
            @ApiParam(value = "文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "文件类型") @RequestParam(value = "type", defaultValue = "common") String type) {
        
        logger.info("开始上传文件，类型: {}, 文件名: {}, 大小: {}", type, file.getOriginalFilename(), file.getSize());
        
        if (file.isEmpty()) {
            logger.error("上传失败：文件为空");
            return ResultUtils.error(400, "文件不能为空");
        }
        
        // 创建目录
        String datePath = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String dirPath = uploadPath + "/" + type + "/" + datePath;
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean mkdirResult = dir.mkdirs();
            logger.info("创建目录: {}, 结果: {}", dirPath, mkdirResult);
            if (!mkdirResult) {
                logger.error("创建目录失败: {}", dirPath);
                return ResultUtils.error(500, "创建上传目录失败");
            }
        }
        
        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        String filename = UUID.randomUUID().toString().replaceAll("-", "") + suffix;
        
        // 保存文件
        try {
            File targetFile = new File(dirPath + "/" + filename);
            logger.info("保存文件到: {}", targetFile.getAbsolutePath());
            file.transferTo(targetFile);
            
            // 检查文件是否成功保存
            if (!targetFile.exists()) {
                logger.error("文件保存失败，文件不存在: {}", targetFile.getAbsolutePath());
                return ResultUtils.error(500, "文件保存失败");
            }
            
            // 返回文件访问路径
            String url = accessPath + "/" + type + "/" + datePath + "/" + filename;
            logger.info("文件上传成功，访问路径: {}", url);
            return ResultUtils.success(url);
        } catch (IOException e) {
            logger.error("文件上传失败", e);
            return ResultUtils.error(500, "文件上传失败: " + e.getMessage());
        }
    }
} 