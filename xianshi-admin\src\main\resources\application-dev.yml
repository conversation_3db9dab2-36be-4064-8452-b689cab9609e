# 开发环境配置---采用本地配置文件模式
server:
  # 服务器的HTTP端口，默认为8082
  port: 9999
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    # Tomcat启动初始化的线程数，默认值25
    threads:
      max: 800
      min-spare: 30

# WebSocket配置
websocket:
  # WebSocket服务端口
  port: 9012
  # 心跳超时时间(秒)
  heartbeat: 60
  # 连接超时时间(秒)
  connection-timeout: 30

app:
  member:
    defaultAvatar: /static/images/default-avatar.png
    defaultNickname: 默认昵称

mybatis-plus:
  configuration:
    mapUnderscoreToCamelCase: true
    # 打印SQL日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
# 日志配置
logging:
  level:
    com.kiwi: debug
    org.springframework: warn
    # 打印SQL日志
    com.play.xianshibusiness.mapper: debug
    com.play.xianshiapp.mapper: debug
    druid:
      sql:
        Statement: debug
    # 添加下面几行，开启身份验证和JWT相关日志
    com.play.xianshibusiness.aspect: debug
    com.play.xianshibusiness.utils.JwtSignUtils: debug
    com.play.xianshibusiness.exception: debug
    org.springframework.security: debug
# Spring配置
spring:
  resources:
    static-locations: file:resources,classpath:/template/,classpath:/resources/upload/image/
  thymeleaf:
    prefix: classpath:/template
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  # 数据表迁移
  flyway:
    enabled: false
    baseline-on-migrate: true
    clean-on-validation-error: false
    out-of-order: true
    sql-migration-prefix: V
    sql-migration-suffixes: .sql
    locations: classpath:db/migration
  main:
    allow-bean-definition-overriding: true
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 50MB
      # 设置总上传的文件大小
      max-request-size: 50MB
  datasource:
    db-type: mysql
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************
    username: xianshi-app
    password: 123456
#    url: ****************************************************************************************************************************************************
#    username: root
#    password: root
    name: dev_db
    initial-size: 20
    min-idle: 5
    max-active: 100
    #是否要加密  解密算法需要继承DecryptDatabasePWDInterface这个方法进行实现
    isencrypt: N
    #获取连接等待超时时间
    max-wait: 60000

  # redis 配置
  redis:
    #Redis 单机模式
    host: *************
    port: 6379
    password: su123456
    lettuce:
      pool:
        max-active: 100
        max-idle: 100
        max-wait: 3000
        min-idle: 0
      shutdown-timeout: 10000
    database: 6

xss:
  enabled: false
  excludes:
  urlPatterns: /*

# 文件上传配置
file:
  upload:
    path: E:/code/闲时APP/xianshi-app-new/upload
  access:
    path: /upload



