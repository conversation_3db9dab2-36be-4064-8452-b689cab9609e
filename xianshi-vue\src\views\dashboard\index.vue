<template>
  <div class="dashboard-container">
    <div class="dashboard-header clay-card">
      <div class="header-left">
        <h2 class="dashboard-title">欢迎使用闲时有伴后台管理系统</h2>
        <div class="dashboard-date">{{ currentDate }}</div>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="handleDateRangeChange"
          size="small">
        </el-date-picker>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
      </div>
    </div>
    
    <el-row :gutter="20" class="statistics-row">
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card shadow="hover" class="statistics-card clay-card">
          <div class="statistics-icon" style="background-color: rgba(248, 208, 16, 0.2)">
            <i class="el-icon-s-order" style="color: #F8D010"></i>
          </div>
          <div class="statistics-content">
            <div class="statistics-title">订单总数</div>
            <div class="statistics-number">{{ statisticsData.totalOrders }}</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card shadow="hover" class="statistics-card clay-card">
          <div class="statistics-icon" style="background-color: rgba(248, 208, 16, 0.2)">
            <i class="el-icon-s-cooperation" style="color: #F8D010"></i>
          </div>
          <div class="statistics-content">
            <div class="statistics-title">进行中订单</div>
            <div class="statistics-number">{{ statisticsData.processingOrders }}</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card shadow="hover" class="statistics-card clay-card">
          <div class="statistics-icon" style="background-color: rgba(248, 208, 16, 0.2)">
            <i class="el-icon-user" style="color: #F8D010"></i>
          </div>
          <div class="statistics-content">
            <div class="statistics-title">用户数量</div>
            <div class="statistics-number">{{ statisticsData.totalUsers }}</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card shadow="hover" class="statistics-card clay-card">
          <div class="statistics-icon" style="background-color: rgba(248, 208, 16, 0.2)">
            <i class="el-icon-s-check" style="color: #F8D010"></i>
          </div>
          <div class="statistics-content">
            <div class="statistics-title">已完成订单</div>
            <div class="statistics-number">{{ statisticsData.completedOrders }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card clay-card">
          <div slot="header" class="chart-header">
            <span>订单趋势</span>
            <el-radio-group v-model="chartDateRange" size="mini" @change="handleChartRangeChange">
              <el-radio-button label="week">本周</el-radio-button>
              <el-radio-button label="month">本月</el-radio-button>
              <el-radio-button label="year">本年</el-radio-button>
            </el-radio-group>
          </div>
          <div class="chart-container" ref="orderChart"></div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card clay-card">
          <div slot="header" class="chart-header">
            <span>订单类型分布</span>
          </div>
          <div class="chart-container" ref="orderTypeChart"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { 
  getOrderStatistics, 
  getOrderStatisticsByDateRange,
  getOrderTrendData,
  getOrderTypeDistribution
} from '@/api/order';
import request from '@/utils/request';

export default {
  name: 'Dashboard',
  data() {
    return {
      currentDate: this.formatDate(new Date()),
      dateRange: null,
      statisticsData: {
        totalOrders: 0,
        processingOrders: 0,
        totalUsers: 0,
        completedOrders: 0
      },
      chartDateRange: 'month',
      orderChart: null,
      orderTypeChart: null,
      loading: false,
      orderTrendData: {
        dateLabels: [],
        orderCounts: [],
        groupBy: 'day'
      },
      orderTypeData: [],
      refreshInterval: null
    };
  },
  created() {
    this.getStatistics();
    // 添加定时刷新，每60秒刷新一次数据
    this.refreshInterval = setInterval(() => {
      this.getStatistics();
    }, 60000);
  },
  mounted() {
    this.initCharts();
    
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.orderChart) {
      this.orderChart.dispose();
    }
    if (this.orderTypeChart) {
      this.orderTypeChart.dispose();
    }
    // 清除定时器
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
  },
  methods: {
    formatDate(date) {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    },
    getStatistics() {
      this.loading = true;
      console.log('正在获取统计数据...');
      
      // 打印请求配置
      const requestConfig = request.defaults;
      console.log('请求配置:', {
        baseURL: requestConfig.baseURL,
        timeout: requestConfig.timeout,
        headers: requestConfig.headers
      });
      
      // 根据是否选择了日期范围，调用不同的API
      if (this.dateRange && this.dateRange.length === 2) {
        getOrderStatisticsByDateRange(this.dateRange[0], this.dateRange[1]).then(response => {
          console.log('获取到日期范围统计数据:', response);
          if (response && response.data) {
            // 直接赋值，不做类型转换
            this.statisticsData = {
              totalOrders: response.data.totalOrders,
              processingOrders: response.data.processingOrders,
              totalUsers: response.data.totalUsers,
              completedOrders: response.data.completedOrders
            };
            console.log('处理后的统计数据:', this.statisticsData);
            
            // 更新图表数据
            if (response.data.orderTrend) {
              this.orderTrendData = response.data.orderTrend;
              this.updateOrderChart();
            } else {
              console.warn('返回数据中缺少orderTrend');
              // 单独获取趋势数据
              this.getOrderTrendData();
            }
            
            if (response.data.orderTypeDistribution) {
              this.orderTypeData = response.data.orderTypeDistribution;
              this.updateOrderTypeChart();
            } else {
              console.warn('返回数据中缺少orderTypeDistribution');
              // 单独获取类型分布数据
              this.getOrderTypeDistribution();
            }
          } else {
            console.error('返回的数据格式不正确:', response);
            // 加载模拟数据以保证UI显示
            this.loadMockData();
          }
          this.loading = false;
        }).catch(error => {
          console.error('获取日期范围统计数据失败:', error);
          this.loading = false;
          // 显示错误提示
          this.$message.error('获取统计数据失败，请稍后重试');
          // 加载模拟数据以保证UI显示
          this.loadMockData();
        });
      } else {
        getOrderStatistics().then(response => {
          console.log('获取到统计数据:', response);
          if (response && response.data) {
            // 直接赋值，不做类型转换
            this.statisticsData = {
              totalOrders: response.data.totalOrders,
              processingOrders: response.data.processingOrders,
              totalUsers: response.data.totalUsers,
              completedOrders: response.data.completedOrders
            };
            console.log('处理后的统计数据:', this.statisticsData);
            
            // 更新图表数据
            if (response.data.orderTrend) {
              this.orderTrendData = response.data.orderTrend;
              this.updateOrderChart();
            } else {
              console.warn('返回数据中缺少orderTrend');
              // 如果API没有返回趋势数据，则单独获取
              this.getOrderTrendData();
            }
            
            if (response.data.orderTypeDistribution) {
              this.orderTypeData = response.data.orderTypeDistribution;
              this.updateOrderTypeChart();
            } else {
              console.warn('返回数据中缺少orderTypeDistribution');
              // 如果API没有返回类型分布数据，则单独获取
              this.getOrderTypeDistribution();
            }
          } else {
            console.error('返回的数据格式不正确:', response);
            // 加载模拟数据以保证UI显示
            this.loadMockData();
          }
          this.loading = false;
        }).catch(error => {
          console.error('获取统计数据失败:', error);
          this.loading = false;
          // 显示错误提示
          this.$message.error('获取统计数据失败，请稍后重试');
          
          // 加载模拟数据以保证UI显示
          this.loadMockData();
        });
      }
    },
    handleResize() {
      console.log('窗口大小改变，重新调整图表大小');
      // 使用nextTick确保DOM已更新
      this.$nextTick(() => {
        if (this.orderChart) {
          this.orderChart.resize();
        }
        if (this.orderTypeChart) {
          this.orderTypeChart.resize();
        }
      });
    },
    initCharts() {
      console.log('初始化图表...');
      
      // 确保DOM元素已经渲染完成
      this.$nextTick(() => {
        // 检查DOM元素是否存在
        if (this.$refs.orderChart) {
          console.log('初始化订单趋势图表');
          this.orderChart = echarts.init(this.$refs.orderChart);
          
          // 设置一个基本配置，确保图表可见
          const baseOption = {
            tooltip: {
              trigger: 'axis'
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: ['加载中...']
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                name: '订单数',
                type: 'line',
                data: [0],
                lineStyle: {
                  color: '#F8D010',
                  width: 3
                }
              }
            ]
          };
          this.orderChart.setOption(baseOption);
        } else {
          console.error('订单趋势图表DOM元素不存在');
        }
        
        if (this.$refs.orderTypeChart) {
          console.log('初始化订单类型分布图表');
          this.orderTypeChart = echarts.init(this.$refs.orderTypeChart);
          
          // 设置一个基本配置，确保图表可见
          const baseOption = {
            tooltip: {
              trigger: 'item'
            },
            legend: {
              orient: 'vertical',
              right: 10,
              top: 'center'
            },
            series: [
              {
                name: '订单类型',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['40%', '50%'],
                data: [{ name: '加载中...', value: 100 }],
                color: ['#F8D010']
              }
            ]
          };
          this.orderTypeChart.setOption(baseOption);
        } else {
          console.error('订单类型分布图表DOM元素不存在');
        }
        
        // 获取订单趋势数据
        this.getOrderTrendData();
        
        // 获取订单类型分布数据
        this.getOrderTypeDistribution();
      });
    },
    updateOrderChart() {
      if (!this.orderChart) {
        console.warn('订单趋势图表未初始化，正在重新初始化...');
        if (this.$refs.orderChart) {
          this.orderChart = echarts.init(this.$refs.orderChart);
        } else {
          console.error('订单趋势图表DOM元素不存在');
          return;
        }
      }
      
      console.log('更新订单趋势图表，数据:', this.orderTrendData);
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.orderTrendData.dateLabels || []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '订单数',
            type: 'line',
            smooth: true,
            data: this.orderTrendData.orderCounts || [],
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(248, 208, 16, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(248, 208, 16, 0.1)'
                }
              ])
            },
            itemStyle: {
              color: '#F8D010'
            },
            lineStyle: {
              color: '#F8D010',
              width: 3
            }
          }
        ]
      };
      
      try {
        this.orderChart.setOption(option);
        console.log('订单趋势图表更新成功');
      } catch (error) {
        console.error('更新订单趋势图表失败:', error);
      }
    },
    updateOrderTypeChart() {
      if (!this.orderTypeChart) return;
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: this.orderTypeData.map(item => item.name)
        },
        series: [
          {
            name: '订单类型',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.orderTypeData,
            color: ['#F8D010', '#F8E068', '#909090', '#303030', '#666666', '#999999', '#cccccc', '#dddddd']
          }
        ]
      };
      
      this.orderTypeChart.setOption(option);
    },
    getOrderTrendData() {
      console.log('获取订单趋势数据, 周期:', this.chartDateRange);
      getOrderTrendData(this.chartDateRange, 
        this.dateRange ? this.dateRange[0] : null, 
        this.dateRange ? this.dateRange[1] : null
      ).then(response => {
        console.log('获取到订单趋势数据:', response);
        if (response && response.data) {
          // 检查返回数据结构
          console.log('订单趋势数据结构:', {
            dateLabels: response.data.dateLabels ? response.data.dateLabels.length : 'undefined',
            orderCounts: response.data.orderCounts ? response.data.orderCounts.length : 'undefined',
            groupBy: response.data.groupBy
          });
          
          // 确保数据格式正确
          if (response.data.dateLabels && response.data.orderCounts) {
            this.orderTrendData = {
              dateLabels: response.data.dateLabels,
              orderCounts: response.data.orderCounts,
              groupBy: response.data.groupBy || 'day'
            };
          } else {
            // 尝试从其他可能的格式中提取数据
            console.warn('尝试从其他格式中提取订单趋势数据');
            const data = response.data;
            if (Array.isArray(data)) {
              // 如果返回的是数组格式
              const dateLabels = data.map(item => item.date || item.label || '');
              const orderCounts = data.map(item => item.count || item.value || 0);
              this.orderTrendData = {
                dateLabels,
                orderCounts,
                groupBy: 'day'
              };
            }
          }
          
          console.log('处理后的订单趋势数据:', this.orderTrendData);
          this.updateOrderChart();
        } else {
          console.error('返回的订单趋势数据格式不正确:', response);
          // 加载模拟数据
          this.loadMockTrendData();
        }
      }).catch(error => {
        console.error('获取订单趋势数据失败:', error);
        // 加载模拟数据
        this.loadMockTrendData();
      });
    },
    getOrderTypeDistribution() {
      console.log('获取订单类型分布数据');
      getOrderTypeDistribution(
        this.dateRange ? this.dateRange[0] : null, 
        this.dateRange ? this.dateRange[1] : null
      ).then(response => {
        console.log('获取到订单类型分布数据:', response);
        if (response && response.data) {
          this.orderTypeData = response.data;
          this.updateOrderTypeChart();
        } else {
          console.error('返回的订单类型分布数据格式不正确:', response);
          // 加载模拟数据
          this.loadMockTypeData();
        }
      }).catch(error => {
        console.error('获取订单类型分布数据失败:', error);
        // 加载模拟数据
        this.loadMockTypeData();
      });
    },
    // 加载模拟数据
    loadMockData() {
      // 模拟统计数据
      this.statisticsData = {
        totalOrders: 256,
        processingOrders: 42,
        totalUsers: 158,
        completedOrders: 189
      };
      
      // 加载模拟图表数据
      this.loadMockTrendData();
      this.loadMockTypeData();
    },
    // 加载模拟趋势数据
    loadMockTrendData() {
      const dateLabels = [];
      const orderCounts = [];
      
      // 生成过去7天的日期标签
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dateLabels.push(`${date.getMonth() + 1}-${date.getDate()}`);
        orderCounts.push(Math.floor(Math.random() * 30) + 10);
      }
      
      this.orderTrendData = {
        dateLabels,
        orderCounts,
        groupBy: 'day'
      };
      
      this.updateOrderChart();
    },
    // 加载模拟类型分布数据
    loadMockTypeData() {
      this.orderTypeData = [
        { name: 'KTV', value: 335 },
        { name: '桌球', value: 234 },
        { name: '健身', value: 135 },
        { name: '篮球', value: 148 },
        { name: '其他', value: 58 }
      ];
      
      this.updateOrderTypeChart();
    },
    handleDateRangeChange() {
      this.getStatistics();
    },
    handleChartRangeChange() {
      this.getOrderTrendData();
    },
    refreshData() {
      console.log('手动刷新数据');
      this.getStatistics();
    }
  }
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  
  .dashboard-header {
    margin-bottom: 30px;
    margin-top: 20px; // 新增上边距
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .header-left {
      .dashboard-title {
        font-size: 22px;
        font-weight: 600;
        color: #303030;
        margin: 0;
        margin-bottom: 8px;
      }
      
      .dashboard-date {
        font-size: 16px;
        color: #909090;
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  
  .statistics-row {
    margin-bottom: 30px;
    
    .statistics-card {
      display: flex;
      min-height: 120px;
      margin-bottom: 20px;
      overflow: visible !important;
      border-radius: 12px;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }
      
      .statistics-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 60px;
        height: 60px;
        border-radius: 12px;
        margin-right: 15px;
        
        i {
          font-size: 28px;
        }
      }
      
      .statistics-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;
        
        .statistics-title {
          font-size: 16px;
          color: #606266;
          margin-bottom: 8px;
          white-space: nowrap;
        }
        
        .statistics-number {
          font-size: 24px;
          font-weight: bold;
          color: #303030;
          margin-bottom: 8px;
          overflow: visible;
          white-space: nowrap;
          line-height: 1.2;
        }
        
        .statistics-desc {
          font-size: 14px;
          color: #909090;
          white-space: nowrap;
        }
      }
    }
  }
  
  .chart-row {
    .chart-card {
      margin-bottom: 20px;
      border-radius: 12px;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }
      
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        span {
          font-size: 18px;
          font-weight: 600;
          color: #303030;
        }
      }
      
      .chart-container {
        height: 350px;
        width: 100%;
        margin-top: 20px;
      }
    }
  }
  
  .clay-card {
    background: #ffffff;
    border: none;
    box-shadow: 8px 8px 16px #e6e6e6, -8px -8px 16px #ffffff;
    padding: 20px;
    
    &:hover {
      box-shadow: 12px 12px 24px #d9d9d9, -12px -12px 24px #ffffff;
    }
  }
}

/* 覆盖Element UI的默认样式 */
::v-deep .el-card {
  border: none !important;
  overflow: visible !important;
  
  .el-card__header {
    border-bottom: none;
    padding: 15px 20px;
  }
  
  .el-card__body {
    padding: 15px 20px;
  }
}

/* 确保图表容器正确显示 */
::v-deep .chart-container {
  min-height: 350px !important;
  width: 100% !important;
}

::v-deep .el-radio-button__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  transition: all 0.3s;
  
  &:hover {
    transform: scale(1.05);
  }
  
  &.is-active {
    background-color: #F8D010;
    border-color: #F8D010;
    color: #303030;
    box-shadow: 0 0 10px rgba(248, 208, 16, 0.3);
  }
}
</style> 