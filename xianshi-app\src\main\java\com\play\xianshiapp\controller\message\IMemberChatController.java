package com.play.xianshiapp.controller.message;

import com.play.xianshibusiness.dto.ChatMessageDTO;
import com.play.xianshibusiness.pojo.IMemberChat;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.IMemberChatService;
import com.play.xianshibusiness.service.WebSocketService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * (IMemberChat)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:18
 */
@RestController
@RequestMapping("/app/iMemberChat")
@Api(tags = "聊天消息接口")
public class IMemberChatController {
    
    @Resource
    private IMemberChatService iMemberChatService;
    
    @Resource
    private WebSocketService webSocketService;
    
    @ApiOperation("发送聊天消息")
    @PostMapping("/send")
    public Result<String> sendMessage(@RequestBody ChatMessageDTO messageDTO) {
        String memberId = PrincipalUtil.getMemberId();
        String messageId = iMemberChatService.sendChatMessage(memberId, messageDTO.getTargetId(), messageDTO.getContent());
        return ResultUtils.success(messageId);
    }
    
    @ApiOperation("获取聊天历史记录")
    @GetMapping("/history/{targetId}")
    public Result<List<IMemberChat>> getChatHistory(@PathVariable("targetId") String targetId) {
        String memberId = PrincipalUtil.getMemberId();
        List<IMemberChat> chatHistory = iMemberChatService.getChatHistory(memberId, targetId);
        return ResultUtils.success(chatHistory);
    }
    
    @ApiOperation("获取聊天列表")
    @GetMapping("/list")
    public Result<List<String>> getChatList() {
        String memberId = PrincipalUtil.getMemberId();
        List<String> chatList = iMemberChatService.getChatList(memberId);
        return ResultUtils.success(chatList);
    }
    
    @ApiOperation("删除聊天记录")
    @DeleteMapping("/delete/{targetId}")
    public Result<Boolean> deleteChatHistory(@PathVariable("targetId") String targetId) {
        String memberId = PrincipalUtil.getMemberId();
        boolean result = iMemberChatService.deleteChatHistory(memberId, targetId);
        return ResultUtils.success(result);
    }
    
    @ApiOperation("检查用户是否在线")
    @GetMapping("/online/{userId}")
    public Result<Boolean> checkUserOnline(@PathVariable("userId") String userId) {
        boolean isOnline = webSocketService.isUserOnline(userId);
        return ResultUtils.success(isOnline);
    }
    
    @ApiOperation("获取在线用户数量")
    @GetMapping("/online/count")
    public Result<Integer> getOnlineUserCount() {
        int count = webSocketService.getOnlineUserCount();
        return ResultUtils.success(count);
    }
    
    @ApiOperation("获取未读聊天消息数")
    @GetMapping("/unread/count")
    public Result<Integer> getUnreadCount() {
        String memberId = PrincipalUtil.getMemberId();
        // 实现获取未读消息数的逻辑
        Integer unreadCount = iMemberChatService.getUnreadCount(memberId);
        return ResultUtils.success(unreadCount);
    }
}

