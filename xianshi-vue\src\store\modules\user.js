import { login, logout, getInfo } from '@/api/user';
import { getToken, setToken, removeToken, getAvatar, setAvatar, getName, setName, removeAvatar, removeName } from '@/utils/auth';
import router, { resetRouter } from '@/router';

const state = {
  token: getToken(),
  name: getName() || '',
  avatar: getAvatar() || '',
  roles: []
};

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_NAME: (state, name) => {
    state.name = name;
    setName(name);
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar;
    setAvatar(avatar);
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles;
  }
};

const actions = {
  // 用户登录
  login({ commit }, userInfo) {
    const { username, password, verifyCode, verifyKey, loginType } = userInfo;
    return new Promise((resolve, reject) => {
      login({ 
        username: username.trim(), 
        password: password, 
        verifyCode: verifyCode,
        verifyKey: verifyKey,
        loginType: loginType || 'PASSWORD' 
      })
        .then(response => {
          const { data } = response;
          commit('SET_TOKEN', data.token);
          setToken(data.token);
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token)
        .then(response => {
          const { data } = response;

          if (!data) {
            reject('验证失败，请重新登录。');
          }

          // 确保返回了角色信息，否则设置默认角色
          const { roles = ['admin'], name = '管理员', avatar = '' } = data;

          // 确保roles是数组格式
          const formattedRoles = Array.isArray(roles) ? roles : [roles];

          commit('SET_ROLES', formattedRoles);
          commit('SET_NAME', name);
          commit('SET_AVATAR', avatar);
          resolve({ roles: formattedRoles, name, avatar });
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  // 用户登出
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          commit('SET_TOKEN', '');
          commit('SET_ROLES', []);
          commit('SET_NAME', '');
          commit('SET_AVATAR', '');
          removeToken();
          removeAvatar();
          removeName();
          resetRouter();
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  // 重置token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '');
      commit('SET_ROLES', []);
      removeToken();
      resolve();
    });
  },
  
  // 更新头像
  updateAvatar({ commit }, avatar) {
    commit('SET_AVATAR', avatar);
    return Promise.resolve();
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
}; 