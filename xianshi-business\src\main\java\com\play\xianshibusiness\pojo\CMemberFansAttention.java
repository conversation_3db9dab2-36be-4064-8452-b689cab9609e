package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * (CMemberFansAttantion)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_member_fans_attention")
@ApiModel("会员粉丝/关注表")
public class CMemberFansAttention extends BaseObjPo {

    //会员ID
    @ApiModelProperty("会员ID")
    private String memberId;
    //目标ID
    @ApiModelProperty("目标ID")
    private String targetId;
    //浏览/收藏【枚举】
    @ApiModelProperty("粉丝/关注【枚举】")
    private String type;



}

