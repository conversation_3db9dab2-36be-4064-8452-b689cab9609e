<template>
  <view class="container">
    <!-- 固定顶部导航栏 -->
    <view class="fixed-header">
      <view class="header-container">
        <scroll-view class="city-list" scroll-x enable-flex>
          <view
            v-for="(item, index) in cities"
            :key="index"
            class="city-item"
            :class="{ active: currentCity === index }"
            @tap="switchCity"
            :data-index="index"
            :data-city="item"
          >
            {{ item }}
          </view>
        </scroll-view>

        <!-- 筛选按钮 -->
        <view
          class="filter-btn"
          :class="{ active: showFilter }"
          @tap="toggleFilter"
        >
          <text>筛选</text>
          <text class="filter-icon">▼</text>
          <text class="filter-count" v-if="filterCount > 0">{{
            filterCount
          }}</text>
        </view>
      </view>
    </view>

    <!-- 内容区域，添加顶部内边距，避免被固定导航栏遮挡 -->
    <view class="content-area">
      <!-- 筛选选项区域 -->
      <view class="filter-container">
        <!-- 筛选面板 -->
        <view class="filter-panel" v-if="showFilter">
          <!-- 位置信息 -->
          <view class="filter-section">
            <view class="section-title">位置信息</view>
            <view class="location-info">
              <text class="current-location">当前定位：{{ userLocation }}</text>
              <text class="select-city" @tap="showCityPicker">选择定位城市</text>
            </view>
          </view>
          
          <!-- 性别筛选 -->
          <view class="filter-section">
            <view class="section-title">性别</view>
            <view class="option-list">
              <view
                class="filter-option"
                :class="{ active: selectedGender === 'all' }"
                @tap="selectGender"
                data-gender="all"
                >全部</view
              >
              <view
                class="filter-option"
                :class="{ active: selectedGender === 'male' }"
                @tap="selectGender"
                data-gender="male"
                >男</view
              >
              <view
                class="filter-option"
                :class="{ active: selectedGender === 'female' }"
                @tap="selectGender"
                data-gender="female"
                >女</view
              >
            </view>
          </view>

<!--          &lt;!&ndash; 身份筛选 &ndash;&gt;-->
<!--          <view class="filter-section">-->
<!--            <view class="section-title">身份</view>-->
<!--            <view class="option-list">-->
<!--              <view-->
<!--                class="filter-option"-->
<!--                :class="{ active: selectedIdentity === 'all' }"-->
<!--                @tap="selectIdentity"-->
<!--                data-identity="all"-->
<!--                >全部</view-->
<!--              >-->
<!--              -->
<!--              <view-->
<!--                class="filter-option"-->
<!--                :class="{ active: selectedIdentity === 'anchor' }"-->
<!--                @tap="selectIdentity"-->
<!--                data-identity="anchor"-->
<!--                >主播</view-->
<!--              >-->
<!--              <view-->
<!--                class="filter-option"-->
<!--                :class="{ active: selectedIdentity === 'actor' }"-->
<!--                @tap="selectIdentity"-->
<!--                data-identity="actor"-->
<!--                >演员</view-->
<!--              >-->
<!--              <view-->
<!--                class="filter-option"-->
<!--                :class="{ active: selectedIdentity === 'model' }"-->
<!--                @tap="selectIdentity"-->
<!--                data-identity="model"-->
<!--                >模特</view-->
<!--              >-->
<!--              <view-->
<!--                class="filter-option"-->
<!--                :class="{ active: selectedIdentity === 'host' }"-->
<!--                @tap="selectIdentity"-->
<!--                data-identity="host"-->
<!--                >主持人</view-->
<!--              >-->
<!--              <view-->
<!--                class="filter-option"-->
<!--                :class="{ active: selectedIdentity === 'dancer' }"-->
<!--                @tap="selectIdentity"-->
<!--                data-identity="dancer"-->
<!--                >舞者</view-->
<!--              >-->
<!--              <view-->
<!--                class="filter-option"-->
<!--                :class="{ active: selectedIdentity === 'kol' }"-->
<!--                @tap="selectIdentity"-->
<!--                data-identity="kol"-->
<!--                >红人</view-->
<!--              >-->
<!--              <view-->
<!--                class="filter-option"-->
<!--                :class="{ active: selectedIdentity === 'producer' }"-->
<!--                @tap="selectIdentity"-->
<!--                data-identity="producer"-->
<!--                >经纪人</view-->
<!--              >-->
<!--              <view-->
<!--                class="filter-option"-->
<!--                :class="{ active: selectedIdentity === 'business' }"-->
<!--                @tap="selectIdentity"-->
<!--                data-identity="business"-->
<!--                >商家</view-->
<!--              >-->
<!--            </view>-->
<!--          </view>-->

          <!-- 重置和确认按钮 -->
          <view class="filter-actions">
            <view class="reset-btn" @tap="resetFilter">重置</view>
            <view class="confirm-btn" @tap="applyFilter">确认</view>
          </view>
        </view>
      </view>

      <!-- 达人列表 -->
      <view class="expert-list">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>
        
        <block v-else-if="currentExperts && currentExperts.length > 0">
          <view
            class="expert-card"
            v-for="item in currentExperts"
            :key="item.id"
            @tap="goToExpertHomepage"
            :data-id="item.id"
          >
            <!-- 达人基本信息 -->
            <view class="expert-info-row">
              <view class="expert-avatar-container">
                <image
                  class="expert-avatar"
                  :src="item.avatar"
                  mode="aspectFill"
                />
                <view
                  class="online-status"
                  :class="{ online: item.isOnline }"
                  v-if="item.isOnline"
                ></view>
              </view>
              <!-- 修改达人基本信息部分 -->
              <view class="expert-basic-info">
                <view class="expert-name-row">
                  <text class="expert-name">{{ item.name }}</text>
                  <text
                    class="gender"
                    :class="{
                      female: item.gender === '女',
                      male: item.gender !== '女',
                    }"
                  >
                    {{ item.gender === "女" ? "♀" : "♂" }}
                  </text>
                </view>

                <view class="expert-identity">
                  <text>{{ item.age ? item.age + '岁' : '--' }} · {{ item.height || '--' }} · {{ item.weight || '--' }} | {{ item.city || "--" }}</text>
                </view>
              </view>

              <!-- 修改右侧功能区 -->
              <view class="expert-right-actions">
                <!-- 在线状态文本 -->
                <view
                  class="online-text"
                  :class="{ 'online-text-active': item.isOnline }"
                >
                  {{ item.isOnline ? '在线' : formatLastOnlineTime(item.lastOnlineTime) }}
                </view>

                <!-- 收藏按钮 - 改为图标 -->
                <view
                  class="favorite-btn"
                  @tap.stop="toggleFavorite"
                  :data-id="item.id"
                  :data-name="item.name"
                  :data-is-favorite="item.isFavorite || false"
                >
                  <text class="favorite-icon" :class="{'favorite-icon-active': item.isFavorite}">♥</text>
                </view>
              </view>
            </view>

            <!-- 照片展示区域 -->
            <view class="expert-photos">
              <view
                class="photo-container"
                v-for="(photo, photoIndex) in (item.photos && item.photos.length > 0 ? item.photos.slice(0, 3) : ['', '', ''])"
                :key="photoIndex"
              >
                <image
                  v-if="photo"
                  class="expert-photo"
                  :src="photo"
                  mode="aspectFill"
                  @tap.stop="previewExpertPhoto"
                  :data-expert-id="item.id"
                  :data-photo-index="photoIndex"
                />
                <view v-else class="empty-photo"></view>
              </view>
            </view>
          </view>
        </block>
        <block v-else>
          <view class="no-data">暂无达人数据</view>
        </block>
      </view>
    </view>
  </view>
  <Tabbar currentPath="/pages/expert/expert"></Tabbar>
</template>

<script>
import { getTalentList, followMember, unfollowMember, isFollowing, viewMemberProfile } from '../../api/index.js';

export default {
  data() {
    return {
      currentCity: 0,
      // cities: ["全部", "同城", "最新", "推荐"],
      cities: ["全部", "同城"],
      userLocation: "成都", // 用户当前所在城市，实际应通过定位获取
      // 筛选相关
      showFilter: false,
      filterCount: 0,
      selectedGender: "all",
      selectedIdentity: "all",
      filterOptions: {
        gender: "all",
        identity: "all",
      },
      expertsData: {
        全部: [], // 初始为空，接口返回后填充
        同城: [],
        最新: [],
        推荐: [],
      },
      currentExperts: [],
      filteredExperts: [],
      loading: false, // 添加加载状态
    };
  },

  onLoad: function () {
    this.fetchTalentList();
    // 更新tabbar激活状态
    uni.$emit('updateTabbar', '/pages/expert/expert');
  },
  onShow() {
    // 确保每次显示页面时tabbar状态正确
    uni.$emit('updateTabbar', '/pages/expert/expert');
    
    // 如果有原生tabBar调用，可以保留，但推荐移除
    if (typeof this.getTabBar === "function" && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1 // expert对应的索引
      });
    }

    // 检查是否有从个人主页返回的更新
    const currentExpertInfo = uni.getStorageSync("currentExpertInfo");
    if (currentExpertInfo && currentExpertInfo.id) {
      console.log(
        "检测到达人信息更新:",
        currentExpertInfo.name,
        "收藏状态:",
        currentExpertInfo.isFavorite
      );

      // 更新对应达人的关注状态
      const allExperts = this.expertsData["全部"];
      const targetExpert = allExperts.find(item => item.id == currentExpertInfo.id);
      if (targetExpert && targetExpert.isFavorite !== currentExpertInfo.isFavorite) {
        targetExpert.isFavorite = currentExpertInfo.isFavorite;
        // 重新初始化筛选数据
        this.initializeFilteredData();
        // 更新当前显示的达人列表
        const currentCategory = this.cities[this.currentCity];
        this.currentExperts = this.expertsData[currentCategory] || [];
      }
      
      // 清除本地存储的更新信息
      uni.removeStorageSync("currentExpertInfo");
    }
  },
  methods: {
    // 对接后端达人列表接口
    fetchTalentList() {
      this.loading = true;
      getTalentList({ pageNum: 1, pageSize: 50 }).then(res => {
        if (res && res.data && res.data.records) {
          // 字段映射
          const experts = res.data.records.map((item, index) => {
            // 为不同达人设置不同的在线状态和最后在线时间
            let isOnline = false;
            let lastOnlineTime = null;
            
            // 根据索引设置不同的在线状态
            if (index % 5 === 0) {
              // 每5个达人中的第1个设为在线
              isOnline = true;
            } else {
              // 其他达人设置不同的最后在线时间
              const now = new Date().getTime();
              if (index % 5 === 1) {
                // 5分钟前
                lastOnlineTime = now - 5 * 60 * 1000;
              } else if (index % 5 === 2) {
                // 30分钟前
                lastOnlineTime = now - 30 * 60 * 1000;
              } else if (index % 5 === 3) {
                // 3小时前
                lastOnlineTime = now - 3 * 60 * 60 * 1000;
              } else {
                // 2天前
                lastOnlineTime = now - 2 * 24 * 60 * 60 * 1000;
              }
            }
            
            return {
            id: item.id,
            name: item.nickname || '',
            avatar: item.avatar || '',
            gender: item.gender === 2 ? '女' : '男',
            age: item.age || '',
            constellation: item.constellation || '',
            height: item.height || '',
            weight: item.weight || '', // 后端无此字段，前端补空
            city: item.city || '',
            tags: item.styleIds ? item.styleIds.split(',') : [], // 后端风格标签
            photos: item.images ? item.images.split(',') : [], // 后端照片集合
              isOnline: isOnline, // 设置在线状态
              lastOnlineTime: lastOnlineTime, // 设置最后在线时间
            isFavorite: false, // 后端无此字段，默认false
            };
          });
          this.expertsData["全部"] = experts;
          this.currentExperts = experts;
          // 重新初始化分类数据
          this.initializeFilteredData();
          // 检查关注状态
          this.checkFollowStatus();
        }
        this.loading = false;
      }).catch(() => {
        // 接口失败时可回退到mock数据
        this.loading = false;
        uni.showToast({
          title: "加载失败",
          icon: "none",
          duration: 2000,
        });
      });
    },

    // 检查关注状态
    checkFollowStatus() {
      const allExperts = this.expertsData["全部"];
      allExperts.forEach(expert => {
        isFollowing(expert.id).then(res => {
          if (res && res.data !== undefined) {
            expert.isFavorite = res.data;
          }
        }).catch(() => {
          // 接口失败时保持默认状态
        });
      });
    },

    // 初始化各个筛选分类的数据
    initializeFilteredData: function () {
      // 所有专家数据
      const allExperts = this.expertsData["全部"];

      // 确保所有专家数据都有收藏状态属性
      allExperts.forEach((expert) => {
        if (typeof expert.isFavorite === "undefined") {
          expert.isFavorite = false;
        }
      });

      // 同城数据 - 筛选用户当前城市的达人
      const sameCity = allExperts.filter(
        (expert) => expert.city === this.userLocation
      );

      // 最新数据 - 根据在线状态和ID(假设ID越大表示越新)排序
      const latest = [...allExperts].sort((a, b) => {
        // 优先按在线状态排序
        if (a.isOnline && !b.isOnline) return -1;
        if (!a.isOnline && b.isOnline) return 1;
        // 然后按ID排序（假设ID越大表示越新）
        return b.id - a.id;
      });

      // 推荐数据 - 假设每个达人有一个隐藏的评分属性，这里我们简单地用ID作为评分依据
      const recommended = [...allExperts].sort((a, b) => {
        // 这里用照片数量和是否在线作为评分标准的示例
        const scoreA = a.photos.length * 2 + (a.isOnline ? 5 : 0);
        const scoreB = b.photos.length * 2 + (b.isOnline ? 5 : 0);
        return scoreB - scoreA;
      });

      // 更新数据
      const expertsData = this.expertsData;
      expertsData["全部"] = allExperts;
      expertsData["同城"] = sameCity;
      expertsData["最新"] = latest;
      expertsData["推荐"] = recommended;

      this.expertsData = expertsData;
    },

    switchCity: function (e) {
      const index = e.currentTarget.dataset.index;
      const category = e.currentTarget.dataset.city;

      if (this.currentCity === index) {
        return; // 如果点击当前分类，不做处理
      }

      uni.showLoading({
        title: "切换中...",
      });

      this.loadCityExperts(index, category);
    },

    loadCityExperts: function (index, category) {
      setTimeout(() => {
        let experts = this.expertsData[category] || [];

        this.currentCity = index;

        // 如果有筛选条件，则应用筛选
        if (this.filterCount > 0) {
          this.applyFilterToCurrentExperts();
        } else {
          this.currentExperts = experts;
        }

        uni.hideLoading();
      }, 300);
    },

    // 切换收藏状态
    toggleFavorite: function (e) {
      const expertId = e.currentTarget.dataset.id;
      const expert = this.currentExperts.find(item => item.id == expertId);
      
      if (!expert) {
        uni.showToast({
          title: "操作失败",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      // 显示加载提示
      uni.showLoading({
        title: expert.isFavorite ? "取消关注中..." : "关注中...",
        mask: true
      });

      // 调用关注/取消关注接口
      const apiCall = expert.isFavorite ? unfollowMember(expertId) : followMember(expertId);
      
      apiCall.then(res => {
        uni.hideLoading();
        if (res && res.data) {
          // 更新关注状态
          expert.isFavorite = !expert.isFavorite;
          
          // 同步更新所有分类中的达人状态
          const allExperts = this.expertsData["全部"];
          const targetExpert = allExperts.find(item => item.id == expertId);
          if (targetExpert) {
            targetExpert.isFavorite = expert.isFavorite;
          }
          
          // 重新初始化筛选数据
          this.initializeFilteredData();
          
          // 提示操作成功
          uni.showToast({
            title: expert.isFavorite ? "关注成功" : "取消关注成功",
            icon: "success",
            duration: 1500,
          });
        } else {
          uni.showToast({
            title: "操作失败",
            icon: "none",
            duration: 1500,
          });
        }
      }).catch((error) => {
        uni.hideLoading();
        console.error("关注操作失败:", error);
        uni.showToast({
          title: "操作失败，请重试",
          icon: "none",
          duration: 1500,
        });
      });
    },

    // 前往达人主页
    goToExpertHomepage: function (e) {
      const expertId = e.currentTarget.dataset.id;
      console.log(
        "前往达人主页",
        expertId,
        this.currentExperts.find((item) => item.id == expertId)?.name
      );

      // 找到对应的达人信息
      const expert = this.currentExperts.find((item) => item.id == expertId);

      if (expert) {
        // 存储当前达人信息到本地，用于返回时更新关注状态
        uni.setStorageSync("currentExpertInfo", expert);

        // 记录浏览历史（通过查看会员资料API）
            console.log("开始记录浏览历史，达人ID:", expertId);
            viewMemberProfile(expertId).then(res => {
              console.log("浏览历史记录成功", res);
              // 记录成功后，更新本地用户信息
              this.updateUserViewCount();
            }).catch(err => {
              console.error("浏览历史记录失败", err);
        });

        // 跳转到达人个人主页
        uni.navigateTo({
          url:
            "/pages/profile/preview?id=" + expert.id + "&name=" + expert.name,
          fail: (err) => {
            console.log("跳转失败", err);
          },
        });
      } else {
        uni.showToast({
          title: "找不到该达人信息",
          icon: "none",
        });
      }
    },

    // 预览达人照片
    previewExpertPhoto: function (e) {
      // 获取点击的达人ID和照片索引
      const expertId = e.currentTarget.dataset.expertId;
      const photoIndex = e.currentTarget.dataset.photoIndex;

      console.log("预览达人照片", expertId, photoIndex);

      // 找到对应的达人信息
      const expert = this.currentExperts.find((item) => item.id == expertId);

      if (expert && expert.photos && expert.photos.length > 0) {
        // 预览图片
        uni.previewImage({
          current: expert.photos[photoIndex], // 当前显示图片的链接
          urls: expert.photos, // 需要预览的图片链接列表
          fail: (err) => {
            console.log("预览图片失败", err);
          },
        });
      } else {
        uni.showToast({
          title: "获取照片失败",
          icon: "none",
        });
      }
    },

    // 显示城市选择器
    showCityPicker: function () {
      uni.showToast({
        title: "城市选择功能开发中",
        icon: "none",
      });
    },

    // 切换筛选面板显示
    toggleFilter: function () {
      this.showFilter = !this.showFilter;
    },

    // 选择性别
    selectGender: function (e) {
      const gender = e.currentTarget.dataset.gender;
      this.selectedGender = gender;
    },

    // 选择身份
    selectIdentity: function (e) {
      const identity = e.currentTarget.dataset.identity;
      this.selectedIdentity = identity;
    },

    // 重置筛选
    resetFilter: function () {
      this.selectedGender = "all";
      this.selectedIdentity = "all";
    },

    // 应用筛选
    applyFilter: function () {
      // 计算选中的筛选选项数量
      let count = 0;
      if (this.selectedGender !== "all") count++;
      if (this.selectedIdentity !== "all") count++;

      // 保存筛选选项
      const filterOptions = {
        gender: this.selectedGender,
        identity: this.selectedIdentity,
      };

      this.filterCount = count;
      this.filterOptions = filterOptions;
      this.showFilter = false;

      // 应用筛选条件到当前达人列表
      this.applyFilterToCurrentExperts();
    },

    // 将筛选条件应用到当前达人列表
    applyFilterToCurrentExperts: function () {
      const category = this.cities[this.currentCity];
      let experts = this.expertsData[category] || [];
      const { gender, identity } = this.filterOptions;

      // 应用性别筛选
      if (gender !== "all") {
        const genderValue = gender === "male" ? "男" : "女";
        experts = experts.filter((expert) => expert.gender === genderValue);
      }

      // 应用身份筛选（这里假设每个达人的tags中包含身份信息）
      if (identity !== "all") {
        // 身份映射表
        const identityMap = {
          anchor: "主播",
          actor: "演员",
          model: "模特",
          host: "主持人",
          dancer: "舞者",
          kol: "红人",
          producer: "经纪人",
          business: "商家",
        };

        const identityTag = identityMap[identity];
        experts = experts.filter((expert) => {
          // 检查tags中是否包含对应身份
          return (
            expert.tags && expert.tags.some((tag) => tag.includes(identityTag))
          );
        });
      }

      // 更新显示的达人列表
      this.currentExperts = experts;

      // 如果筛选后没有数据，提示用户
      if (experts.length === 0) {
        uni.showToast({
          title: "没有符合条件的达人",
          icon: "none",
        });
      }
    },

    // 约单功能
    makeOrder: function () {
      const expertInfo = this.currentExperts.find(
        (item) => item.id === parseInt(e.currentTarget.dataset.id)
      );
      if (!expertInfo) {
        return;
      }

      uni.navigateTo({
        url:
          "/pages/publish-demand/publish-demand?expertId=" +
          expertInfo.id +
          "&expertName=" +
          expertInfo.name,
        fail: function (res) {
          uni.showToast({
            title: "约单功能开发中",
            icon: "none",
            duration: 1500,
          });
        },
      });
    },

    // 模拟达人登录，用于测试
    mockExpertLogin: function (e) {
      const expertId = e.currentTarget.dataset.id;
      const expertList = this.currentExperts || [];
      const expertInfo = expertList.find((expert) => expert.id == expertId);

      if (!expertInfo) {
        uni.showToast({
          title: "未找到达人信息",
          icon: "none",
        });
        return;
      }

      // 设置当前登录的达人ID
      uni.setStorageSync("loginExpertId", expertInfo.id);
      uni.setStorageSync("loginExpertInfo", expertInfo);

      // 检查是否有约单请求
      this.checkPendingOrders(expertInfo.id);
    },

    // 检查待处理的约单
    checkPendingOrders: function (expertId) {
      // 获取达人收到的订单
      const expertReceiveOrders =
        uni.getStorageSync("expertReceiveOrders") || {};
      const pendingOrders = expertReceiveOrders[expertId] || [];

      if (pendingOrders.length > 0) {
        // 显示有新订单
        uni.showModal({
          title: "您有新的约单请求",
          content: `您有${pendingOrders.length}个待确认的约单，是否查看？`,
          confirmText: "立即查看",
          cancelText: "稍后查看",
          success: (res) => {
            if (res.confirm) {
              // 跳转到达人端订单页面（这里可以创建一个新页面或使用现有页面）
              uni.navigateTo({
                url: "/pages/message/message",
                fail: () => {
                  // 如果页面不存在，则显示订单详情弹窗
                  this.showOrderDetails(pendingOrders[0]);
                },
              });
            }
          },
        });
      } else {
        uni.showToast({
          title: "暂无约单请求",
          icon: "none",
        });
      }
    },

    // 显示订单详情
    showOrderDetails: function (order) {
      uni.showModal({
        title: "约单详情",
        content: `用户想约您参加${order.service}>${order.serviceSubType}活动\n日期：${order.date}\n时间：${order.time}\n地点：${order.location}\n是否接受？`,
        confirmText: "接受",
        cancelText: "拒绝",
        success: (res) => {
          if (res.confirm) {
            // 接受约单
            this.acceptOrder(order);
          } else {
            // 拒绝约单
            this.rejectOrder(order);
          }
        },
      });
    },

    // 接受约单
    acceptOrder: function (order) {
      // 更新订单状态
      order.status = "accepted";

      // 更新达人收到的订单列表
      let expertReceiveOrders = uni.getStorageSync("expertReceiveOrders") || {};
      let expertOrders = expertReceiveOrders[order.directExpertId] || [];

      // 找到并更新订单
      const index = expertOrders.findIndex((o) => o.orderId === order.orderId);
      if (index !== -1) {
        expertOrders[index] = order;
        expertReceiveOrders[order.directExpertId] = expertOrders;
        uni.setStorageSync("expertReceiveOrders", expertReceiveOrders);
      }

      // 更新用户的订单状态
      let myOrders = uni.getStorageSync("myOrders") || [];
      const userOrderIndex = myOrders.findIndex(
        (o) => o.orderId === order.orderId
      );
      if (userOrderIndex !== -1) {
        myOrders[userOrderIndex].status = "accepted";
        uni.setStorageSync("myOrders", myOrders);
      }

      uni.showToast({
        title: "已接受约单",
        icon: "success",
      });
    },

    // 拒绝约单
    rejectOrder: function (order) {
      // 更新订单状态
      order.status = "rejected";

      // 更新达人收到的订单列表
      let expertReceiveOrders = uni.getStorageSync("expertReceiveOrders") || {};
      let expertOrders = expertReceiveOrders[order.directExpertId] || [];

      // 找到并更新订单
      const index = expertOrders.findIndex((o) => o.orderId === order.orderId);
      if (index !== -1) {
        expertOrders[index] = order;
        expertReceiveOrders[order.directExpertId] = expertOrders;
        uni.setStorageSync("expertReceiveOrders", expertReceiveOrders);
      }

      // 更新用户的订单状态
      let myOrders = uni.getStorageSync("myOrders") || [];
      const userOrderIndex = myOrders.findIndex(
        (o) => o.orderId === order.orderId
      );
      if (userOrderIndex !== -1) {
        myOrders[userOrderIndex].status = "rejected";
        uni.setStorageSync("myOrders", myOrders);

        // 退还用户金币
        const coinBalance = uni.getStorageSync("coinBalance") || 0;
        const refundAmount = myOrders[userOrderIndex].coinFee || 2;
        uni.setStorageSync("coinBalance", coinBalance + refundAmount);
      }

      uni.showToast({
        title: "已拒绝约单",
        icon: "none",
      });
    },

    // 格式化最后在线时间
    formatLastOnlineTime(timestamp) {
      // 如果有时间戳，显示多久前来过
      if (timestamp) {
        const now = new Date().getTime();
        const lastTime = timestamp; // 已经是毫秒时间戳
        const diffMinutes = Math.floor((now - lastTime) / (60 * 1000));
        
        if (diffMinutes < 10) {
          return "刚刚来过";
        } else if (diffMinutes < 60) {
          return diffMinutes + "分钟前来过";
        } else if (diffMinutes < 24 * 60) {
          const hours = Math.floor(diffMinutes / 60);
          return hours + "小时前来过";
        } else {
          const days = Math.floor(diffMinutes / (24 * 60));
          return days + "天前来过";
        }
      }
      
      // 默认显示
      return "刚刚来过";
    },

    // 更新用户浏览数量
    updateUserViewCount() {
      // 获取当前用户信息
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        // 增加浏览数量
        userInfo.myViewCount = (userInfo.myViewCount || 0) + 1;
        // 保存到本地存储
        uni.setStorageSync('userInfo', userInfo);
        console.log("更新用户浏览数量:", userInfo.myViewCount);
      }
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding: 0;
  box-sizing: border-box;
}

/* 固定顶部导航栏 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  width: 100%;
}

/* 内容区域样式 */
.content-area {
  padding-top: 60rpx; /* 为固定导航栏留出空间，增加了定位信息的高度 */
  padding-left: 20rpx;
  padding-right: 20rpx;
}

/* 头部容器样式 */
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 10rpx 16rpx 20rpx;
}

/* 城市选择样式 */
.city-list {
  white-space: nowrap;
  flex: 1;
  margin-right: 10rpx;
}

.city-item {
  display: inline-block;
  padding: 10rpx 24rpx;
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #666;
  background: #f7f7f7;
  border-radius: 999rpx;
  transition: all 0.3s ease;
}

.city-item.active {
  background: #ff8c00;
  color: #fff;
  font-weight: 500;
}

/* 无数据提示 */
.no-data {
  text-align: center;
  color: #999;
  padding: 40rpx 0;
}

/* 达人列表样式 */
.expert-list {
  padding: 20rpx 0;
}

/* 达人卡片样式 */
.expert-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 达人基本信息行 */
.expert-info-row {
  display: flex;
  margin-bottom: 24rpx;
  position: relative;
}

/* 头像容器 */
.expert-avatar-container {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

/* 在线状态指示器 */
.online-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background-color: #52c41a;
  border: 2rpx solid #fff;
  display: none;
}

.online-status.online {
  display: block;
}

/* 达人头像样式 */
.expert-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

/* 达人基本信息 */
.expert-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 4rpx;
}

/* 名字、性别、年龄行 */
.expert-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.expert-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 8rpx;
  line-height: 1;
}

.gender {
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  line-height: 1;
}

.gender.female {
  color: #ff6b9c;
}

.gender.male {
  color: #3498db;
}

/* 右侧功能区 - 修改上边距 */
.expert-right-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: 0; /* 调整上边距 */
  min-width: 90rpx;
}

/* 在线状态文本 - 往上移动5rpx */
.online-text {
  font-size: 24rpx;
  color: #999;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx; /* 减少下边距 */
  text-align: center;
  margin-top: -5rpx; /* 往上移动5rpx */
}

.online-text-active {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
  font-weight: 500;
}

/* 关注按钮 - 往上移动5rpx */
.favorite-btn {
  height: 48rpx;
  width: 48rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-top: -5rpx; /* 往上移动5rpx */
}

.favorite-icon {
  font-size: 32rpx;
  color: #999;
  line-height: 1;
}

.favorite-icon-active {
  color: #ff5792;
}

.expert-age {
  font-size: 24rpx;
  color: #999;
}

/* 达人身份信息 */
.expert-identity {
  font-size: 24rpx;
  color: #888;
  margin-top: 6rpx;
}

/* 详细信息行 */
.expert-details {
  font-size: 24rpx;
  color: #888;
  margin-top: 12rpx;
  text-align: center;
}

/* 标签样式 */
.expert-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.tag {
  padding: 4rpx 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  background: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  border-radius: 999rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.tag:nth-child(3n + 1) {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.tag:nth-child(3n + 2) {
  background: rgba(114, 46, 209, 0.1);
  color: #722ed1;
}

/* 照片展示区域 */
.expert-photos {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.photo-container {
  flex: 1;
  height: 280rpx;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #f7f7f7;
}

.empty-photo {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
}

.expert-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.expert-photo:hover {
  transform: scale(1.05);
}

/* 筛选区域样式 */
.filter-container {
  position: relative;
  padding: 0 30rpx 20rpx;
  z-index: 100;
}

/* 筛选按钮样式 */
.filter-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 24rpx;
  background: #fff;
  border: 1rpx solid #eee;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  position: relative;
  flex-shrink: 0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  margin-right: 15rpx;
}

.filter-btn.active {
  background: #ffecdb;
  color: #ff8c00;
  border: 1rpx solid #ff8c00;
}

.filter-icon {
  font-size: 20rpx;
  margin-left: 8rpx;
  color: #999;
  transform: scale(0.8);
}

.filter-btn.active .filter-icon {
  color: #ff8c00;
  transform: rotate(180deg) scale(0.8);
}

.filter-count {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 20rpx;
  min-width: 30rpx;
  height: 30rpx;
  line-height: 30rpx;
  border-radius: 15rpx;
  text-align: center;
  padding: 0 6rpx;
  box-sizing: border-box;
}

/* 筛选面板样式 */
.filter-panel {
  position: fixed;
  top: 80rpx; /* 与固定导航栏底部对齐 */
  left: 0;
  right: 0;
  width: 100%;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  overflow: hidden;
  padding: 20rpx;
  box-sizing: border-box;
}

.filter-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.option-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.filter-option {
  padding: 12rpx 30rpx;
  background: #f7f7f7;
  border-radius: 999rpx;
  font-size: 24rpx;
  color: #666;
  margin: 10rpx;
  transition: all 0.3s ease;
}

.filter-option.active {
  background: #ffecdb;
  color: #ff8c00;
  font-weight: 500;
}

/* 筛选操作按钮样式 */
.filter-actions {
  display: flex;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.reset-btn {
  color: #666;
}

.confirm-btn {
  color: #fff;
  background: #ff8c00;
  border-radius: 10rpx;
}

.filter-btn .iconfont {
  font-size: 24rpx;
  margin-right: 6rpx;
}

.filter-btn .filter-text {
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container text {
  font-size: 28rpx;
  color: #999;
}
</style>
