<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <h1 class="error-code">404</h1>
      </div>
      <h2 class="error-title">页面不存在</h2>
      <p class="error-desc">抱歉，您访问的页面不存在或已被删除</p>
      <router-link to="/">
        <el-button type="primary" class="error-btn">返回首页</el-button>
      </router-link>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Page404'
};
</script>

<style lang="scss" scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 100vh;
  background-color: var(--background-color);
  
  .error-content {
    text-align: center;
    padding: 40px;
    
    .error-image {
      margin-bottom: 20px;
      
      .error-code {
        font-size: 120px;
        font-weight: bold;
        color: var(--primary-color);
        margin: 0;
        line-height: 1;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        animation: float 3s ease-in-out infinite;
      }
    }
    
    .error-title {
      font-size: 28px;
      color: var(--dark-color);
      margin-bottom: 15px;
    }
    
    .error-desc {
      font-size: 16px;
      color: var(--gray-color);
      margin-bottom: 30px;
    }
    
    .error-btn {
      padding: 12px 25px;
      font-size: 16px;
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      
      &:hover {
        background-color: var(--primary-light);
        border-color: var(--primary-light);
      }
    }
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0);
  }
}
</style> 