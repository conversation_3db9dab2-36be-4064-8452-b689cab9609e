<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>充值记录管理</span>
      </div>
      
      <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.native.prevent>
        <el-form-item label="订单号">
          <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select v-model="queryParams.payType" placeholder="请选择支付方式" clearable>
            <el-option label="微信支付" value="WECHAT" />
            <el-option label="支付宝" value="ALIPAY" />
            <el-option label="管理员充值" value="ADMIN" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="待支付" value="0" />
            <el-option label="支付成功" value="1" />
            <el-option label="支付失败" value="2" />
            <el-option label="已取消" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="充值时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table
        v-loading="loading"
        :data="recordList"
        style="width: 100%"
        border
      >
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column prop="orderNo" label="订单号" min-width="180" />
        <el-table-column prop="memberName" label="会员" min-width="120">
          <template slot-scope="scope">
            <div class="member-info">
              <el-avatar :size="30" :src="scope.row.memberAvatar" />
              <span class="member-name">{{ scope.row.memberName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="充值金额(元)" min-width="100" />
        <el-table-column prop="goldAmount" label="充值金币" min-width="100" />
        <el-table-column prop="payType" label="支付方式" min-width="100">
          <template slot-scope="scope">
            <el-tag :type="getPayTypeTag(scope.row.payType)">
              {{ getPayTypeName(scope.row.payType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTag(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="160" />
        <el-table-column prop="payTime" label="支付时间" min-width="160" />
        <el-table-column label="操作" width="80" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    
    <!-- 详情对话框 -->
    <el-dialog title="充值记录详情" :visible.sync="detailOpen" width="500px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="订单号">{{ detailData.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="会员">{{ detailData.memberName }}</el-descriptions-item>
        <el-descriptions-item label="充值金额">{{ detailData.amount }} 元</el-descriptions-item>
        <el-descriptions-item label="充值金币">{{ detailData.goldAmount }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ getPayTypeName(detailData.payType) }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ getStatusName(detailData.status) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailData.createTime }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ detailData.payTime || '未支付' }}</el-descriptions-item>
        <el-descriptions-item label="交易流水号">{{ detailData.transactionId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ detailData.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRechargeRecords, getRechargeDetail } from '@/api/recharge';
import Pagination from '@/components/Pagination';

export default {
  name: 'RechargeRecords',
  components: { Pagination },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 记录表格数据
      recordList: [],
      // 是否显示详情弹出层
      detailOpen: false,
      // 详情数据
      detailData: {},
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: undefined,
        payType: undefined,
        status: undefined,
        startTime: undefined,
        endTime: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询记录列表 */
    getList() {
      this.loading = true;
      getRechargeRecords(this.queryParams).then(response => {
        this.recordList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        orderNo: undefined,
        payType: undefined,
        status: undefined,
        startTime: undefined,
        endTime: undefined
      };
      this.handleQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 日期范围变化 */
    handleDateRangeChange(dates) {
      if (dates) {
        this.queryParams.startTime = dates[0] + ' 00:00:00';
        this.queryParams.endTime = dates[1] + ' 23:59:59';
      } else {
        this.queryParams.startTime = undefined;
        this.queryParams.endTime = undefined;
      }
    },
    /** 查看详情按钮操作 */
    handleDetail(row) {
      getRechargeDetail(row.id).then(response => {
        this.detailData = response.data;
        this.detailOpen = true;
      });
    },
    /** 获取支付方式名称 */
    getPayTypeName(payType) {
      const payTypeMap = {
        'WECHAT': '微信支付',
        'ALIPAY': '支付宝',
        'ADMIN': '管理员充值'
      };
      return payTypeMap[payType] || '未知方式';
    },
    /** 获取支付方式标签类型 */
    getPayTypeTag(payType) {
      const payTypeTagMap = {
        'WECHAT': 'success',
        'ALIPAY': 'primary',
        'ADMIN': 'warning'
      };
      return payTypeTagMap[payType] || 'info';
    },
    /** 获取状态名称 */
    getStatusName(status) {
      const statusMap = {
        '0': '待支付',
        '1': '支付成功',
        '2': '支付失败',
        '3': '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    /** 获取状态标签类型 */
    getStatusTag(status) {
      const statusTagMap = {
        '0': 'warning',
        '1': 'success',
        '2': 'danger',
        '3': 'info'
      };
      return statusTagMap[status] || 'info';
    }
  }
};
</script>

<style scoped>
.member-info {
  display: flex;
  align-items: center;
}
.member-name {
  margin-left: 10px;
}
</style> 