package com.play.xianshibusiness.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 订单报名详情更新DTO
 */
@Data
@ApiModel("订单报名详情更新DTO")
public class OrderEnrollDetailUpdateDTO {

    @ApiModelProperty(value = "订单报名详情ID", required = true)
    @NotBlank(message = "订单报名详情ID不能为空")
    private String id;
    
    @ApiModelProperty(value = "目标状态", required = true)
    @NotBlank(message = "目标状态不能为空")
    private String targetStatus;
    
    @ApiModelProperty(value = "备注")
    private String remark;

} 