package com.play.xianshibusiness.dto.recharge;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值套餐VO
 */
@Data
@ApiModel("充值套餐VO")
public class RechargePackageVO {

    @ApiModelProperty(value = "套餐ID")
    private String id;
    
    @ApiModelProperty(value = "套餐名称")
    private String name;
    
    @ApiModelProperty(value = "套餐金额(元)")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "赠送金币数量")
    private BigDecimal bonusGold;
    
    @ApiModelProperty(value = "基础金币数量")
    private BigDecimal baseGold;
    
    @ApiModelProperty(value = "总金币数量")
    private BigDecimal totalGold;
    
    @ApiModelProperty(value = "折扣")
    private BigDecimal discount;
    
    @ApiModelProperty(value = "套餐描述")
    private String description;
    
    @ApiModelProperty(value = "套餐图标")
    private String icon;
    
    @ApiModelProperty(value = "是否推荐")
    private Boolean isRecommend;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
} 