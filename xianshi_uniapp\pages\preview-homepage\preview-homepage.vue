<template>
	<view class="container">
		<!-- 用户基本信息 -->
		<view class="user-info">
			<image class="user-avatar" :src="userInfo?.avatar || '/images/default-avatar.png'" mode="aspectFill" />
			<view class="user-name">
				<text>{{ userInfo.nickname }}</text>
			</view>
			<view class="role-container">
				<text class="role">{{ userInfo.roleText }}</text>
			</view>
		</view>

		<!-- 数据统计 -->

			<view class="stat-item">
				<text class="stat-num">1</text>
				<text class="stat-label">粉丝</text>
			</view>
			<view class="stat-item">
				<text class="stat-num">0</text>
				<text class="stat-label">关注</text>
			</view>


		<!-- 主页内容 -->
		<view class="content-section">
			<!-- 形象信息 -->
			<view class="info-card">
				<view class="card-header">
					<text class="card-title">形象信息</text>
				</view>
				<view class="info-grid">
					<view class="info-item">
						<text class="info-value">{{ profileInfo.age || "--" }}</text>
						<text class="info-label">年龄</text>
					</view>
					<view class="info-item">
						<text class="info-value">{{
							profileInfo.height ? profileInfo.height + "cm" : "--"
						}}</text>
						<text class="info-label">身高</text>
					</view>
					<view class="info-item">
						<text class="info-value">{{
              console.log("weigh!!!!:{}",profileInfo.weight),
							profileInfo.weight ? profileInfo.weight + "kg" : "--"
						}}</text>
						<text class="info-label">体重</text>
					</view>
					<view class="info-item">
						<text class="info-value">{{
							profileInfo.constellation || "--"
						}}</text>
						<text class="info-label">地址</text>
					</view>
				</view>
			</view>

			<!-- 自我简介 -->
			<view class="info-card">
				<view class="card-header">
					<text class="card-title">自我简介</text>
				</view>
				<view class="intro-content" v-if="profileInfo.introduction">
					<text>{{ profileInfo.introduction }}</text>
				</view>
				<view class="intro-placeholder" v-else>
					<text>暂无简介</text>
				</view>
			</view>

			<!-- 形象视频 -->
			<view class="info-card" v-if="videoUrl">
				<view class="card-header">
					<text class="card-title">形象视频</text>
				</view>
				<video class="video-player" :src="videoUrl" mode="aspectFill" controls></video>
			</view>

			<!-- 形象照片 -->
			<view class="info-card" v-if="photos.length > 0">
				<view class="card-header">
					<text class="card-title">形象照片</text>
				</view>
				<view class="photos-grid">
					<view class="photo-item" v-for="(_, index) in photos.slice(0, Math.min(photos.length, 3))" :key="index">
						<image class="photo-image" :src="photos[index]" mode="aspectFill" />
						<view class="photo-count" v-if="index === 2 && photos.length > 3">
							<text>+{{ photos.length - 3 }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 形象风格 -->
			<view class="info-card" v-if="styles.length > 0">
				<view class="card-header">
					<text class="card-title">形象风格</text>
				</view>
				<view class="tags-container">
					<view class="tag-item" v-for="item in styles" :key="item">{{
						item
					}}</view>
				</view>
			</view>

			<!-- 身份标签 -->
			<view class="info-card" v-if="tags.length > 0">
				<view class="card-header">
					<text class="card-title">身份标签</text>
				</view>
				<view class="tags-container">
					<view class="tag-item" v-for="item in tags" :key="item">{{
						item
					}}</view>
				</view>
			</view>

			<!-- 接单项目 -->
			<view class="info-card" v-if="notices.length > 0">
				<view class="card-header">
					<text class="card-title">接单项目</text>
				</view>
				<view class="tags-container">
					<view class="tag-item" v-for="item in notices" :key="item">{{
						item
					}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {},
			profileInfo: {},
			photos: [],
			tags: [],
			notices: [],
			styles: [],
			videoUrl: ''
		}
	},
	onLoad(options) {
		uni.setNavigationBarTitle({
			title: '主页预览'
		});
		if (options.data) {
      console.log('主页预览data---->',options.data)
			const data = JSON.parse(decodeURIComponent(options.data));
			if (!data.userInfo.roleText) {
				data.userInfo.roleText = data.userInfo.role === 'visitor' ? '闲时公会' :
					data.userInfo.role === 'user' ? '普通用户' :
						'闲时达人'
			}
			this.userInfo = data.userInfo;
			this.profileInfo = data.profileInfo;
			this.photos = data.photos;
			this.tags = data.tags;
			this.notices = data.notices;
			this.styles = data.styles;
			this.videoUrl = data.videoUrl;
		}
	},
	methods: {

	}
}
</script>

<style scoped>
/* 复用个人主页的样式，但移除编辑按钮和交互相关样式 */
.container {
	min-height: 100vh;
	background: #f7f7f7;
	padding-bottom: 40rpx;
}

.user-info {
	padding: 40rpx 30rpx;
	background: #fff;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.user-avatar {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	margin-bottom: 20rpx;
}

.user-name {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 12rpx;
}

.gender-icon {
	color: #1890ff;
}

.user-title {
	font-size: 28rpx;
	color: #666;
}

.stats-bar {
	display: flex;
	justify-content: space-around;
	padding: 24rpx 0;
	background: #fff;
	border-top: 1rpx solid #f5f5f5;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.stat-num {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.stat-label {
	font-size: 24rpx;
	color: #999;
}

.content-section {
	padding: 24rpx 30rpx;
}

.info-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.card-header {
	margin-bottom: 24rpx;
}

.card-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
}

.info-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 24rpx;
}

.info-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.info-value {
	font-size: 28rpx;
	color: #333;
}

.info-label {
	font-size: 24rpx;
	color: #999;
}

.intro-content {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	padding: 0 24rpx;
}

.intro-placeholder {
	font-size: 28rpx;
	color: #999;
	padding: 0 24rpx;
}

.video-player {
	width: 100%;
	height: 420rpx; /* 16:9比例的高度 */
	border-radius: 12rpx;
	background-color: #000;
}

.photos-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
	margin-top: 11rpx;
}

.photo-item {
	width: calc((100% - 24rpx) / 3); /* 3列布局，间隔12rpx */
	height: 280rpx;
	position: relative;
	border-radius: 12rpx;
	overflow: hidden;
}

.photo-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.photo-count {
	position: absolute;
	right: 12rpx;
	bottom: 12rpx;
	background: rgba(0, 0, 0, 0.6);
	color: #fff;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 999rpx;
}

.tags-container {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	padding: 0 24rpx;
}

.tag-item {
	padding: 8rpx 24rpx;
	background: #f7f7f7;
	border-radius: 999rpx;
	font-size: 24rpx;
	color: #666;
}
</style>
