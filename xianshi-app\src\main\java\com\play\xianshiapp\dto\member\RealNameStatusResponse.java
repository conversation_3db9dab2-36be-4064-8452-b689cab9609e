package com.play.xianshiapp.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 实名认证状态响应DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@ApiModel(value = "实名认证状态响应")
public class RealNameStatusResponse {

    @ApiModelProperty(value = "实名认证状态：0-未认证，1-已认证")
    private Integer realNameStatus;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "实名认证时间")
    private LocalDateTime realNameVerifyTime;

    @ApiModelProperty(value = "是否已认证")
    public boolean isVerified() {
        return realNameStatus != null && realNameStatus == 1;
    }
}