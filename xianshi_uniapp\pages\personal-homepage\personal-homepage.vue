<template>
  <view class="container">
    <!-- 用户基本信息 -->
    <view class="user-info">
          <image
      class="user-avatar"
      :src="userInfo?.avatar || '/images/default-avatar.png'"
      mode="aspectFill"
    />
      <view class="user-name">
        <text>{{ userInfo?.nickname || "闲时" }}</text>
      </view>
      <view class="role-container">
        <text class="role">{{
          userInfo?.role === "visitor"
            ? "闲时公会"
            : userInfo?.role === "user"
            ? "普通用户"
            : "闲时达人"
        }}</text>
      </view>
      <view class="completion-bar">
        <view class="completion-progress">
          <view class="progress" :style="`width: ${completion}%`"></view>
        </view>
        <text class="completion-text">完整度 {{ completion }}%</text>
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="stats-bar">

      <view class="stat-item">
        <text class="stat-num">{{ userInfo?.fansCount || "0" }}</text>
        <text class="stat-label">粉丝</text>
      </view>
      <view class="stat-item">
        <text class="stat-num">{{ userInfo?.followCount || "0" }}</text>
        <text class="stat-label">关注</text>
      </view>
      <view class="stat-item">
        <text class="stat-num">{{ userInfo?.popularity || "0" }}</text>
        <text class="stat-label">浏览</text>
      </view>
    </view>

    <!-- 导航栏 -->
    <view class="tab-bar">
      <view
        class="tab-item"
        :class="{ active: currentTab === 0 }"
        @tap="switchTab"
        data-tab="0"
      >
        <text>主页</text>
      </view>
<!--      <view-->
<!--        class="tab-item"-->
<!--        :class="{ active: currentTab === 1 }"-->
<!--        @tap="switchTab"-->
<!--        data-tab="1"-->
<!--      >-->
<!--        <text>闲时</text>-->
<!--      </view>-->
      <view
        class="tab-item"
        :class="{ active: currentTab === 2 }"
        @tap="switchTab"
        data-tab="2"
      >
        <text>作品</text>
      </view>
      <view
        class="tab-item"
        :class="{ active: currentTab === 3 }"
        @tap="switchTab"
        data-tab="3"
      >
        <text>通告</text>
      </view>
    </view>

    <!-- 主页内容 -->
    <view class="content-section" v-if="currentTab === 0">
      <!-- 形象信息 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">形象信息</text>
          <text class="edit-btn" @tap="handleEdit">编辑</text>
        </view>
        <view class="info-grid">
          <view class="info-item">
            <text class="info-value">{{ 
              (profileInfo.gender !== undefined ? (profileInfo.gender === 1 ? '男' : profileInfo.gender === 2? '女': '--') :
               userInfo?.gender !== undefined ? (userInfo.gender === 1 ? '男' : userInfo === 2? '女': '--') : '--')
            }}</text>
            <text class="info-label">性别</text>
          </view>
          <view class="info-item">
            <text class="info-value">{{ profileInfo.age || "--" }}</text>
            <text class="info-label">年龄</text>
          </view>
          <view class="info-item">
            <text class="info-value">{{
              profileInfo.height ? profileInfo.height + "cm" : "--"
            }}</text>
            <text class="info-label">身高</text>
          </view>
          <view class="info-item">
            <text class="info-value">{{
              profileInfo.weight ? profileInfo.weight + "kg" : "--"
            }}</text>
            <text class="info-label">体重</text>
          </view>
          <view class="info-item">
            <text class="info-value">{{
              profileInfo.city || "--"
            }}</text>
            <text class="info-label">城市</text>
          </view>
        </view>
      </view>

      <!-- 自我简介 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">自我简介</text>
          <text class="edit-btn" @tap="handleIntroEdit">编辑</text>
        </view>
        <view class="intro-content" v-if="profileInfo.introduction">
          <text>{{ profileInfo.introduction }}</text>
        </view>
        <view class="intro-placeholder" v-else>
          <text>请填写自我简介</text>
        </view>
      </view>

      <!-- 形象视频 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">形象视频</text>
          <text class="add-btn" @tap="handleAddVideo">{{
            videoUrl ? "编辑" : "添加"
          }}</text>
        </view>
        <view
          class="video-placeholder"
          @tap="handleAddVideo"
          v-if="!videoUrl"
        >
          <text>+</text>
        </view>
        <video
          v-else
          class="video-player"
          :src="videoUrl"
          mode="aspectFill"
          controls
          @error="handleVideoError"
        ></video>
      </view>

      <!-- 形象照片 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">形象照片</text>
          <text class="add-btn" @tap="handleAddPhotos">添加</text>
        </view>
        <view class="photos-grid">
          <!-- 已有照片 -->
          <view
            class="photo-item"
            v-for="(photo, index) in photos.slice(0, 3)"
            :key="index"
            @tap="handleAddPhotos"
          >
            <image
              class="photo-image"
              :src="photo"
              mode="aspectFill"
            />
            <view
              class="photo-count"
              v-if="index === 2 && photos.length > 3"
            >
              <text>+{{ photos.length - 3 }}</text>
            </view>
          </view>
          <!-- 空白占位框 -->
          <view
            class="photo-item"
            @tap="handleAddPhotos"
            v-for="index in 3 - photos.length"
            :key="'empty-' + index"
            v-if="photos.length < 3"
          >
            <view class="photo-placeholder">
              <text>+</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 形象风格 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">形象风格</text>
          <text class="edit-btn" @tap="handleStyleEdit">编辑</text>
        </view>
        <view class="tags-container">
          <block v-if="styles.length > 0">
            <view class="tag-item" v-for="item in styles" :key="item">{{
              item
            }}</view>
          </block>
          <view class="tag-item empty" v-else>请添加形象风格</view>
        </view>
      </view>

      <!-- 身份标签 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">身份标签</text>
          <text class="edit-btn" @tap="handleTagsEdit">编辑</text>
        </view>
        <view class="tags-container">
          <block v-if="tags.length > 0">
            <view class="tag-item" v-for="item in tags" :key="item">{{
              item
            }}</view>
          </block>
          <view class="tag-item empty" v-else>请添加身份标签</view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <button class="share-btn" open-type="share">分享</button>
      <button class="save-btn" @tap="saveProfileToServer">保存</button>
      <button class="preview-btn" @tap="handlePreview">预览</button>
    </view>
  </view>
</template>

<script>
//const app = getApp().globalData;
export default {
  data() {
    return {
      currentTab: 0,
      userInfo: null,
      profileInfo: {
        age: "",
        height: "",
        weight: "",
        city: "",
        constellation: "",
        introduction: "",
      },
      photos: [],
      tags: [],
      notices: [],
      styles: [],
      videoUrl: "",
      completion: 0,
      cityList: [], // 达人城市列表
      projectList: [], // 达人项目列表
    };
  },
  async onShow() {
    const pages = getCurrentPages();
    const editPage = pages[pages.length - 2];

    // 清除可能存在的旧用户数据缓存，确保显示当前用户信息
    this.clearUserDataCache();

    // 重新获取个人信息以确保数据是最新的
    await this.getUserInfo();

    // 获取个人资料完整度
    this.updateCompletion();

    // 保存数据到本地存储，以便达人主页可以读取
    this.updateLocalStorage();
  },
  async onLoad() {
    uni.setNavigationBarTitle({
      title: "个人主页",
    });

    // 清除可能存在的旧用户数据缓存，确保显示当前用户信息
    this.clearUserDataCache();

    // 获取个人信息
    await this.getUserInfo();

    // 如果API返回的数据不完整，则尝试从本地存储获取补充信息
    if (!this.userInfo || !this.userInfo.nickname) {
      const userInfo = uni.getStorageSync("userInfo") || {
        nickname: "小闲",
        role: "user",
        avatar: "",
      };

      userInfo.roleText =
        userInfo.role === "visitor"
          ? "闲时公会"
          : userInfo.role === "user"
          ? "普通用户"
          : "闲时达人";

      this.userInfo = userInfo;
    }

    const profileData = uni.getStorageSync("profileData") || {};

    console.log("你好profileData", profileData);

    // 仅在API未返回数据时使用本地存储
    if (!this.profileInfo.age && !this.profileInfo.height) {
      this.profileInfo = profileData.profileInfo || this.profileInfo;
    }

    if (this.photos.length === 0) {
      this.photos = profileData.photos || [];
    }

    if (this.tags.length === 0) {
      this.tags = profileData.tags || [];
    }

    if (!this.notices || this.notices.length === 0) {
      this.notices = profileData.notices || [];
    }
    console.log("this.styles", this.styles);
    if (this.styles.length === 0) {
      this.styles = profileData.styles || [];
    }

    if (!this.videoUrl) {
      this.videoUrl = profileData.videoUrl || "";
    }

    if (this.cityList.length === 0) {
      this.cityList = profileData.cityList || [];
    }

    if (this.projectList.length === 0) {
      this.projectList = profileData.projectList || [];
    }
  },
  methods: {
    switchTab(e) {
      const tab = Number(e.currentTarget.dataset.tab);
      this.currentTab = tab;
    },

    // 形象编辑
    handleEdit() {
      const { age, height, weight, city,constellation } = this.profileInfo;
      const avatar = this.userInfo?.avatar || '';
      const gender = this.userInfo?.gender !== undefined ? this.userInfo.gender : '';
      uni.navigateTo({
        url: `/pages/edit-profile/edit-profile?avatar=${encodeURIComponent(avatar)}&gender=${gender}&age=${age}&height=${height}&weight=${weight}&city=${city}&constellation=${constellation}`,
        success: () => {
          // 更新本地存储
          this.updateLocalStorage();
        }
      });
    },

    handleAdd() {
      const video = this.videoUrl;
      uni.navigateTo({
        url: `/pages/edit-videos/edit-videos?video=${encodeURIComponent(
          video
        )}`,
        success: () => {
          // 更新本地存储
          this.updateLocalStorage();
        }
      });
    },

    handleIntroEdit() {
      const introduction = this.profileInfo.introduction || "";
      uni.navigateTo({
        url: `/pages/edit-introduction/edit-introduction?introduction=${encodeURIComponent(
          introduction
        )}`,
        success: () => {
          // 更新本地存储
          this.updateLocalStorage();
        }
      });
    },



    handleAddPhotos() {
      const photos = JSON.stringify(this.photos);
      uni.navigateTo({
        url: `/pages/edit-photos/edit-photos?photos=${encodeURIComponent(
          photos
        )}`,
        success: () => {
          // 更新本地存储
          this.updateLocalStorage();
        }
      });
    },

    handleTagsEdit() {
      const tags = JSON.stringify(this.tags);
      uni.navigateTo({
        url: `/pages/edit-tags/edit-tags?tags=${encodeURIComponent(tags)}`,
        success: () => {
          // 更新本地存储
          this.updateLocalStorage();
        }
      });
    },

    handleNoticeEdit() {
      if (this.userInfo?.role !== "expert") {
        return;
      }
      const notices = JSON.stringify(this.notices);
      uni.navigateTo({
        url: `/pages/edit-notice/edit-notice?notices=${encodeURIComponent(
          notices
        )}`,
      });
    },

    handleStyleEdit() {
      const styles = JSON.stringify(this.styles);
      uni.navigateTo({
        url: `/pages/edit-style/edit-style?styles=${encodeURIComponent(
          styles
        )}`,
        success: () => {
          // 更新本地存储
          this.updateLocalStorage();
        }
      });
    },

    handleAddVideo() {
      const video = this.videoUrl;
      uni.navigateTo({
        url: `/pages/edit-videos/edit-videos?video=${encodeURIComponent(
          video
        )}`,
        success: () => {
          // 更新本地存储
          this.updateLocalStorage();
        }
      });
    },

    // 处理视频加载错误
    handleVideoError(e) {
      console.error('视频加载错误:', e, this.videoUrl);
      uni.showToast({
        title: '视频加载失败，请重新上传',
        icon: 'none'
      });
    },

    // 保存数据到服务器
    async saveProfileToServer() {
      try {
        // 构建要保存的数据对象
        const profileData = {
          avatar: this.profileInfo.avatar || this.userInfo?.avatar,
          gender: this.profileInfo.gender !== undefined ? this.profileInfo.gender : this.userInfo?.gender,
          age: this.profileInfo.age,
          height: this.profileInfo.height,
          weight: this.profileInfo.weight,
          constellation: this.profileInfo.constellation,
          context: this.profileInfo.introduction,
          images: this.photos.join(','),
          identify: this.tags.join(','),
          styleIds: this.styles.join(','),
          video: this.videoUrl
        };

        // 保存到本地存储，以便达人主页可以读取
        uni.setStorageSync("profileData", {
          profileInfo: this.profileInfo,
          photos: this.photos,
          tags: this.tags,
          notices: this.notices,
          styles: this.styles,
          videoUrl: this.videoUrl,
          cityList: this.cityList,
          projectList: this.projectList
        });

        // 调用API保存数据
        const app = getApp().globalData;
        let res = await this.$requestHttp.put(app.commonApi.updateMemberInfo, {
          data: profileData
        });
        if (res && res.code === 200) {
          uni.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000
          });
          // 更新完整度
          this.updateCompletion();
        } else {
          uni.showToast({
            title: res.msg || '保存失败',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('保存数据失败', error);
        uni.showToast({
          title: '保存失败，请稍后再试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    handlePreview() {
      // 先保存到服务器
      this.saveProfileToServer();

      const previewData = {
        userInfo: this.userInfo,
        profileInfo: this.profileInfo,
        photos: this.photos,
        tags: this.tags,
        notices: this.notices,
        styles: this.styles,
        videoUrl: this.videoUrl,
      };

      uni.navigateTo({
        url: `/pages/preview-homepage/preview-homepage?data=${encodeURIComponent(JSON.stringify(previewData))}`,
      });
    },

    onShareAppMessage() {
      return {
        title: `${uni.getStorageSync("userInfo")?.nickname || "小闲"}的个人主页`,
        path: "/pages/personal-homepage/personal-homepage",
        imageUrl:
          this.photos[0] ||
          this.userInfo?.avatar ||
          "../../static/images/default-avatar.png",
      };
    },

    async updateCompletion() {
      // 获取个人资料完整度
      const app = getApp().globalData;
      let res = await this.$requestHttp.get(app.commonApi.getMemberCompletion, {});
      console.log("获取个人资料完整度", res);
      this.completion = res.data;
    },
    // 获取个人信息
    async getUserInfo() {
      const app = getApp().globalData;
      let res = await this.$requestHttp.get(app.commonApi.getCurrentMemberInfo, {});
      console.log("{|获取当前个人信息|}", res);

      if (res && res.data) {
        const userData = res.data;

        // 更新用户基本信息
        this.userInfo = {
          avatar: userData.avatar || userData.wxAvatar || "",
          nickname: userData.nickname || userData.wxName || "闲时",
          role: this.getCurrentRole(userData),
          roleText: this.getRoleText(userData),
          popularity: userData.popularity || "0",
          fansCount: userData.fansCount || "0",
          followCount: userData.followCount || "0",
          city: userData.city || "--",
          id: userData.id,
          tel: userData.tel,
          isVip: userData.isVip,
          gender: userData.gender,
          gold: userData.gold,
          rating: userData.rating,
          orderCount: userData.orderCount,
          applyStatus: userData.applyStatus
        };

        // 更新个人资料信息
        this.profileInfo = {
          age: userData.age || "",
          height: userData.height || "",
          weight: userData.weight, // 接口返回值中没有weight字段，保持原状
          constellation: userData.constellation || "",
          city: userData.city || "--",
          introduction: userData.context || "" // 简介字段是context
        };
        console.log("profileInfo--->",this.profileInfo)
        // 更新视频URL，确保是完整的URL
        if (userData.video) {
          this.videoUrl = userData.video;
          // 确保URL是完整的
          if (this.videoUrl && !this.videoUrl.startsWith('http')) {
            this.videoUrl = 'https:' + this.videoUrl;
          }
        }
        
        // 更新相册
        if (userData.images) {
          try {
            let photoList = userData.images.split(',').filter(img => img);
            // 确保所有图片URL都是完整的
            this.photos = photoList.map(url => {
              if (url && !url.startsWith('http')) {
                return 'https:' + url;
              }
              return url;
            });
          } catch (e) {
            this.photos = [];
          }
        }
        
        // 更新标签和风格
        if (userData.identify) {
          try {
            this.tags = userData.identify.split(',').filter(tag => tag);
          } catch (e) {
            this.tags = [];
          }
        }
        
        if (userData.styleIds) {
          try {
            this.styles = userData.styleIds.split(',').filter(style => style);
          } catch (e) {
            this.styles = [];
          }
        }

        // 更新达人城市列表
        if (userData.cityList && Array.isArray(userData.cityList)) {
          this.cityList = userData.cityList;
        }

        // 更新达人项目列表
        if (userData.projectList && Array.isArray(userData.projectList)) {
          this.projectList = userData.projectList;
        }
        
        // 保存到本地存储
        // uni.setStorageSync('userInfo', this.userInfo);
        // uni.setStorageSync('profileData', {
        //   profileInfo: this.profileInfo,
        //   photos: this.photos,
        //   tags: this.tags,
        //   notices: this.notices,
        //   styles: this.styles,
        //   videoUrl: this.videoUrl,
        //   cityList: this.cityList,
        //   projectList: this.projectList
        // });
      }
    },
    
    // 获取当前角色文本
    getRoleText(userData) {
      if (!userData || !userData.roles || userData.roles.length === 0) {
        return "闲时公会";
      }
      
      const isExpert = userData.roles.some(role => role.code === "expert");
      if (isExpert) return "闲时达人";
      return "普通用户";
    },
    
    // 获取当前角色
    getCurrentRole(userData) {
      if (!userData || !userData.roles || userData.roles.length === 0) {
        return "visitor";
      }
      
      const isExpert = userData.roles.some(role => role.code === "expert");
      return isExpert ? "expert" : "user";
    },

    handleApplyExpert() {
      uni.navigateTo({
        url: "/pages/apply-expert/apply-expert",
      });
    },

    // 保存数据到本地存储，以便达人主页可以读取
    updateLocalStorage() {
      uni.setStorageSync("profileData", {
        profileInfo: this.profileInfo,
        photos: this.photos,
        tags: this.tags,
        notices: this.notices,
        styles: this.styles,
        videoUrl: this.videoUrl,
        city:this.city,
        constellation:this.constellation,
        cityList: this.cityList,
        projectList: this.projectList
      });
    },

    // 清除用户数据缓存，确保新用户登录时不会显示上一个用户的信息
    clearUserDataCache() {
      // 重置所有用户相关的数据
      this.userInfo = null;
      this.profileInfo = {
        age: "",
        height: "",
        weight: "",
        city: "",
        constellation: "",
        introduction: "",
      };
      this.photos = [];
      this.tags = [];
      this.notices = [];
      this.styles = [];
      this.videoUrl = "";
      this.completion = 0;
      this.cityList = [];
      this.projectList = [];
      
      // 清除本地存储中可能存在的旧数据
      try {
        uni.removeStorageSync('profileData');
        // 注意：不清除userInfo，因为它可能包含登录状态信息
      } catch (e) {
        console.log('清除缓存失败:', e);
      }
    }
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 120rpx;
}

/* 用户信息 */
.user-info {
  padding: 40rpx 30rpx;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  text-align: center;
}

/* 数据统计 */
.stats-bar {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0;
  background: #fff;
  border-top: 1rpx solid #f5f5f5;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-num {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 导航栏 */
.tab-bar {
  display: flex;
  background: #fff;
  margin-top: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.tab-item {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #333;
  font-weight: 500;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 48rpx;
  height: 4rpx;
  background: #ff8c00;
  border-radius: 2rpx;
}

/* 内容区域 */
.content-section {
  padding: 24rpx 30rpx;
}

.info-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.edit-btn,
.add-btn {
  font-size: 26rpx;
  color: #ff8c00;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.info-label {
  font-size: 24rpx;
  color: #999;
}

.video-placeholder {
  height: 420rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #999;
}

.video-player {
  width: 100%;
  height: 420rpx; /* 16:9比例的高度 */
  border-radius: 12rpx;
  background-color: #000;
  margin-bottom: 100rpx; /* 添加底部间距，避免控制栏遮挡底部按钮 */
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 30rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 16rpx;
  z-index: 999; /* 增加z-index确保按钮位于最上层 */
}

.share-btn,
.save-btn,
.preview-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 999rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.share-btn {
  background: #fff;
  border: 1rpx solid #ff8c00;
  color: #ff8c00;
}

.save-btn {
  background: #ff8c00;
  color: #fff;
}

.preview-btn {
  background: #ff8c00;
  color: #fff;
  opacity: 0.8;
}

.intro-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  padding: 0 24rpx;
}

.intro-placeholder {
  font-size: 28rpx;
  color: #999;
  padding: 0 24rpx;
}

/* 形象照片样式 */
.photos-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 11rpx;
}

.photo-item {
  width: calc((100% - 24rpx) / 3); /* 3列布局，间隔12rpx */
  height: 280rpx;
  position: relative;
  background: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 0 24rpx;
}

.tag-item {
  padding: 8rpx 24rpx;
  background: #f7f7f7;
  border-radius: 999rpx;
  font-size: 24rpx;
  color: #666;
}

.tag-item.empty {
  color: #999;
  background: #f5f5f5;
}

.photo-count {
  position: absolute;
  right: 12rpx;
  bottom: 12rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 999rpx;
}

.completion-bar {
  margin-top: 20rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.completion-progress {
  width: 300rpx;
  height: 6rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.completion-progress .progress {
  height: 100%;
  background: #ff8c00;
  transition: width 0.3s ease;
}

.completion-text {
  font-size: 24rpx;
  color: #666;
}

.apply-expert-tip {
  padding: 30rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #ff8c00;
  font-size: 28rpx;
}

.apply-expert-tip .arrow {
  color: #999;
  font-size: 24rpx;
}

.apply-expert-tip:active {
  opacity: 0.8;
}
</style>
