package com.play.xianshiapp.controller.recharge;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.recharge.*;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.CMemberRechargeService;
import com.play.xianshibusiness.service.CMemberService;
import com.play.xianshibusiness.service.CRechargePackageService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 充值控制器
 */
@RestController
@RequestMapping("/api/recharge")
@Api(tags = "充值API")
public class RechargeController {

    @Resource
    private CRechargePackageService rechargePackageService;

    @Resource
    private CMemberRechargeService memberRechargeService;

    @Resource
    private CMemberService memberService;

    @GetMapping("/packages")
    @ApiOperation(value = "获取所有充值套餐")
    public Result<List<RechargePackageVO>> getAllPackages() {
        return ResultUtils.success(rechargePackageService.getAllPackages());
    }

    @GetMapping("/recommend-packages")
    @ApiOperation(value = "获取推荐充值套餐")
    public Result<List<RechargePackageVO>> getRecommendPackages() {
        return ResultUtils.success(rechargePackageService.getRecommendPackages());
    }

    @GetMapping("/package/{id}")
    @ApiOperation(value = "获取充值套餐详情")
    public Result<RechargePackageVO> getPackageDetail(
            @ApiParam(value = "套餐ID", required = true) @PathVariable String id) {
        return ResultUtils.success(rechargePackageService.getPackageDetail(id));
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建充值订单")
    @RequireLogin
    public Result<PayResultDTO> createRechargeOrder(@RequestBody @Valid RechargeDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberRechargeService.createRechargeOrder(dto, memberId));
    }

    @PostMapping("/callback")
    @ApiOperation(value = "支付回调")
    public Result<Boolean> payCallback(@RequestBody @Valid PayCallbackDTO dto) {
        return ResultUtils.success(memberRechargeService.handlePayCallback(dto));
    }

    @GetMapping("/records")
    @ApiOperation(value = "获取充值记录")
    @RequireLogin
    public Result<Page<RechargeRecordVO>> getRechargeRecords(RechargeQueryDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberRechargeService.pageRechargeRecords(dto, memberId));
    }

    @GetMapping("/record/{id}")
    @ApiOperation(value = "获取充值记录详情")
    @RequireLogin
    public Result<RechargeRecordVO> getRechargeDetail(
            @ApiParam(value = "充值记录ID", required = true) @PathVariable String id) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberRechargeService.getRechargeDetail(id, memberId));
    }

    @PostMapping("/test-gold")
    @ApiOperation(value = "测试环境免费获取金币")
    @RequireLogin
    public Result<Boolean> getTestGold() {
        String memberId = PrincipalUtil.getMemberId();
        // 为测试用户增加1000金币
        memberService.increaseGold(memberId, BigDecimal.valueOf(1000), "测试环境免费金币");
        return ResultUtils.success(true);
    }
}