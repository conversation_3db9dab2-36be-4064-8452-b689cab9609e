# 地图地址选择器

基于腾讯地图API开发的完整地图地址选择功能，为小程序提供现代化的位置选择体验。

## 功能特性

### 🗺️ 地图功能
- **交互式地图**: 支持拖拽、缩放等地图操作
- **实时定位**: 自动获取用户当前位置
- **中心点标记**: 动态标记当前选择位置
- **地图控制**: 缩放按钮和定位按钮

### 🔍 搜索功能
- **实时搜索**: 输入关键词实时显示搜索建议
- **智能提示**: 基于腾讯地图的地点搜索建议
- **防抖优化**: 减少不必要的API请求
- **全国搜索**: 支持全国范围的地点搜索

### 📍 位置服务
- **逆地理编码**: 坐标自动转换为详细地址
- **附近地点**: 显示当前位置附近的兴趣点(POI)
- **分类图标**: 不同类型地点显示对应图标
- **距离计算**: 显示到当前位置的距离

### 🎨 用户体验
- **现代化UI**: 圆角、阴影、渐变等视觉元素
- **流畅动画**: 标记点动画和交互反馈
- **响应式设计**: 适配不同屏幕尺寸
- **加载状态**: 友好的加载提示

## 使用方法

### 1. 基础调用
```javascript
// 打开地图选择器
uni.navigateTo({
  url: '/pages/map-selector/map-selector'
})
```

### 2. 带参数调用
```javascript
// 从指定位置开始选择
uni.navigateTo({
  url: '/pages/map-selector/map-selector?longitude=116.397428&latitude=39.90923&title=天安门&address=北京市东城区'
})
```

### 3. 获取选择结果
在调用页面的`onShow`生命周期中获取选择结果：

```javascript
onShow() {
  // 获取从地图选择页面返回的位置数据
  const selectedLocation = uni.getStorageSync('selectedLocation');
  if (selectedLocation) {
    console.log('选择的位置:', selectedLocation);
    
    // 使用位置数据
    this.location = selectedLocation.address;
    this.addressValue = selectedLocation.name;
    this.latitude = selectedLocation.latitude;
    this.longitude = selectedLocation.longitude;
    
    // 清除存储的位置数据
    uni.removeStorageSync('selectedLocation');
  }
}
```

## 返回数据格式

选择位置后，会将以下数据保存到本地存储：

```javascript
{
  name: "位置名称",           // 地点名称或POI名称
  address: "详细地址",        // 完整的地址信息
  latitude: 30.572816,       // 纬度
  longitude: 104.066803      // 经度
}
```

## API配置

### 腾讯地图API Key
确保在`manifest.json`中配置了有效的腾讯地图API密钥：

```json
{
  "h5": {
    "sdkConfigs": {
      "maps": {
        "qqmap": {
          "key": "YOUR_TENCENT_MAP_API_KEY"
        }
      }
    }
  }
}
```

### 使用的API接口
- **逆地理编码**: `https://apis.map.qq.com/ws/geocoder/v1/`
- **地点搜索**: `https://apis.map.qq.com/ws/place/v1/search`
- **搜索建议**: `https://apis.map.qq.com/ws/place/v1/suggestion`

## 权限配置

### 小程序权限
在小程序管理后台配置以下权限：
- 位置信息权限
- 网络请求域名：`https://apis.map.qq.com`

### App权限
在`manifest.json`中确保包含定位权限：
```json
{
  "app-plus": {
    "distribute": {
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>"
        ]
      }
    }
  }
}
```

## 兼容性

- ✅ 微信小程序
- ✅ H5
- ✅ App (Android/iOS)
- ✅ 支付宝小程序
- ✅ 百度小程序

## 技术特点

### 跨平台兼容
- H5环境使用JSONP方式调用API避免CORS问题
- 小程序和App环境使用uni.request
- 统一的接口设计，无需关心平台差异

### 性能优化
- 搜索防抖，减少API调用频次
- 逆地理编码防抖，避免频繁请求
- 分页加载附近地点，提升加载速度
- 合理的缓存策略

### 错误处理
- 网络错误时的降级处理
- 定位失败时使用默认位置
- 友好的错误提示信息

## 自定义配置

### 修改默认位置
在组件的`data`中修改默认经纬度：
```javascript
data() {
  return {
    longitude: 104.066803, // 默认经度
    latitude: 30.572816,   // 默认纬度
    // ...
  }
}
```

### 修改搜索范围
在`loadNearbyPlaces`方法中修改搜索半径：
```javascript
// 修改boundary参数中的1000为其他值（单位：米）
boundary: `nearby(${this.latitude},${this.longitude},1000)`
```

### 自定义地点图标
在`getPlaceIcon`方法中添加或修改图标映射：
```javascript
const iconMap = {
  '美食': '🍽️',
  '酒店': '🏨',
  // 添加更多分类图标
  '自定义分类': '🎯'
};
```

## 注意事项

1. **API密钥**: 确保使用有效的腾讯地图API密钥
2. **请求频率**: 注意API调用频率限制
3. **网络环境**: 确保网络连接正常
4. **权限申请**: 及时申请和处理定位权限
5. **错误处理**: 做好网络异常的降级处理

## 更新日志

### v1.0.0 (2025-01-30)
- 🎉 初始版本发布
- ✨ 完整的地图选择功能
- ✨ 搜索建议和附近地点
- ✨ 现代化UI设计
- ✨ 跨平台兼容支持
