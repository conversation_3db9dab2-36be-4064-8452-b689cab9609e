package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 达人城市设置实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_talent_city")
@ApiModel("达人城市设置表")
public class CTalentCity extends BaseObjPo {

    @ApiModelProperty("达人ID")
    private String talentId;
    
    @ApiModelProperty("城市编码")
    private String cityCode;
    
    @ApiModelProperty("城市名称")
    private String cityName;
    
    @ApiModelProperty("排序")
    private Integer sort;
} 