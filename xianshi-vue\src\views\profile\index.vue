<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div class="user-profile">
            <div class="user-avatar-container">
              <el-avatar :size="100" :src="userInfo.avatar || defaultAvatar" :key="avatarKey"></el-avatar>
              <el-upload
                class="avatar-uploader"
                action="#"
                :http-request="uploadAvatar"
                :show-file-list="false"
                accept="image/*"
              >
                <div class="upload-icon">
                  <i class="el-icon-camera"></i>
                </div>
              </el-upload>
            </div>
            <div class="user-info">
              <h3>{{ userInfo.name || '管理员' }}</h3>
              <p>{{ userInfo.role || '系统管理员' }}</p>
            </div>
            <el-divider></el-divider>
            <div class="user-stats">
              <div class="stat-item">
                <p class="stat-title">上次登录</p>
                <p class="stat-value">{{ userInfo.lastLoginTime || '未知' }}</p>
              </div>
              <div class="stat-item">
                <p class="stat-title">登录次数</p>
                <p class="stat-value">{{ userInfo.loginCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="basic">
              <el-form ref="basicForm" :model="basicForm" :rules="basicRules" label-width="100px">
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="basicForm.username" disabled></el-input>
                </el-form-item>
                <el-form-item label="昵称" prop="name">
                  <el-input v-model="basicForm.name" placeholder="请输入昵称"></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="basicForm.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="basicForm.email" placeholder="请输入邮箱"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="updateBasicInfo" :loading="loading.basic">保存</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="password">
              <el-form ref="passwordForm" :model="passwordForm" :rules="passwordRules" label-width="100px">
                <el-form-item label="旧密码" prop="oldPassword">
                  <el-input v-model="passwordForm.oldPassword" type="password" placeholder="请输入旧密码" show-password></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码" show-password></el-input>
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="updatePassword" :loading="loading.password">保存</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getUserProfile, updateUserProfile, updateUserPassword } from '@/api/user';
import { uploadFile } from '@/api/system';
import { mapGetters } from 'vuex';

export default {
  name: 'Profile',
  data() {
    // 确认密码验证
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };
    
    return {
      activeTab: 'basic',
      defaultAvatar: require('@/assets/images/default-avatar.png'),
      avatarKey: 0,
      loading: {
        basic: false,
        password: false
      },
      userInfo: {
        name: '',
        avatar: '',
        role: '',
        lastLoginTime: '',
        loginCount: 0
      },
      basicForm: {
        username: '',
        name: '',
        phone: '',
        email: ''
      },
      basicRules: {
        name: [
          { required: true, message: '请输入昵称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    ...mapGetters([
      'name',
      'avatar'
    ])
  },
  created() {
    this.getProfile();
  },
  methods: {
    // 获取个人资料
    getProfile() {
      // 如果接口未实现，使用vuex中的数据
      this.userInfo.name = this.name;
      this.userInfo.avatar = this.avatar;
      this.userInfo.role = '系统管理员';
      this.userInfo.lastLoginTime = new Date().toLocaleString();
      this.userInfo.loginCount = 1;
      
      this.basicForm.username = 'admin';
      this.basicForm.name = this.name;
      this.basicForm.phone = '';
      this.basicForm.email = '';
      
      // 如果接口已实现，使用以下代码
      /*
      getUserProfile().then(response => {
        const { data } = response;
        this.userInfo = data;
        
        this.basicForm.username = data.username;
        this.basicForm.name = data.name;
        this.basicForm.phone = data.phone;
        this.basicForm.email = data.email;
      });
      */
    },
    
    // 更新基本信息
    updateBasicInfo() {
      this.$refs.basicForm.validate(valid => {
        if (valid) {
          this.loading.basic = true;
          
          // 模拟API调用
          setTimeout(() => {
            this.$message.success('个人资料更新成功');
            this.loading.basic = false;
            
            // 更新Vuex中的用户信息
            this.$store.commit('user/SET_NAME', this.basicForm.name);
            this.userInfo.name = this.basicForm.name;
          }, 1000);
          
          // 如果接口已实现，使用以下代码
          /*
          updateUserProfile(this.basicForm).then(() => {
            this.$message.success('个人资料更新成功');
            this.loading.basic = false;
            
            // 更新Vuex中的用户信息
            this.$store.commit('user/SET_NAME', this.basicForm.name);
          }).catch(() => {
            this.loading.basic = false;
          });
          */
        }
      });
    },
    
    // 更新密码
    updatePassword() {
      this.$refs.passwordForm.validate(valid => {
        if (valid) {
          this.loading.password = true;
          
          // 模拟API调用
          setTimeout(() => {
            this.$message.success('密码修改成功');
            this.loading.password = false;
            this.passwordForm = {
              oldPassword: '',
              newPassword: '',
              confirmPassword: ''
            };
          }, 1000);
          
          // 如果接口已实现，使用以下代码
          /*
          updateUserPassword(this.passwordForm).then(() => {
            this.$message.success('密码修改成功');
            this.loading.password = false;
            this.passwordForm = {
              oldPassword: '',
              newPassword: '',
              confirmPassword: ''
            };
          }).catch(() => {
            this.loading.password = false;
          });
          */
        }
      });
    },
    
    // 上传头像
    uploadAvatar(options) {
      const formData = new FormData();
      formData.append('file', options.file);
      formData.append('type', 'avatar');
      
      console.log('开始上传头像:', options.file.name, options.file.size);
      
      // 显示上传中提示
      const loading = this.$loading({
        lock: true,
        text: '头像上传中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      uploadFile(formData).then(response => {
        loading.close();
        console.log('上传头像响应:', response);
        
        if (response.code === 200) {
          // 确保URL是绝对路径
          let avatarUrl = response.data;
          if (avatarUrl && !avatarUrl.startsWith('http') && !avatarUrl.startsWith('/')) {
            avatarUrl = '/' + avatarUrl;
          }
          
          console.log('设置新头像URL:', avatarUrl);
          
          // 更新本地显示
          this.userInfo.avatar = avatarUrl;
          this.avatarKey++; // 更新key强制刷新头像
          
          // 更新Vuex中的头像
          this.$store.dispatch('user/updateAvatar', avatarUrl).then(() => {
            console.log('Vuex头像更新成功');
            this.$message.success('头像上传成功');
            
            // 强制刷新Navbar组件
            this.$root.$emit('avatar-updated', avatarUrl);
          }).catch(err => {
            console.error('Vuex头像更新失败:', err);
          });
        } else {
          this.$message.error(response.msg || '上传失败');
        }
      }).catch(error => {
        loading.close();
        console.error('上传头像错误:', error);
        this.$message.error('上传失败: ' + (error.message || '未知错误'));
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.user-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  
  .user-avatar-container {
    position: relative;
    margin-bottom: 20px;
    
    .avatar-uploader {
      position: absolute;
      bottom: 0;
      right: 0;
      
      .upload-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #F8D010;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        i {
          color: #ffffff;
          font-size: 16px;
        }
      }
    }
  }
  
  .user-info {
    text-align: center;
    
    h3 {
      margin: 10px 0 5px;
      font-size: 18px;
      font-weight: 500;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #909399;
    }
  }
  
  .user-stats {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-top: 10px;
    
    .stat-item {
      text-align: center;
      
      .stat-title {
        font-size: 12px;
        color: #909399;
        margin-bottom: 5px;
      }
      
      .stat-value {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
      }
    }
  }
}

.el-divider {
  margin: 15px 0;
}
</style> 