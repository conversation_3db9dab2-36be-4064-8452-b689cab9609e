/* eslint-disable */
import axios from 'axios';
import { MessageBox, Message } from 'element-ui';
import store from '@/store';
import { getToken } from '@/utils/auth';
import config from '@/config';

// 创建axios实例
const service = axios.create({
  baseURL: config.BASE_API, // 使用配置文件的API路径
  timeout: 120000 // 增加超时时间，文件上传需要更长时间
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求前做些什么
    if (store.getters.token) {
      // 让每个请求携带自定义token
      const token = getToken();
      // 确保token格式正确（以Bearer开头）
      if (token && !token.startsWith('Bearer ')) {
        config.headers['Authorization'] = `Bearer ${token}`;
      } else {
        config.headers['Authorization'] = token;
      }
    }
    
    // 如果是文件上传，不要处理Content-Type，让浏览器自动设置
    if (config.headers && config.headers['Content-Type'] === 'multipart/form-data') {
      console.log('检测到文件上传请求');
    }
    
    return config;
  },
  error => {
    // 对请求错误做些什么
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    console.log('API响应原始数据:', response.config.url, res);

    // 如果返回的状态码不是200，则判断为错误
    if (res.code !== 200) {
      Message({
        message: res.message || res.msg || '系统错误',
        type: 'error',
        duration: 5 * 1000
      });

      // 401: 未登录或token过期; 403: 无权限; 50008: 非法token; 50012: 其他客户端登录; 50014: Token过期;
      if (res.code === 401 || res.code === 50008 || res.code === 50012 || res.code === 50014) {
        // 重新登录
        MessageBox.confirm('您已登出，可以取消以保留在该页面上，或者重新登录', '确认登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload();
          });
        });
      }
      return Promise.reject(new Error(res.message || res.msg || '系统错误'));
    } else {
      return res;
    }
  },
  error => {
    console.log('err', error); // for debug
    
    // 处理HTTP错误状态码
    if (error.response) {
      const { status } = error.response;
      
      // 401: 未登录或token过期
      if (status === 401) {
        MessageBox.confirm('您的登录已过期，请重新登录', '登录过期', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload();
          });
        });
      } 
      // 403: 无权限
      else if (status === 403) {
        Message({
          message: '您没有权限访问该资源',
          type: 'error',
          duration: 5 * 1000
        });
      } 
      // 其他错误
      else {
        Message({
          message: error.response && error.response.data && (error.response.data.message || error.response.data.msg) || error.message || '请求失败',
          type: 'error',
          duration: 5 * 1000
        });
      }
    } else {
      Message({
        message: error.message || '网络连接异常，请检查您的网络',
        type: 'error',
        duration: 5 * 1000
      });
    }
    
    return Promise.reject(error);
  }
);

export default service; 