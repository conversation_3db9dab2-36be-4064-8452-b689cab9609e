package com.play.xianshiapp.controller.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.gold.GoldRecordQueryDTO;
import com.play.xianshibusiness.dto.gold.GoldRecordVO;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.CMemberGoldRecordService;
import com.play.xianshibusiness.service.CMemberService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 前端用户金币管理控制器
 */
@RestController
@RequestMapping("/api/member/gold")
@Api(tags = "用户金币管理API")
public class MemberGoldController {

    @Resource
    private CMemberGoldRecordService goldRecordService;
    
    @Resource
    private CMemberService memberService;
    
    /**
     * 获取当前用户金币余额
     */
    @GetMapping("/balance")
    @ApiOperation(value = "获取当前用户金币余额")
    @RequireLogin
    public Result<Integer> getGoldBalance() {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(memberService.getMemberGoldBalance(memberId));
    }
    
    /**
     * 获取当前用户金币记录
     */
    @GetMapping("/records")
    @ApiOperation(value = "获取当前用户金币记录")
    @RequireLogin
    public Result<Page<GoldRecordVO>> getGoldRecords(
            @ApiParam(value = "页码", defaultValue = "1") 
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页条数", defaultValue = "10") 
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "操作类型") 
            @RequestParam(value = "operationType", required = false) Integer operationType,
            @ApiParam(value = "开始时间") 
            @RequestParam(value = "startTime", required = false) String startTime,
            @ApiParam(value = "结束时间") 
            @RequestParam(value = "endTime", required = false) String endTime) {
        
        String memberId = PrincipalUtil.getMemberId();
        
        // 构建查询条件
        GoldRecordQueryDTO queryDTO = new GoldRecordQueryDTO();
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(pageSize);
        queryDTO.setMemberId(memberId);
        queryDTO.setOperationType(operationType);
        queryDTO.setCreateTimeStart(startTime);
        queryDTO.setCreateTimeEnd(endTime);
        
        return ResultUtils.success(goldRecordService.pageGoldRecords(queryDTO));
    }
    
    /**
     * 获取金币记录详情
     */
    @GetMapping("/record/{id}")
    @ApiOperation(value = "获取金币记录详情")
    @RequireLogin
    public Result<GoldRecordVO> getGoldRecordDetail(
            @ApiParam(value = "记录ID", required = true) @PathVariable String id) {
        String memberId = PrincipalUtil.getMemberId();
        
        // 获取记录详情并验证是否属于当前用户
        GoldRecordVO record = goldRecordService.getGoldRecordDetail(id);
        if (record != null && !memberId.equals(record.getMemberId())) {
            return ResultUtils.error(403, "无权访问该记录");
        }
        
        return ResultUtils.success(record);
    }
}
