/* 项目主题色变量 */
:root {
  --primary-color: #F8D010;      /* 主题色 */
  --primary-light: #F8E068;      /* 亮色 */
  --gray-color: #909090;         /* 灰色 */
  --dark-color: #303030;         /* 深色 */
  --white-color: #ffffff;        /* 白色 */
  --background-color: #f5f5f5;   /* 背景色 */
  --success-color: #67C23A;      /* 成功色 */
  --warning-color: #E6A23C;      /* 警告色 */
  --danger-color: #F56C6C;       /* 危险色 */
  --info-color: #909399;         /* 信息色 */
  --border-color: #EBEEF5;       /* 边框色 */
  --text-primary: #303133;       /* 主要文字 */
  --text-regular: #606266;       /* 常规文字 */
  --text-secondary: #909399;     /* 次要文字 */
}

/* 全局样式 */
body {
  height: 100%;
  margin: 0;
  padding: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: var(--background-color);
}

html {
  height: 100%;
  box-sizing: border-box;
}

/* 修改Element UI主题色 */
.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  
  &:hover, &:focus {
    background-color: var(--primary-light);
    border-color: var(--primary-light);
  }
}

/* 布局相关 */
.app-container {
  padding: 20px;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 间距类 */
.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.p-20 {
  padding: 20px;
}

/* 定义卡片样式 */
.custom-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: var(--white-color);
  overflow: hidden;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
  }
}

/* 定义按钮样式 */
.custom-button {
  background-color: var(--primary-color);
  color: var(--white-color);
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    background-color: var(--primary-light);
  }
  
  &.danger {
    background-color: var(--danger-color);
    
    &:hover {
      opacity: 0.9;
    }
  }
}

/* 侧边栏菜单样式修复 */
.sidebar-container {
  /* 移除菜单边框 */
  .el-menu {
    border: none !important;
  }
  
  /* 移除菜单项边框 */
  .el-menu-item, 
  .el-submenu__title,
  .el-menu--inline,
  .el-submenu .el-menu,
  .el-submenu .el-menu-item {
    border: none !important;
    border-bottom: none !important;
    box-shadow: none !important;
  }
  
  /* 修复滚动条样式 */
  .el-scrollbar__wrap {
    overflow-x: hidden !important;
  }
  
  /* 隐藏滚动条轨道 */
  .el-scrollbar__bar.is-vertical {
    opacity: 0;
  }
  
  /* 修复菜单项文本下划线问题 */
  span[slot="title"],
  .el-submenu__title span,
  .el-menu-item span,
  span[data-v-10973580],
  [class*="data-v-"] span {
    border-bottom: none !important;
    text-decoration: none !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  /* 确保菜单项标题没有下划线 */
  .el-menu-item,
  .el-submenu__title {
    &::after,
    &::before {
      display: none !important;
    }
  }
  
  /* 修复子菜单项下划线 */
  .el-menu--popup {
    .el-menu-item span,
    .el-submenu__title span {
      border-bottom: none !important;
      text-decoration: none !important;
    }
  }
} 