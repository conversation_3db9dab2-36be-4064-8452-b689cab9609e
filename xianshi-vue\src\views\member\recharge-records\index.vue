<template>
  <div class="app-container clay-container">
    <!-- 搜索区域 -->
    <div class="filter-container clay-card">
      <el-form :inline="true" :model="listQuery" class="filter-form">
        <el-form-item label="订单号">
          <el-input v-model="listQuery.orderNo" placeholder="请输入订单号" clearable class="clay-input" />
        </el-form-item>
        <el-form-item label="充值方式">
          <el-select v-model="listQuery.payType" placeholder="请选择充值方式" clearable class="clay-select">
            <el-option v-for="item in payTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="充值状态">
          <el-select v-model="listQuery.status" placeholder="请选择充值状态" clearable class="clay-select">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            class="clay-date-picker"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" class="clay-button">查询</el-button>
          <el-button @click="resetQuery" class="clay-button clay-button-secondary">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格区域 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      class="clay-table"
    >
      <el-table-column label="订单号" align="center" min-width="180">
        <template slot-scope="{row}">
          <span>{{ row.orderNo }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="充值金额" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.amount }} 元</span>
        </template>
      </el-table-column>
      
      <el-table-column label="金币数量" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.goldAmount }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="充值方式" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.payTypeDesc }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="充值状态" min-width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getStatusType(row.status)" class="clay-tag">{{ row.statusDesc }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="套餐名称" min-width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.packageName || '直接充值' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createTime }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="支付时间" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.payTime || '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" min-width="100" class-name="fixed-width">
        <template slot-scope="{row}">
          <el-button 
            type="primary" 
            size="mini" 
            @click="handleDetail(row)" 
            class="clay-button clay-button-sm"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
      class="clay-pagination"
    />
    
    <!-- 详情对话框 -->
    <el-dialog title="充值详情" :visible.sync="dialogVisible" width="600px" class="clay-dialog">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ detailData.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="充值金额">{{ detailData.amount }} 元</el-descriptions-item>
        <el-descriptions-item label="金币数量">{{ detailData.goldAmount }}</el-descriptions-item>
        <el-descriptions-item label="充值方式">{{ detailData.payTypeDesc }}</el-descriptions-item>
        <el-descriptions-item label="充值状态">
          <el-tag :type="getStatusType(detailData.status)">{{ detailData.statusDesc }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="套餐名称">{{ detailData.packageName || '直接充值' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailData.createTime }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ detailData.payTime || '-' }}</el-descriptions-item>
        <el-descriptions-item :span="2" label="备注">{{ detailData.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" class="clay-button clay-button-secondary">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRechargeRecords, getRechargeDetail } from '@/api/recharge';
import Pagination from '@/components/Pagination';

export default {
  name: 'RechargeRecords',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        orderNo: '',
        payType: '',
        status: '',
        startTime: '',
        endTime: ''
      },
      dateRange: [],
      payTypeOptions: [
        { value: 'ALIPAY', label: '支付宝' },
        { value: 'WECHAT', label: '微信支付' },
        { value: 'BANK_CARD', label: '银行卡' },
        { value: 'GOLD', label: '金币支付' }
      ],
      statusOptions: [
        { value: 'PENDING', label: '待支付' },
        { value: 'SUCCESS', label: '支付成功' },
        { value: 'FAILED', label: '支付失败' },
        { value: 'REFUND', label: '已退款' }
      ],
      dialogVisible: false,
      detailData: {}
    };
  },
  watch: {
    dateRange(val) {
      if (val) {
        this.listQuery.startTime = val[0];
        this.listQuery.endTime = val[1];
      } else {
        this.listQuery.startTime = '';
        this.listQuery.endTime = '';
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      getRechargeRecords(this.listQuery).then(response => {
        this.list = response.data.records || [];
        this.total = response.data.total || 0;
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      });
    },
    handleSearch() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        orderNo: '',
        payType: '',
        status: '',
        startTime: '',
        endTime: ''
      };
      this.dateRange = [];
      this.getList();
    },
    handleDetail(row) {
      this.dialogVisible = true;
      this.detailData = Object.assign({}, row);
      
      // 如果需要获取更详细的数据，可以调用详情接口
      getRechargeDetail(row.id).then(response => {
        this.detailData = response.data;
      });
    },
    getStatusType(status) {
      const statusMap = {
        'PENDING': 'warning',
        'SUCCESS': 'success',
        'FAILED': 'danger',
        'REFUND': 'info'
      };
      return statusMap[status] || 'info';
    }
  }
};
</script>

<style lang="scss" scoped>
.clay-container {
  background-color: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
}

.clay-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.clay-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  
  ::v-deep .el-table__header-wrapper {
    th {
      background-color: #f7f9fc;
      color: #606266;
      font-weight: 600;
    }
  }
  
  ::v-deep .el-table__body-wrapper {
    td {
      padding: 16px 0;
    }
  }
}

.clay-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  &.clay-button-sm {
    padding: 7px 12px;
  }
  
  &.clay-button-secondary {
    background-color: #f5f7fa;
    color: #606266;
    border-color: #dcdfe6;
    
    &:hover {
      background-color: #e9ecf2;
    }
  }
}

.clay-input, .clay-select, .clay-date-picker {
  width: 200px;
  
  ::v-deep .el-input__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    transition: all 0.3s;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-date-picker {
  width: 350px;
}

.clay-pagination {
  margin-top: 24px;
  text-align: right;
  
  ::v-deep .el-pagination {
    padding: 10px 0;
    
    button, li {
      background-color: #f9fafc;
      border-radius: 6px;
      margin: 0 3px;
      
      &.active {
        background-color: #409eff;
        color: white;
      }
      
      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}

.clay-tag {
  border-radius: 16px;
  padding: 2px 10px;
  font-weight: 500;
}

.clay-dialog {
  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
    
    .el-dialog__header {
      background-color: #f7f9fc;
      padding: 16px 20px;
      
      .el-dialog__title {
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style> 