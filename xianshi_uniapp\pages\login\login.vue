<template>
  <view class="container">
    <video
        class="bg-video"
        src="../../static/videos/login-bg.mp4"
        object-fit="cover"
        autoplay
        loop
        muted
        :controls="false"
        show-center-play-btn="false"
        show-fullscreen-btn="false"
        show-play-btn="false"
        enable-progress-gesture="false"
        disable-progress="true"
    />
    <view class="content">
      <view class="close" @tap="goBack">
        <text class="iconfont icon-close"></text>
      </view>
      <view class="welcome">
        <view class="logo">
          <text class="logo-text">FREE TIME</text>
        </view>
        <text class="subtitle">闲时有伴</text>
      </view>
      <view class="login-box">
        <button class="phone-btn" @tap="inputLogin">手机号登录</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    inputLogin() {
      // 跳转到手机号输入页面
      uni.navigateTo({
        url: "/pages/phone-input/phone-input",
      });
    }
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.bg-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.bg-video {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  object-fit: cover;
}
.content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 100rpx 40rpx;
  box-sizing: border-box;
}

.close {
  position: absolute;
  top: 100rpx;
  left: 40rpx;
  color: #fff;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close .iconfont {
  font-size: 40rpx;
}

.welcome {
  text-align: center;
  color: #fff;
  margin-top: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: bold;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.logo-text {
  font-size: 48rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 32rpx;
  margin-top: 10rpx;
  font-weight: bold;
}

.login-box {
  margin-top: auto;
  width: 100%;
  margin-bottom: 40rpx;
}

.phone-btn {
  width: 670rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.line {
  width: 100rpx;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
}

.text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 28rpx;
  margin: 0 20rpx;
}

.input-btn {
  width: 670rpx;
  height: 88rpx;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.visitor-btn {
  width: 350rpx;
  height: 60rpx;
  background: rgba(224, 222, 222, 0.1);
  border: none;
  border-radius: 44rpx;
  color: rgba(134, 134, 134, 0.8);
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
