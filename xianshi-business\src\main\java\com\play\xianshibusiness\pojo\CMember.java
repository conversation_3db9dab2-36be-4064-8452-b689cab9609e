package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * (CMember)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_member")
@ApiModel("订单表")
public class CMember extends BaseObjPo {

    @ApiModelProperty("上次登录时间")
    private LocalDateTime lastLoginTime;
    //昵称
    @ApiModelProperty("昵称")
    private String nickname;
    //系统ID
    @ApiModelProperty("系统ID")
    private String sysId;
    //头像
    @ApiModelProperty("头像")
    private String avatar;
    //年龄
    @ApiModelProperty("年龄")
    private Integer age;
    //星座
    @ApiModelProperty("星座")
    private String constellation;
    //身高
    @ApiModelProperty("身高")
    private String height;
    //体重
    @ApiModelProperty("体重")
    private String weight;
    //简述
    @ApiModelProperty("简述")
    private String context;
    //视频
    @ApiModelProperty("视频")
    private String video;
    //照片集合
    @ApiModelProperty("照片集合")
    private String images;
    //身份字典IDS
    @ApiModelProperty("身份字典IDS")
    private String identify;
    //风格字典IDS
    @ApiModelProperty("风格字典IDS")
    private String styleIds;
    //金币余额
    @ApiModelProperty("金币余额")
    private BigDecimal gold;
    //人气值
    @ApiModelProperty("人气值")
    private String popularity;
    //是否VIP
    @ApiModelProperty("是否VIP")
    private Boolean isVip;
    //当前角色ID
    @ApiModelProperty("当前角色ID")
    private String currentRoleId;
    //手机号
    @ApiModelProperty("手机号")
    private String tel;
    //微信CODE
    @ApiModelProperty("微信CODE")
    private String wxCode;
    //微信OPEN_ID
    @ApiModelProperty("微信OPEN_ID")
    private String wxOpenId;
    //微信名称
    @ApiModelProperty("微信名称")
    private String wxName;
    //微信头像
    @ApiModelProperty("微信头像")
    private String wxAvatar;
    //备注
    @ApiModelProperty("备注")
    private String remark;
    //审核状态【枚举】
    @ApiModelProperty("审核状态【枚举】")
    private String applyStatus;
    
    //性别：1-男，2-女
    @ApiModelProperty("性别：1-男，2-女")
    private Integer gender;

    //城市
    @ApiModelProperty("城市")
    private String city;

    //实名认证状态：0-未认证，1-已认证
    @ApiModelProperty("实名认证状态：0-未认证，1-已认证")
    private Integer realNameStatus;

    //真实姓名
    @ApiModelProperty("真实姓名")
    private String realName;

    //身份证号码（加密存储）
    @ApiModelProperty("身份证号码")
    private String idCardNumber;

    //实名认证时间
    @ApiModelProperty("实名认证时间")
    private LocalDateTime realNameVerifyTime;
}

