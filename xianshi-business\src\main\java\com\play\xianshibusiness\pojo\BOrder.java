package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.enums.OrderStatusEnum;
import com.play.xianshibusiness.enums.OrderTypeEnum;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * (BOrder)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("b_order")
@ApiModel("订单表")
public class BOrder extends BaseObjPo {

    @ApiModelProperty(value = "系统ID")
    private String sysId;

    @ApiModelProperty(value = "会员ID")
    private String memberId;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "性别要求")
    private String sexRequire;

    @ApiModelProperty(value = "地点")
    private String addressText;

    @ApiModelProperty(value = "地点JSON")
    private String addressJson;

    @ApiModelProperty(value = "参与人数")
    private Integer personCount;

    @ApiModelProperty(value = "需求描述")
    private String description;

    @ApiModelProperty(value = "时长")
    private Double longTime;

    @ApiModelProperty(value = "订单状态")
    @EnumValue
    private OrderStatusEnum status;

    @ApiModelProperty(value = "订单类型：DISPATCH-派单模式，APPOINTMENT-约单模式")
    @EnumValue
    private OrderTypeEnum orderType;

    @ApiModelProperty(value = "目标达人ID（约单模式时使用，派单模式为空）")
    private String targetId;

    @ApiModelProperty(value = "小时费用")
    private BigDecimal hourlyRate;

    @ApiModelProperty(value = "活动类型ID")
    private String activityTypeId;
    
    @ApiModelProperty(value = "标题")
    private String title;
    
    @ApiModelProperty(value = "经度")
    private String longitude;
    
    @ApiModelProperty(value = "纬度")
    private String latitude;
    
    @ApiModelProperty(value = "总费用")
    private BigDecimal totalFee;
    
    @ApiModelProperty(value = "地址")
    private String address;
    
    @ApiModelProperty(value = "地点")
    private String location;
    
    @ApiModelProperty(value = "审核结果：0-未审核，1-通过，2-拒绝")
    private Integer auditResult;
    
    @ApiModelProperty(value = "审核意见")
    private String auditComment;
    
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;
    
    @ApiModelProperty(value = "审核人ID")
    private String auditorId;
    
    @ApiModelProperty(value = "取消原因")
    private String cancelReason;
    
    @ApiModelProperty(value = "取消时间")
    private LocalDateTime cancelTime;
    
    @ApiModelProperty(value = "金币消耗")
    private BigDecimal goldCost;
    
    @ApiModelProperty(value = "报名人数")
    private Integer enrollCount;
}

