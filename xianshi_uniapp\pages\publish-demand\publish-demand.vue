<template>
  <view class="container">
    <!-- 主体内容 -->
    <view class="main-content">
      <!-- 地址信息 -->
      <view class="location-info" @tap="chooseLocation">
        <view class="location-detail">
          <view class="location-area">{{ location || "请选择地址" }}</view>
          <view class="location-address">{{
            addressValue || "点击选择具体地址"
          }}</view>
        </view>
        <view class="location-icon">
          <text class="fas fa-chevron-right"></text>
        </view>
      </view>

      <!-- 时间信息 -->
      <view class="time-info">
        <view class="time-row">
          <!-- 日期选择 -->
          <view class="time-item">
            <text class="time-label">日期</text>
            <!-- <text class="time-value">{{ date }}</text> -->
            <picker mode="date" 
              :value="date" 
              :start="startDate" 
              :end="endDate" 
              @change="bindDateChange">
                <view class="uni-input">{{date}}</view>
            </picker>
          </view>
          <!-- 时间段选择 -->
          <view class="time-item" @tap="showTimeSlotPicker">
            <text class="time-label">时间段</text>
            <text class="time-value">{{ time }}</text>
          </view>
          <!-- 时长选择 -->
          <view class="time-item">
            <text class="time-label">时长</text>
            <text class="time-value">{{ duration }}</text>
          </view>
        </view>
      </view>
      <!-- 分类选择 -->
      <view class="category-grid">
        <view
          v-for="(item, index) in categories"
          :key="index"
          class="category-item"
          :class="{ selected: selectedCategory === index }"
          @tap="selectCategory"
          :data-index="index"
        >
          <view class="category-icon">
            <text :class="item.icon"></text>
          </view>
          <text class="category-name">{{ item.name }}</text>
        </view>
      </view>

      <!-- 在分类选择后添加服务项目显示 -->
      <view class="service-info" v-if="selectedService">
        <view class="service-label">服务项目</view>
        <view class="service-value">{{ selectedService }}</view>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 约单对象 -->
        <view class="form-item" v-if="expertName">
          <view class="form-label">约单对象</view>
          <view class="form-input-wrap expert-target">
            <view class="expert-name">{{ expertName }}</view>
          </view>
        </view>

        <!-- 小时费用 -->
        <view class="form-item" @tap="clearAllModals">
          <view class="form-label">小时费用</view>
          <view class="form-input-wrap">
            <input
              type="digit"
              placeholder="请输入小时费用(最高10000)"
              class="form-input"
              v-model="hourlyRate"
              @input="onHourlyRateChange"
              cursor-spacing="30"
              maxlength="7"
              @focus="onInputFocus"
              @tap="onInputFocus"
              @dblclick="onInputFocus"
              adjust-position="true"
            />
            <text class="unit">元/小时</text>
          </view>
        </view>

        <!-- 性别要求和参与人数（两列布局） -->
        <view class="dual-column-container" v-if="!expertId">
          <!-- 性别要求 -->
          <view class="form-item half-width">
            <view class="form-label">性别要求</view>
            <view class="time-select" @tap="showGenderPicker">
              <text class="gender-text">{{ genderText || "不限" }}</text>
            </view>
          </view>

          <!-- 参与人数 -->
          <view class="form-item half-width">
            <view class="form-label">参与人数</view>
            <view class="time-select" @tap="showPeopleCountPicker">
              <text class="people-count">{{ peopleCount }}人</text>
            </view>
          </view>
        </view>

        <!-- 性别选择器蒙层 -->
        <view
          class="picker-mask"
          v-if="showGenderPicker_data"
          @tap="hideGenderPicker"
        ></view>

        <!-- 性别选择器 -->
        <view class="picker-container" v-if="showGenderPicker_data">
          <view class="picker-header">
            <view class="picker-title">选择性别要求</view>
            <view class="picker-close" @tap="confirmGenderSelection"
              >完成</view
            >
          </view>
          <picker-view
            class="gender-picker"
            :value="genderValue"
            @change="onGenderChange"
            indicator-style="height: 88rpx;"
          >
            <picker-view-column>
              <view
                v-for="(item, index) in genderRange"
                :key="index"
                class="picker-column-item"
              >
                {{ item }}
              </view>
            </picker-view-column>
          </picker-view>
        </view>

        <!-- 参与人数选择器蒙层 -->
        <view
          class="picker-mask"
          v-if="showPeopleCountPicker_data"
          @tap="hidePeopleCountPicker"
        ></view>

        <!-- 参与人数选择器 -->
        <view class="picker-container" v-if="showPeopleCountPicker_data">
          <view class="picker-header">
            <view class="picker-title">选择参与人数</view>
            <view class="picker-close" @tap="confirmPeopleCountSelection"
              >完成</view
            >
          </view>
          <picker-view
            class="people-count-picker"
            :value="peopleCountValue"
            @change="onPeopleCountChange"
            indicator-style="height: 88rpx;"
          >
            <picker-view-column>
              <view
                v-for="(item, index) in peopleCountRange"
                :key="index"
                class="picker-column-item"
              >
                {{ item }}
              </view>
            </picker-view-column>
          </picker-view>
        </view>

        <!-- 需求描述 -->
        <view class="form-item" @tap="clearAllModals">
          <view class="form-label">需求描述</view>
          <textarea
            placeholder="请详细描述您的活动需求..."
            class="form-textarea"
            maxlength="200"
            v-model="description"
            @input="onDescriptionChange"
            cursor-spacing="30"
            show-confirm-bar="true"
            @focus="onTextareaFocus"
            @tap="onTextareaFocus"
            @dblclick="onTextareaFocus"
            adjust-position="true"
          ></textarea>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-button">
      <button class="publish-btn" @tap="publishDemand">确认发布</button>
    </view>

    <!-- 二级分类弹窗 -->
    <view class="subcategory-popup" v-if="showSubCategories">
      <view class="popup-mask" @tap="closeSubCategories"></view>
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title"
            >选择{{ categories[selectedCategory].name }}类型</text
          >
          <text class="popup-close" @tap="confirmSubCategorySelection"
            >完成</text
          >
        </view>
        <picker-view
          class="subcategory-picker"
          :value="subCategoryValue"
          @change="onSubCategoryChange"
          indicator-style="height: 88rpx;"
        >
          <picker-view-column>
            <view
              v-for="(item, index) in currentSubCategories"
              :key="index"
              class="picker-column-item"
            >
              {{ item.name }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>

    <timeSlot 
      ref="timeslot"
      :title="'选择时间段'"
      @confirm="confirmTime">
    </timeSlot>
  </view>
</template>

<script>
import timeSlot from "@/components/wanghexu-timeslot/wanghexu-timeslot.vue"

export default {
  data() {
    return {
      location: "龙泉驿区",
      date: "",
      time: "",
      duration: '3小时',
      latitude: null, // 纬度
      longitude: null, // 经度
      selectedCategory: -1,
      selectedSubCategory: -1,
      selectedService: "",
      showSubCategories: false,
      subCategoryValue: [0],
      gender: "any",
      showGenderPicker_data: false,
      genderValue: [0],
      genderRange: ["女", "男", "不限"],
      peopleCount: 1,
      showPeopleCountPicker_data: false,
      peopleCountValue: [0],
      peopleCountRange: [
        "1人",
        "2人",
        "3人",
        "4人",
        "5人",
        "6人",
        "7人",
        "8人",
        "9人",
        "10人",
      ],
      categories: [], // 将从API获取
      subCategories: {}, // 将从API获取
      activityTypes: [], // 存储API返回的活动类型数据
      currentSubCategories: [],
      addressValue: "",
      hourlyRate: "",
      description: "",
      city: "成都",
      startHour: 14,
      startMinute: 0,
      expertName: "", // 约单对象名称
      genderText: "不限",
      hourlyRateFocus: false,
      descriptionFocus: false,
      expertId: null, // 添加缺少的属性
       reverseGeocodingTimer: null, // 防抖定时器
      tempSubCategoryValue: 0, // 添加缺少的属性
      tempPeopleCountValue: 0, // 添加缺少的属性
      tempTimeSlotValue: 0, // 添加缺少的属性
    };
  },
  components: {timeSlot},
  onLoad(options) {
    console.log("发布需求页面载入，接收到的原始选项:", options);

    // 初始化性别文本
    this.genderText = "不限";
    
    // 获取陪玩类型数据
    this.fetchActivityTypes();

    // 确保页面加载完全后，再清除可能的遮罩层
    setTimeout(() => {
      this.clearAllModals();
      console.log("页面加载完成，清除了所有可能的遮罩层");
    }, 500);

    try {
      // 获取当前日期
      const today = new Date();
      const currentDate = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;

      // 设置一些合理的默认值
      let defaultData = {
        location: "四川省成都市龙泉驿区",
        addressValue: "",
        date: currentDate,
        time: "14:00 - 17:00",
        duration: '3小时',
        city: "成都", // 默认城市
        startHour: 14,
        startMinute: 0,
      };

      if (options && options.location) {
        const fullAddress = decodeURIComponent(options.location || "");
        const date = decodeURIComponent(options.date || "");
        const time = decodeURIComponent(options.time || "");
        const duration = parseInt(options.duration || "3小时");
        const startHour = parseInt(options.startHour || "14");
        const startMinute = parseInt(options.startMinute || "0");

        console.log("解析后的参数:", {
          fullAddress,
          date,
          time,
          duration,
          startHour,
          startMinute,
        });

        // 处理地址显示
        let area = "";
        let detail = "";
        let city = "成都"; // 默认城市

        // 解析地址
        if (fullAddress && fullAddress.trim() !== "") {
          // 尝试提取城市信息
          const cityMatch = fullAddress.match(/[^省]+市/);
          if (cityMatch && cityMatch[0]) {
            city = cityMatch[0].replace("市", "");
          }

          const addressParts = fullAddress.split(/[省市区县]/);
          if (addressParts.length > 1) {
            // 有明确的省市区划分
            area = fullAddress.slice(
              0,
              fullAddress.length - addressParts[addressParts.length - 1].length
            );
            detail = addressParts[addressParts.length - 1].trim();
          } else {
            // 没有明确划分，使用整个地址
            area = "四川省成都市";
            detail = fullAddress;
          }
        }

        this.location = area || defaultData.location; // 使用默认值作为备选
        this.addressValue = detail || defaultData.addressValue;
        this.date = date || defaultData.date;
        this.time = time || defaultData.time;
        this.duration = isNaN(duration) ? defaultData.duration : duration;
        this.city = city; // 设置提取的城市或默认城市
        this.startHour = isNaN(startHour) ? defaultData.startHour : startHour;
        this.startMinute = isNaN(startMinute)
          ? defaultData.startMinute
          : startMinute;

        console.log("设置后的数据:", {
          location: this.location,
          addressValue: this.addressValue,
          date: this.date,
          time: this.time,
          duration: this.duration,
          city: this.city,
          startHour: this.startHour,
          startMinute: this.startMinute,
        });
      } else {
        console.log("没有收到有效的location参数，使用默认值");
        // 使用默认值
        Object.assign(this, defaultData);
      }
    } catch (error) {
      console.error("处理参数时出错:", error);
      // 发生错误时使用安全的默认值
      const today = new Date();
      const currentDate = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;

      this.location = "四川省成都市龙泉驿区";
      this.addressValue = "";
      this.date = currentDate;
      this.time = "14:00 - 17:00";
      this.duration = '3小时';
      this.city = "成都";
      this.startHour = 14;
      this.startMinute = 0;
    }

    // 获取约单对象信息
    if (options.expertId) {
      this.expertId = options.expertId;

      // 直接从参数中获取专家名称
      if (options.expertName) {
        this.expertName = decodeURIComponent(options.expertName);
        console.log("从参数获取专家名称:", this.expertName);
      } else {
        // 从本地缓存获取专家信息
        const expertInfo = uni.getStorageSync("currentExpertInfo");
        if (expertInfo && expertInfo.name) {
          this.expertName = expertInfo.name;
          console.log("从本地存储获取专家名称:", this.expertName);
        }
      }
    }
  },
  
  // 页面显示时处理从地图选择页面返回的数据
  onShow() {
    // 获取从地图选择页面返回的位置数据
    const selectedLocation = uni.getStorageSync('selectedLocation');
    if (selectedLocation) {
      console.log('从地图选择页面获取到位置数据:', selectedLocation);
      
      // 更新位置信息
      this.location = selectedLocation.address || '';
      this.addressValue = selectedLocation.name || '';
      this.latitude = selectedLocation.latitude;
      this.longitude = selectedLocation.longitude;
      
      // 清除存储的位置数据
      uni.removeStorageSync('selectedLocation');
      
      uni.showToast({
        title: '位置选择成功',
        icon: 'success'
      });
    }
  },
  
  computed: {
      startDate() {
          return this.getDate('start');
      },
      endDate() {
          return this.getDate('end');
      }
  },
  methods: {
    // 获取陪玩类型数据
    fetchActivityTypes() {
      uni.showLoading({
        title: "加载活动类型...",
        mask: true
      });
      
      const app = getApp().globalData;
      console.log("vv", app.commonApi.getPlayType);
      this.$requestHttp.get(app.commonApi.getPlayType, {
          data: {}
        })
        .then(res => {
          uni.hideLoading();
          
          if (res.code === 200 && res.data) {
            this.activityTypes = res.data;
            console.log("this---", this.activityTypes)
            
            // 处理返回的数据，构建categories和subCategories
            this.categories = this.activityTypes.map((item, index) => {
              return {
                name: item.name,
                icon: this.getCategoryIcon(item.name), // 根据名称获取对应图标
                id: item.id
              };
            });
            
            // 构建子分类数据结构
            const subCategoriesObj = {};
            this.activityTypes.forEach((mainType, index) => {
              if (mainType.children && mainType.children.length > 0) {
                subCategoriesObj[index] = mainType.children.map(subType => {
                  return {
                    name: subType.name,
                    icon: this.getSubCategoryIcon(subType.name),
                    id: subType.id
                  };
                });
              } else {
                subCategoriesObj[index] = [];
              }
            });
            
            this.subCategories = subCategoriesObj;
            console.log("陪玩类型数据处理完成:", this.categories, this.subCategories);
          } else {
            console.error("获取活动类型失败:", res);
            uni.showToast({
              title: "获取活动类型失败",
              icon: "none"
            });
            
            // 设置默认分类数据
            this.setDefaultCategories();
          }
        })
        .catch(err => {
          uni.hideLoading();
          console.error("获取活动类型请求错误:", err);
          uni.showToast({
            title: "网络错误，使用默认数据",
            icon: "none"
          });
          
          // 设置默认分类数据
          this.setDefaultCategories();
        });
    },
    
    // 设置默认分类数据（当API调用失败时使用）
    setDefaultCategories() {
      this.categories = [
        { name: "休闲娱乐", icon: "fas fa-coffee" },
        { name: "游戏", icon: "fas fa-gamepad" },
        { name: "运动", icon: "fas fa-running" },
        { name: "商务", icon: "fas fa-briefcase" }
      ];
      
      this.subCategories = {
        0: [
          { name: "棋牌游戏", icon: "https://ai-public.mastergo.com/ai/img_res/84a038aee2fdb35bad448be597f1a5ce.jpg" },
          { name: "密室逃脱", icon: "https://ai-public.mastergo.com/ai/img_res/770071c7d2c2f4d7714748e9b3343097.jpg" },
          { name: "KTV", icon: "https://ai-public.mastergo.com/ai/img_res/df7007ddb7e7fa522e869336633b8666.jpg" },
          { name: "桌游", icon: "https://ai-public.mastergo.com/ai/img_res/4ef9bf46e1eb2f5cccdc17d3603458b4.jpg" }
        ],
        1: [
          { name: "手游", icon: "https://ai-public.mastergo.com/ai/img_res/84a038aee2fdb35bad448be597f1a5ce.jpg" },
          { name: "主机游戏", icon: "https://ai-public.mastergo.com/ai/img_res/770071c7d2c2f4d7714748e9b3343097.jpg" },
          { name: "电竞", icon: "https://ai-public.mastergo.com/ai/img_res/df7007ddb7e7fa522e869336633b8666.jpg" }
        ],
        2: [
          { name: "健身", icon: "https://ai-public.mastergo.com/ai/img_res/84a038aee2fdb35bad448be597f1a5ce.jpg" },
          { name: "篮球", icon: "https://ai-public.mastergo.com/ai/img_res/770071c7d2c2f4d7714748e9b3343097.jpg" },
          { name: "羽毛球", icon: "https://ai-public.mastergo.com/ai/img_res/df7007ddb7e7fa522e869336633b8666.jpg" }
        ],
        3: [
          { name: "商务陪同", icon: "https://ai-public.mastergo.com/ai/img_res/84a038aee2fdb35bad448be597f1a5ce.jpg" },
          { name: "翻译", icon: "https://ai-public.mastergo.com/ai/img_res/770071c7d2c2f4d7714748e9b3343097.jpg" },
          { name: "会展", icon: "https://ai-public.mastergo.com/ai/img_res/df7007ddb7e7fa522e869336633b8666.jpg" }
        ]
      };
    },
    
    // 根据分类名获取对应的图标
    getCategoryIcon(categoryName) {
      const iconMap = {
        "休闲娱乐": "fas fa-coffee",
        "商务活动": "fas fa-briefcase",
        "游戏": "fas fa-gamepad",
        "运动": "fas fa-running"
      };
      
      return iconMap[categoryName] || "fas fa-star"; // 默认图标
    },
    
    // 根据子分类名获取对应的图标
    getSubCategoryIcon(subCategoryName) {
      // 这里可以根据实际需求扩展
      return "https://ai-public.mastergo.com/ai/img_res/84a038aee2fdb35bad448be597f1a5ce.jpg";
    },
    
    getDate(type) {
      const date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();

      if (type === 'start') {
          year = year - 10;
      } else if (type === 'end') {
          year = year + 10;
      }
      month = month > 9 ? month : '0' + month;
      day = day > 9 ? day : '0' + day;
      return `${year}-${month}-${day}`;
    },
    bindDateChange(e){
      this.date = e.detail.value
    },

    selectCategory(e) {
      const index = e.currentTarget.dataset.index;
      console.log("index", index, this.subCategories);
      this.selectedCategory = index;
      this.currentSubCategories = this.subCategories[index];
      this.subCategoryValue = [0]; // 重置子类选择到第一个
      this.tempSubCategoryValue = 0;
      this.showSubCategories = true;
    },

    selectGender(e) {
      const gender = e.currentTarget.dataset.gender;
      const genderText =
        gender === "male" ? "男" : gender === "female" ? "女" : "不限";
      this.gender = gender;
      this.genderText = genderText;
    },

    // 显示参与人数选择器
    showPeopleCountPicker() {
      this.showPeopleCountPicker_data = true;
    },

    // 隐藏参与人数选择器
    hidePeopleCountPicker() {
      this.showPeopleCountPicker_data = false;
    },

    // 参与人数改变时的临时处理
    onPeopleCountChange(e) {
      const val = e.detail.value[0];
      this.peopleCountValue = [val];
      this.tempPeopleCountValue = val;
    },

    // 确认人数选择
    confirmPeopleCountSelection() {
      const val =
        this.tempPeopleCountValue || this.peopleCountValue[0];
      const peopleText = this.peopleCountRange[val];
      // 提取文本中的数字部分
      const peopleCount = parseInt(peopleText);

      this.peopleCount = peopleCount;
      this.peopleCountValue = [val];
      this.showPeopleCountPicker_data = false;
    },

    // 隐藏参与人数选择器
    hidePeopleCountPicker() {
      this.confirmPeopleCountSelection();
    },

    // 子类别改变时的临时处理
    onSubCategoryChange(e) {
      const val = e.detail.value[0];
      this.subCategoryValue = [val];
      this.tempSubCategoryValue = val;
    },

    // 确认子类别选择
    confirmSubCategorySelection() {
      console.log("Sss", this.categories, this.currentSubCategories);
      console.log("Zzz", this.tempSubCategoryValue, this.subCategoryValue[0]);
      const val =
        this.tempSubCategoryValue || this.subCategoryValue[0];
      const index = this.selectedCategory;
      const mainCategory = this.categories[index].name;
      const subCategory = this.currentSubCategories[val].name;

      // 生成服务项目名称
      const serviceName = `${mainCategory}-${subCategory}`;

      this.selectedSubCategory = val;
      this.selectedService = serviceName;
      this.subCategoryValue = [val];
      this.showSubCategories = false;

      // 显示选择成功提示
      uni.showToast({
        title: `已选择"${serviceName}"`,
        icon: "success",
      });
    },

    // 关闭子类别选择
    closeSubCategories() {
      this.showSubCategories = false;
    },

    // 显示性别选择器
    showGenderPicker() {
      this.showGenderPicker_data = true;
    },

    // 隐藏性别选择器
    hideGenderPicker() {
      this.showGenderPicker_data = false;
    },

    // 性别选择变化处理
    onGenderChange(e) {
      const val = e.detail.value[0];
      this.genderValue = [val];
      this.genderText = this.genderRange[val];
      
      // 根据选择设置性别值
      if (val === 0) {
        this.gender = "female";
      } else if (val === 1) {
        this.gender = "male";
      } else {
        this.gender = "any";
      }
    },

    // 确认性别选择
    confirmGenderSelection() {
      this.showGenderPicker_data = false;
    },

    // 选择地址
    chooseLocation() {
      // 跳转到地图选择页面
      uni.navigateTo({
        url: '/pages/map-selector/map-selector',
        success: () => {
          console.log('跳转到地图选择页面成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          // 如果跳转失败，回退到原生方法
          this.chooseLocationFallback();
        }
      });
    },
    
    // 原生地图选择方法（作为备用）
    chooseLocationFallback() {
      // 检查平台支持
      // #ifdef H5
      // H5环境下使用腾讯地图选择
      this.chooseLocationForH5();
      // #endif
      
      // #ifndef H5
      // 非H5环境使用uni.chooseLocation
      uni.chooseLocation({
        success: (res) => {
          console.log('位置选择成功:', res);
          this.location = res.address || '';
          this.addressValue = res.name || '';
          this.latitude = res.latitude;
          this.longitude = res.longitude;
          
          // 如果没有详细地址，进行逆地理编码
          if (!this.addressValue && this.latitude && this.longitude) {
            this.reverseGeocoding(this.latitude, this.longitude);
          }
        },
        fail: (err) => {
          console.error('位置选择失败:', err);
          uni.showToast({
            title: '位置选择失败',
            icon: 'none'
          });
        }
      });
      // #endif
    },
    
    // H5环境下的地图选择
    chooseLocationForH5() {
      // 获取当前位置
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          console.log('当前位置:', res);
          this.latitude = res.latitude;
          this.longitude = res.longitude;
          
          // 进行逆地理编码获取地址信息
          this.reverseGeocoding(res.latitude, res.longitude);
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          uni.showToast({
            title: '获取位置失败，请检查定位权限',
            icon: 'none'
          });
          // 使用默认位置
          this.useDefaultLocation();
        }
      });
    },
    
    // 逆地理编码
    reverseGeocoding(latitude, longitude) {
      // H5环境下使用JSONP方式调用腾讯地图API避免CORS问题
      // 从manifest.json配置中获取API Key
      const platform = uni.getSystemInfoSync().platform;
      const key = platform === 'h5' ? 
        (window.__uniConfig?.h5?.sdkConfigs?.maps?.qqmap?.key || 'LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP') :
        'LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP';
      
      const callbackName = 'reverseGeocodingCallback' + Date.now();
      
      // 添加防抖机制，避免频繁调用
      if (this.reverseGeocodingTimer) {
        clearTimeout(this.reverseGeocodingTimer);
      }
      
      this.reverseGeocodingTimer = setTimeout(() => {
      
      // 创建全局回调函数
      window[callbackName] = (data) => {
        if (data && data.status === 0 && data.result) {
          const result = data.result;
          const addressComponent = result.address_component;
          
          // 设置地址信息
           this.location = `${addressComponent.province}${addressComponent.city}${addressComponent.district}`;
           this.addressValue = result.formatted_addresses?.recommend || result.address || '详细地址';
           this.latitude = latitude;
           this.longitude = longitude;
           
           // 如果有POI信息，优先使用POI名称
           if (result.pois && result.pois.length > 0) {
             this.addressValue = result.pois[0].title;
           }
          
          uni.showToast({
            title: '位置获取成功',
            icon: 'success'
          });
        } else {
          this.useDefaultLocation();
        }
        
        // 清理回调函数和script标签
        delete window[callbackName];
        const script = document.getElementById(callbackName);
        if (script) {
          document.head.removeChild(script);
        }
      };
      
      // 创建JSONP请求
      const script = document.createElement('script');
      script.id = callbackName;
      script.src = `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${key}&get_poi=1&output=jsonp&callback=${callbackName}`;
      script.onerror = () => {
        this.useDefaultLocation();
        delete window[callbackName];
        document.head.removeChild(script);
      };
      
      document.head.appendChild(script);
      }, 300); // 300ms防抖延迟
    },
    
    // 使用默认位置
    useDefaultLocation() {
      this.location = '四川省成都市龙泉驿区';
      this.addressValue = '欧鹏大道349号';
      this.latitude = 30.572816;
      this.longitude = 104.066803;
      
      uni.showToast({
        title: '使用默认位置',
        icon: 'none'
      });
    },

    // 显示时间段选择器
    showTimeSlotPicker() {
      console.log("aaa")
      this.$refs.timeslot.open()
    },
    // 选择时间段
    confirmTime(val) {
      console.log("val", val);
      const { start, end } = val;
      this.time = `${start.hour}:${start.min} - ${end.hour}:${end.min}`
      // 17:03 - 19:01
      const startHour = start.hour;
      const startMin = start.min;
      const endHour = end.hour;
      const endMin = end.min;
      console.log("startHour", startHour, startMin, endHour, endMin);
      const startMinutes = (startHour*60)+startMin*1;
      const endMinutes = (endHour*60)+endMin*1;
      console.log("ddd", startMinutes, endMinutes);
      let totalMinutes = endMinutes - startMinutes;
      console.log("totalMinutes", totalMinutes);
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      this.duration = `${hours}小时${minutes}分钟`;
    },
    // 小时费用变化处理
    onHourlyRateChange(e) {
      // 限制小时费用最高10000
      const value = e.detail.value;
      if (value > 10000) {
        this.hourlyRate = "10000";
      }
    },

    // 需求描述变化处理
    onDescriptionChange(e) {
      // 这里可以添加字数统计等功能
    },

    // 输入框聚焦处理
    onInputFocus() {
      this.hourlyRateFocus = true;
      this.clearAllModals();
    },

    // 文本域聚焦处理
    onTextareaFocus() {
      this.descriptionFocus = true;
      this.clearAllModals();
    },

    // 清除所有模态窗口
    clearAllModals() {
      this.showGenderPicker_data = false;
      this.showPeopleCountPicker_data = false;
      this.showSubCategories = false;
    },

    // 发布需求
    publishDemand() {
      // 表单验证
      if (!this.validateForm()) {
        return;
      }

      // 根据日期和时间构建开始和结束时间
      const dateText = this.date; // 格式：yyyy-MM-dd
      const [currentYear, month, day] = dateText.split('-').map(Number);
      
      // 解析时间范围 (例如：14:00 - 17:00)
      const timeRange = this.time.split(" - ");
      const startTimeStr = timeRange[0]; // 例如：14:00
      const endTimeStr = timeRange[1];   // 例如：17:00
      
      const [startHour, startMinute] = startTimeStr.split(":").map(Number);
      const [endHour, endMinute] = endTimeStr.split(":").map(Number);
      
      // 构建日期对象
      const startTime = new Date(currentYear, month - 1, day, startHour, startMinute);
      const endTime = new Date(currentYear, month - 1, day, endHour, endMinute);
      
      // 转换为'yyyy-MM-dd HH:mm:ss'格式
      const formatDateTime = (date) => {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      };
      
      const startTimeISO = formatDateTime(startTime);
      const endTimeISO = formatDateTime(endTime);

      // 性别要求转换：0-不限，1-男，2-女
      let sexRequire;
      let genderText;
      if (this.gender === "male") {
        sexRequire = "1";
        genderText = "男";
      } else if (this.gender === "female") {
        sexRequire = "2";
        genderText = "女";
      } else {
        sexRequire = "0"; // 不限
        genderText = "不限";
      }

      // 获取活动类型ID
      const activityTypeId = this.getActivityTypeId();

      // 构建地址JSON（包含经纬度等信息）
      const addressJson = JSON.stringify({
        name: this.location + this.addressValue,
        address: this.location + this.addressValue,
        latitude: this.latitude ? this.latitude.toString() : "30.572816", // 使用真实坐标或默认值
        longitude: this.longitude ? this.longitude.toString() : "104.066803", // 使用真实坐标或默认值
        province: "四川省",
        city: this.city || "成都市",
        district: this.location.includes("区") ? this.location : "",
      });

      // 构建请求参数
      const params = {
        activityTypeId: activityTypeId,
        address: this.addressValue || "",
        addressJson: addressJson,
        addressText: this.location + (this.addressValue || ""),
        description: this.description || "",
        endTime: endTimeISO,
        hourlyRate: parseFloat(this.hourlyRate),
        latitude: this.latitude ? this.latitude.toString() : "30.572816", // 使用真实坐标或默认值
        longitude: this.longitude ? this.longitude.toString() : "104.066803", // 使用真实坐标或默认值
        personCount: this.peopleCount,
        sexRequire: sexRequire,
        startTime: startTimeISO,
        title: this.selectedService || this.getServiceTitle()
      };
      
      // 设置订单类型和targetId
      if (this.expertId && this.expertId.trim() !== "") {
        // 约单模式
        params.orderType = "APPOINTMENT";
        params.targetId = this.expertId;
      } else {
        // 派单模式
        params.orderType = "DISPATCH";
      }
      
      // 为order-payment页面准备订单数据
      const orderInfo = {
        location: this.location,
        addressDetail: this.addressValue,
        date: this.date,
        time: this.time,
        duration: this.duration,
        gender: this.gender,
        genderText: genderText,
        people: this.peopleCount,
        hourlyRate: parseFloat(this.hourlyRate),
        description: this.description,
        service: (this.selectedCategory >= 0 && this.categories[this.selectedCategory]) ? this.categories[this.selectedCategory].name : "",
        serviceSubType: (this.selectedSubCategory >= 0 && this.currentSubCategories && this.currentSubCategories[this.selectedSubCategory]) ? this.currentSubCategories[this.selectedSubCategory].name : "",
        expertName: this.expertName || "",
        expertId: this.expertId || "",
        activityTypeId: params.activityTypeId // 添加活动类型ID
      };
      
      console.log("准备跳转到订单确认页面，订单数据:", orderInfo);
      
      // 跳转到订单支付页面
      uni.navigateTo({
        url: `/pages/order-payment/order-payment?orderData=${encodeURIComponent(JSON.stringify(orderInfo))}&createParams=${encodeURIComponent(JSON.stringify(params))}`,
        fail: (err) => {
          console.error("跳转支付页面失败:", err);
          uni.showToast({
            title: "页面跳转失败，请重试",
            icon: "none",
            duration: 2000
          });
        }
      });
    },
    
    // 获取服务标题
    getServiceTitle() {
      try {
        if (this.selectedCategory >= 0 && this.categories && this.categories[this.selectedCategory]) {
          const categoryName = this.categories[this.selectedCategory].name;
          if (this.selectedSubCategory >= 0 && this.currentSubCategories && this.currentSubCategories[this.selectedSubCategory]) {
            const subCategoryName = this.currentSubCategories[this.selectedSubCategory].name;
            return `${categoryName}-${subCategoryName}`;
          }
          return categoryName;
        }
        return '未指定服务';
      } catch (e) {
        console.error('获取服务标题失败:', e);
        return '未指定服务';
      }
    },

    // 获取活动类型ID
    getActivityTypeId() {
      if (this.selectedCategory >= 0 && this.selectedSubCategory >= 0) {
        // 检查是否有API返回的活动类型数据
        if (this.activityTypes && this.activityTypes.length > 0) {
          // 尝试从当前选择的子类别获取ID
          try {
            const subCategory = this.subCategories[this.selectedCategory][this.selectedSubCategory];
            if (subCategory && subCategory.id) {
              return subCategory.id;
            }
          } catch (e) {
            console.error("获取活动类型ID失败:", e);
          }
        }
        
        // 如果没有API数据或获取失败，使用本地映射表
        const categoryMap = {
          0: { // 休闲娱乐
            0: "leisure_chess", // 棋牌游戏
            1: "leisure_escape", // 密室逃脱
            2: "leisure_ktv",    // KTV
            3: "leisure_boardgame" // 桌游
          },
          1: { // 游戏
            0: "game_mobile",    // 手游
            1: "game_console",   // 主机游戏
            2: "game_esports"    // 电竞
          },
          2: { // 运动
            0: "sports_fitness", // 健身
            1: "sports_basketball", // 篮球
            2: "sports_badminton"  // 羽毛球
          },
          3: { // 商务
            0: "business_accompany", // 商务陪同
            1: "business_translate", // 翻译
            2: "business_exhibition" // 会展
          }
        };
        
        return categoryMap[this.selectedCategory]?.[this.selectedSubCategory] || "default_activity_type";
      }
      
      return "default_activity_type";
    },

    // 表单验证
    validateForm() {
      // 验证地址
      if (!this.location || !this.addressValue) {
        uni.showToast({
          title: "请选择活动地点",
          icon: "none",
        });
        return false;
      }

      // 验证服务类型
      if (this.selectedCategory === -1 || this.selectedSubCategory === -1) {
        uni.showToast({
          title: "请选择活动类型",
          icon: "none",
        });
        return false;
      }

      // 验证小时费用
      if (!this.hourlyRate || isNaN(parseFloat(this.hourlyRate)) || parseFloat(this.hourlyRate) <= 0) {
        uni.showToast({
          title: "请输入有效的小时费用",
          icon: "none",
        });
        return false;
      }

      // 验证描述
      if (!this.description || this.description.trim() === "") {
        uni.showToast({
          title: "请填写需求描述",
          icon: "none",
        });
        return false;
      }

      // 验证活动开始时间不能早于当前时间 2025-09-05
      console.log("date", this.date);
      const dateText = this.date;
      const [currentYear, month, day] = dateText.split('-').map(Number);
      
      // 解析时间范围
      const timeRange = this.time.split(" - ");
      const startTimeStr = timeRange[0];
      const [startHour, startMinute] = startTimeStr.split(":").map(Number);
      
      // 构建开始时间
      const startTime = new Date(currentYear, month - 1, day, startHour, startMinute);
      const currentTime = new Date();
      
      if (startTime < currentTime) {
        uni.showToast({
          title: "活动开始时间不能早于当前时间",
          icon: "none",
        });
        return false;
      }

      return true;
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #fafafa;
  padding-bottom: 120rpx;
}

/* 顶部导航 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 90rpx;
  background: #ff8c00;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 40rpx;
  z-index: 100;
  color: #fff;
}

.nav-left {
  width: 60rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #fff;
  position: absolute;
  left: 20rpx;
  top: 40rpx;
}

.nav-left text {
  font-size: 24rpx;
  line-height: 1;
}

.back-icon {
  font-size: 24rpx;
  color: #fff;
}

.nav-left:active {
  opacity: 0.8;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
}

.nav-right {
  width: 60rpx;
  visibility: hidden;
}

/* 主体内容 */
.main-content {
  padding: 30rpx;
}

/* 地址信息 */
.location-info {
  background: #fff;
  padding: 24rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.location-detail {
  flex: 1;
  margin-right: 20rpx;
}

.location-area {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.location-address {
  font-size: 24rpx;
  color: #999;
}

.location-icon {
  color: #999;
  font-size: 24rpx;
}

/* 点击效果 */
.location-info:active {
  background: #f8f8f8;
}

/* 时间信息 */
.time-info {
  margin-bottom: 40rpx;
  background: #fff;
  padding: 24rpx;
  border-radius: 16rpx;
}

.time-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
}

.time-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
}

.time-item:active {
  background: #f0f0f0;
}

.time-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.time-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 分类选择 */
.category-grid {
  display: flex;
  justify-content: space-between;
  gap: 10rpx;
  margin-bottom: 40rpx;
}

.category-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 16rpx 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.category-icon {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 140, 0, 0.1);
  border-radius: 999rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff8c00;
  font-size: 28rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.category-item.selected {
  color: #ff8c00;
}

/* 表单区域 */
.form-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.form-input-wrap {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 5;
  width: 100%;
}

.form-input {
  flex: 1;
  width: 100%;
  height: 80rpx;
  border-radius: 8rpx;
  padding: 0 84rpx 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f8f8;
}

.unit {
  position: absolute;
  right: 24rpx;
  font-size: 28rpx;
  color: #999;
  pointer-events: none;
}

.location-icon {
  font-size: 36rpx;
  color: #ff8c00;
}

/* 定位图标样式 */
.location-icon-wrap {
  padding: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-text {
  font-size: 36rpx;
  color: #ff8c00;
}

.location-icon-wrap:active {
  opacity: 0.7;
}

/* 性别选择 */
.gender-buttons {
  display: flex;
  gap: 24rpx;
}

.gender-btn {
  flex: 1;
  height: 72rpx;
  border: 1rpx solid #ddd;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  gap: 8rpx;
}

.gender-btn.selected {
  background: #ff8c00;
  color: #fff;
  border-color: #ff8c00;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  height: 180rpx;
  font-size: 28rpx;
  line-height: 1.5;
  padding: 20rpx 24rpx;
  box-sizing: border-box;
  border-radius: 8rpx;
  background-color: #f8f8f8;
}

/* 底部按钮 */
.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 30rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
}

.publish-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 999rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 二级分类弹窗 */
.subcategory-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx 30rpx 0 30rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
}

.subcategory-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.subcategory-icon {
  width: 108rpx;
  height: 108rpx;
  border-radius: 16rpx;
}

.subcategory-name {
  font-size: 24rpx;
  color: #333;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 服务项目显示样式 */
.service-info {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.service-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.service-value {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

/* 调整二级分类弹窗样式 */
.popup-title {
  font-size: 32rpx;
  font-weight: bold;
}

.popup-close {
  color: #ff8c00;
  font-size: 28rpx;
}

.time-row text {
  color: #333;
  font-size: 18rpx;
}

/* 选择器相关样式 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 900;
}

.picker-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  z-index: 1000;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
}

.picker-close {
  color: #ff8c00;
  font-size: 28rpx;
}

.time-picker-view,
.date-picker-view,
.people-count-picker {
  width: 100%;
  height: 400rpx;
}

.picker-column-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
}

/* 可点击效果 */
.time-main,
.time-select {
  position: relative;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  cursor: pointer;
}

.time-main:active,
.time-select:active {
  background: #f5f5f5;
}

/* 参与人数选择 */
.people-count {
  font-size: 28rpx;
  color: #666;
}

/* 性别选择 */
.gender-text {
  font-size: 28rpx;
  color: #666;
}

.gender-picker {
  width: 100%;
  height: 400rpx;
}

/* 双列布局容器 */
.dual-column-container {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

/* 半宽元素 */
.half-width {
  width: calc(50% - 10rpx);
}

/* 二级分类滚轮选择器 */
.subcategory-picker {
  width: 100%;
  height: 400rpx;
}

/* 时间段选择器 */
.time-slot-picker {
  width: 100%;
  height: 400rpx;
}

/* 约单对象样式 */
.expert-target {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.expert-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 表单项样式 */
</style>
