package com.play.xianshibusiness.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 订单审核DTO
 */
@Data
@ApiModel(value = "订单审核DTO")
public class AuditOrderDTO {
    
    @NotBlank(message = "订单ID不能为空")
    @ApiModelProperty(value = "订单ID", required = true)
    private String orderId;
    
    @NotNull(message = "审核结果不能为空")
    @ApiModelProperty(value = "审核结果：1-审核通过，2-审核拒绝", required = true)
    private Integer result;
    
    @ApiModelProperty(value = "审核意见，拒绝时必填")
    private String comment;
    
    @ApiModelProperty(value = "金币消耗量（仅审核通过时有效）")
    private BigDecimal goldCost;
    
    @ApiModelProperty(value = "审核人ID")
    private String auditorId;
} 