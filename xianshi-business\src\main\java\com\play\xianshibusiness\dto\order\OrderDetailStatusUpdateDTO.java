package com.play.xianshibusiness.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 子订单状态更新DTO
 */
@Data
@ApiModel("子订单状态更新DTO")
public class OrderDetailStatusUpdateDTO {

    @ApiModelProperty(value = "子订单ID", required = true)
    @NotBlank(message = "子订单ID不能为空")
    private String detailId;

    @ApiModelProperty(value = "目标状态", required = true)
    @NotNull(message = "目标状态不能为空")
    private Integer status;
} 