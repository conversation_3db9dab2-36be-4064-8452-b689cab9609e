#!/bin/bash
echo "===== [PRE] 停止旧进程 ====="
PID=$(ps -ef | grep "Xianshi-App" | grep -v grep | grep java | awk '{print $2}')
if [ -n "$PID" ]; then
  echo "正在停止进程: $PID"
  kill -15 "$PID"
  sleep 2
else
  echo "未找到旧进程，无需停止"
fi
echo "===== [PRE] 清理旧部署目录 /xianshi ====="
rm -rf /xianshi
mkdir -p /xianshi
echo "===== [PRE] 删除 Jenkins 构建产物目录中的旧 JAR ====="
JENKINS_JAR="/var/lib/jenkins/workspace/xianshi-app/Xianshi-App/target/Xianshi-App-0.0.1-SNAPSHOT.jar"
if [ -f "$JENKINS_JAR" ]; then
  echo "删除旧 JAR 文件: $JENKINS_JAR"
  rm -f "$JENKINS_JAR"
else
  echo "Jenkins 构建目录中无旧 JAR 文件"
fi