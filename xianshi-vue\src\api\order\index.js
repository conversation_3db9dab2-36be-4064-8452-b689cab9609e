import request from '@/utils/request';

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/order/page',
    method: 'get',
    params
  });
}

// 获取订单详情
export function getOrderDetail(orderId) {
  return request({
    url: `/order/detail/${orderId}`,
    method: 'get'
  });
}

// 审核订单
export function auditOrder(data) {
  // 转换前端参数到后端需要的格式
  const auditData = {
    orderId: data.orderId,
    result: data.approved ? 1 : 2, // 1-审核通过，2-审核拒绝
    comment: data.remark || ''
  };
  
  return request({
    url: '/order/audit',
    method: 'post',
    data: auditData
  });
}

// 取消订单
export function cancelOrder(orderId, reason) {
  return request({
    url: `/order/cancel/${orderId}`,
    method: 'post',
    params: { reason }
  });
}

// 获取订单统计数据
export function getOrderStatistics() {
  console.log('调用获取订单统计数据API');
  return request({
    url: '/order/statistics',
    method: 'get'
  }).then(response => {
    console.log('订单统计数据API响应:', response);
    return response;
  }).catch(error => {
    console.error('订单统计数据API错误:', error);
    throw error;
  });
}

// 根据时间范围获取订单统计数据
export function getOrderStatisticsByDateRange(startDate, endDate) {
  console.log('调用根据时间范围获取订单统计数据API, 参数:', { startDate, endDate });
  return request({
    url: '/order/statistics/by-date',
    method: 'get',
    params: { startDate, endDate }
  }).then(response => {
    console.log('时间范围订单统计数据API响应:', response);
    return response;
  }).catch(error => {
    console.error('时间范围订单统计数据API错误:', error);
    throw error;
  });
}

// 获取订单趋势数据
export function getOrderTrendData(period, startDate, endDate) {
  console.log('调用获取订单趋势数据API, 参数:', { period, startDate, endDate });
  return request({
    url: '/order/trend',
    method: 'get',
    params: { period, startDate, endDate }
  }).then(response => {
    console.log('订单趋势数据API响应:', response);
    return response;
  }).catch(error => {
    console.error('订单趋势数据API错误:', error);
    throw error;
  });
}

// 获取订单类型分布数据
export function getOrderTypeDistribution(startDate, endDate) {
  console.log('调用获取订单类型分布数据API, 参数:', { startDate, endDate });
  return request({
    url: '/order/type-distribution',
    method: 'get',
    params: { startDate, endDate }
  }).then(response => {
    console.log('订单类型分布数据API响应:', response);
    return response;
  }).catch(error => {
    console.error('订单类型分布数据API错误:', error);
    throw error;
  });
}

// 获取订单评价
export function getOrderComments(orderId, pageNum, pageSize) {
  return request({
    url: `/order/comments/${orderId}`,
    method: 'get',
    params: { pageNum, pageSize }
  });
}

/**
 * 获取订单状态流转记录
 * @param {String} orderId 订单ID
 * @returns {Promise}
 */
export function getOrderStatusRecords(orderId) {
  return request({
    url: `/order/status-records/${orderId}`,
    method: 'get'
  });
}

/**
 * 更新子订单状态
 * @param {Object} data 子订单状态更新数据
 * @returns {Promise}
 */
export function updateOrderDetailStatus(data) {
  return request({
    url: '/order/update-detail-status',
    method: 'post',
    data
  });
}

/**
 * 选择达人
 * @param {Object} data 包含orderId和达人ID数组
 * @returns {Promise}
 */
export function selectTalents(data) {
  return request({
    url: '/order/select-talents',
    method: 'post',
    data
  });
} 