package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.member.MemberViewHistoryDTO;
import com.play.xianshibusiness.mapper.CMemberMapper;
import com.play.xianshibusiness.mapper.MemberViewHistoryMapper;
import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.pojo.MemberViewHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 会员浏览历史服务
 */
@Service
@Slf4j
public class MemberViewHistoryService {
    
    @Resource
    private MemberViewHistoryMapper memberViewHistoryMapper;
    
    @Resource
    private CMemberMapper cMemberMapper;
    
    /**
     * 记录会员浏览历史
     * @param memberId 当前会员ID
     * @param targetMemberId 被查看会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean recordViewHistory(String memberId, String targetMemberId) {
        // 不记录查看自己的历史
        if(memberId.equals(targetMemberId)) {
            return false;
        }
        
        // 检查目标会员是否存在
        CMember targetMember = cMemberMapper.selectById(targetMemberId);
        if(targetMember == null || targetMember.getDeleted()) {
            return false;
        }
        
        // 查找是否有历史记录
        LambdaQueryWrapper<MemberViewHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberViewHistory::getMemberId, memberId)
                   .eq(MemberViewHistory::getTargetMemberId, targetMemberId);
        
        MemberViewHistory viewHistory = memberViewHistoryMapper.selectOne(queryWrapper);
        
        LocalDateTime now = LocalDateTime.now();
        
        // 如果有记录则更新时间，否则创建新记录
        if(viewHistory != null) {
            viewHistory.setViewCount(viewHistory.getViewCount() + 1);
            viewHistory.setLastViewTime(now);
            viewHistory.setUpdateTime(now);
            memberViewHistoryMapper.updateById(viewHistory);
        } else {
            viewHistory = new MemberViewHistory();
            viewHistory.setId(UUID.randomUUID().toString());
            viewHistory.setMemberId(memberId);
            viewHistory.setTargetMemberId(targetMemberId);
            viewHistory.setViewCount(1);
            viewHistory.setFirstViewTime(now);
            viewHistory.setLastViewTime(now);
            viewHistory.setCreateTime(now);
            viewHistory.setUpdateTime(now);
            viewHistory.setDeleted(false);
            viewHistory.setAvailable(true);
            memberViewHistoryMapper.insert(viewHistory);
        }
        
        return true;
    }
    
    /**
     * 获取会员浏览历史列表
     * @param memberId 会员ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 浏览历史列表
     */
    public Page<MemberViewHistoryDTO> getMemberViewHistory(String memberId, Integer pageNum, Integer pageSize) {
        // 创建分页对象
        Page<MemberViewHistory> page = new Page<>(pageNum, pageSize);
        
        // 构建查询条件
        LambdaQueryWrapper<MemberViewHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberViewHistory::getMemberId, memberId)
                   .eq(MemberViewHistory::getDeleted, false)
                   .orderByDesc(MemberViewHistory::getLastViewTime);
        
        // 执行查询
        Page<MemberViewHistory> historyPage = memberViewHistoryMapper.selectPage(page, queryWrapper);
        
        // 转换为DTO
        Page<MemberViewHistoryDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(historyPage, dtoPage, "records");
        
        List<MemberViewHistoryDTO> dtoList = historyPage.getRecords().stream().map(history -> {
            MemberViewHistoryDTO dto = new MemberViewHistoryDTO();
            BeanUtils.copyProperties(history, dto);
            
            // 填充目标会员基本信息
            CMember targetMember = cMemberMapper.selectById(history.getTargetMemberId());
            if(targetMember != null) {
                dto.setTargetMemberNickname(targetMember.getNickname());
                dto.setTargetMemberAvatar(targetMember.getAvatar());
            }
            
            return dto;
        }).collect(Collectors.toList());
        
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }
    
    /**
     * 清除指定浏览历史记录
     * @param memberId 会员ID
     * @param historyId 历史记录ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteViewHistory(String memberId, String historyId) {
        // 构建查询条件
        LambdaQueryWrapper<MemberViewHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberViewHistory::getMemberId, memberId)
                   .eq(MemberViewHistory::getId, historyId);
        
        // 查询记录
        MemberViewHistory history = memberViewHistoryMapper.selectOne(queryWrapper);
        
        // 验证记录存在且属于当前会员
        if(history == null) {
            return false;
        }
        
        // 逻辑删除
        history.setDeleted(true);
        history.setUpdateTime(LocalDateTime.now());
        memberViewHistoryMapper.updateById(history);
        
        return true;
    }
    
    /**
     * 清空会员所有浏览历史
     * @param memberId 会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean clearAllViewHistory(String memberId) {
        // 更新条件
        LambdaQueryWrapper<MemberViewHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberViewHistory::getMemberId, memberId)
                   .eq(MemberViewHistory::getDeleted, false);
        
        // 创建更新对象
        MemberViewHistory updateEntity = new MemberViewHistory();
        updateEntity.setDeleted(true);
        updateEntity.setUpdateTime(LocalDateTime.now());
        
        // 批量更新
        memberViewHistoryMapper.update(updateEntity, queryWrapper);
        
        return true;
    }
    
    /**
     * 获取会员被浏览的总次数
     * @param memberId 会员ID
     * @return 被浏览总次数
     */
    public Integer getMemberViewCount(String memberId) {
        // 构建查询条件：查询所有浏览该会员的记录
        LambdaQueryWrapper<MemberViewHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberViewHistory::getTargetMemberId, memberId)
                   .eq(MemberViewHistory::getDeleted, false);
        
        // 查询所有记录
        List<MemberViewHistory> viewHistories = memberViewHistoryMapper.selectList(queryWrapper);
        
        // 计算总浏览次数
        return viewHistories.stream()
                .mapToInt(MemberViewHistory::getViewCount)
                .sum();
    }
    
    /**
     * 获取会员浏览其他人的总次数
     * @param memberId 会员ID
     * @return 浏览其他人总次数
     */
    public Integer getMemberMyViewCount(String memberId) {
        // 构建查询条件：查询该会员浏览其他人的记录
        LambdaQueryWrapper<MemberViewHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberViewHistory::getMemberId, memberId)
                   .eq(MemberViewHistory::getDeleted, false);
        
        // 查询所有记录
        List<MemberViewHistory> viewHistories = memberViewHistoryMapper.selectList(queryWrapper);
        
        // 计算总浏览次数
        return viewHistories.stream()
                .mapToInt(MemberViewHistory::getViewCount)
                .sum();
    }
} 