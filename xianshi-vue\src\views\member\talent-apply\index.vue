<template>
  <div class="app-container clay-container">
    <div class="filter-container clay-card">
      <el-form :inline="true" :model="listQuery" class="filter-form">
        <el-form-item label="审核状态">
          <el-select v-model="listQuery.status" placeholder="请选择状态" clearable class="clay-select">
            <el-option :value="0" label="待审核" />
            <el-option :value="1" label="已通过" />
            <el-option :value="2" label="已拒绝" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" class="clay-button" style="background-color: #409EFF; color: #fff; border-color: #409EFF;">查询</el-button>
          <el-button @click="resetQuery" class="clay-button clay-button-secondary" style="background-color: #f5f7fa; color: #606266; border-color: #dcdfe6;">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      class="clay-table"
    >
      <el-table-column label="会员ID" align="center" min-width="120">
        <template slot-scope="{row}">
          <span>{{ row.memberId }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="头像" align="center" width="80">
        <template slot-scope="{row}">
          <el-avatar :src="row.avatar" :size="40">
            <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
          </el-avatar>
        </template>
      </el-table-column>
      
      <el-table-column label="昵称" min-width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.nickname }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="手机号" min-width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.tel }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="申请理由" min-width="200" align="center">
        <template slot-scope="{row}">
          <el-tooltip class="item" effect="dark" :content="row.reason" placement="top" :disabled="row.reason && row.reason.length < 20">
            <span>{{ row.reason ? (row.reason.length > 20 ? row.reason.substring(0, 20) + '...' : row.reason) : '无' }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" min-width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getStatusType(row.status)" class="clay-tag">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="申请时间" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createTime }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" min-width="220" class-name="fixed-width">
        <template slot-scope="{row}">
          <el-button 
            type="info" 
            size="mini" 
            @click="handleDetail(row)" 
            style="background-color: #909399; color: #fff; border-color: #909399;"
          >查看</el-button>
          
          <el-button 
            type="success" 
            size="mini" 
            v-if="row.status === 0" 
            @click="handleAudit(row, 1)"
            style="background-color: #67c23a; color: #fff; border-color: #67c23a;"
          >通过</el-button>
          
          <el-button 
            type="danger" 
            size="mini" 
            v-if="row.status === 0" 
            @click="handleAudit(row, 2)"
            style="background-color: #f56c6c; color: #fff; border-color: #f56c6c;"
          >拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 审核对话框 -->
    <el-dialog :title="auditForm.result === 1 ? '通过申请' : '拒绝申请'" :visible.sync="auditDialogVisible" width="500px" class="clay-dialog">
      <el-form ref="auditForm" :model="auditForm" label-width="100px">
        <el-form-item label="审核意见" prop="comment" :rules="{ required: auditForm.result === 2, message: '请填写拒绝原因', trigger: 'blur' }">
          <el-input 
            v-model="auditForm.comment" 
            type="textarea" 
            :placeholder="auditForm.result === 1 ? '审核意见（选填）' : '请填写拒绝原因'"
            :rows="4"
            class="clay-textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false" class="clay-button clay-button-secondary" style="background-color: #f5f7fa; color: #606266; border-color: #dcdfe6;">取消</el-button>
        <el-button type="primary" @click="submitAudit" class="clay-button" style="background-color: #409EFF; color: #fff; border-color: #409EFF;">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTalentApplyList, auditTalentApply } from '@/api/member';

export default {
  name: 'TalentApply',
  data() {
    return {
      list: [],
      listLoading: true,
      listQuery: {
        status: ''
      },
      auditDialogVisible: false,
      auditForm: {
        memberId: '',
        result: 1,
        comment: ''
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      getTalentApplyList(this.listQuery.status).then(response => {
        this.list = response.data || [];
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      });
    },
    handleSearch() {
      this.getList();
    },
    resetQuery() {
      this.listQuery = {
        status: ''
      };
      this.getList();
    },
    handleDetail(row) {
      this.$router.push({ path: `/member/detail/${row.memberId}` });
    },
    handleAudit(row, result) {
      this.auditForm = {
        memberId: row.memberId,
        result: result,
        comment: ''
      };
      this.auditDialogVisible = true;
    },
    submitAudit() {
      this.$refs.auditForm.validate(valid => {
        if (valid) {
          auditTalentApply(this.auditForm.memberId, this.auditForm.result, this.auditForm.comment).then(response => {
            this.$message({
              type: 'success',
              message: this.auditForm.result === 1 ? '已通过申请' : '已拒绝申请'
            });
            this.auditDialogVisible = false;
            this.getList();
          });
        }
      });
    },
    getStatusType(status) {
      const statusMap = {
        0: 'warning', // 待审核
        1: 'success', // 已通过
        2: 'danger'   // 已拒绝
      };
      return statusMap[status] || 'info';
    },
    getStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style lang="scss" scoped>
.clay-container {
  background-color: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
}

.clay-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.clay-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  
  ::v-deep .el-table__header-wrapper {
    th {
      background-color: #f7f9fc;
      color: #606266;
      font-weight: 600;
    }
  }
  
  ::v-deep .el-table__body-wrapper {
    td {
      padding: 16px 0;
    }
  }
}

.clay-input, .clay-select, .clay-date-picker {
  ::v-deep .el-input__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    transition: all 0.3s;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-textarea {
  ::v-deep .el-textarea__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-tag {
  border-radius: 16px;
  padding: 2px 10px;
  font-weight: 500;
}

.clay-dialog {
  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
    
    .el-dialog__header {
      background-color: #f7f9fc;
      padding: 16px 20px;
      
      .el-dialog__title {
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style> 