package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.enums.OrderEnrollDetailStatusEnum;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (BOrderEnrollDetail)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("b_order_enroll_detail")
@ApiModel("订单报名表")
public class BOrderEnrollDetail extends BaseObjPo {

    //主订单ID
    @ApiModelProperty(value = "主订单ID")
    private String orderId;
    //报名会员ID
    @ApiModelProperty(value = "报名会员ID")
    private String memberId;
    //是否被选择
    @ApiModelProperty(value = "是否被选择")
    private Boolean isSelect;
    //子订单状态
    @ApiModelProperty(value = "子订单状态")
    @EnumValue
    private OrderEnrollDetailStatusEnum status;
    //选中时间
    @ApiModelProperty(value = "选中时间")
    private LocalDateTime selectTime;
    //出发时间
    @ApiModelProperty(value = "出发时间")
    private LocalDateTime departureTime;
    //到达时间
    @ApiModelProperty(value = "到达时间")
    private LocalDateTime arrivalTime;
    //服务开始时间
    @ApiModelProperty(value = "服务开始时间")
    private LocalDateTime serviceStartTime;
    //服务结束时间
    @ApiModelProperty(value = "服务结束时间")
    private LocalDateTime serviceEndTime;
    //是否被选择
    @ApiModelProperty(value = "是否被选择")
    private Boolean selected;
}

