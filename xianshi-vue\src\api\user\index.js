import request from '@/utils/request';

// 登录接口
export function login(data) {
  return request({
    url: '/system/login',
    method: 'post',
    data
  });
}

// 获取用户信息
export function getInfo() {
  return request({
    url: '/system/info',
    method: 'get'
  });
}

// 退出登录
export function logout() {
  return request({
    url: '/system/logout',
    method: 'post'
  });
}

// 获取验证码
export function getVerifyCode() {
  return request({
    url: '/system/verifyCode',
    method: 'get'
  });
}

// 获取个人资料
export function getUserProfile() {
  return request({
    url: '/system/profile',
    method: 'get'
  });
}

// 更新个人资料
export function updateUserProfile(data) {
  return request({
    url: '/system/profile',
    method: 'put',
    data
  });
}

// 更新用户密码
export function updateUserPassword(data) {
  return request({
    url: '/system/password',
    method: 'put',
    data
  });
} 