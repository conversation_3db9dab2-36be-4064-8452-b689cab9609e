<template>
  <view class="container">
    <view class="category-grid">
      <view
        v-for="(item, index) in categories"
        :key="index"
        :class="['category-item', {selected: selectedCategories[index]}]"
        @tap="toggleCategory"
        :data-index="index"
      >
        <view class="category-icon">
          <text :class="item.icon"></text>
        </view>
        <text class="category-name">{{ item.name }}</text>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-btn-wrap">
      <button class="save-btn" @tap="saveNotices">保存</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      categories: [
        { name: "休闲娱乐", icon: "fas fa-coffee" },
        { name: "游戏", icon: "fas fa-gamepad" },
        { name: "运动", icon: "fas fa-running" },
        { name: "商务", icon: "fas fa-briefcase" },
      ],
      selectedCategories: {},
      selectedNotices: [],
    };
  },
  onLoad(options) {
    uni.setNavigationBarTitle({
      title: "编辑接单项目",
    });

    // 如果有已选择的项目，初始化选中状态
    if (options.notices) {
      const notices = JSON.parse(decodeURIComponent(options.notices));
      const selectedCategories = {};

      notices.forEach((notice) => {
        const index = this.categories.findIndex(
          (cat) => cat.name === notice
        );
        if (index !== -1) {
          selectedCategories[index] = true;
        }
      });

      this.selectedCategories = selectedCategories;
    }
  },
  methods: {
    toggleCategory(e) {
      const index = e.currentTarget.dataset.index;
      const selectedCategories = { ...this.selectedCategories };

      if (selectedCategories[index]) {
        delete selectedCategories[index];
      } else {
        selectedCategories[index] = true;
      }

      this.selectedCategories = selectedCategories;
    },

    async saveNotices() {
      try {
        uni.showLoading({ title: '保存中...' });
        
        const selectedNotices = Object.keys(this.selectedCategories).map(
          (index) => this.categories[index].name
        );
        
        // 调用API保存通告项目
        const app = getApp().globalData;
        const res = await this.$requestHttp.put(app.commonApi.updateMemberInfo, {
          data: {
            notice: selectedNotices.join(',')
          }
        });
        
        if (res && res.code === 200) {
          // 保存选中的项目到当前页面数据中
          this.selectedNotices = selectedNotices;

          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];

          if (prevPage) {
            prevPage.notices = selectedNotices;
          }

          // 更新本地存储
          const profileData = uni.getStorageSync("profileData") || {};
          profileData.notices = selectedNotices;
          uni.setStorageSync("profileData", profileData);

          // 触发上一页面的完成度更新
          if (prevPage.updateCompletion) {
            prevPage.updateCompletion();
          }
          
          uni.hideLoading();
          uni.showToast({
            title: "保存成功",
            icon: "success",
            duration: 1500,
            success: () => {
              setTimeout(() => {
                uni.navigateBack();
              }, 1500);
            },
          });
        } else {
          throw new Error(res?.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存通告失败:', error);
        uni.hideLoading();
        uni.showToast({ title: error.message || '保存失败，请重试', icon: 'none' });
      }
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.category-item {
  background: #f8f8f8;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.category-item.selected {
  background: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  font-weight: 500;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 140, 0, 0.1);
  border-radius: 999rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff8c00;
  font-size: 36rpx;
}

.category-name {
  font-size: 26rpx;
  color: #333;
}

.selected .category-name {
  color: #ff8c00;
  font-weight: 500;
}

.save-btn-wrap {
  margin-top: auto;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
