<template>
  <view class="container">
    <!-- 顶部标签导航 -->
    <view class="tabs-container">
      <view 
        :class="['tab-item', { active: activeTab === 'projects' }]" 
        @tap="switchTab('projects')"
      >
        接单项目
      </view>
      <view 
        :class="['tab-item', { active: activeTab === 'cities' }]" 
        @tap="switchTab('cities')"
      >
        接单城市
      </view>
    </view>
    
    <!-- 接单项目内容区 -->
    <view class="content-container" v-if="activeTab === 'projects'">
      <view class="category-grid">
        <view
          v-for="(item, index) in categories"
          :key="index"
          :class="['category-item', {selected: selectedCategories[index]}]"
          @tap="toggleCategory"
          :data-index="index"
        >
          <view class="category-icon">
            <text :class="item.icon"></text>
          </view>
          <text class="category-name">{{ item.name }}</text>
        </view>
      </view>
    </view>
    
    <!-- 接单城市内容区 -->
    <view class="content-container" v-if="activeTab === 'cities'">
      <view class="city-list">
        <view
          :class="['city-item', {selected: selectedCity === city}]"
          v-for="city in cityList"
          :key="city"
          @tap="toggleCity"
          :data-city="city"
        >
          <text class="city-name">{{ city }}</text>
          <text class="selected-icon" v-if="selectedCity === city">✓</text>
        </view>
      </view>
      <text class="tip-text">* 每次只能选择一个城市进行接单</text>
    </view>
    
    <!-- 底部保存按钮 -->
    <view class="save-btn-wrap">
      <button class="save-btn" @tap="saveSettings">保存</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'projects', // 默认显示接单项目标签
      // 接单项目数据
      categories: [
        { name: "休闲娱乐", icon: "fas fa-coffee" },
        { name: "游戏", icon: "fas fa-gamepad" },
        { name: "运动", icon: "fas fa-running" },
        { name: "商务", icon: "fas fa-briefcase" },
      ],
      selectedCategories: {},
      selectedNotices: [],
      // 接单城市数据
      cityList: ["成都", "重庆", "杭州", "上海", "深圳", "广州"],
      selectedCity: "",
    };
  },
  onLoad() {
    // 加载已保存的接单项目设置
    const profileData = uni.getStorageSync("profileData") || {};
    const notices = profileData.notices || [];
    
    const selectedCategories = {};
    notices.forEach((notice) => {
      const index = this.categories.findIndex(cat => cat.name === notice);
      if (index !== -1) {
        selectedCategories[index] = true;
      }
    });
    this.selectedCategories = selectedCategories;
    
    // 加载已保存的接单城市设置
    const cities = profileData.cities || [];
    this.selectedCity = cities[0] || "";
  },
  methods: {
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab;
    },
    
    // 接单项目相关方法
    toggleCategory(e) {
      const index = e.currentTarget.dataset.index;
      const selectedCategories = { ...this.selectedCategories };

      if (selectedCategories[index]) {
        delete selectedCategories[index];
      } else {
        selectedCategories[index] = true;
      }

      this.selectedCategories = selectedCategories;
    },
    
    // 接单城市相关方法
    toggleCity(e) {
      const city = e.currentTarget.dataset.city;
      this.selectedCity = this.selectedCity === city ? "" : city;
    },
    
    // 保存设置
    saveSettings() {
      // 保存接单项目设置
      const selectedNotices = Object.keys(this.selectedCategories).map(
        index => this.categories[index].name
      );
      
      // 保存接单城市设置
      const selectedCitiesList = this.selectedCity ? [this.selectedCity] : [];
      
      // 更新本地存储
      const profileData = uni.getStorageSync("profileData") || {};
      profileData.notices = selectedNotices;
      profileData.cities = selectedCitiesList;
      uni.setStorageSync("profileData", profileData);
      
      // 返回上一页
      uni.showToast({
        title: "设置保存成功",
        icon: "success",
        duration: 2000,
        success: () => {
          // 延迟返回，让用户看到提示
          setTimeout(() => {
            // 设置标记，让上一个页面知道设置已更新
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];
            if (prevPage) {
              prevPage.notices = selectedNotices;
              prevPage.cities = selectedCitiesList;
              prevPage.hasOrderItems = selectedNotices.length > 0;
              prevPage.hasCitySettings = selectedCitiesList.length > 0;
              
              // 如果有更新完成度的方法，调用它
              if (prevPage.updateCompletion) {
                prevPage.updateCompletion();
              }
            }
            
            uni.navigateBack();
          }, 1500);
        }
      });
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #fff;
  padding: 0 0 30rpx;
  display: flex;
  flex-direction: column;
}

/* 标签样式 */
.tabs-container {
  display: flex;
  background: #ff8c00;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  position: relative;
}

.tab-item.active {
  color: #ffffff;
  font-weight: bold;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #ffffff;
  border-radius: 3rpx;
}

.content-container {
  flex: 1;
  padding: 30rpx;
}

/* 接单项目样式 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.category-item {
  background: #f8f8f8;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.category-item.selected {
  background: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  font-weight: 500;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 140, 0, 0.1);
  border-radius: 999rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff8c00;
  font-size: 36rpx;
}

.category-name {
  font-size: 26rpx;
  color: #333;
}

.selected .category-name {
  color: #ff8c00;
  font-weight: 500;
}

/* 接单城市样式 */
.city-list {
  background: #f8f8f8;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.city-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.city-item:last-child {
  border-bottom: none;
}

.city-item.selected {
  background: rgba(255, 140, 0, 0.05);
  font-weight: 500;
}

.city-name {
  font-size: 28rpx;
  color: #333;
}

.selected-icon {
  color: #ff8c00;
  font-weight: bold;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 30rpx;
  display: block;
}

/* 保存按钮样式 */
.save-btn-wrap {
  padding: 0 30rpx;
  margin-top: 40rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 