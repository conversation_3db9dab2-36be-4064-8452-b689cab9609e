package com.play.xianshibusiness.annotation;

import com.play.xianshibusiness.vailad.LoginRequestValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(
        validatedBy = {LoginRequestValidator.class}
)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface LoginRequestValid {
    String message() default "Invalid login request";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
