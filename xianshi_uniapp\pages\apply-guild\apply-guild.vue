<template>
  <view class="container">
    <view class="form-card">
      <view class="form-title">闲时公会认证</view>
      <view class="form-subtitle">机构平台认证，仅限企业法人申请，不接受个人</view>
      
      <!-- 基本信息 -->
      <view class="form-group">
        <view class="form-label">企业名称</view>
        <input class="form-input" type="text" v-model="formData.companyName" placeholder="请输入企业名称" />
      </view>
      
      <view class="form-group">
        <view class="form-label">营业执照编码</view>
        <input class="form-input" type="text" v-model="formData.licenseCode" placeholder="组织机构代码证/社会信用代码" />
      </view>
      
      <!-- 营业执照上传 -->
      <view class="form-group">
        <view class="form-label important">上传营业执照照片（必须加盖公章）<text class="required-mark">*</text></view>
        <view class="upload-area">
          <view class="upload-preview" v-if="formData.licenseImage">
            <image class="preview-image" :src="formData.licenseImage" mode="aspectFit"></image>
            <view class="delete-btn" @tap="deleteLicenseImage">×</view>
          </view>
          <view class="upload-btn" v-else @tap="uploadLicenseImage">
            <view class="upload-icon">
              <text class="camera-icon">📷</text>
            </view>
            <view class="upload-text">上传营业执照</view>
            <view class="upload-hint">请确保信息清晰可见</view>
          </view>
        </view>
      </view>
      
      <!-- 认证方式选择 -->
      <view class="form-group">
        <view class="form-label">选择验证方式</view>
        <view class="verify-options">
          <view class="verify-option" :class="{ active: formData.verifyMethod === 'faceId' }" @tap="selectVerifyMethod('faceId')">
            <view class="option-inner">法人人脸识别</view>
          </view>
          <view class="verify-option" :class="{ active: formData.verifyMethod === 'bankVerify' }" @tap="selectVerifyMethod('bankVerify')">
            <view class="option-inner">对公打款验证</view>
          </view>
        </view>
      </view>
      
      <!-- 法人信息 -->
      <view class="form-group">
        <view class="form-label">法人姓名</view>
        <input class="form-input" type="text" v-model="formData.legalName" placeholder="请输入法人姓名" />
      </view>
      
      <view class="form-group">
        <view class="form-label">法人身份证号</view>
        <input class="form-input" type="idcard" v-model="formData.legalIdCard" placeholder="请输入法人身份证号" />
      </view>
      
      <view class="form-group">
        <view class="form-label">联系电话</view>
        <input class="form-input" type="number" v-model="formData.contactPhone" placeholder="请输入认证人联系电话" />
      </view>
      
      <view class="form-group">
        <view class="form-label">邀请码</view>
        <input class="form-input" type="text" v-model="formData.inviteCode" placeholder="请输入邀请码（选填）" />
      </view>
      
      <!-- 提交按钮 -->
      <button class="submit-btn" @tap="submitApplication">提交认证申请</button>
    </view>
    
    <!-- 提示信息 -->
    <view class="tips-card">
      <view class="tips-title"><text class="tips-icon">📢</text> 认证须知</view>
      <view class="tips-item important">1. 闲时公会必须是企业法人，个人用户请选择达人认证</view>
      <view class="tips-item">2. 请确保提交的营业执照真实有效</view>
      <view class="tips-item">3. 认证审核需要1-3个工作日</view>
      <view class="tips-item">4. 认证通过后可使用公会管理功能</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        companyName: '',
        licenseCode: '',
        licenseImage: '',
        verifyMethod: 'faceId', // 默认法人人脸识别
        legalName: '',
        legalIdCard: '',
        contactPhone: '',
        inviteCode: ''
      },
      isSubmitting: false
    };
  },
  methods: {
    // 上传营业执照照片
    uploadLicenseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 预览图片
          this.formData.licenseImage = res.tempFilePaths[0];
          
          // 在实际应用中，这里应该调用上传API
          // this.uploadImageToServer(res.tempFilePaths[0]);
        }
      });
    },
    
    // 删除已选择的营业执照照片
    deleteLicenseImage() {
      this.formData.licenseImage = '';
    },
    
    // 选择验证方式
    selectVerifyMethod(method) {
      this.formData.verifyMethod = method;
    },
    
    // 表单验证
    validateForm() {
      if (!this.formData.companyName.trim()) {
        uni.showToast({
          title: '请输入企业名称',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.licenseCode.trim()) {
        uni.showToast({
          title: '请输入营业执照编码',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.licenseImage) {
        uni.showToast({
          title: '请上传营业执照照片',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.legalName.trim()) {
        uni.showToast({
          title: '请输入法人姓名',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.legalIdCard.trim()) {
        uni.showToast({
          title: '请输入法人身份证号',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.contactPhone.trim()) {
        uni.showToast({
          title: '请输入联系电话',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 提交申请
    submitApplication() {
      if (this.isSubmitting) return;
      
      if (!this.validateForm()) return;
      
      this.isSubmitting = true;
      uni.showLoading({
        title: '提交中...'
      });
      
      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading();
        this.isSubmitting = false;
        
        // 显示成功提示
        uni.showModal({
          title: '提交成功',
          content: '公会认证申请已提交，请耐心等待审核结果',
          showCancel: false,
          success: () => {
            // 返回上一页
            uni.navigateBack();
          }
        });
      }, 1500);
    }
  }
};
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding: 30rpx;
}

/* 表单卡片样式 */
.form-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.form-subtitle {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

/* 表单元素样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.form-label.important {
  font-weight: bold;
}

.required-mark {
  color: #ff0000;
  margin-left: 6rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}

/* 上传区域样式 */
.upload-area {
  display: flex;
  justify-content: center;
}

.upload-btn {
  width: 360rpx;
  height: 480rpx;
  background-color: #f7f7f7;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-icon {
  font-size: 60rpx;
  color: #999;
}

.upload-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #999;
  margin-bottom: 10rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #bbb;
}

.upload-preview {
  width: 360rpx;
  height: 480rpx;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

/* 验证方式选择 */
.verify-options {
  display: flex;
  gap: 20rpx;
}

.verify-option {
  flex: 1;
  height: 88rpx;
}

.option-inner {
  background-color: #f7f7f7;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.verify-option.active .option-inner {
  background-color: #ffb800;
  color: #fff;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #ffb800;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  margin-top: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 184, 0, 0.3);
}

.submit-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}

/* 提示卡片 */
.tips-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.tips-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

.tips-item:last-child {
  margin-bottom: 0;
}

.tips-icon {
  margin-right: 10rpx;
}

.tips-item.important {
  color: #ff0000; /* 强调重要信息 */
  font-weight: bold;
}
</style> 