package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 达人项目设置实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_talent_project")
@ApiModel("达人项目设置表")
public class CTalentProject extends BaseObjPo {

    @ApiModelProperty("达人ID")
    private String talentId;
    
    @ApiModelProperty("项目ID")
    private String projectId;
    
    @ApiModelProperty("项目名称")
    private String projectName;
    
    @ApiModelProperty("价格(元/小时)")
    private Integer price;
    
    @ApiModelProperty("排序")
    private Integer sort;
} 