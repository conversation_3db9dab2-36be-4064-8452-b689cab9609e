package com.play.xianshibusiness.dto.activity;

import com.play.xianshibusiness.pojo.BOrderType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动类型树形结构VO
 */
@Data
@ApiModel("活动类型树形结构VO")
public class ActivityTypeTreeVO {

    @ApiModelProperty(value = "主键")
    private String id;
    
    @ApiModelProperty(value = "父类ID")
    private String parentId;
    
    @ApiModelProperty(value = "分类名称")
    private String name;
    
    @ApiModelProperty(value = "编码")
    private String code;
    
    @ApiModelProperty(value = "所需金币")
    private BigDecimal needGold;
    
    @ApiModelProperty(value = "排序")
    private Integer comparable;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;
    
    @ApiModelProperty(value = "子类型列表")
    private List<ActivityTypeTreeVO> children;
    
    /**
     * 从BOrderType转换为ActivityTypeTreeVO
     * @param orderType 订单类型
     * @return ActivityTypeTreeVO
     */
    public static ActivityTypeTreeVO fromBOrderType(BOrderType orderType) {
        if (orderType == null) {
            return null;
        }
        
        ActivityTypeTreeVO vo = new ActivityTypeTreeVO();
        vo.setId(orderType.getId());
        vo.setParentId(orderType.getParentId());
        vo.setName(orderType.getName());
        vo.setCode(orderType.getCode());
        vo.setNeedGold(orderType.getNeedGold());
        vo.setCreateTime(orderType.getCreateTime());
        vo.setUpdateTime(orderType.getUpdateTime());
        
        return vo;
    }
} 