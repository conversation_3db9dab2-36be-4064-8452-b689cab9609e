package com.play.xianshibusiness.dto.recharge;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 充值DTO
 */
@Data
@ApiModel("充值DTO")
public class RechargeDTO {

    @ApiModelProperty(value = "充值金额(元)", required = true)
    @NotNull(message = "充值金额不能为空")
    @Min(value = 1, message = "充值金额必须大于等于1元")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "充值方式", required = true)
    @NotBlank(message = "充值方式不能为空")
    private String payType; // ALIPAY, WECHAT, BANK_CARD
    
    @ApiModelProperty(value = "充值套餐ID")
    private String packageId;
    
    @ApiModelProperty(value = "备注")
    private String remark;
} 