package com.play.xianshibusiness.dto.gold;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 金币调整DTO
 */
@Data
@ApiModel(value = "金币调整DTO")
public class GoldAdjustDTO {
    
    /**
     * 会员ID
     */
    @ApiModelProperty(value = "会员ID", required = true)
    @NotBlank(message = "会员ID不能为空")
    private String memberId;
    
    /**
     * 调整金额，正数为增加，负数为减少
     */
    @ApiModelProperty(value = "调整金额，正数为增加，负数为减少", required = true)
    @NotNull(message = "调整金额不能为空")
    private Integer amount;
    
    /**
     * 调整原因
     */
    @ApiModelProperty(value = "调整原因", required = true)
    @NotBlank(message = "调整原因不能为空")
    private String reason;
    
    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private String operatorId;
} 