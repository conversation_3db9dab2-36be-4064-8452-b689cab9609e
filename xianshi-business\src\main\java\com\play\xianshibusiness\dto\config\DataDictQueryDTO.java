package com.play.xianshibusiness.dto.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据字典查询DTO
 */
@Data
@ApiModel("数据字典查询DTO")
public class DataDictQueryDTO {
    
    @ApiModelProperty(value = "关键字", notes = "支持类型名称、编码、描述模糊查询")
    private String keyword;
    
    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;
    
    @ApiModelProperty(value = "每页条数", example = "10")
    private Integer pageSize = 10;
} 