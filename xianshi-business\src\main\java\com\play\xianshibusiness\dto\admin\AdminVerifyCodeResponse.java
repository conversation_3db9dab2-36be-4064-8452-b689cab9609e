package com.play.xianshibusiness.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 后台验证码响应DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "验证码响应")
public class AdminVerifyCodeResponse implements Serializable {
    
    @ApiModelProperty(value = "验证码图片Base64编码")
    private String imageBase64;
    
    @ApiModelProperty(value = "验证码键")
    private String verifyKey;
    
    @ApiModelProperty(value = "验证码过期时间（秒）")
    private Integer expireIn;
} 