package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据字典类型实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_data_dict_type")
@ApiModel("数据字典类型表")
public class TDataDictType extends BaseObjPo {

    @ApiModelProperty("类型编码")
    private String typeCode;
    
    @ApiModelProperty("类型名称")
    private String typeName;
    
    @ApiModelProperty("类型描述")
    private String description;
    
    @ApiModelProperty("排序")
    private Integer sort;
}

