package com.play.xianshiadmin.controller.system;

import com.play.xianshiadmin.config.AdminInitializer;
import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理员注册控制器
 * 用于创建新的管理员账号
 */
@RestController
@RequestMapping("/admin/system")
@Api(tags = "管理员注册-API")
public class AdminRegisterController {

    @Autowired
    private AdminInitializer adminInitializer;
    
    /**
     * 注册新的管理员账号
     * 需要管理员权限
     */
    @ApiOperation("注册管理员")
    @PostMapping("/register")
    @RequireAdmin
    public Result<Boolean> registerAdmin(
            @ApiParam(value = "用户名", required = true) @RequestParam String username,
            @ApiParam(value = "密码", required = true) @RequestParam String password,
            @ApiParam(value = "昵称", required = true) @RequestParam String nickname,
            @ApiParam(value = "手机号", required = true) @RequestParam String phone) {
        
        boolean success = adminInitializer.registerAdmin(username, password, nickname, phone);
        return ResultUtils.success(success);
    }
    
    /**
     * 注册默认管理员账号
     * 仅用于测试，生产环境应禁用
     */
    @ApiOperation("注册默认管理员")
    @PostMapping("/register/default")
    public Result<Boolean> registerDefaultAdmin() {
        adminInitializer.createAdmin("19973409989", "123456");
        return ResultUtils.success(true);
    }
} 