package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.gold.GoldAdjustDTO;
import com.play.xianshibusiness.dto.gold.GoldRecordQueryDTO;
import com.play.xianshibusiness.dto.gold.GoldRecordVO;
import com.play.xianshibusiness.enums.GoldOperationTypeEnum;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.CMemberGoldRecordMapper;
import com.play.xianshibusiness.mapper.CMemberMapper;
import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.pojo.CMemberGoldRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员金币记录服务
 */
@Service
public class CMemberGoldRecordService {

    @Resource
    private CMemberGoldRecordMapper goldRecordMapper;

    @Resource
    private CMemberMapper memberMapper;

    @Resource
    private CMemberService memberService;

    /**
     * 分页查询金币记录
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    public Page<GoldRecordVO> pageGoldRecords(GoldRecordQueryDTO queryDTO) {
        if (queryDTO == null) {
            queryDTO = new GoldRecordQueryDTO();
        }

        // 构建查询条件
        LambdaQueryWrapper<CMemberGoldRecord> queryWrapper = new LambdaQueryWrapper<>();

        // 条件过滤
        if (!StringUtils.isEmpty(queryDTO.getMemberId())) {
            queryWrapper.eq(CMemberGoldRecord::getMemberId, queryDTO.getMemberId());
        }

        if (!StringUtils.isEmpty(queryDTO.getMemberName())) {
            List<String> memberIds = memberMapper.selectList(new LambdaQueryWrapper<CMember>()
                    .like(CMember::getNickname, queryDTO.getMemberName())
                    .select(CMember::getId))
                    .stream()
                    .map(CMember::getId)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(memberIds)) {
                queryWrapper.in(CMemberGoldRecord::getMemberId, memberIds);
            } else {
                return new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
            }
        }

        if (queryDTO.getOperationType() != null) {
            queryWrapper.eq(CMemberGoldRecord::getOperationType, queryDTO.getOperationType());
        }

        if (!StringUtils.isEmpty(queryDTO.getCreateTimeStart())) {
            try {
                LocalDateTime startTime = LocalDateTime.parse(queryDTO.getCreateTimeStart(),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                queryWrapper.ge(CMemberGoldRecord::getCreateTime, startTime);
            } catch (Exception e) {
                // 格式错误，忽略该条件
            }
        }

        if (!StringUtils.isEmpty(queryDTO.getCreateTimeEnd())) {
            try {
                LocalDateTime endTime = LocalDateTime.parse(queryDTO.getCreateTimeEnd(),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                queryWrapper.le(CMemberGoldRecord::getCreateTime, endTime);
            } catch (Exception e) {
                // 格式错误，忽略该条件
            }
        }

        // 排序
        if (!StringUtils.isEmpty(queryDTO.getOrderBy())) {
            if ("createTime".equals(queryDTO.getOrderBy())) {
                queryWrapper.orderBy(true, "asc".equalsIgnoreCase(queryDTO.getOrderDirection()),
                        CMemberGoldRecord::getCreateTime);
            } else if ("amount".equals(queryDTO.getOrderBy())) {
                queryWrapper.orderBy(true, "asc".equalsIgnoreCase(queryDTO.getOrderDirection()),
                        CMemberGoldRecord::getAmount);
            } else {
                queryWrapper.orderByDesc(CMemberGoldRecord::getCreateTime);
            }
        } else {
            queryWrapper.orderByDesc(CMemberGoldRecord::getCreateTime);
        }

        // 执行查询
        Page<CMemberGoldRecord> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<CMemberGoldRecord> resultPage = goldRecordMapper.selectPage(page, queryWrapper);

        // 转换结果
        List<GoldRecordVO> records = resultPage.getRecords().stream()
                .map(this::convertToGoldRecordVO)
                .collect(Collectors.toList());

        // 批量查询会员信息
        if (!CollectionUtils.isEmpty(records)) {
            List<String> memberIds = records.stream()
                    .map(GoldRecordVO::getMemberId)
                    .distinct()
                    .collect(Collectors.toList());

            Map<String, CMember> memberMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(memberIds)) {
                memberMap = memberMapper.selectList(new LambdaQueryWrapper<CMember>()
                        .in(CMember::getId, memberIds))
                        .stream()
                        .collect(Collectors.toMap(CMember::getId, Function.identity(), (o1, o2) -> o1));
            }

            // 填充会员信息
            for (GoldRecordVO record : records) {
                CMember member = memberMap.get(record.getMemberId());
                if (member != null) {
                    record.setMemberName(member.getNickname());
                    record.setMemberAvatar(member.getAvatar());
                }
            }
        }

        Page<GoldRecordVO> resultVOPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(),
                resultPage.getTotal());
        resultVOPage.setRecords(records);

        return resultVOPage;
    }

    /**
     * 转换为金币记录VO
     */
    private GoldRecordVO convertToGoldRecordVO(CMemberGoldRecord record) {
        if (record == null) {
            return null;
        }

        GoldRecordVO vo = new GoldRecordVO();
        BeanUtils.copyProperties(record, vo);

        // 设置操作类型描述
        if (record.getOperationType() != null) {
            GoldOperationTypeEnum typeEnum = record.getOperationType();
            if (typeEnum != null) {
                vo.setOperationTypeDesc(typeEnum.getDesc());
            }
        }

        return vo;
    }

    /**
     * 获取金币记录详情
     *
     * @param id 记录ID
     * @return 记录详情
     */
    public GoldRecordVO getGoldRecordDetail(String id) {
        // 获取记录信息
        CMemberGoldRecord record = goldRecordMapper.selectById(id);
        if (record == null) {
            throw new GlobalException(400, "记录不存在");
        }

        // 转换为VO
        GoldRecordVO vo = convertToGoldRecordVO(record);

        // 获取会员信息
        CMember member = memberMapper.selectById(record.getMemberId());
        if (member != null) {
            vo.setMemberName(member.getNickname());
            vo.setMemberAvatar(member.getAvatar());
        }

        return vo;
    }

    /**
     * 手动调整会员金币
     *
     * @param adjustDTO 调整参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean adjustMemberGold(GoldAdjustDTO adjustDTO) {
        if (adjustDTO == null || StringUtils.isEmpty(adjustDTO.getMemberId())) {
            throw new GlobalException(400, "会员ID不能为空");
        }

        // 获取会员信息
        CMember member = memberMapper.selectById(adjustDTO.getMemberId());
        if (member == null) {
            throw new GlobalException(400, "会员不存在");
        }

        // 获取当前金币余额
        Integer currentGold = memberService.getMemberGoldBalance(adjustDTO.getMemberId());

        // 计算新金币余额
        int newGold = currentGold + adjustDTO.getAmount();
        if (newGold < 0) {
            throw new GlobalException(400, "金币余额不足，无法调整");
        }

        // 更新会员金币
        member.setGold(BigDecimal.valueOf(newGold));
        memberMapper.updateById(member);

        // 创建金币记录
        CMemberGoldRecord record = new CMemberGoldRecord();
        record.setMemberId(adjustDTO.getMemberId());
        record.setOperationType(
                adjustDTO.getAmount() > 0 ? GoldOperationTypeEnum.SYSTEM_ADD : GoldOperationTypeEnum.SYSTEM_DEDUCT);
        record.setAmount(Math.abs(adjustDTO.getAmount())); // 设置金额，使用绝对值
        record.setRemark(adjustDTO.getReason());
        record.setOperatorId(adjustDTO.getOperatorId());
        record.setCreateTime(LocalDateTime.now());
        goldRecordMapper.insert(record);

        return true;
    }

    /**
     * 获取金币统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getGoldStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 今日增加金币总量
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        Integer todayAddTotal = goldRecordMapper.selectTodayAddTotal(today);
        statistics.put("todayAddTotal", todayAddTotal != null ? todayAddTotal : 0);

        // 今日减少金币总量
        Integer todayReduceTotal = goldRecordMapper.selectTodayReduceTotal(today);
        statistics.put("todayReduceTotal", todayReduceTotal != null ? todayReduceTotal : 0);

        // 系统金币总量
        Integer systemTotal = goldRecordMapper.selectSystemTotal();
        statistics.put("systemTotal", systemTotal != null ? systemTotal : 0);

        // 各操作类型金币量
        for (GoldOperationTypeEnum typeEnum : GoldOperationTypeEnum.values()) {
            Integer typeTotal = goldRecordMapper.selectTypeTotal(typeEnum.getCode());
            statistics.put(typeEnum.name() + "Total", typeTotal != null ? typeTotal : 0);
        }

        return statistics;
    }

    /**
     * 获取会员金币记录
     * 
     * @param memberId 会员ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 金币记录分页数据
     */
    public Page<CMemberGoldRecord> getMemberGoldRecords(String memberId, Integer pageNum, Integer pageSize) {
        Page<CMemberGoldRecord> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<CMemberGoldRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMemberGoldRecord::getMemberId, memberId);
        queryWrapper.orderByDesc(CMemberGoldRecord::getCreateTime);

        return goldRecordMapper.selectPage(page, queryWrapper);
    }
}
