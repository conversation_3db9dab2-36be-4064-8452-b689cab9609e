package com.play.xianshiadmin.controller.system;

import com.play.xianshibusiness.annotation.RequireAdmin;
import com.play.xianshibusiness.dto.admin.AdminLoginRequest;
import com.play.xianshibusiness.dto.admin.AdminLoginResponse;
import com.play.xianshibusiness.dto.admin.AdminVerifyCodeResponse;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.AdminService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 后台登录控制器
 */
@RestController
@RequestMapping("/admin/system")
@Api(tags = "后台登录-API")
public class AdminLoginController {

    @Resource
    private AdminService adminService;
    
    /**
     * 后台登录
     */
    @ApiOperation("后台登录")
    @PostMapping("/login")
    public Result<AdminLoginResponse> login(@RequestBody @Valid AdminLoginRequest request) {

        return ResultUtils.success(adminService.adminLogin(request));
    }
    
    /**
     * 获取验证码图片
     */
    @ApiOperation("获取验证码图片")
    @GetMapping("/verifyCode")
    public Result<AdminVerifyCodeResponse> getVerifyCode() {
        return ResultUtils.success(adminService.generateVerifyCode());
    }
    
    /**
     * 获取当前登录管理员信息
     */
    @ApiOperation("获取当前登录管理员信息")
    @GetMapping("/info")
    @RequireAdmin
    public Result<Object> getInfo() {
        String adminId = PrincipalUtil.getAdminId();
        return ResultUtils.success(adminService.getAdminInfo(adminId));
    }
    
    /**
     * 退出登录
     */
    @ApiOperation("退出登录")
    @PostMapping("/logout")
    @RequireAdmin
    public Result<Boolean> logout() {
        String adminId = PrincipalUtil.getAdminId();
        return ResultUtils.success(adminService.adminLogout(adminId));
    }
} 