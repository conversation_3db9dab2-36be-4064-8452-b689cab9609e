<template>
  <div class="app-container clay-container">
    <div v-loading="loading" class="clay-card">
      <!-- 会员基本信息 -->
      <div class="member-header">
        <div class="member-avatar">
          <el-avatar :src="memberInfo.avatar" :size="100">
            <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
          </el-avatar>
        </div>
        <div class="member-info">
          <h2>{{ memberInfo.nickname || '未知用户' }}</h2>
          <div class="member-meta">
            <span class="meta-item">
              <i class="el-icon-mobile-phone"></i> {{ memberInfo.tel || '未绑定手机' }}
            </span>
            <span class="meta-item">
              <i class="el-icon-user"></i> {{ memberInfo.currentRoleId === 'TALENT' ? '达人' : '普通用户' }}
            </span>
            <span class="meta-item">
              <i class="el-icon-coin"></i> {{ memberInfo.gold || 0 }} 金币
            </span>
            <span class="meta-item">
              <i class="el-icon-time"></i> 注册时间: {{ memberInfo.createTime || '未知' }}
            </span>
            <el-tag 
              :type="memberInfo.available ? 'success' : 'danger'" 
              class="status-tag"
            >{{ memberInfo.available ? '正常' : '禁用' }}</el-tag>
          </div>
        </div>
        <div class="member-actions">
          <el-button 
            type="success" 
            size="small" 
            v-if="!memberInfo.available" 
            @click="handleStatus(true)"
            style="background-color: #67c23a; color: #fff; border-color: #67c23a;"
          >启用</el-button>
          <el-button 
            type="danger" 
            size="small" 
            v-if="memberInfo.available" 
            @click="handleStatus(false)"
            style="background-color: #f56c6c; color: #fff; border-color: #f56c6c;"
          >禁用</el-button>
        </div>
      </div>
      
      <!-- 会员详细信息标签页 -->
      <el-tabs v-model="activeTab" class="member-tabs">
        <el-tab-pane label="基本信息" name="info">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">会员ID:</span>
                <span class="info-value">{{ memberInfo.id }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">系统ID:</span>
                <span class="info-value">{{ memberInfo.sysId }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">昵称:</span>
                <span class="info-value">{{ memberInfo.nickname }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">手机号:</span>
                <span class="info-value">{{ memberInfo.tel }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">当前角色:</span>
                <span class="info-value">{{ memberInfo.currentRoleId === 'TALENT' ? '达人' : '普通用户' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">拥有角色:</span>
                <span class="info-value">
                  <el-tag 
                    v-for="role in memberInfo.roles" 
                    :key="role.code" 
                    :type="role.code === 'TALENT' ? 'success' : 'primary'"
                    class="role-tag"
                  >
                    {{ role.name }}
                  </el-tag>
                  <span v-if="!memberInfo.roles || memberInfo.roles.length === 0">无</span>
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">达人申请状态:</span>
                <span class="info-value">{{ getApplyStatusText(memberInfo.applyStatus) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">金币余额:</span>
                <span class="info-value">{{ memberInfo.gold || 0 }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">账号状态:</span>
                <span class="info-value">{{ memberInfo.available ? '正常' : '禁用' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">粉丝数:</span>
                <span class="info-value">{{ memberInfo.fansCount || 0 }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">关注数:</span>
                <span class="info-value">{{ memberInfo.attentionCount || 0 }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">注册时间:</span>
                <span class="info-value">{{ memberInfo.createTime }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">最后登录时间:</span>
                <span class="info-value">{{ memberInfo.lastLoginTime || '未登录' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
        
        <el-tab-pane label="订单记录" name="orders">
          <el-table
            v-loading="ordersLoading"
            :data="ordersList"
            border
            fit
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column label="订单ID" align="center" min-width="120">
              <template slot-scope="{row}">
                <span>{{ row.orderId }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="订单标题" min-width="150" align="center">
              <template slot-scope="{row}">
                <span>{{ row.title }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="陪玩项目" min-width="100" align="center">
              <template slot-scope="{row}">
                <span>{{ row.typeName }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="订单状态" min-width="100" align="center">
              <template slot-scope="{row}">
                <el-tag :type="getOrderStatusType(row.status)" class="clay-tag">{{ row.statusDesc }}</el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="小时费用" min-width="100" align="center">
              <template slot-scope="{row}">
                <span>{{ row.hourlyRate }} 元/时</span>
              </template>
            </el-table-column>
            
            <el-table-column label="创建时间" min-width="150" align="center">
              <template slot-scope="{row}">
                <span>{{ row.createTime }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" align="center" width="120">
              <template slot-scope="{row}">
                <el-button 
                  type="primary" 
                  size="mini" 
                  @click="viewOrderDetail(row)"
                  style="background-color: #409EFF; color: #fff; border-color: #409EFF;"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination
            v-show="ordersTotal>0"
            :total="ordersTotal"
            :page.sync="ordersQuery.pageNum"
            :limit.sync="ordersQuery.pageSize"
            @pagination="getOrdersList"
            class="clay-pagination"
          />
        </el-tab-pane>
        
        <el-tab-pane label="金币记录" name="gold">
          <el-table
            v-loading="goldLoading"
            :data="goldList"
            border
            fit
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column label="记录ID" align="center" width="120">
              <template slot-scope="{row}">
                <span>{{ row.id }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="类型" min-width="100" align="center">
              <template slot-scope="{row}">
                <el-tag :type="row.type === 1 ? 'success' : 'danger'">
                  {{ row.type === 1 ? '增加' : '减少' }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="金币数量" min-width="100" align="center">
              <template slot-scope="{row}">
                <span :style="{ color: row.type === 1 ? '#67c23a' : '#f56c6c' }">
                  {{ row.type === 1 ? '+' : '-' }}{{ row.amount }}
                </span>
              </template>
            </el-table-column>
            
            <el-table-column label="备注" min-width="200" align="center">
              <template slot-scope="{row}">
                <span>{{ row.remark }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="时间" min-width="150" align="center">
              <template slot-scope="{row}">
                <span>{{ row.createTime }}</span>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination
            v-show="goldTotal>0"
            :total="goldTotal"
            :page.sync="goldQuery.pageNum"
            :limit.sync="goldQuery.pageSize"
            @pagination="getGoldList"
            class="clay-pagination"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 禁用/启用对话框 -->
    <el-dialog :title="statusForm.status ? '启用会员' : '禁用会员'" :visible.sync="statusDialogVisible" width="500px" class="clay-dialog">
      <el-form ref="statusForm" :model="statusForm" label-width="100px">
        <el-form-item label="操作原因" prop="reason" :rules="{ required: !statusForm.status, message: '请填写禁用原因', trigger: 'blur' }">
          <el-input 
            v-model="statusForm.reason" 
            type="textarea" 
            :placeholder="statusForm.status ? '启用原因（选填）' : '请填写禁用原因'"
            :rows="4"
            class="clay-textarea"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="statusDialogVisible = false" class="clay-button clay-button-secondary" style="background-color: #f5f7fa; color: #606266; border-color: #dcdfe6;">取消</el-button>
        <el-button type="primary" @click="submitStatus" class="clay-button" style="background-color: #409EFF; color: #fff; border-color: #409EFF;">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMemberDetail, updateMemberStatus, getMemberOrders, getMemberGoldRecords } from '@/api/member';
import Pagination from '@/components/Pagination';

export default {
  name: 'MemberDetail',
  components: { Pagination },
  data() {
    return {
      loading: true,
      memberId: this.$route.params.id,
      memberInfo: {},
      activeTab: 'info',
      
      // 订单相关
      ordersLoading: false,
      ordersList: [],
      ordersTotal: 0,
      ordersQuery: {
        pageNum: 1,
        pageSize: 10,
        status: ''
      },
      
      // 金币记录相关
      goldLoading: false,
      goldList: [],
      goldTotal: 0,
      goldQuery: {
        pageNum: 1,
        pageSize: 10
      },
      
      // 状态修改相关
      statusDialogVisible: false,
      statusForm: {
        status: true,
        reason: ''
      }
    };
  },
  created() {
    this.getMemberInfo();
    // 检查是否有指定的标签页
    if (this.$route.query.tab) {
      this.activeTab = this.$route.query.tab;
    }
  },
  watch: {
    activeTab(val) {
      if (val === 'orders') {
        this.getOrdersList();
      } else if (val === 'gold') {
        this.getGoldList();
      }
    }
  },
  methods: {
    getMemberInfo() {
      this.loading = true;
      getMemberDetail(this.memberId).then(response => {
        this.memberInfo = response.data || {};
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    getOrdersList() {
      this.ordersLoading = true;
      getMemberOrders(this.memberId, this.ordersQuery).then(response => {
        this.ordersList = response.data.records || [];
        this.ordersTotal = response.data.total || 0;
        this.ordersLoading = false;
      }).catch(() => {
        this.ordersLoading = false;
      });
    },
    getGoldList() {
      this.goldLoading = true;
      getMemberGoldRecords(this.memberId, this.goldQuery).then(response => {
        this.goldList = response.data.records || [];
        this.goldTotal = response.data.total || 0;
        this.goldLoading = false;
      }).catch(() => {
        this.goldLoading = false;
      });
    },
    handleStatus(status) {
      this.statusForm = {
        status: status,
        reason: ''
      };
      this.statusDialogVisible = true;
    },
    submitStatus() {
      this.$refs.statusForm.validate(valid => {
        if (valid) {
          updateMemberStatus(this.memberId, this.statusForm.status).then(response => {
            this.$message({
              type: 'success',
              message: this.statusForm.status ? '启用会员成功' : '禁用会员成功'
            });
            this.statusDialogVisible = false;
            this.getMemberInfo();
          });
        }
      });
    },
    viewOrderDetail(row) {
      this.$router.push({ path: `/order/detail/${row.orderId}` });
    },
    getApplyStatusText(status) {
      const statusMap = {
        0: '未申请',
        1: '待审核',
        2: '已通过',
        3: '已拒绝'
      };
      return statusMap[status] || '未知';
    },
    getOrderStatusType(status) {
      const statusMap = {
        0: 'info',    // 待发布
        1: 'warning', // 待审核
        2: 'primary', // 发布中
        3: 'success', // 待选择达人
        4: 'info',    // 进行中
        5: 'success', // 已完成
        6: 'danger'   // 已取消
      };
      return statusMap[status] || 'info';
    }
  }
};
</script>

<style lang="scss" scoped>
.clay-container {
  background-color: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
}

.clay-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.member-header {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.member-avatar {
  margin-right: 20px;
}

.member-info {
  flex: 1;
  
  h2 {
    margin: 0 0 10px;
    font-size: 20px;
    font-weight: 600;
  }
}

.member-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  
  .meta-item {
    margin-right: 20px;
    margin-bottom: 5px;
    color: #606266;
    
    i {
      margin-right: 5px;
    }
  }
  
  .status-tag {
    margin-left: auto;
  }
}

.member-actions {
  margin-left: 20px;
}

.member-tabs {
  margin-top: 20px;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  
  .info-label {
    width: 120px;
    color: #909399;
    font-weight: 500;
  }
  
  .info-value {
    flex: 1;
    color: #303133;
  }
}

.clay-pagination {
  margin-top: 24px;
  text-align: right;
  
  ::v-deep .el-pagination {
    padding: 10px 0;
    
    button, li {
      background-color: #f9fafc;
      border-radius: 6px;
      margin: 0 3px;
      
      &.active {
        background-color: #409eff;
        color: white;
      }
      
      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}

.clay-dialog {
  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
    
    .el-dialog__header {
      background-color: #f7f9fc;
      padding: 16px 20px;
      
      .el-dialog__title {
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}

.clay-textarea {
  ::v-deep .el-textarea__inner {
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #f9fafc;
    border: 1px solid #e6e8f0;
    
    &:focus, &:hover {
      border-color: #409eff;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.clay-tag {
  border-radius: 16px;
  padding: 2px 10px;
  font-weight: 500;
}

.role-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
</style> 