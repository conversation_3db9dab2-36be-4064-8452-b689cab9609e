package com.play.xianshibusiness.dto.gold;

import com.play.xianshibusiness.dto.PageQueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金币记录查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoldRecordQueryDTO extends PageQueryParam {
    
    /**
     * 会员ID
     */
    private String memberId;
    
    /**
     * 会员昵称，模糊查询
     */
    private String memberName;
    
    /**
     * 操作类型
     */
    private Integer operationType;
    
    /**
     * 创建时间开始
     */
    private String createTimeStart;
    
    /**
     * 创建时间结束
     */
    private String createTimeEnd;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向：asc/desc
     */
    private String orderDirection;
} 