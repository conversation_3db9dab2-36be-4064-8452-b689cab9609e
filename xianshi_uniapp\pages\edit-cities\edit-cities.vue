<template>
  <view class="container">
    <view class="city-list">
      <view
        :class="['city-item', {selected: selectedCity === city}]"
        v-for="city in cityList"
        :key="city"
        @tap="toggleCity"
        :data-city="city"
      >
        <text class="city-name">{{ city }}</text>
        <text class="selected-icon" v-if="selectedCity === city">✓</text>
      </view>
    </view>

    <text class="tip-text">* 每次只能选择一个城市进行接单</text>
    <button class="save-btn" @tap="saveCities">保存</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      cityList: ["成都", "重庆", "杭州", "上海", "深圳", "广州"],
      selectedCity: "",
    };
  },
  onLoad(options) {
    if (options.cities) {
      const cities = JSON.parse(decodeURIComponent(options.cities));
      const selectedCity = cities[0] || "";
      this.selectedCity = selectedCity;
    }
  },
  methods: {
    toggleCity(e) {
      const city = e.currentTarget.dataset.city;
      this.selectedCity = this.selectedCity === city ? "" : city;
    },

    async saveCities() {
      try {
        uni.showLoading({ title: '保存中...' });
        
        const selectedCitiesList = this.selectedCity
          ? [this.selectedCity]
          : [];
        
        // 调用API保存城市信息
        const app = getApp().globalData;
        const res = await this.$requestHttp.put(app.commonApi.updateMemberInfo, {
          data: {
            city: selectedCitiesList.join(',')
          }
        });
        
        if (res && res.code === 200) {
          // 更新本地存储
          const profileData = uni.getStorageSync("profileData") || {};
          profileData.cities = selectedCitiesList;
          uni.setStorageSync("profileData", profileData);

          // 返回上一页并更新状态
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];
          if (prevPage) {
            prevPage.hasCitySettings = selectedCitiesList.length > 0;
          }
          
          uni.hideLoading();
          uni.showToast({ title: '保存成功', icon: 'success' });
          
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(res?.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存城市失败:', error);
        uni.hideLoading();
        uni.showToast({ title: error.message || '保存失败，请重试', icon: 'none' });
      }
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #f7f7f7;
  padding: 30rpx;
}

.city-list {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 48rpx;
}

.city-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.city-item:last-child {
  border-bottom: none;
}

.city-item.selected {
  background: rgba(255, 140, 0, 0.05);
  font-weight: 500;
}

.city-name {
  font-size: 28rpx;
  color: #333;
}

.selected-icon {
  color: #ff8c00;
  font-weight: bold;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  padding: 0 20rpx;
  margin-bottom: 24rpx;
}
</style>
