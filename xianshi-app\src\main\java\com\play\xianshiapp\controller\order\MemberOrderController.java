package com.play.xianshiapp.controller.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.order.OrderDetailVO;
import com.play.xianshibusiness.dto.order.OrderQueryDTO;
import com.play.xianshibusiness.enums.OrderStatusEnum;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.BOrderService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 会员订单管理控制器
 */
@RestController
@RequestMapping("/api/member/order")
@Api(tags = "会员订单管理API")
public class MemberOrderController {

    @Resource
    private BOrderService orderService;

    @GetMapping("/publish-orders")
    @ApiOperation(value = "获取会员发布的订单列表（支持状态筛选）")
    public Result<Page<OrderDetailVO>> getPublishOrders(
            @ApiParam(value = "订单状态，0-草稿，1-审核中，2-已发布，3-待选择，4-进行中，5-已完成，6-已取消")
            @RequestParam(required = false) Integer statusCode,
            @ApiParam(value = "页码", defaultValue = "1") 
            @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", defaultValue = "10") 
            @RequestParam(defaultValue = "10") Integer pageSize,
            HttpServletRequest request) {

        String memberId = PrincipalUtil.getMemberId();

        // 构建查询条件
        OrderQueryDTO queryDTO = new OrderQueryDTO();
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(pageSize);
        // 直接使用statusCode，不进行枚举转换
        queryDTO.setStatus(statusCode);
        
        // 调用服务层方法获取订单列表
        Page<OrderDetailVO> result = orderService.pageOrderByRole(queryDTO, memberId);
        
        return ResultUtils.success(result);
    }

    @GetMapping("/statistics")
    @ApiOperation(value = "获取会员订单统计数据")
    public Result<Map<String, Object>> getOrderStatistics(HttpServletRequest request) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.getUserOrderStatistics(memberId));
    }

    @GetMapping("/detail/{orderId}")
    @ApiOperation(value = "获取订单详情")
    public Result<OrderDetailVO> getOrderDetail(
            @ApiParam(value = "订单ID", required = true) 
            @PathVariable String orderId) {
        return ResultUtils.success(orderService.getOrderDetail(orderId));
    }

    @PostMapping("/cancel/{orderId}")
    @ApiOperation(value = "取消订单")
    public Result<Boolean> cancelOrder(
            @ApiParam(value = "订单ID", required = true) 
            @PathVariable String orderId,
            @ApiParam(value = "取消原因") 
            @RequestParam(required = false) String reason,
            HttpServletRequest request) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.cancelOrder(orderId, memberId, reason));
    }
} 