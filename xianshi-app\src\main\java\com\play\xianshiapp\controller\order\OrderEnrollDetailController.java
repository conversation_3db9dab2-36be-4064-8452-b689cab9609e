package com.play.xianshiapp.controller.order;


import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.order.OrderCommentDTO;
import com.play.xianshibusiness.dto.order.OrderEnrollDetailUpdateDTO;
import com.play.xianshibusiness.dto.order.OrderEnrollDetailVO;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.BOrderCommentService;
import com.play.xianshibusiness.service.BOrderEnrollDetailService;
import com.play.xianshibusiness.service.BOrderService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/api/order/enroll-detail")
@Api(tags = "订单报名 模块API")
public class OrderEnrollDetailController {

    @Resource
    private BOrderService orderService;

    @Resource
    private BOrderEnrollDetailService orderEnrollDetailService;

    @Resource
    private BOrderCommentService orderCommentService;

    @PostMapping("/enroll/{orderId}")
    @ApiOperation(value = "达人报名订单")
    @RequireLogin
    public Result<Boolean> enrollOrder(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.enrollOrder(orderId, memberId));
    }

    @PostMapping("/cancel/{orderId}")
    @ApiOperation(value = "达人取消报名")
    @RequireLogin
    public Result<Boolean> cancelOrder(
            @ApiParam(value = "订单ID", required = true) @PathVariable String orderId) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderService.cancelEnrollment(orderId, memberId));
    }



    @PostMapping("/complete-service/{id}")
    @ApiOperation(value = "服务完成")
    @RequireLogin
    public Result<Boolean> completeService(
            @ApiParam(value = "子订单ID", required = true) @PathVariable String id) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderEnrollDetailService.completeService(id, memberId));
    }

    @PostMapping("/comment")
    @ApiOperation(value = "评价订单")
    @RequireLogin
    public Result<Boolean> comment(@RequestBody @Valid OrderCommentDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderEnrollDetailService.confirmAndComment(dto, memberId));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新子订单状态")
    @RequireLogin
    public Result<Boolean> updateOrderEnrollDetailStatus(
            @ApiParam(value = "子订单更新DTO", required = true) @RequestBody @Valid OrderEnrollDetailUpdateDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderEnrollDetailService.updateOrderEnrollDetailStatus(dto, memberId));
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取子订单详情")
    public Result<OrderEnrollDetailVO> getOrderEnrollDetail(
            @ApiParam(value = "子订单ID", required = true) @PathVariable String id) {
        return ResultUtils.success(orderEnrollDetailService.getOrderEnrollDetail(id));
    }
}
