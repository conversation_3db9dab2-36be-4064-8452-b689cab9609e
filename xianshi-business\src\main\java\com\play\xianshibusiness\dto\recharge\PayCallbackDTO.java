package com.play.xianshibusiness.dto.recharge;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 支付回调DTO
 */
@Data
@ApiModel("支付回调DTO")
public class PayCallbackDTO {

    @ApiModelProperty(value = "订单号", required = true)
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    
    @ApiModelProperty(value = "支付状态", required = true)
    @NotBlank(message = "支付状态不能为空")
    private String status; // SUCCESS, FAILED
    
    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "支付流水号")
    private String transactionId;
    
    @ApiModelProperty(value = "支付方式")
    private String payType;
    
    @ApiModelProperty(value = "支付时间")
    private String payTime;
    
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;
    
    @ApiModelProperty(value = "签名")
    private String sign;
} 