package com.play.xianshibusiness.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.play.xianshibusiness.pojo.BPayInfo;

/**
 * (BPayInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:11
 */
public interface BPayInfoMapper extends BaseMapper<BPayInfo> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BPayInfo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BPayInfo> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BPayInfo> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BPayInfo> entities);

}

