<template>
  <view class="container">
    <!-- 用户信息部分 -->
    <view class="user-info">
      <view class="top-section">
        <view class="nickname-section">{{ userInfo.nickname }}</view>
        <view class="verify-btn" @tap="goToVerify">
          <text v-if="isUserVerified">实名已完成 ></text>
          <text v-else class="unverified">实名未完成 ></text>
        </view>
      </view>
      <view class="user-id">用户ID: {{ userInfo.sysId }}</view>
    </view>

    <!-- 认证类型列表 -->
    <view class="cert-list">
      <!-- 闲时达人认证 -->
      <view class="cert-item">
        <view class="cert-left">
          <view class="cert-avatar personal-icon">
            <view class="icon-circle"></view>
          </view>
          <view class="cert-info">
            <view class="cert-title">闲时达人认证</view>
            <view class="cert-desc">个人服务者认证（需完成个人主页100%和实名认证）</view>
          </view>
        </view>
        <view class="cert-right">
          <button class="cert-btn personal-btn" @tap="goCertification('personal')">去认证 ></button>
        </view>
      </view>

      <!-- 闲时公会认证 -->
      <view class="cert-item">
        <view class="cert-left">
          <view class="cert-avatar guild-icon">
            <view class="icon-circle"></view>
          </view>
          <view class="cert-info">
            <view class="cert-title">闲时公会认证</view>
            <view class="cert-desc">接单平台/公司认证（必须是机构，需上传营业执照）</view>
          </view>
        </view>
        <view class="cert-right">
          <button class="cert-btn guild-btn" @tap="goCertification('enterprise')">去认证 ></button>
        </view>
      </view>
    </view>

    <!-- 认证说明 -->
    <view class="certification-guide">
      <view class="guide-header">
        <view class="icon-tip">💡</view>
        <text class="guide-title">认证说明</text>
      </view>
      <view class="guide-content">
        <text class="guide-text">1. 闲时达人 - 个人服务者，可接单提供玩伴服务</text>
        <text class="guide-text">2. 闲时公会 - 机构平台，可管理多个达人和订单</text>
        <text class="guide-text">3. 普通用户无需认证，可直接发布订单</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getCurrentMemberInfo } from '@/api/index.js';

export default {
  data() {
    return {
      userInfo: {
        nickname: "",
        sysId: "",
        avatar: "/static/images/default-avatar.png"
      },
      isUserVerified: false,
      profileCompletion: 0
    };
  },
  onLoad() {
    // 获取用户信息和认证状态
    this.getUserInfo();
    this.checkProfileCompletion();
  },
  methods: {
    async getUserInfo() {
      try {
        console.log('开始获取用户信息...');
        
        // 调用获取当前会员信息API
        const res = await getCurrentMemberInfo({ withExtra: true, withPrivacy: true });
        
        if (res.code === 200 && res.data) {
          const memberInfo = res.data;
          console.log('获取到用户信息:', memberInfo);
          
          // 更新用户信息
          this.userInfo = {
            nickname: memberInfo.nickname || '未设置昵称',
            sysId: memberInfo.sysId || '',
            avatar: memberInfo.avatar || '/static/images/default-avatar.png',
            ...memberInfo
          };
          
          // 根据realNameStatus字段判断实名认证状态
          // 1表示已认证，0表示未认证
          this.isUserVerified = memberInfo.realNameStatus === 1;
          
          // 保存到本地存储
          uni.setStorageSync('userInfo', this.userInfo);
          
          console.log('实名认证状态:', this.isUserVerified);
        } else {
          console.error('获取用户信息失败:', res);
          this.isUserVerified = false;
        }
      } catch (error) {
        console.error('获取用户信息异常:', error);
        this.isUserVerified = false;
        
        // 如果API调用失败，尝试从本地存储获取
        const localUserInfo = uni.getStorageSync('userInfo');
        if (localUserInfo) {
          this.userInfo = { ...this.userInfo, ...localUserInfo };
          this.isUserVerified = localUserInfo.realNameStatus === 1;
        }
      }
    },
     
    goToVerify() {
      uni.navigateTo({
        url: '/pages/verify-identity/verify-identity'
      });
    },
    
    goCertification(type) {
      // 先检查实名认证状态
      if (!this.isUserVerified) {
        uni.showModal({
          title: '提示',
          content: '请先完成实名认证',
          confirmText: '去认证',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: '/pages/verify-identity/verify-identity'
              });
            }
          }
        });
        return;
      }
      
      // 根据类型导航到相应的认证页面
      if (type === 'personal') {
        // 闲时达人认证 - 需要个人主页完整度50%和实名认证
        // 检查个人主页完整度
        this.checkProfileCompletion().then(completion => {
          if (completion < 50) {
            uni.showModal({
              title: '提示',
              content: `您的个人主页完整度为${completion}%，需要达到50%才能申请闲时达人认证，是否去完善？`,
              confirmText: '去完善',
              success: (res) => {
                if (res.confirm) {
                  uni.navigateTo({
                    url: '/pages/edit-profile/edit-profile'
                  });
                }
              }
            });
          } else {
            // 个人主页完整度50%，且已实名认证，可以进入达人申请
            uni.navigateTo({
              url: '/pages/apply-expert/apply-expert'
            });
          }
        });
      } else if (type === 'enterprise') {
        // 闲时公会认证 - 需要上传营业执照，必须是机构
        uni.navigateTo({
          url: '/pages/apply-guild/apply-guild'
        });
      }
    },
    
    // 检查个人主页完整度
    async checkProfileCompletion() {
      try {
        const app = getApp().globalData;
        const res = await this.$requestHttp.get(app.commonApi.getMemberCompletion, {});
        
        if (res.code === 200) {
          this.profileCompletion = res.data;
          console.log('个人资料完整度:', this.profileCompletion + '%');
          return this.profileCompletion;
        } else {
          console.error('获取资料完整度失败:', res);
          this.profileCompletion = 0;
          return 0;
        }
      } catch (error) {
        console.error('获取资料完整度异常:', error);
        this.profileCompletion = 0;
        return 0;
      }
    }
  }
};
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: 40rpx;
}

/* 用户信息样式 */
.user-info {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.top-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.nickname-section {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.verify-btn {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.unverified {
  color: #ff8c00;
}

.user-id {
  font-size: 24rpx;
  color: #999;
}

/* 认证列表样式 */
.cert-list {
  margin-top: 0;
  background-color: #ffffff;
}

.cert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.cert-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.cert-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.personal-icon {
  background-color: #ffecdb;
}

.guild-icon {
  background-color: #e6f7ff;
}

.icon-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #ffd591;
}

.guild-icon .icon-circle {
  background-color: #91d5ff;
}

.cert-info {
  flex: 1;
}

.cert-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.cert-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.cert-right {
  margin-left: 20rpx;
}

.cert-btn {
  padding: 12rpx 30rpx;
  background-color: #ffb800;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  border: none;
  line-height: 1.8;
  min-width: 120rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 184, 0, 0.3);
}

.personal-btn {
  background-color: #ff9500;
}

.guild-btn {
  background-color: #ff9500;
}

/* 认证说明样式 */
.certification-guide {
  margin: 30rpx;
  background-color: #fff9e6;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.guide-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.icon-tip {
  margin-right: 10rpx;
  font-size: 32rpx;
  color: #ffb800;
}

.guide-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #ff8c00;
}

.guide-content {
  display: flex;
  flex-direction: column;
}

.guide-text {
  font-size: 26rpx;
  color: #666;
  line-height: 2;
  margin-bottom: 10rpx;
  position: relative;
  padding-left: 10rpx;
}

/* 按钮按下效果 */
button::after {
  border: none;
}

.cert-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}
</style>