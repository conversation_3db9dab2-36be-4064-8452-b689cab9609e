package com.play.xianshiapp.controller.activity;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.activity.ActivityTypeDTO;
import com.play.xianshibusiness.dto.activity.ActivityTypeTreeVO;
import com.play.xianshibusiness.pojo.BOrderType;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.BOrderTypeService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 陪玩类型控制器
 */
@RestController
@RequestMapping("/api/activity-type")
@Api(tags = "陪玩类型API")
public class ActiveTypeController {

    @Resource
    private BOrderTypeService orderTypeService;

    @GetMapping("/list")
    @ApiOperation(value = "获取所有陪玩类型")
    public Result<List<BOrderType>> getAllTypes() {
        return ResultUtils.success(orderTypeService.getAllTypes());
    }

    @GetMapping("/parent-list")
    @ApiOperation(value = "获取所有父级陪玩类型")
    public Result<List<BOrderType>> getAllParentTypes() {
        return ResultUtils.success(orderTypeService.getAllParentTypes());
    }

    @GetMapping("/children/{parentId}")
    @ApiOperation(value = "根据父级ID获取子类型")
    public Result<List<BOrderType>> getChildrenByParentId(
            @ApiParam(value = "父级ID", required = true) @PathVariable String parentId) {
        return ResultUtils.success(orderTypeService.getChildrenByParentId(parentId));
    }

    @GetMapping("/detail/{id}")
    @ApiOperation(value = "获取陪玩类型详情")
    public Result<BOrderType> getTypeDetail(
            @ApiParam(value = "类型ID", required = true) @PathVariable String id) {
        return ResultUtils.success(orderTypeService.getTypeDetail(id));
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建陪玩类型")
    @RequireLogin
    public Result<String> createType(@RequestBody @Valid ActivityTypeDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderTypeService.createType(dto, memberId));
    }

    @PutMapping("/update/{id}")
    @ApiOperation(value = "更新陪玩类型")
    @RequireLogin
    public Result<Boolean> updateType(
            @ApiParam(value = "类型ID", required = true) @PathVariable String id,
            @RequestBody @Valid ActivityTypeDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderTypeService.updateType(id, dto, memberId));
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除陪玩类型")
    @RequireLogin
    public Result<Boolean> deleteType(
            @ApiParam(value = "类型ID", required = true) @PathVariable String id) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(orderTypeService.deleteType(id, memberId));
    }

    @GetMapping("/page")
    @ApiOperation(value = "分页查询陪玩类型")
    public Result<Page<BOrderType>> pageTypes(
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页条数", defaultValue = "10") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "父级ID") @RequestParam(required = false) String parentId,
            @ApiParam(value = "类型名称") @RequestParam(required = false) String name) {
        return ResultUtils.success(orderTypeService.pageTypes(pageNum, pageSize, parentId, name));
    }
    
    @GetMapping("/tree")
    @ApiOperation(value = "获取陪玩类型树形结构")
    public Result<List<ActivityTypeTreeVO>> getTypeTree() {
        return ResultUtils.success(orderTypeService.getTypeTree());
    }
} 