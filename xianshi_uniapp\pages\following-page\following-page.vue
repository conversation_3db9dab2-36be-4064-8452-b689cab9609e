<script>
import {getMemberInfo, getFollowingList, unfollowMember } from '@/api/index.js';


export default {
  data() {
    return {
      followings: [],
      page: 1,
      size: 10,
      hasMore: true,
      isLoading: false,
      userId: '' // 用户ID
    };
  },
  
  onLoad(options) {
    if (options.userId) {
      this.userId = options.userId;
    } else {
      // 如果没有传入userId，则使用当前登录用户的ID
      const userInfo = uni.getStorageSync('userInfo');
      this.userId = userInfo?.sysId || '';
    }
    this.loadFollowings();
  },
  
  onPullDownRefresh() {
    this.page = 1;
    this.followings = [];
    this.hasMore = true;
    this.loadFollowings().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.page++;
      this.loadFollowings();
    }
  },
  
  methods: {
    // 加载关注列表
    async loadFollowings() {
      if (this.isLoading || !this.hasMore) return;
      
      this.isLoading = true;
      
      try {
        const res = await getFollowingList({ 
          page: this.page, 
          size: this.size 
        });
        
        if (res.code === 200) {
          if (res.data && res.data.length > 0) {
            this.followings = [...this.followings, ...res.data];
          } else {
            this.hasMore = false;
          }
        } else {
          uni.showToast({
            title: res.message || '获取关注列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取关注列表出错:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 取消关注
    async handleUnfollow(following, index) {
      try {
        uni.showLoading({
          title: '取消关注中...'
        });
        
        const res = await unfollowMember(following.id);
        
        if (res.code === 200) {
          // 从关注列表中移除
          this.followings.splice(index, 1);
          
          uni.showToast({
            title: '已取消关注',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('取消关注操作失败:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 打开用户个人主页
    async goToUserProfile(userId) {
      try {
        const res = await getMemberInfo(userId, {withExtra: true, withPrivacy: false});
        console.log("=========",res.data)
        if (res.code === 200) {
          uni.navigateTo({
            url:
                "/pages/profile/preview?id=" + res.data.id + "&name=" + res.data.nickname,
            fail: (err) => {
              console.log("跳转失败", err);
            },
          });
        }else {
          uni.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      }catch (e){
        console.log(e);
      }

    },
    
    // 格式化用户详细信息
    formatUserDetails(user) {
      let details = [];
      
      if (user.age) {
        details.push(`${user.age}岁`);
      }
      
      if (user.height) {
        details.push(`${user.height}`);
      }
      
      if (user.weight) {
        details.push(`${user.weight}`);
      }
      
      return details.join(' · ');
    }
  }
};
</script>

<template>
  <view class="following-page">


    
    <view class="following-list" v-if="followings.length > 0">
      <view class="following-item" v-for="(following, index) in followings" :key="following.id">
        <!-- 头像区域 -->
        <image class="avatar" :src="following.avatar || '/static/images/default-avatar.png'" mode="aspectFill" @tap="goToUserProfile(following.id)"></image>
        
        <!-- 用户信息区域 -->
        <view class="user-info" @tap="goToUserProfile(following.id)">
          <view class="name-row">
            <text class="username">{{ following.nickname }}</text>
            <text class="gender-icon" v-if="following.gender !== undefined">{{ following.gender === 1 ? '♂' : '♀' }}</text>
          </view>
          
          <view class="user-details">
            <text>{{ formatUserDetails(following) || '未知' }}</text>
          </view>
        </view>
        
        <!-- 取消关注按钮 -->
        <view class="unfollow-btn" @tap.stop="handleUnfollow(following, index)">
          <text>已关注</text>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else-if="!isLoading">
      <image class="empty-icon" src="/static/images/default-avatar.png" mode="aspectFit"></image>
      <text class="empty-text">暂无关注</text>
      <view class="find-experts-btn" @tap="() => uni.navigateTo({ url: '/pages/expert/expert' })">
        <text>发现达人</text>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-more" v-if="isLoading && page > 1">
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" v-if="!hasMore && followings.length > 0">
      <text>— 没有更多关注了 —</text>
    </view>
  </view>
</template>

<style scoped>
.following-page {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 0 30rpx;
}

.back-btn {
  position: fixed;
  top: 60rpx;
  left: 30rpx;
  z-index: 100;
}

.iconfont {
  font-family: "iconfont";
  font-size: 40rpx;
  color: #333;
}

.header {
  padding: 120rpx 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.following-list {
  padding-bottom: 30rpx;
}

.following-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.user-info {
  flex: 1;
  overflow: hidden;
}

.name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.username {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-right: 10rpx;
}

.gender-icon {
  font-size: 28rpx;
  color: #ff6b9a;
}

.gender-icon:last-child {
  color: #6b9aff;
}

.user-details {
  font-size: 24rpx;
  color: #999;
}

.unfollow-btn {
  min-width: 120rpx;
  height: 54rpx;
  border-radius: 27rpx;
  background-color: #f2f2f2;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.unfollow-btn text {
  color: #666;
  font-size: 24rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.find-experts-btn {
  width: 240rpx;
  height: 70rpx;
  border-radius: 35rpx;
  background-color: #ff8c00;
  display: flex;
  align-items: center;
  justify-content: center;
}

.find-experts-btn text {
  color: #fff;
  font-size: 28rpx;
}

.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-more text, .no-more text {
  font-size: 24rpx;
  color: #999;
}
</style> 