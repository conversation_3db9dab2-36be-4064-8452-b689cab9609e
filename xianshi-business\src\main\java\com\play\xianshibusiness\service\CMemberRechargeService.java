package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.recharge.*;
import com.play.xianshibusiness.enums.PayTypeEnum;
import com.play.xianshibusiness.enums.RechargeStatusEnum;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.CMemberRechargeMapper;
import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.pojo.CMemberRecharge;
import com.play.xianshibusiness.pojo.CRechargePackage;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 会员充值服务
 */
@Service
public class CMemberRechargeService {

    @Resource
    private CMemberRechargeMapper memberRechargeMapper;
    
    @Resource
    private CRechargePackageService rechargePackageService;
    
    @Resource
    private CMemberService memberService;
    
    @Resource
    private ISysMessageService sysMessageService;
    
    /**
     * 创建充值订单
     * @param dto 充值DTO
     * @param memberId 会员ID
     * @return 支付结果
     */
    @Transactional(rollbackFor = Exception.class)
    public PayResultDTO createRechargeOrder(RechargeDTO dto, String memberId) {
        // 1.获取会员信息
        CMember member = memberService.getMemberPo(memberId);
        
        // 2.计算充值金币数量
        BigDecimal goldAmount = dto.getAmount();
        String packageName = "直接充值";
        
        // 3.如果选择了套餐，则使用套餐的金币数量
        if (!StringUtils.isEmpty(dto.getPackageId())) {
            RechargePackageVO packageVO = rechargePackageService.getPackageDetail(dto.getPackageId());
            if (!packageVO.getAmount().equals(dto.getAmount())) {
                throw new GlobalException(400, "充值金额与套餐金额不匹配");
            }
            goldAmount = packageVO.getTotalGold();
            packageName = packageVO.getName();
        }
        
        // 4.生成订单号
        String orderNo = generateOrderNo();
        
        // 5.创建充值记录
        CMemberRecharge recharge = new CMemberRecharge();
        recharge.setId(UUID.randomUUID().toString());
        recharge.setMemberId(memberId);
        recharge.setOrderNo(orderNo);
        recharge.setAmount(dto.getAmount());
        recharge.setGoldAmount(goldAmount);
        recharge.setPayType(dto.getPayType());
        recharge.setStatus(RechargeStatusEnum.PENDING.getCode());
        recharge.setPackageId(dto.getPackageId());
        recharge.setRemark(dto.getRemark());
        recharge.setCreateTime(LocalDateTime.now());
        recharge.setUpdateTime(LocalDateTime.now());
        recharge.setDeleted(false);
        recharge.setAvailable(true);
        
        memberRechargeMapper.insert(recharge);
        
        // 6.调用支付接口（这里模拟，实际需要对接第三方支付）
        PayResultDTO payResult = callPayApi(dto.getPayType(), orderNo, dto.getAmount(), "闲时APP-" + packageName);
        
        return payResult;
    }
    
    /**
     * 处理支付回调
     * @param dto 支付回调DTO
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean handlePayCallback(PayCallbackDTO dto) {
        // 1.校验签名（实际项目中需要实现）
        verifySign(dto);
        
        // 2.查询充值记录
        CMemberRecharge recharge = memberRechargeMapper.selectOne(
                new LambdaQueryWrapper<CMemberRecharge>()
                        .eq(CMemberRecharge::getOrderNo, dto.getOrderNo())
                        .eq(CMemberRecharge::getDeleted, false)
        );
        
        if (recharge == null) {
            throw new GlobalException(400, "充值订单不存在");
        }
        
        // 3.校验订单状态
        if (!recharge.getStatus().equals(RechargeStatusEnum.PENDING.getCode())) {
            return true; // 订单已处理，直接返回成功
        }
        
        // 4.更新充值记录状态
        LocalDateTime payTime = LocalDateTime.now();
        if (!StringUtils.isEmpty(dto.getPayTime())) {
            try {
                payTime = LocalDateTime.parse(dto.getPayTime(), DateTimeFormatter.ISO_DATE_TIME);
            } catch (Exception e) {
                // 解析失败，使用当前时间
            }
        }
        
        recharge.setStatus(dto.getStatus());
        recharge.setTransactionId(dto.getTransactionId());
        recharge.setPayTime(payTime);
        recharge.setUpdateTime(LocalDateTime.now());
        
        memberRechargeMapper.updateById(recharge);
        
        // 5.如果支付成功，增加会员金币
        if (dto.getStatus().equals(RechargeStatusEnum.SUCCESS.getCode())) {
            memberService.increaseGold(
                    recharge.getMemberId(), 
                    recharge.getGoldAmount(), 
                    "充值金币，订单号：" + recharge.getOrderNo()
            );
            
            // 6.发送系统消息
            sysMessageService.sendMessage(
                    recharge.getMemberId(),
                    "您已成功充值" + recharge.getAmount() + "元，获得" + recharge.getGoldAmount() + "金币",
                    null
            );
        }
        
        return true;
    }
    
    /**
     * 获取会员充值记录
     * @param dto 查询条件
     * @param memberId 会员ID
     * @return 分页结果
     */
    public Page<RechargeRecordVO> pageRechargeRecords(RechargeQueryDTO dto, String memberId) {
        LambdaQueryWrapper<CMemberRecharge> queryWrapper = new LambdaQueryWrapper<CMemberRecharge>()
                .eq(CMemberRecharge::getMemberId, memberId)
                .eq(CMemberRecharge::getDeleted, false);
        
        if (!StringUtils.isEmpty(dto.getOrderNo())) {
            queryWrapper.eq(CMemberRecharge::getOrderNo, dto.getOrderNo());
        }
        
        if (!StringUtils.isEmpty(dto.getPayType())) {
            queryWrapper.eq(CMemberRecharge::getPayType, dto.getPayType());
        }
        
        if (!StringUtils.isEmpty(dto.getStatus())) {
            queryWrapper.eq(CMemberRecharge::getStatus, dto.getStatus());
        }
        
        if (dto.getStartTime() != null) {
            queryWrapper.ge(CMemberRecharge::getCreateTime, dto.getStartTime());
        }
        
        if (dto.getEndTime() != null) {
            queryWrapper.le(CMemberRecharge::getCreateTime, dto.getEndTime());
        }
        
        queryWrapper.orderByDesc(CMemberRecharge::getCreateTime);
        
        Page<CMemberRecharge> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<CMemberRecharge> rechargePage = memberRechargeMapper.selectPage(page, queryWrapper);
        
        Page<RechargeRecordVO> resultPage = new Page<>(rechargePage.getCurrent(), rechargePage.getSize(), rechargePage.getTotal());
        List<RechargeRecordVO> records = rechargePage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        resultPage.setRecords(records);
        
        return resultPage;
    }
    
    /**
     * 管理员查询充值记录
     * @param dto 查询条件
     * @return 分页结果
     */
    public Page<RechargeRecordVO> adminPageRechargeRecords(RechargeQueryDTO dto) {
        LambdaQueryWrapper<CMemberRecharge> queryWrapper = new LambdaQueryWrapper<CMemberRecharge>()
                .eq(CMemberRecharge::getDeleted, false);
        
        if (!StringUtils.isEmpty(dto.getOrderNo())) {
            queryWrapper.eq(CMemberRecharge::getOrderNo, dto.getOrderNo());
        }
        
        if (!StringUtils.isEmpty(dto.getPayType())) {
            queryWrapper.eq(CMemberRecharge::getPayType, dto.getPayType());
        }
        
        if (!StringUtils.isEmpty(dto.getStatus())) {
            queryWrapper.eq(CMemberRecharge::getStatus, dto.getStatus());
        }
        
        if (dto.getStartTime() != null) {
            queryWrapper.ge(CMemberRecharge::getCreateTime, dto.getStartTime());
        }
        
        if (dto.getEndTime() != null) {
            queryWrapper.le(CMemberRecharge::getCreateTime, dto.getEndTime());
        }
        
        queryWrapper.orderByDesc(CMemberRecharge::getCreateTime);
        
        Page<CMemberRecharge> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<CMemberRecharge> rechargePage = memberRechargeMapper.selectPage(page, queryWrapper);
        
        Page<RechargeRecordVO> resultPage = new Page<>(rechargePage.getCurrent(), rechargePage.getSize(), rechargePage.getTotal());
        List<RechargeRecordVO> records = rechargePage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        resultPage.setRecords(records);
        
        return resultPage;
    }
    
    /**
     * 查询充值记录详情
     * @param id 充值记录ID
     * @param memberId 会员ID
     * @return 充值记录详情
     */
    public RechargeRecordVO getRechargeDetail(String id, String memberId) {
        CMemberRecharge recharge = memberRechargeMapper.selectOne(
                new LambdaQueryWrapper<CMemberRecharge>()
                        .eq(CMemberRecharge::getId, id)
                        .eq(CMemberRecharge::getMemberId, memberId)
                        .eq(CMemberRecharge::getDeleted, false)
        );
        
        if (recharge == null) {
            throw new GlobalException(400, "充值记录不存在");
        }
        
        return convertToVO(recharge);
    }
    
    /**
     * 管理员查询充值记录详情
     * @param id 充值记录ID
     * @return 充值记录详情
     */
    public RechargeRecordVO adminGetRechargeDetail(String id) {
        CMemberRecharge recharge = memberRechargeMapper.selectById(id);
        if (recharge == null || recharge.getDeleted()) {
            throw new GlobalException(400, "充值记录不存在");
        }
        
        return convertToVO(recharge);
    }
    
    /**
     * 转换为VO
     * @param recharge 充值记录
     * @return 充值记录VO
     */
    private RechargeRecordVO convertToVO(CMemberRecharge recharge) {
        RechargeRecordVO vo = new RechargeRecordVO();
        BeanUtils.copyProperties(recharge, vo);
        
        // 设置支付方式描述
        try {
            PayTypeEnum payTypeEnum = PayTypeEnum.valueOf(recharge.getPayType());
            vo.setPayTypeDesc(payTypeEnum.getDesc());
        } catch (Exception e) {
            vo.setPayTypeDesc(recharge.getPayType());
        }
        
        // 设置充值状态描述
        try {
            RechargeStatusEnum statusEnum = RechargeStatusEnum.valueOf(recharge.getStatus());
            vo.setStatusDesc(statusEnum.getDesc());
        } catch (Exception e) {
            vo.setStatusDesc(recharge.getStatus());
        }
        
        // 设置套餐名称
        if (!StringUtils.isEmpty(recharge.getPackageId())) {
            try {
                RechargePackageVO packageVO = rechargePackageService.getPackageDetail(recharge.getPackageId());
                vo.setPackageName(packageVO.getName());
            } catch (Exception e) {
                vo.setPackageName("未知套餐");
            }
        }
        
        return vo;
    }
    
    /**
     * 生成订单号
     * @return 订单号
     */
    private String generateOrderNo() {
        // 生成格式：年月日时分秒+6位随机数
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = (int) ((Math.random() * 9 + 1) * 100000);
        return "CZ" + dateStr + random;
    }
    
    /**
     * 调用支付API
     * @param payType 支付方式
     * @param orderNo 订单号
     * @param amount 金额
     * @param subject 商品名称
     * @return 支付结果
     */
    private PayResultDTO callPayApi(String payType, String orderNo, BigDecimal amount, String subject) {
        // 实际项目中需要对接第三方支付API
        PayResultDTO result = new PayResultDTO();
        result.setOrderNo(orderNo);
        result.setPayType(payType);
        result.setStatus(RechargeStatusEnum.PENDING.getCode());
        
        // 模拟不同支付方式的返回结果
        switch (payType) {
            case "ALIPAY":
                result.setPayUrl("https://openapi.alipay.com/gateway.do?out_trade_no=" + orderNo);
                break;
            case "WECHAT":
                result.setQrCode("weixin://wxpay/bizpayurl?pr=" + orderNo);
                break;
            case "BANK_CARD":
                result.setPayUrl("https://payment.bank.com/pay?order=" + orderNo);
                break;
            default:
                throw new GlobalException(400, "不支持的支付方式");
        }
        
        return result;
    }
    
    /**
     * 校验签名
     * @param dto 支付回调DTO
     */
    private void verifySign(PayCallbackDTO dto) {
        // 实际项目中需要实现签名验证
    }
    
    /**
     * 管理员给会员充值
     * @param dto 充值信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean adminRecharge(AdminRechargeDTO dto) {
        // 1.获取会员信息
        CMember member = memberService.getMemberPo(dto.getMemberId());
        if (member == null) {
            throw new GlobalException(400, "会员不存在");
        }
        
        // 2.生成订单号
        String orderNo = generateOrderNo();
        
        // 3.创建充值记录
        CMemberRecharge recharge = new CMemberRecharge();
        recharge.setId(UUID.randomUUID().toString());
        recharge.setMemberId(dto.getMemberId());
        recharge.setOrderNo(orderNo);
        recharge.setAmount(new BigDecimal(dto.getAmount()));
        recharge.setGoldAmount(new BigDecimal(dto.getGoldAmount()));
        recharge.setPayType("ADMIN"); // 管理员充值
        recharge.setStatus(RechargeStatusEnum.SUCCESS.getCode()); // 直接设置为成功
        recharge.setRemark(dto.getRemark());
        recharge.setCreateTime(LocalDateTime.now());
        recharge.setUpdateTime(LocalDateTime.now());
        recharge.setPayTime(LocalDateTime.now());
        recharge.setDeleted(false);
        recharge.setAvailable(true);
        
        memberRechargeMapper.insert(recharge);
        
        // 4.增加会员金币
        memberService.increaseGold(
                dto.getMemberId(), 
                new BigDecimal(dto.getGoldAmount()), 
                "管理员充值金币，订单号：" + orderNo
        );
        
        // 5.发送系统消息
        sysMessageService.sendMessage(
                dto.getMemberId(),
                "管理员已为您充值" + dto.getAmount() + "元，获得" + dto.getGoldAmount() + "金币",
                null
        );
        
        return true;
    }
} 