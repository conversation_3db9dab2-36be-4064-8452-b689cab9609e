<template>
  <view class="container">
    <view class="content">
      <view class="faq-item" v-for="(item, index) in faqList" :key="index">
        <view class="question-box" @click="toggleItem(index)">
          <text class="question">{{ item.question }}</text>
          <text class="arrow" :class="{ 'arrow-down': item.isExpanded }">›</text>
        </view>
        <view class="answer-box" v-if="item.isExpanded">
          <text class="answer">{{ item.answer }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      faqList: [
        {
          question: "什么是闲时有伴APP?",
          answer: "闲时有伴APP是陪玩社交服务平台，达人可以在线建立自己的个人主页，展示自我，获得工作机会。有需求的用户可以通过闲时有伴APP找到适合的达人。",
          isExpanded: false
        },
        {
          question: "小程序和模卡失败怎么办?",
          answer: "如果您在使用小程序或模卡时遇到问题，可以尝试以下解决方案：\n1. 清除缓存后重新打开\n2. 检查网络连接\n3. 更新小程序到最新版本\n4. 联系客服获取帮助",
          isExpanded: false
        },
        {
          question: "什么是通告?",
          answer: "通告是用户发布的寻找陪玩服务的需求信息，包含服务类型、时长、价格等内容。达人用户可以浏览并报名参与通告，获得陪玩订单。",
          isExpanded: false
        },
        {
          question: "如何制作模卡?",
          answer: "制作模卡需要以下步骤：\n1. 进入个人主页，点击编辑资料\n2. 上传清晰的个人照片\n3. 填写个人介绍、特长和服务内容\n4. 添加相关标签\n5. 设置接单项目和价格\n6. 保存并发布",
          isExpanded: false
        }
      ]
    }
  },
  methods: {
    toggleItem(index) {
      // 切换当前项的展开状态
      this.$set(this.faqList[index], 'isExpanded', !this.faqList[index].isExpanded);
    }
  }
};
</script>

<style>
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}

.content {
  background-color: #ffffff;
  border-radius: 12rpx;
}

.faq-item {
  border-bottom: 1rpx solid #eeeeee;
}

.faq-item:last-child {
  border-bottom: none;
}

.question-box {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question {
  font-size: 28rpx;
  font-weight: 500;
  flex: 1;
}

.arrow {
  font-size: 32rpx;
  color: #999;
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.arrow-down {
  transform: rotate(-90deg);
}

.answer-box {
  padding: 0 30rpx 30rpx;
  background-color: #f9f9f9;
}

.answer {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
</style> 