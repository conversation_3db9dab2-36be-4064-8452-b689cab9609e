package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * (CRole)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("c_role")
@ApiModel("角色")
public class CRole extends BaseObjPo {

    //角色编码
    @ApiModelProperty(value = "角色编码")
    private String code;
    //角色描述
    @ApiModelProperty(value = "角色描述")
    private String name;
    //父类ID
    @ApiModelProperty(value = "父类ID")
    private String parentId;
    //所需金币
    @ApiModelProperty(value = "所需金币")
    private Double needGold;
    //备注
    @ApiModelProperty(value = "备注")
    private String remark;

}

