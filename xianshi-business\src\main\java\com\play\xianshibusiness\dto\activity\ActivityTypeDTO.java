package com.play.xianshibusiness.dto.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 活动类型DTO
 */
@Data
@ApiModel("活动类型DTO")
public class ActivityTypeDTO {

    private String id;

    @ApiModelProperty(value = "分类名称", required = true)
    @NotBlank(message = "分类名称不能为空")
    private String name;
    
    @ApiModelProperty(value = "编码", required = true)
    @NotBlank(message = "编码不能为空")
    private String code;
    
    @ApiModelProperty(value = "所需金币")
    @NotNull(message = "所需金币不能为空")
    @Min(value = 0, message = "所需金币必须大于等于0")
    private BigDecimal needGold;
    
    @ApiModelProperty(value = "父类ID")
    private String parentId;

    @ApiModelProperty(value = "排序")
    private Integer comparable;
} 