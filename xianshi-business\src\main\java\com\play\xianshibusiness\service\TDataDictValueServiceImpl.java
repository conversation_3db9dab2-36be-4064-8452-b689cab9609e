package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.dto.config.DataDictValueDTO;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.TDataDictTypeMapper;
import com.play.xianshibusiness.mapper.TDataDictValueMapper;
import com.play.xianshibusiness.pojo.TDataDictType;
import com.play.xianshibusiness.pojo.TDataDictValue;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据字典值服务实现类
 */
@Service
public class TDataDictValueServiceImpl implements TDataDictValueService {

    @Resource
    private TDataDictValueMapper dataDictValueMapper;
    
    @Resource
    private TDataDictTypeMapper dataDictTypeMapper;
    
    @Override
    public List<TDataDictValue> getDictValuesByTypeCode(String typeCode) {
        if (!StringUtils.hasText(typeCode)) {
            throw new GlobalException(400, "类型编码不能为空");
        }
        
        return dataDictValueMapper.selectList(
                new LambdaQueryWrapper<TDataDictValue>()
                        .eq(TDataDictValue::getTypeCode, typeCode)
                        .eq(TDataDictValue::getDeleted, false)
                        .eq(TDataDictValue::getAvailable, true)
                        .orderByAsc(TDataDictValue::getSort)
        );
    }
    
    @Override
    public TDataDictValue getDictValueDetail(String id) {
        if (!StringUtils.hasText(id)) {
            throw new GlobalException(400, "ID不能为空");
        }
        
        TDataDictValue dictValue = dataDictValueMapper.selectById(id);
        if (dictValue == null || dictValue.getDeleted() || !dictValue.getAvailable()) {
            throw new GlobalException(400, "数据字典值不存在");
        }
        
        return dictValue;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createDictValue(DataDictValueDTO valueDTO) {
        if (valueDTO == null) {
            throw new GlobalException(400, "参数不能为空");
        }
        
        // 检查类型是否存在
        TDataDictType dictType = dataDictTypeMapper.selectById(valueDTO.getTypeId());
        if (dictType == null || dictType.getDeleted() || !dictType.getAvailable()) {
            throw new GlobalException(400, "数据字典类型不存在");
        }
        
        // 检查值编码是否已存在
        if (checkValueCodeExists(dictType.getTypeCode(), valueDTO.getValueCode(), null)) {
            throw new GlobalException(400, "值编码已存在");
        }
        
        // 创建新值
        TDataDictValue dictValue = new TDataDictValue();
        BeanUtils.copyProperties(valueDTO, dictValue);
        dictValue.setTypeCode(dictType.getTypeCode());
        dictValue.setCreateTime(LocalDateTime.now());
        dictValue.setUpdateTime(LocalDateTime.now());
        dictValue.setDeleted(false);
        dictValue.setAvailable(true);
        
        dataDictValueMapper.insert(dictValue);
        
        return dictValue.getId();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDictValue(String id, DataDictValueDTO valueDTO) {
        if (!StringUtils.hasText(id) || valueDTO == null) {
            throw new GlobalException(400, "参数不能为空");
        }
        
        // 检查值是否存在
        TDataDictValue existValue = getDictValueDetail(id);
        
        // 检查类型是否存在
        TDataDictType dictType = dataDictTypeMapper.selectById(valueDTO.getTypeId());
        if (dictType == null || dictType.getDeleted() || !dictType.getAvailable()) {
            throw new GlobalException(400, "数据字典类型不存在");
        }
        
        // 检查值编码是否已存在（排除自身）
        if (checkValueCodeExists(dictType.getTypeCode(), valueDTO.getValueCode(), id)) {
            throw new GlobalException(400, "值编码已存在");
        }
        
        // 更新值
        BeanUtils.copyProperties(valueDTO, existValue);
        existValue.setTypeCode(dictType.getTypeCode());
        existValue.setUpdateTime(LocalDateTime.now());
        
        return dataDictValueMapper.updateById(existValue) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDictValue(String id) {
        if (!StringUtils.hasText(id)) {
            throw new GlobalException(400, "ID不能为空");
        }
        
        // 检查值是否存在
        TDataDictValue existValue = getDictValueDetail(id);
        
        // 逻辑删除
        existValue.setDeleted(true);
        existValue.setUpdateTime(LocalDateTime.now());
        
        return dataDictValueMapper.updateById(existValue) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateSort(Map<String, Integer> sortMap) {
        if (sortMap == null || sortMap.isEmpty()) {
            return true;
        }
        
        for (Map.Entry<String, Integer> entry : sortMap.entrySet()) {
            String id = entry.getKey();
            Integer sort = entry.getValue();
            
            TDataDictValue dictValue = dataDictValueMapper.selectById(id);
            if (dictValue != null && !dictValue.getDeleted() && dictValue.getAvailable()) {
                dictValue.setSort(sort);
                dictValue.setUpdateTime(LocalDateTime.now());
                dataDictValueMapper.updateById(dictValue);
            }
        }
        
        return true;
    }
    
    /**
     * 检查值编码是否已存在
     *
     * @param typeCode 类型编码
     * @param valueCode 值编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    private boolean checkValueCodeExists(String typeCode, String valueCode, String excludeId) {
        if (!StringUtils.hasText(typeCode) || !StringUtils.hasText(valueCode)) {
            return false;
        }
        
        LambdaQueryWrapper<TDataDictValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TDataDictValue::getTypeCode, typeCode);
        queryWrapper.eq(TDataDictValue::getValueCode, valueCode);
        queryWrapper.eq(TDataDictValue::getDeleted, false);
        
        if (StringUtils.hasText(excludeId)) {
            queryWrapper.ne(TDataDictValue::getId, excludeId);
        }
        
        return dataDictValueMapper.selectCount(queryWrapper) > 0;
    }
} 