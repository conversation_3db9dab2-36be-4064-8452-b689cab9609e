package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.play.xianshibusiness.dto.config.BasicConfigDTO;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.TBasicConfigMapper;
import com.play.xianshibusiness.pojo.TBasicConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 基础配置服务实现类
 */
@Service
public class TBasicConfigServiceImpl implements TBasicConfigService {

    @Resource
    private TBasicConfigMapper basicConfigMapper;
    
    @Override
    public List<TBasicConfig> getAllBasicConfig() {
        return basicConfigMapper.selectList(
                new LambdaQueryWrapper<TBasicConfig>()
                        .eq(TBasicConfig::getAvailable, true)
                        .orderByAsc(TBasicConfig::getConfigGroup)
                        .orderByAsc(TBasicConfig::getComparable));
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBasicConfig(BasicConfigDTO configDTO) {
        if (configDTO == null || configDTO.getConfigs() == null || configDTO.getConfigs().isEmpty()) {
            throw new GlobalException(400, "配置参数不能为空");
        }
        
        Map<String, String> configs = configDTO.getConfigs();
        
        for (Map.Entry<String, String> entry : configs.entrySet()) {
            String configKey = entry.getKey();
            String configValue = entry.getValue();
            
            TBasicConfig config = getConfigByKey(configKey);
            
            if (config != null) {
                // 更新配置
                config.setConfigValue(configValue);
                config.setUpdateTime(LocalDateTime.now());
                basicConfigMapper.updateById(config);
            } else {
                // 创建新配置
                config = new TBasicConfig();
                config.setConfigKey(configKey);
                config.setConfigValue(configValue);
                config.setConfigName(configKey); // 默认使用key作为名称
                config.setConfigGroup("default"); // 默认分组
                config.setAvailable(true);
                config.setComparable(0); // 默认排序
                config.setCreateTime(LocalDateTime.now());
                config.setUpdateTime(LocalDateTime.now());
                basicConfigMapper.insert(config);
            }
        }
        
        return true;
    }
    
    @Override
    public String getConfigValue(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            return null;
        }
        
        TBasicConfig config = getConfigByKey(configKey);
        return config != null ? config.getConfigValue() : null;
    }
    
    @Override
    public TBasicConfig getConfigByKey(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            return null;
        }
        
        return basicConfigMapper.selectOne(
                new LambdaQueryWrapper<TBasicConfig>()
                        .eq(TBasicConfig::getConfigKey, configKey)
                        .eq(TBasicConfig::getAvailable, true));
    }
    
    /**
     * 启用/禁用配置
     *
     * @param configKey 配置键
     * @param enabled 是否启用
     * @return 是否成功
     */
    public Boolean updateConfigStatus(String configKey, Boolean enabled) {
        if (!StringUtils.hasText(configKey)) {
            throw new GlobalException(400, "配置键不能为空");
        }
        
        TBasicConfig config = getConfigByKey(configKey);
        
        if (config == null) {
            throw new GlobalException(400, "配置不存在");
        }
        
        config.setAvailable(enabled);
        config.setUpdateTime(LocalDateTime.now());
        
        return basicConfigMapper.updateById(config) > 0;
    }
    
    /**
     * 删除配置
     *
     * @param configKey 配置键
     * @return 是否成功
     */
    public Boolean deleteConfig(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            throw new GlobalException(400, "配置键不能为空");
        }
        
        return basicConfigMapper.delete(
                new LambdaQueryWrapper<TBasicConfig>()
                        .eq(TBasicConfig::getConfigKey, configKey)) > 0;
    }
} 