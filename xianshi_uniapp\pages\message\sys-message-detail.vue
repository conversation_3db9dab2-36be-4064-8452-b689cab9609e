<template>
  <view class="container">
    <view v-if="!message" class="no-message">
      <text>消息不存在或已被删除</text>
    </view>
    <view v-else class="message-detail">
      <view class="message-title">{{ message.title }}</view>
      <view class="message-time">{{ message.startTime ? message.startTime.slice(0, 16).replace('T', ' ') : '' }}</view>
      <view class="message-desc">{{ message.context }}</view>
    </view>
  </view>
</template>

<script>
import { getSysMessageDetail } from '../../api/index.js';
export default {
  data() {
    return {
      message: null
    }
  },
  onLoad(options) {
    if (options.id) {
      this.loadMessage(options.id);
    }
  },
  methods: {
    async loadMessage(id) {
      try {
        const res = await getSysMessageDetail(id);
        if ((res.code === 200 || res.status === 0) && res.data && res.data.id) {
          this.message = res.data;
        } else {
          this.message = null;
        }
      } catch (e) {
        this.message = null;
        console.error('获取消息详情失败', e);
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 40rpx;
}
.nav-bar {
  height: 88rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  border-bottom: 1rpx solid #eee;
}
.message-detail {
  background: #fff;
  margin: 24rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.message-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
}
.message-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
}
.message-desc {
  font-size: 28rpx;
  color: #444;
  line-height: 1.7;
}
.no-message {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}
</style> 