package com.play.xianshibusiness.dto.recharge;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 管理员给会员充值DTO
 */
@Data
@ApiModel(value = "管理员充值DTO")
public class AdminRechargeDTO {

    @ApiModelProperty(value = "会员ID", required = true)
    @NotBlank(message = "会员ID不能为空")
    private String memberId;
    
    @ApiModelProperty(value = "充值金额（元）", required = true)
    @NotNull(message = "充值金额不能为空")
    @Min(value = 1, message = "充值金额最小为1元")
    private Integer amount;
    
    @ApiModelProperty(value = "充值金币数量", required = true)
    @NotNull(message = "充值金币数量不能为空")
    @Min(value = 1, message = "充值金币数量最小为1")
    private Integer goldAmount;
    
    @ApiModelProperty(value = "充值备注")
    private String remark;
} 