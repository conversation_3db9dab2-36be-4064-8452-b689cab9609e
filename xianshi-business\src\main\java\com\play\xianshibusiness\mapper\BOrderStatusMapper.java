package com.play.xianshibusiness.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.play.xianshibusiness.pojo.BOrderStatus;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * (BOrderStauts)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:14
 */
public interface BOrderStatusMapper extends BaseMapper<BOrderStatus> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BOrderStauts> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BOrderStatus> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BOrderStauts> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BOrderStatus> entities);

    /**
     * 根据订单ID查询子订单状态记录
     * @param orderId 订单ID
     * @return 子订单状态记录列表
     */
    @Select("SELECT s.* FROM b_order_status s " +
            "JOIN b_order_enroll_detail d ON s.bussiness_id = d.id " +
            "WHERE d.order_id = #{orderId} AND s.type = 1 AND s.deleted = 0 " +
            "ORDER BY s.create_time DESC")
    List<BOrderStatus> selectOrderDetailStatusByOrderId(@Param("orderId") String orderId);

}

