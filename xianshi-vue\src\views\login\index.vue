<template>
  <div class="login-wrapper">
    <!-- 左侧品牌宣传 -->
    <div class="login-left">
      <div class="brand-content">
        <!-- <img src="@/assets/images/logoin.png" alt="logo" class="brand-logo" /> -->
        <div class="brand-title">闲时有伴</div>
        <div class="brand-subtitle">让每一刻闲暇时光都充满温暖陪伴</div>
      </div>
    </div>
    <!-- 右侧登录表单 -->
    <div class="login-right">
      <div class="login-form-container">
        <div class="login-header">
          <div class="login-title">管理员登录</div>
          <div class="login-desc">欢迎回来，请登录您的账号</div>
        </div>
        <div class="login-form">
          <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on" label-position="left">
            <el-form-item prop="username" class="input-icon-item">
              <el-input
                ref="username"
                v-model="loginForm.username"
                placeholder="请输入用户名"
                name="username"
                type="text"
                tabindex="1"
                auto-complete="on"
              />
              <i class="el-icon-user input-icon-left"></i>
            </el-form-item>
            <el-form-item prop="password" class="input-icon-item">
              <el-input
                ref="password"
                v-model="loginForm.password"
                placeholder="请输入密码"
                show-password
                name="password"
                tabindex="2"
                auto-complete="on"
                @keyup.enter.native="handleLogin"
              />
              <i class="el-icon-lock input-icon-left"></i>
            </el-form-item>
            <el-form-item prop="verifyCode" class="verify-item input-icon-item">
              <div class="verify-label">
                <span>验证码</span>
              </div>
              <el-input
                ref="verifyCode"
                v-model="loginForm.verifyCode"
                placeholder="请输入验证码"
                name="verifyCode"
                type="text"
                tabindex="3"
                auto-complete="off"
                class="verify-input"
                @keyup.enter.native="handleLogin"
              >
                <template #prefix>
                  <i class="el-icon-key input-icon-left"></i>
                </template>
                <template #suffix>
                  <div class="verify-code-container" @click="refreshVerifyCode">
                    <img v-if="verifyCodeSrc" :src="verifyCodeSrc" alt="验证码" class="verify-code-img" />
                    <div v-else class="verify-code-loading">加载中...</div>
                  </div>
                </template>
              </el-input>
            </el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.remember">记住密码</el-checkbox>
              <a class="forgot-link" href="javascript:void(0)">忘记密码?</a>
            </div>
            <div class="login-btn-wrapper">
              <el-button :loading="loading" type="primary" class="login-button" @click.native.prevent="handleLogin">
                登录
              </el-button>
            </div>
          </el-form>
        </div>
        <div class="login-copyright">
          © 2024 闲时有伴. All rights reserved.
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate';
import { getVerifyCode } from '@/api/user';

export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('请输入正确的用户名'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('密码不能少于6位'));
      } else {
        callback();
      }
    };
    const validateVerifyCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error('验证码不能为空'));
      } else if (value.length !== 4) {
        callback(new Error('验证码长度为4位'));
      } else {
        callback();
      }
    };
    return {
      loginForm: {
        username: '',
        password: '',
        verifyCode: '',
        verifyKey: '',
        loginType: 'PASSWORD',
        remember: false
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        verifyCode: [{ required: true, trigger: 'blur', validator: validateVerifyCode }]
      },
      loading: false,
      redirect: undefined,
      verifyCodeSrc: ''
    };
  },
  mounted() {
    this.refreshVerifyCode();
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  methods: {
    refreshVerifyCode() {
      this.verifyCodeSrc = '';
      getVerifyCode().then(response => {
        const { data } = response;
        this.loginForm.verifyKey = data.verifyKey;
        // 直接使用后端返回的Base64数据
        this.verifyCodeSrc = 'data:image/png;base64,' + data.imageBase64;
      });
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          this.$store.dispatch('user/login', this.loginForm)
            .then(() => {
              this.$router.push({ path: this.redirect || '/' });
              this.loading = false;
            })
            .catch(() => {
              this.refreshVerifyCode();
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.login-wrapper {
  display: flex;
  min-height: 100vh;
  width: 100vw;
}
.login-left {
  flex: 1.2;
  background: url('~@/assets/images/logoin.png') center center no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.brand-content {
  text-align: center;
  color: #fff;
  width: 100%;
  max-width: 400px;
}
.brand-title {
  font-size: 38px;
  font-weight: bold;
  margin-bottom: 24px;
  letter-spacing: 2px;
  text-shadow: 0 2px 16px rgba(246,174,45,0.2);
}
.brand-subtitle {
  font-size: 18px;
  opacity: 0.95;
  line-height: 1.8;
}
.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}
.login-form-container {
  width: 380px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  padding: 48px 36px 24px 36px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.login-header {
  text-align: center;
  margin-bottom: 32px;
}
.login-title {
  font-size: 24px;
  font-weight: 700;
  color: #222;
  margin-bottom: 8px;
}
.login-desc {
  font-size: 15px;
  color: #888;
}
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
}
.forgot-link {
  color: #f6ae2d;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.2s;
}
.forgot-link:hover {
  color: #ffb300;
}
.login-copyright {
  text-align: center;
  color: #bbb;
  font-size: 13px;
  margin-top: 32px;
  letter-spacing: 1px;
}
.bg-video {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  object-fit: cover;
}
.login-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #303030 0%, #505050 100%);

  .login-form-container {
    width: 450px;
    padding: 40px;
    margin: 0 auto;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-25px) scale(1.02);
    }
    
    .login-title {
      text-align: center;
      font-size: 26px;
      color: #F8D010;
      margin-bottom: 40px;
      font-weight: 600;
      
      &:after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 4px;
        background-color: #F8D010;
        border-radius: 2px;
      }
    }
    
    .login-form {
      .svg-container {
        padding: 0;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff7e0;
        border-radius: 50%;
        margin-right: 10px;
        margin-left: 2px;
        box-shadow: 0 2px 8px rgba(246,174,45,0.08);
        i {
          font-size: 20px;
          color: #f6ae2d;
        }
      }
      
      .el-input {
        display: inline-block;
        width: calc(100% - 50px);
        vertical-align: middle;
        
        ::v-deep input {
          background: #f9fafb;
          border: 1.5px solid #f3e6c4;
          border-radius: 24px;
          padding: 0 18px;
          height: 48px;
          color: #222;
          font-size: 16px;
          box-shadow: 0 2px 8px rgba(246,174,45,0.04);
          transition: all 0.3s;
          
          &:focus {
            border-color: #f6ae2d;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(246,174,45,0.08);
          }
          
          &::placeholder {
            color: #d2b97c;
            font-size: 15px;
          }
        }
      }
      
      .password-item {
        position: relative;
        margin-bottom: 25px;
        
        .show-pwd {
          position: absolute;
          right: 18px;
          top: 12px;
          font-size: 18px;
          color: #d2b97c;
          cursor: pointer;
          z-index: 10;
          transition: color 0.2s;
          &:hover {
            color: #f6ae2d;
          }
        }
      }
      
      .verify-item {
        margin-bottom: 26px;
        
        .verify-label {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          
          i {
            font-size: 18px;
            color: #f6ae2d;
            margin-right: 5px;
          }
          
          span {
            font-size: 14px;
            color: #bfa14a;
          }
        }
        
        .verify-input-container {
          display: flex;
          align-items: center;
          
          .verify-input {
            width: calc(100% - 130px);
            margin-right: 10px;
          }
          
          .verify-code-container {
            width: 110px;
            height: 40px;
            border-radius: 16px;
            overflow: hidden;
            cursor: pointer;
            background: #f9fafb;
            border: 1.5px solid #f3e6c4;
            transition: box-shadow 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &:hover {
              box-shadow: 0 0 8px #ffe25944;
            }
            
            .verify-code-img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 16px;
            }
            
            .verify-code-loading {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              color: #d2b97c;
            }
          }
        }
      }
      
      .login-button {
        width: 100%;
        margin-top: 10px;
        padding: 0 15px;
        font-size: 18px;
        font-weight: 600;
        background: linear-gradient(90deg, #ffe259 0%, #f6ae2d 100%);
        border: none;
        border-radius: 24px;
        color: #222;
        height: 48px;
        box-shadow: 0 4px 16px rgba(246,174,45,0.10);
        letter-spacing: 2px;
        transition: all 0.2s;
        
        &:hover, &:focus {
          background: linear-gradient(90deg, #fff7e0 0%, #ffe259 100%);
          color: #bfa14a;
          box-shadow: 0 8px 24px rgba(246,174,45,0.18);
          transform: translateY(-2px) scale(1.01);
        }
      }
      
      .el-form-item {
        margin-bottom: 22px;
        
        &.is-error {
          ::v-deep .el-input__inner {
            border-color: #f56c6c;
          }
        }
      }
      .el-checkbox {
        --el-checkbox-checked-bg-color: #ffe259;
        --el-checkbox-checked-border-color: #f6ae2d;
        --el-checkbox-checked-icon-color: #f6ae2d;
        font-size: 14px;
        color: #bfa14a;
        margin-left: 2px;
      }
    }
  }
}

@media (max-width: 576px) {
  .login-container {
    .login-form-container {
      width: 90%;
      padding: 30px 20px;
      
      .login-title {
        font-size: 22px;
        margin-bottom: 30px;
      }
    }
  }
}
.input-icon-item {
  position: relative;
  .input-icon-left {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    color: #bfbfbf;
    z-index: 2;
    pointer-events: none;
  }
  // 只设置左侧内边距，保留右侧空间给 show-password 眼睛图标
  ::v-deep .el-input__inner {
    padding-left: 42px !important;
  }
  // 验证码输入框只加左内边距
  &.verify-item {
    ::v-deep .el-input__inner {
      padding-left: 42px !important;
      padding-right: 12px !important;
    }
  }
  .input-pwd-wrapper {
    position: relative;
    width: 100%;
    .input-icon-left {
      left: 18px;
    }
    .show-pwd.input-icon-right {
      right: 18px;
      cursor: pointer;
      z-index: 3;
      color: #bfbfbf;
      transition: color 0.2s;
      &:hover {
        color: #f6ae2d;
      }
    }
    ::v-deep .el-input__inner {
      padding-left: 42px !important;
      padding-right: 42px !important;
    }
  }
}
.login-btn-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 18px;
}
.login-button {
  width: 260px !important;
  max-width: 100%;
  min-width: 180px;
  display: block;
  margin: 0 auto;
  border-radius: 28px !important;
  font-size: 20px !important;
  font-weight: 700;
  height: 52px !important;
}
.show-pwd .el-icon-view.is-hide {
  position: relative;
}
.show-pwd .el-icon-view.is-hide::after {
  content: '';
  position: absolute;
  left: 2px;
  top: 10px;
  width: 22px;
  height: 3px;
  background: #bfbfbf;
  transform: rotate(-20deg);
  border-radius: 2px;
  z-index: 2;
  pointer-events: none;
}
.verify-code-container {
  display: flex;
  align-items: center;
  height: 48px; /* 与输入框高度一致 */
  min-width: 110px;
  max-width: 130px;
  margin-right: 0;
  border-radius: 8px; /* 与输入框圆角一致 */
  overflow: hidden;
  cursor: pointer;
  background: #f9fafb;
  border: 1px solid #f3e6c4;
  transition: box-shadow 0.3s;
  box-sizing: border-box;
  &:hover {
    box-shadow: 0 0 8px #ffe25944;
  }
  .verify-code-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    display: block;
  }
  .verify-code-loading {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: #d2b97c;
  }
}
.brand-logo {
  width: 120px;
  height: auto;
  margin-bottom: 32px;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
</style> 