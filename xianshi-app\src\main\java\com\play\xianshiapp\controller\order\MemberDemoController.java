//package com.play.xianshiapp.controller.order;
//
//import com.play.xianshibusiness.annotation.RequireLogin;
//import com.play.xianshibusiness.result.Result;
//import com.play.xianshibusiness.result.ResultUtils;
//import com.play.xianshibusiness.utils.PrincipalUtil;
//import io.swagger.annotations.Api;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 会员演示控制器
// * 用于展示RequireLogin注解的使用
// */
//@RestController
//@RequestMapping("/api/member")
//@Slf4j
//@Api(tags = "会员演示模块-API")
//public class MemberDemoController {
//
//    /**
//     * 公开接口 - 不需要登录验证
//     */
//    @GetMapping("/public")
//    public Result<String> publicApi() {
//        return ResultUtils.success("这是一个公开的API，无需登录");
//    }
//
//    /**
//     * 需要登录的接口
//     */
//    @GetMapping("/protected")
//    @RequireLogin
//    public Result<Map<String, String>> protectedApi() {
//        // 通过PrincipalUtil获取当前登录用户ID
//        String memberId = PrincipalUtil.getMemberId();
//
//        Map<String, String> data = new HashMap<>();
//        data.put("memberId", memberId);
//        data.put("message", "这是一个受保护的API，需要登录");
//
//        return ResultUtils.success(data);
//    }
//
//    /**
//     * 需要特定角色的接口
//     */
//    @GetMapping("/admin")
//    @RequireLogin(checkRole = true, roles = {"ADMIN", "SUPER_ADMIN"})
//    public Result<String> adminApi() {
//        String memberId = PrincipalUtil.getMemberId();
//        return ResultUtils.success("这是管理员API，当前用户ID: " + memberId);
//    }
//}