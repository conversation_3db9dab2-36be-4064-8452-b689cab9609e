package com.play.xianshibusiness.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.play.xianshibusiness.enums.ResultCode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * JWT认证入口点，处理身份验证异常
 */
@Component
@RequiredArgsConstructor
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, 
                          AuthenticationException authException) 
                          throws IOException, ServletException {
        // 设置响应状态码和内容类型
        response.setStatus(ResultCode.INVALID_TOKEN.getCode());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        
        // 构建错误响应

        // 写入响应
        response.getWriter().write(objectMapper.writeValueAsString(ResultCode.INVALID_TOKEN.getMsg()));
    }
} 