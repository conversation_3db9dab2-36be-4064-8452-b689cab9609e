// 引入订单状态模块
import { 
  OrderState, 
  StateTransitions, 
  StateDisplayText, 
  UserExpertStateMapping,
  getUserProgressValue,
  getExpertProgressValue
} from './order-state';
import { updateOrderEnrollDetailStatus, getOrderDetail } from '../api/index.js';

/**
 * 验证状态转换是否有效
 * @param {string} currentState 当前状态
 * @param {string} nextState 目标状态
 * @param {string} role 用户角色 'user'/'expert'
 * @param {number} orderId 订单ID
 * @returns {boolean} 是否可转换
 */
export const validateStateTransition = (currentState, nextState, role, orderId) => {
  // 检查基本的状态转换是否允许
  const allowedTransitions = StateTransitions[currentState] || [];
  if (!allowedTransitions.includes(nextState)) {
    console.error(`不允许从 ${currentState} 转换到 ${nextState}`);
    return false;
  }
  
  // 根据角色验证权限
  if (role === 'user') {
    // 用户只能操作用户侧状态
    if (![
      OrderState.PUBLISHED, 
      OrderState.EXPERT_SELECTED, 
      OrderState.ORDER_STARTED, 
      OrderState.COMPLETED, 
      OrderState.EVALUATED
    ].includes(nextState)) {
      console.error(`用户无权设置状态: ${nextState}`);
      return false;
    }
    
    // 特殊规则: 用户只能在达人确认到达后才能开始订单
    if (nextState === OrderState.ORDER_STARTED) {
      // 获取当前订单信息进行判断
      const app = getApp();
      const orders = app.globalData.publishedOrders || [];
      const order = orders.find(o => o.id === orderId || o.orderId === orderId);
      
      if (order && order.expertStatus !== OrderState.ARRIVAL_CONFIRMED) {
        console.error('达人尚未确认到达，用户不能开始订单');
        return false;
      }
    }
  } else if (role === 'expert') {
    // 达人只能操作达人侧状态
    if (![
      OrderState.APPLIED,
      OrderState.DEPARTURE_CONFIRMED,
      OrderState.ARRIVAL_CONFIRMED,
      OrderState.SERVICE_COMPLETED
    ].includes(nextState)) {
      console.error(`达人无权设置状态: ${nextState}`);
      return false;
    }
  }
  
  // 特殊规则: 达人服务完成后用户才能确认完成
  if (nextState === OrderState.COMPLETED) {
    // 这里需要获取当前订单信息进行判断
    const app = getApp();
    const orders = app.globalData.publishedOrders || [];
    const order = orders.find(o => o.id === orderId || o.orderId === orderId);
    
    if (order && order.expertStatus !== OrderState.SERVICE_COMPLETED) {
      console.error('达人尚未完成服务，用户不能结束订单');
      return false;
    }
  }
  
  return true;
}

/**
 * 执行状态转换并记录日志
 * @param {number} orderId 订单ID
 * @param {string} currentState 当前状态
 * @param {string} nextState 目标状态
 * @param {string} role 角色 'user'/'expert'
 * @param {string} remark 备注
 * @returns {Promise<boolean>} 转换结果
 */
export const changeOrderState = async (orderId, currentState, nextState, role, remark) => {
  if (!validateStateTransition(currentState, nextState, role, orderId)) {
    return false;
  }
  
  try {
    // 在实际项目中，这里应该调用API更新订单状态
    // 构建状态更新请求
    const timestamp = formatDate(new Date());
    const logEntry = {
      state: nextState,
      time: timestamp,
      role: role,
      remark: remark || StateDisplayText[nextState]
    };
    
    // 调用API更新订单状态
    try {
      const updateData = {
        id: orderId,
        status: nextState,
        role: role,
        remark: remark
      };
      await updateOrderEnrollDetailStatus(updateData);
      console.log('状态转换成功:', { orderId, currentState, nextState, role, remark });
    } catch (error) {
      console.error('状态转换失败:', error);
      uni.showToast({
        title: '状态更新失败',
        icon: 'none'
      });
      throw error;
    }
    return true;
  } catch (error) {
    console.error('状态更新失败:', error);
    return false;
  }
}

/**
 * 通知达人状态变更
 * @param {number} orderId 订单ID
 * @param {string} expertState 达人新状态
 */
export const notifyExpertStateChange = (orderId, expertState) => {
  // 调用API通知达人状态变更
  try {
    // 这里可以调用消息推送API或WebSocket通知
    console.log(`通知达人状态更新: 订单${orderId}, 新状态${expertState}`);
    // 实际项目中可以调用推送服务
    // await sendNotificationToExpert(expertId, { orderId, expertState });
  } catch (error) {
    console.error('通知达人失败:', error);
  }
}

/**
 * 格式化日期为字符串
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}`;
}

/**
 * 判断当前用户是否可以执行特定操作
 * @param {object} order 订单对象
 * @param {string} action 操作类型
 * @returns {boolean} 是否有权限
 */
export const canPerformAction = (order, action) => {
  const app = getApp();
  const currentUserRole = app.globalData.userRole; // 'normal' 或 'expert'
  
  // 检查用户是否是订单创建者
  const isOrderOwner = order.creatorId === app.globalData.userId;
  
  // 检查用户是否是报名达人
  const isAppliedExpert = order.appliedExperts && 
                         order.appliedExperts.some(expert => 
                           expert.id === app.globalData.userId);
  
  // 用户对应操作权限
  const userActions = {
    // 选择达人: 订单已发布且用户是订单创建者
    selectExpert: currentUserRole === 'normal' && 
                  isOrderOwner && 
                  order.status === OrderState.PUBLISHED,
    
    // 开始订单: 已选择达人且达人已确认到达，且用户是订单创建者
    startOrder: currentUserRole === 'normal' && 
                isOrderOwner && 
                order.status === OrderState.EXPERT_SELECTED &&
                order.expertStatus === OrderState.ARRIVAL_CONFIRMED,
    
    // 确认完成: 达人已完成服务且用户是订单创建者
    confirmComplete: currentUserRole === 'normal' && 
                     isOrderOwner && 
                     order.expertStatus === OrderState.SERVICE_COMPLETED,
    
    // 评价: 订单已完成且用户是订单创建者
    evaluate: currentUserRole === 'normal' && 
              isOrderOwner && 
              order.status === OrderState.COMPLETED
  };
  
  // 达人对应操作权限
  const expertActions = {
    // 报名: 订单已发布且达人未报名
    apply: currentUserRole === 'expert' && 
           order.status === OrderState.PUBLISHED && 
           !isAppliedExpert,
    
    // 确认出发: 达人已被选中
    confirmDeparture: currentUserRole === 'expert' && 
                      isAppliedExpert && 
                      order.expertStatus === OrderState.CHOSEN,
    
    // 确认到达: 达人已确认出发
    confirmArrival: currentUserRole === 'expert' && 
                    isAppliedExpert && 
                    order.expertStatus === OrderState.DEPARTURE_CONFIRMED,
    
    // 确认服务完成: 达人已确认到达且订单状态为已开始
    confirmServiceComplete: currentUserRole === 'expert' && 
                           isAppliedExpert && 
                           order.expertStatus === OrderState.ARRIVAL_CONFIRMED &&
                           order.status === OrderState.ORDER_STARTED
  };
  
  // 合并两种操作权限
  const allActions = {...userActions, ...expertActions};
  
  return allActions[action] || false;
}

/**
 * 实时状态同步设置
 */
export const setupRealTimeSync = () => {
  // 在实际项目中，这里应该实现WebSocket连接或轮询
  console.log('设置实时状态同步');
  
  // TODO: 实现WebSocket连接
  // const connectWebSocket = () => {
  //   console.log('连接WebSocket');
  //   // 实现真实的WebSocket连接逻辑
  // };
  // connectWebSocket();
}

/**
 * 检查服务器更新
 */
export const checkForUpdates = () => {
  // 请求服务器API获取最新的订单状态
  try {
    // 这里可以调用获取订单列表的API来检查更新
    // const updatedOrders = await getMyOrders();
    // 处理订单更新逻辑
    console.log('检查订单更新...');
  } catch (error) {
    console.error('检查订单更新失败:', error);
  }
}

/**
 * 更新订单状态（通过API）
 * @param {number} orderId 订单ID
 * @param {string} status 新状态
 * @param {object} logEntry 日志条目
 */
export const updateOrderStatus = async (orderId, status, logEntry) => {
  try {
    // 调用API更新订单状态
    try {
      const updateData = {
        id: orderId,
        status: status,
        logEntry: logEntry
      };
      const result = await updateOrderEnrollDetailStatus(updateData);
      console.log('订单状态更新成功:', { orderId, status, logEntry });
      return result;
    } catch (error) {
      console.error('订单状态更新失败:', error);
      uni.showToast({
        title: '状态更新失败',
        icon: 'none'
      });
      throw error;
    }
    return { success: true };
  } catch (error) {
    console.error('更新订单状态失败:', error);
    return { success: false, error };
  }
}