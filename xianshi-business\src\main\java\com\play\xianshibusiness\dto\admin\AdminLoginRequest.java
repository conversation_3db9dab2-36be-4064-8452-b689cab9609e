package com.play.xianshibusiness.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 后台登录请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "后台登录请求")
public class AdminLoginRequest implements Serializable {
    
    @NotBlank(message = "登录账号不能为空")
    @ApiModelProperty(value = "登录账号", required = true, example = "admin")
    private String username;
    
    @NotBlank(message = "登录密码不能为空")
    @ApiModelProperty(value = "登录密码", required = true, example = "123456")
    private String password;
    
    @ApiModelProperty(value = "验证码", required = false, example = "1234")
    private String verifyCode;
    
    @ApiModelProperty(value = "验证码键", required = false, example = "ab123456")
    private String verifyKey;
    
    @ApiModelProperty(value = "手机号", required = false, example = "13800138000")
    private String phone;
    
    @ApiModelProperty(value = "短信验证码", required = false, example = "123456")
    private String smsCode;
    
    @ApiModelProperty(value = "记住我", required = false, example = "false")
    private Boolean rememberMe = false;
    
    @ApiModelProperty(value = "登录方式", required = true, example = "PASSWORD")
    private String loginType; // PASSWORD, SMS, 可扩展其他登录方式
} 