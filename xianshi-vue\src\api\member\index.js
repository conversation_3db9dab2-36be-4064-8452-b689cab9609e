import request from '@/utils/request';

// 获取会员列表
export function getMemberList(params) {
  return request({
    url: '/member/list',
    method: 'get',
    params
  });
}

// 获取会员详情
export function getMemberDetail(memberId) {
  return request({
    url: `/member/${memberId}`,
    method: 'get'
  });
}

// 更新会员状态
export function updateMemberStatus(memberId, status) {
  return request({
    url: '/member/status',
    method: 'post',
    params: { memberId, status }
  });
}

// 获取达人申请列表
export function getTalentApplyList(status) {
  return request({
    url: '/member/talent/apply/list',
    method: 'get',
    params: { status }
  });
}

// 审核达人申请
export function auditTalentApply(memberId, result, comment) {
  return request({
    url: '/member/talent/apply/audit',
    method: 'post',
    params: { memberId, result, comment }
  });
}

// 获取会员订单列表
export function getMemberOrders(memberId, params) {
  return request({
    url: `/member/${memberId}/orders`,
    method: 'get',
    params
  });
}

// 获取会员金币记录
export function getMemberGoldRecords(memberId, params) {
  return request({
    url: `/member/${memberId}/gold/records`,
    method: 'get',
    params
  });
}

// 创建会员
export function createMember(data) {
  return request({
    url: '/member/create',
    method: 'post',
    data
  });
} 