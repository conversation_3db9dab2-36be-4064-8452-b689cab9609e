package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.play.xianshibusiness.enums.GoldOperationTypeEnum;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员金币记录实体类
 */
@Data
@TableName("c_member_gold_record")
public class CMemberGoldRecord extends BaseObjPo {

    /**
     * 会员ID
     */
    private String memberId;

    /**
     * 操作类型：1-充值，2-消费，3-系统调整-增加，4-系统调整-减少，5-任务奖励
     */
    @ApiModelProperty(value = "操作类型：1-充值，2-消费，3-系统调整-增加，4-系统调整-减少，5-任务奖励")
    @EnumValue
    private GoldOperationTypeEnum operationType;

    /**
     * 变更金币数量
     */
    @TableField("gold_value")
    private Integer amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 支付信息
     */
    private String payInfo;

    /**
     * 收支类型
     */
    private String type;

    /**
     * 操作人ID
     */
    @TableField("update_op")
    private String operatorId;

    /**
     * 创建操作人
     */
    @TableField("create_op")
    private String createOp;

    /**
     * 版本号
     */
    private String version;
}
