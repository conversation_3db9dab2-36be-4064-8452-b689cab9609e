package com.play.xianshibusiness.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据字典值实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_data_dict_value")
@ApiModel("数据字典值表")
public class TDataDictValue extends BaseObjPo {

    @ApiModelProperty("类型ID")
    private String typeId;
    
    @ApiModelProperty("类型编码")
    private String typeCode;
    
    @ApiModelProperty("值编码")
    private String valueCode;
    
    @ApiModelProperty("值名称")
    private String valueName;
    
    @ApiModelProperty("值描述")
    private String description;
    
    @ApiModelProperty("附加数据（JSON格式）")
    private String extraData;
    
    @ApiModelProperty("排序")
    private Integer sort;
}

