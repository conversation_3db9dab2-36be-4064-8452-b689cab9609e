package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.member.AdminCreateMemberDTO;
import com.play.xianshibusiness.dto.member.CMemberDto;
import com.play.xianshibusiness.dto.member.LoginRequest;
import com.play.xianshibusiness.dto.member.LoginResponse;
import com.play.xianshibusiness.dto.member.MemberUpdateDTO;
import com.play.xianshibusiness.dto.member.MemberSimpleVO;
import com.play.xianshibusiness.dto.member.TalentApplyDTO;
import com.play.xianshibusiness.dto.member.TalentQueryDTO;
import com.play.xianshibusiness.enums.MemberApplyStatus;
import com.play.xianshibusiness.enums.MemberRoleEnum;
import com.play.xianshibusiness.enums.MessageType;
import com.play.xianshibusiness.enums.ResultCode;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.CMemberGoldRecordMapper;
import com.play.xianshibusiness.mapper.CMemberMapper;
import com.play.xianshibusiness.mapper.CMemberRoleMapper;
import com.play.xianshibusiness.mapper.CTalentApplyMapper;
import com.play.xianshibusiness.pojo.CMember;
import com.play.xianshibusiness.pojo.CMemberGoldRecord;
import com.play.xianshibusiness.pojo.CMemberRole;
import com.play.xianshibusiness.pojo.CRole;
import com.play.xianshibusiness.pojo.CTalentApply;
import com.play.xianshibusiness.utils.JwtSignUtils;
import com.play.xianshibusiness.utils.PrincipalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import com.play.xianshibusiness.enums.GoldOperationTypeEnum;

/**
 * (CMember)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:15
 */
@Service
@Slf4j
public class CMemberService {

    // @Value("${app.member.defaultAvatar}")
    private String defaultAvatar = "/static/images/default-avatar.png";
    // @Value("${app.member.defaultRole}")
    private String defaultRole = MemberRoleEnum.USER.getCode();

    @Resource
    private CMemberMapper cMemberMapper;
    @Resource
    private CaptchaService captchaService;

    @Resource
    private CMemberFansAttentionService cMemberFansAttentionService;

    @Resource
    private ISysMessageService sysMessageService;

    @Resource
    private CMemberGoldRecordMapper memberGoldRecordMapper;

    @Resource
    private CMemberRoleMapper memberRoleMapper;

    @Resource
    private CMemberRoleService cMemberRoleService;

    @Resource
    private MemberViewHistoryService memberViewHistoryService;
    
    @Resource
    private CTalentApplyMapper cTalentApplyMapper;

    /**
     * 根据会员ID获取会员DTO对象
     *
     * @param memberId 会员ID
     * @return 包含会员信息的CMemberDto对象
     */
    @Deprecated
    public CMemberDto getMemberDto(String memberId) {
        return getMemberInfo(memberId, true, true);
    }

    /**
     * 获取会员信息
     * 
     * @param memberId    会员ID
     * @param withExtra   是否包含附加信息
     * @param withPrivacy 是否包含隐私信息
     * @return 会员信息DTO
     */
    public CMemberDto getMemberInfo(String memberId, Boolean withExtra, Boolean withPrivacy) {
        // 根据会员ID获取会员PO对象
        CMember memberPo = getMemberPo(memberId);
        CMemberDto cMemberDto = new CMemberDto();
        // 将会员PO对象的属性复制到CMemberDto对象中
        BeanUtils.copyProperties(memberPo, cMemberDto);

        // 填充附加信息（粉丝数、关注数等）
        if (withExtra != null && withExtra) {
            cMemberFansAttentionService.fullMemberFansAttention(cMemberDto);

            // 如果是在已登录状态下查看其他用户信息，检查是否已关注该用户
            String currentMemberId = PrincipalUtil.getMemberIdOrNull();
            if (currentMemberId != null && !currentMemberId.equals(memberId)) {
                boolean isFollowing = cMemberFansAttentionService.isFollowing(currentMemberId, memberId);
                cMemberDto.setFollowing(isFollowing);
            }

            // 获取会员拥有的角色
            List<CRole> roles = cMemberRoleService.getMemberRoles(memberId);
            cMemberDto.setRoles(roles);
            
            // 设置当前角色信息
            if (memberPo.getCurrentRoleId() != null) {
                CRole currentRole = roles.stream()
                    .filter(role -> role.getCode().equals(memberPo.getCurrentRoleId()))
                    .findFirst()
                    .orElse(null);
                cMemberDto.setCurrentRole(currentRole);
                
                // 设置角色类型和名称
                Integer roleType = Integer.parseInt(memberPo.getCurrentRoleId());
                cMemberDto.setRoleType(roleType);
                
                switch (roleType) {
                    case 1:
                        cMemberDto.setCurrentRoleName("普通用户");
                        break;
                    case 2:
                        cMemberDto.setCurrentRoleName("闲时达人");
                        cMemberDto.setIsTalent(true);
                        break;
                    case 3:
                        cMemberDto.setCurrentRoleName("闲时公会");
                        break;
                    default:
                        cMemberDto.setCurrentRoleName("游客用户");
                }
            }
            
            // 检查是否拥有达人和公会角色权限
            boolean hasExpertRole = roles.stream().anyMatch(role -> "2".equals(role.getCode()));
            boolean hasGuildRole = roles.stream().anyMatch(role -> "3".equals(role.getCode()));
            cMemberDto.setHasExpertRole(hasExpertRole);
            cMemberDto.setHasGuildRole(hasGuildRole);
            
            // 设置是否为达人（当前角色是达人或拥有达人权限）
            cMemberDto.setIsTalent("2".equals(memberPo.getCurrentRoleId()) || hasExpertRole);

            // 统计浏览量（被其他用户浏览的次数）
            Integer viewCount = memberViewHistoryService.getMemberViewCount(memberId);
            cMemberDto.setViewCount(viewCount);
            
            // 统计用户浏览其他人的次数
            Integer myViewCount = memberViewHistoryService.getMemberMyViewCount(memberId);
            cMemberDto.setMyViewCount(myViewCount);
        }

        // 设置认证状态
        cMemberDto.setIsVerified(memberPo.getRealNameStatus() != null && memberPo.getRealNameStatus() == 1);
        
        // 设置金币余额
        cMemberDto.setGoldBalance(memberPo.getGold());
        
        // 处理隐私信息，如果不返回隐私则清空
        if (withPrivacy == null || !withPrivacy) {
            cMemberDto.setTel(null);
            cMemberDto.setIdCardNumber(null);
            cMemberDto.setWxCode(null);
            cMemberDto.setWxOpenId(null);
            cMemberDto.setRealName(null);
        }

        return cMemberDto;
    }

    /**
     * 会员登录
     * 
     * @param loginRequest
     * @return
     */
    @Transactional
    public LoginResponse memberLogin(LoginRequest loginRequest) {
        // 置前校验
        this.verifyLoginByMethod(loginRequest);
        // 根据手机查询账号
        Optional<CMember> cMemberOptional = new LambdaQueryChainWrapper<>(cMemberMapper)
                .eq(CMember::getTel, loginRequest.getPhone()).eq(CMember::getDeleted, false)
                .oneOpt();
        AtomicReference<CMember> cMemberAtomicReference = new AtomicReference<>();
        // 判断账号是否存在 不存在则注册 存在则登录
        if (!cMemberOptional.isPresent()) {
            cMemberAtomicReference.set(this.registerMember(loginRequest.getPhone()));
        } else {
            CMember cMember = cMemberOptional.get();
            cMemberAtomicReference.set(cMember);
            // 修改上次登录时间
            new LambdaUpdateChainWrapper<>(cMemberMapper)
                    .eq(CMember::getId, cMember.getId())
                    .set(CMember::getLastLoginTime, LocalDateTime.now())
                    .update();
            log.info("用户:{},登录成功", cMember.getNickname() + cMember.getId());
        }
        CMember cMember = cMemberAtomicReference.get();
        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setToken(JwtSignUtils.createJWtTokenByMemberInfo(cMember));
        loginResponse.setAvatar(cMember.getAvatar());
        loginResponse.setNickname(cMember.getNickname());
        loginResponse.setApplyStatus(cMember.getApplyStatus());
        return loginResponse;
    }

    /**
     * 根据登录方式验证登录请求
     *
     * @param loginRequest 包含登录信息的请求对象
     * @throws GlobalException 如果登录方式错误或验证失败，则抛出全局异常
     */
    private void verifyLoginByMethod(LoginRequest loginRequest) {
        // 根据登录请求中的方法进行不同的验证处理
        switch (loginRequest.getMethod()) {
            case PHONE_CODE:
                // 验证手机号和验证码
                captchaService.verifyPhoneCode(loginRequest.getPhone(), loginRequest.getCode());
                break;
            case CURRENT_PHONE:
                // 本机号码一键登录，验证token有效性
                verifyLocalPhoneToken(loginRequest.getPhone(), loginRequest.getCode());
                break;
            default:
                // 如果登录方式无效，抛出全局异常
                throw new GlobalException(ResultCode.InValid_Param, "登录方式错误");
        }
    }

    /**
     * 验证本机号码一键登录的token
     * 
     * @param phone 手机号码
     * @param token 运营商返回的token
     */
    private void verifyLocalPhoneToken(String phone, String token) {
        // 在实际应用中，这里需要调用运营商提供的API验证token有效性
        // 由于我们是模拟，所以简单校验token长度
        if (StringUtils.isEmpty(token) || token.length() < 8) {
            throw new GlobalException(ResultCode.InValid_Param, "无效的本机号码认证");
        }

        log.info("本机号码一键登录验证成功，手机号：{}", phone);
    }

    private CMember registerMember(String phone) {
        CMember cMember = new CMember();
        cMember.setId(UUID.randomUUID().toString());
        cMember.setAvatar(defaultAvatar);
        cMember.setSysId(getMemberSysNumber());
        cMember.setNickname(fetchMemberNickname());
        cMember.setApplyStatus(MemberApplyStatus.NOT_APPLIED.getCode());
        // 新用户注册时赠送1000金币
        cMember.setGold(BigDecimal.valueOf(1000));
        cMember.setCurrentRoleId(defaultRole);
        cMember.setTel(phone);
        // 初始化实名认证状态为未认证
        cMember.setRealNameStatus(0);
        cMember.setAvailable(true);
        cMember.setDeleted(false);
        cMember.setCreateTime(LocalDateTime.now());
        cMember.setUpdateTime(LocalDateTime.now());
        cMemberMapper.insert(cMember);

        // 添加默认角色关联
        CMemberRole memberRole = new CMemberRole();
        memberRole.setMemberId(cMember.getId());
        memberRole.setRoleId(defaultRole);
        memberRoleMapper.insert(memberRole);

        // 记录新用户赠送金币
        CMemberGoldRecord record = new CMemberGoldRecord();
        record.setId(UUID.randomUUID().toString());
        record.setMemberId(cMember.getId());
        record.setOperationType(GoldOperationTypeEnum.WELCOME_GIFT);
        record.setAmount(1000);
        record.setRemark("新用户注册赠送");
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setDeleted(false);
        record.setAvailable(true);
        memberGoldRecordMapper.insert(record);

        log.info("新用户注册成功：{}", cMember.getNickname() + cMember.getId());
        return cMember;
    }

    /**
     * 获取会员系统编号
     * 
     * @return 会员系统编号
     */
    private String getMemberSysNumber() {
        return "XS：" + new LambdaQueryChainWrapper<>(cMemberMapper).eq(CMember::getDeleted, false).count();
    }

    /**
     * 获取会员昵称
     * 
     * @return
     */
    private String fetchMemberNickname() {
        return "闲时会员" + (int) (Math.random() * 1000);
    }

    /**
     * 根据会员ID获取会员实体
     * 
     * @param memberId 会员ID
     * @return 会员实体
     */
    public CMember getMemberPo(String memberId) {
        CMember cMember = new LambdaQueryChainWrapper<>(cMemberMapper).eq(CMember::getId, memberId)
                .eq(CMember::getDeleted, false)
                .oneOpt().orElseThrow(() -> new GlobalException(ResultCode.InValid_Param, "用户不存在"));
        if (!cMember.getAvailable()) {
            throw new GlobalException(ResultCode.Member_Closed);
        }
        return cMember;
    }

    /**
     * 扣减会员金币
     * 
     * @param memberId 会员ID
     * @param amount   金币数量
     * @param remark   备注
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean decreaseGold(String memberId, BigDecimal amount, String remark) {
        // 1.获取会员信息
        CMember member = getMemberPo(memberId);

        // 2.校验金币余额
        if (member.getGold() == null || member.getGold().compareTo(amount) < 0) {
            throw new GlobalException(400, "金币余额不足");
        }

        // 3.扣减金币
        member.setGold(member.getGold().subtract(amount));
        member.setUpdateTime(LocalDateTime.now());
        cMemberMapper.updateById(member);

        // 4.记录金币变动
        CMemberGoldRecord record = new CMemberGoldRecord();
        record.setId(UUID.randomUUID().toString());
        record.setMemberId(memberId);
        record.setOperationType(GoldOperationTypeEnum.CONSUMPTION);
        record.setAmount(amount.intValue()); // 设置金额
        record.setRemark(remark);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setDeleted(false);
        record.setAvailable(true);

        memberGoldRecordMapper.insert(record);

        return true;
    }
    
    /**
     * 实名认证（模拟）
     * 
     * @param realName 真实姓名
     * @param idCardNumber 身份证号码
     * @param phone 手机号码
     * @param memberId 会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean verifyRealName(String realName, String idCardNumber, String phone, String memberId) {
        // 1.获取会员信息
        CMember member = getMemberPo(memberId);
        
        // 2.检查是否已经实名认证
        if (member.getRealNameStatus() != null && member.getRealNameStatus() == 1) {
            throw new GlobalException(ResultCode.InValid_Param, "您已经完成实名认证，无需重复认证");
        }
        
        // 3.验证输入参数
        if (StringUtils.isEmpty(realName)) {
            throw new GlobalException(ResultCode.InValid_Param, "请输入真实姓名");
        }
        if (StringUtils.isEmpty(idCardNumber)) {
            throw new GlobalException(ResultCode.InValid_Param, "请输入身份证号码");
        }
        if (StringUtils.isEmpty(phone)) {
            throw new GlobalException(ResultCode.InValid_Param, "请输入手机号码");
        }
        
        // 4.简单的身份证号码格式验证
        if (!isValidIdCard(idCardNumber)) {
            throw new GlobalException(ResultCode.InValid_Param, "身份证号码格式不正确");
        }
        
        // 5.验证手机号码是否与当前用户一致
        if (!phone.equals(member.getTel())) {
            throw new GlobalException(ResultCode.InValid_Param, "手机号码与当前账户不一致");
        }
        
        // 6.模拟第三方实名认证（这里直接返回成功）
        // 在真实环境中，这里应该调用第三方实名认证API
        boolean verifyResult = simulateThirdPartyVerify(realName, idCardNumber, phone);
        
        if (!verifyResult) {
            throw new GlobalException(ResultCode.InValid_Param, "实名认证失败，请检查信息是否正确");
        }
        
        // 7.更新会员实名认证信息
        member.setRealName(realName);
        member.setIdCardNumber(encryptIdCard(idCardNumber)); // 加密存储身份证号
        member.setRealNameStatus(1); // 设置为已认证
        member.setRealNameVerifyTime(LocalDateTime.now());
        member.setUpdateTime(LocalDateTime.now());
        
        cMemberMapper.updateById(member);
        
        // 8.发送系统消息
        sysMessageService.sendMessage(memberId, "恭喜您，实名认证已完成！现在可以申请达人认证了。", MessageType.ORDER_AUDIT);
        
        return true;
    }
    
    /**
     * 简单的身份证号码格式验证
     */
    private boolean isValidIdCard(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return false;
        }
        // 18位身份证号码正则表达式
        String regex = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        return idCard.matches(regex);
    }
    
    /**
     * 模拟第三方实名认证
     */
    private boolean simulateThirdPartyVerify(String realName, String idCardNumber, String phone) {
        // 模拟认证过程，这里直接返回true
        // 在真实环境中，这里应该调用第三方实名认证API
        try {
            Thread.sleep(1000); // 模拟网络请求延迟
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return true;
    }
    
    /**
     * 加密身份证号码（简单处理，只显示前4位和后4位）
     */
    private String encryptIdCard(String idCard) {
        if (StringUtils.isEmpty(idCard) || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }

    /**
     * 增加会员金币
     * 
     * @param memberId 会员ID
     * @param amount   金币数量
     * @param remark   备注
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean increaseGold(String memberId, BigDecimal amount, String remark) {
        // 1.获取会员信息
        CMember member = getMemberPo(memberId);

        // 2.增加金币
        if (member.getGold() == null) {
            member.setGold(amount);
        } else {
            member.setGold(member.getGold().add(amount));
        }
        member.setUpdateTime(LocalDateTime.now());
        cMemberMapper.updateById(member);

        // 3.记录金币变动
        CMemberGoldRecord record = new CMemberGoldRecord();
        record.setId(UUID.randomUUID().toString());
        record.setMemberId(memberId);
        record.setOperationType(GoldOperationTypeEnum.SYSTEM_ADD);
        record.setAmount(amount.intValue()); // 设置金额
        record.setRemark(remark);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setDeleted(false);
        record.setAvailable(true);

        memberGoldRecordMapper.insert(record);

        return true;
    }

    /**
     * 更新会员资料
     * 
     * @param dto      会员资料更新DTO
     * @param memberId 会员ID
     * @return 更新后的会员DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public CMemberDto updateMemberProfile(MemberUpdateDTO dto, String memberId) {
        // 1.获取会员信息
        CMember member = getMemberPo(memberId);

        // 2.更新会员资料
        if (!StringUtils.isEmpty(dto.getNickname())) {
            member.setNickname(dto.getNickname());
        }
        if (!StringUtils.isEmpty(dto.getAvatar())) {
            member.setAvatar(dto.getAvatar());
        }
        if (dto.getGender() != null) {
            member.setGender(dto.getGender());
        }
        if (dto.getAge() != null) {
            member.setAge(dto.getAge());
        }
        if (!StringUtils.isEmpty(dto.getConstellation())) {
            member.setConstellation(dto.getConstellation());
        }
        if (!StringUtils.isEmpty(dto.getHeight())) {
            member.setHeight(dto.getHeight());
        }
        if (!StringUtils.isEmpty(dto.getWeight())) {
            member.setWeight(dto.getWeight());
        }
        if (!StringUtils.isEmpty(dto.getContext())) {
            member.setContext(dto.getContext());
        }
        if (!StringUtils.isEmpty(dto.getVideo())) {
            member.setVideo(dto.getVideo());
        }
        if (!StringUtils.isEmpty(dto.getImages())) {
            member.setImages(dto.getImages());
        }
        if (!StringUtils.isEmpty(dto.getIdentify())) {
            member.setIdentify(dto.getIdentify());
        }
        if (!StringUtils.isEmpty(dto.getStyleIds())) {
            member.setStyleIds(dto.getStyleIds());
        }

        member.setUpdateTime(LocalDateTime.now());
        cMemberMapper.updateById(member);

        // 3.返回更新后的会员DTO
        return getMemberDto(memberId);
    }

    /**
     * 切换角色
     * 
     * @param roleId   角色ID
     * @param memberId 会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean switchRole(String roleId, String memberId) {
        // 1.获取会员信息
        CMember member = getMemberPo(memberId);

        // 2.验证是否可以切换到达人角色
        if (MemberRoleEnum.TALENT.getCode().equals(roleId)) {
            if (!MemberApplyStatus.APPROVED.getCode().equals(member.getApplyStatus())) {
                throw new GlobalException(ResultCode.InValid_Param, "您还未通过达人认证，无法切换到达人角色");
            }
        } else if (!MemberRoleEnum.USER.getCode().equals(roleId)) {
            throw new GlobalException(ResultCode.InValid_Param, "无效的角色ID");
        }

        // 3.更新角色
        member.setCurrentRoleId(roleId);
        member.setUpdateTime(LocalDateTime.now());
        cMemberMapper.updateById(member);

        return true;
    }

    /**
     * 申请达人认证
     * 
     * @param dto      申请DTO
     * @param memberId 会员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean applyForTalent(TalentApplyDTO dto, String memberId) {
        // 1.获取会员信息
        CMember member = getMemberPo(memberId);

        // 2.检查是否已经是达人
        if (MemberApplyStatus.APPROVED.getCode().equals(member.getApplyStatus())) {
            throw new GlobalException(ResultCode.InValid_Param, "您已经是达人，无需再次申请");
        }
        
        // 检查是否有待审核的申请
        if (MemberApplyStatus.PENDING.getCode().equals(member.getApplyStatus())) {
            throw new GlobalException(ResultCode.InValid_Param, "您的申请正在审核中，请耐心等待");
        }

        // 3.检查资料完整度
        int completionRate = calculateProfileCompletion(member);
        if (completionRate < 50) {
            throw new GlobalException(ResultCode.InValid_Param, "个人资料完善度不足50%，请先完善个人资料");
        }

        // 4.检查实名认证
        if (member.getRealNameStatus() == null || member.getRealNameStatus() != 1) {
            throw new GlobalException(ResultCode.InValid_Param, "请先完成实名认证");
        }

        // 5.验证申请数据
        if (StringUtils.isEmpty(dto.getIntroduction())) {
            throw new GlobalException(ResultCode.InValid_Param, "请填写自我介绍");
        }
        if (StringUtils.isEmpty(dto.getSkills())) {
            throw new GlobalException(ResultCode.InValid_Param, "请填写技能特长");
        }

        // 6.保存申请详细信息到申请表
        CTalentApply talentApply = new CTalentApply();
        talentApply.setId(UUID.randomUUID().toString());
        talentApply.setMemberId(memberId);
        talentApply.setStatus(0); // 0-待审核

        talentApply.setIntroduction(dto.getIntroduction());
        talentApply.setSkills(dto.getSkills());
        // 将照片数组转换为逗号分隔的字符串
        if (dto.getPhotos() != null && dto.getPhotos().length > 0) {
            talentApply.setPhotos(String.join(",", dto.getPhotos()));
        }
        talentApply.setVideo(dto.getVideo());
        talentApply.setCreateTime(LocalDateTime.now());
        talentApply.setUpdateTime(LocalDateTime.now());
        talentApply.setDeleted(false);
        talentApply.setAvailable(true);
        cTalentApplyMapper.insert(talentApply);

        // 7.更新会员申请状态
        member.setApplyStatus(MemberApplyStatus.PENDING.getCode());
        member.setUpdateTime(LocalDateTime.now());
        cMemberMapper.updateById(member);

        // 8.发送系统消息
        sysMessageService.sendMessage(memberId, "您的达人认证申请已提交，请等待审核", MessageType.ORDER_AUDIT);

        return true;
    }

    /**
     * 审核达人申请
     * 
     * @param memberId 会员ID
     * @param passed   是否通过
     * @param reason   拒绝原因（如果不通过）
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewTalentApplication(String memberId, boolean passed, String reason) {
        // 1.获取会员信息
        CMember member = getMemberPo(memberId);

        // 2.检查当前状态
        if (!MemberApplyStatus.PENDING.getCode().equals(member.getApplyStatus())) {
            throw new GlobalException(ResultCode.InValid_Param, "该会员未处于待审核状态");
        }

        // 3.更新申请状态
        member.setApplyStatus(passed ? MemberApplyStatus.APPROVED.getCode() : MemberApplyStatus.REJECTED.getCode());
        member.setUpdateTime(LocalDateTime.now());
        cMemberMapper.updateById(member);

        // 4.发送系统消息
        String message = passed ? "恭喜您，您的达人认证已通过审核，现在可以切换到达人角色"
                : "很遗憾，您的达人认证未通过审核，原因：" + (reason != null ? reason : "未满足达人条件");

        sysMessageService.sendMessage(memberId, message, MessageType.ORDER_AUDIT);

        return true;
    }

    /**
     * 计算资料完整度
     * 
     * @param member 会员信息
     * @return 完整度百分比（0-100）
     */
    private int calculateProfileCompletion(CMember member) {
        int totalFields = 8; // 总字段数（与前端保持一致）
        int completedFields = 0;

        if (!StringUtils.isEmpty(member.getNickname()))
            completedFields++;
        if (!StringUtils.isEmpty(member.getAvatar()))
            completedFields++;
        if (member.getGender() != null)
            completedFields++;
        if (member.getAge() != null)
            completedFields++;
        if (!StringUtils.isEmpty(member.getHeight()))
            completedFields++;
        if (!StringUtils.isEmpty(member.getWeight()))
            completedFields++;
        if (!StringUtils.isEmpty(member.getContext()))
            completedFields++;
        if (member.getRealNameStatus() != null && member.getRealNameStatus() == 1)
            completedFields++;

        return (completedFields * 100) / totalFields;
    }

    /**
     * 根据会员ID计算资料完整度
     * 
     * @param memberId 会员ID
     * @return 完整度百分比（0-100）
     */
    public int calculateProfileCompletion(String memberId) {
        CMember member = getMemberPo(memberId);
        return calculateProfileCompletion(member);
    }

    public Page<CMemberDto> getTalentApplicationsByStatus(String status, Integer pageNum, Integer pageSize,
            String excludeUserId) {
        // 创建分页对象
        Page<CMember> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<CMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMember::getApplyStatus, status)
                .eq(CMember::getDeleted, false)
                .orderByDesc(CMember::getUpdateTime);

        // 如果需要排除指定用户，添加排除条件
        if (excludeUserId != null && !excludeUserId.trim().isEmpty()) {
            queryWrapper.ne(CMember::getId, excludeUserId);
        }

        // 执行查询
        Page<CMember> memberPage = cMemberMapper.selectPage(page, queryWrapper);

        // 转换为DTO
        Page<CMemberDto> dtoPage = new Page<>();
        BeanUtils.copyProperties(memberPage, dtoPage, "records");

        List<CMemberDto> dtoList = memberPage.getRecords().stream().map(member -> {
            CMemberDto dto = new CMemberDto();
            BeanUtils.copyProperties(member, dto);
            // 填充粉丝关注信息
            cMemberFansAttentionService.fullMemberFansAttention(dto);
            return dto;
        }).collect(Collectors.toList());

        dtoPage.setRecords(dtoList);

        return dtoPage;
    }

    /**
     * 根据审核状态获取达人申请列表（不排除任何用户）
     *
     * @param status   审核状态
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 达人申请分页列表
     */
    public Page<CMemberDto> getTalentApplicationsByStatus(String status, Integer pageNum, Integer pageSize) {
        // 调用4参数版本，excludeUserId参数传null表示不排除任何用户
        return getTalentApplicationsByStatus(status, pageNum, pageSize, null);
    }

    /**
     * 获取达人列表（拥有达人角色的用户）
     * 
     * @param pageNum         页码
     * @param pageSize        每页大小
     * @param excludeMemberId 排除的用户ID
     * @return 分页结果
     */
    public Page<CMemberDto> getTalentList(Integer pageNum, Integer pageSize, String excludeMemberId) {
        // 创建分页对象
        Page<CMember> page = new Page<>(pageNum, pageSize);

         // 构建查询条件：查询拥有达人角色的用户
        LambdaQueryWrapper<CMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CMember::getDeleted, false)
                .eq(CMember::getAvailable, true)
                .exists("SELECT 2 FROM c_member_role cmr WHERE cmr.member_id = c_member.id AND cmr.role_id = '" + MemberRoleEnum.TALENT.getCode() + "'")
                .orderByDesc(CMember::getUpdateTime);

        // 如果需要排除指定用户，添加排除条件
        if (excludeMemberId != null && !excludeMemberId.trim().isEmpty()) {
            queryWrapper.ne(CMember::getId, excludeMemberId);
        }

        // 执行查询
        Page<CMember> memberPage = cMemberMapper.selectPage(page, queryWrapper);

        // 转换为DTO
        Page<CMemberDto> dtoPage = new Page<>();
        BeanUtils.copyProperties(memberPage, dtoPage, "records");

        List<CMemberDto> dtoList = memberPage.getRecords().stream().map(member -> {
            CMemberDto dto = new CMemberDto();
            BeanUtils.copyProperties(member, dto);
            // 填充粉丝关注信息
            cMemberFansAttentionService.fullMemberFansAttention(dto);
            // 获取会员拥有的角色
            List<CRole> roles = cMemberRoleService.getMemberRoles(member.getId());
            dto.setRoles(roles);
            return dto;
        }).collect(Collectors.toList());

        dtoPage.setRecords(dtoList);

        return dtoPage;
    }

    /**
     * 更新会员状态
     * 
     * @param memberId  会员ID
     * @param available 是否可用
     * @param reason    原因
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMemberStatus(String memberId, boolean available, String reason) {
        // 1.获取会员信息
        CMember member = getMemberPo(memberId);

        // 2.更新状态
        member.setAvailable(available);
        member.setUpdateTime(LocalDateTime.now());
        cMemberMapper.updateById(member);

        // 3.发送系统消息
        String message = available ? "您的账号已被解除封禁，现在可以正常使用了" : "您的账号已被封禁，原因：" + (reason != null ? reason : "违反平台规则");

        sysMessageService.sendMessage(memberId, message, MessageType.ORDER_AUDIT);

        return true;
    }

    /**
     * 分页查询会员列表
     * 
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param nickname 会员昵称（模糊查询）
     * @param phone    手机号
     * @param status   状态
     * @return 会员分页数据
     */
    public Page<CMemberDto> pageMembers(Integer pageNum, Integer pageSize, String nickname, String phone,
            Boolean status) {
        Page<CMember> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<CMember> queryWrapper = new LambdaQueryWrapper<>();
        // 昵称模糊查询
        if (StringUtils.hasText(nickname)) {
            queryWrapper.like(CMember::getNickname, nickname);
        }
        // 手机号精确查询
        if (StringUtils.hasText(phone)) {
            queryWrapper.eq(CMember::getTel, phone);
        }
        // 状态筛选
        if (status != null) {
            queryWrapper.eq(CMember::getAvailable, status);
        }
        // 默认只查询未删除的数据
        queryWrapper.eq(CMember::getDeleted, false);
        // 按创建时间降序排序
        queryWrapper.orderByDesc(CMember::getCreateTime);

        // 执行查询
        Page<CMember> memberPage = cMemberMapper.selectPage(page, queryWrapper);

        // 转换为DTO
        Page<CMemberDto> dtoPage = new Page<>();
        BeanUtils.copyProperties(memberPage, dtoPage, "records");

        List<CMemberDto> dtoList = memberPage.getRecords().stream().map(member -> {
            CMemberDto dto = new CMemberDto();
            BeanUtils.copyProperties(member, dto);
            // 如果是达人，获取达人信息
            if ("TALENT".equals(member.getCurrentRoleId())) {
                // TODO: 获取达人信息
            }

            return dto;
        }).collect(Collectors.toList());

        dtoPage.setRecords(dtoList);

        return dtoPage;
    }

    /**
     * 更新会员状态
     * 
     * @param memberId 会员ID
     * @param status   状态
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMemberStatus(String memberId, Boolean status) {
        // 查询会员
        CMember member = getMemberPo(memberId);

        // 更新状态
        member.setAvailable(status);
        member.setUpdateTime(LocalDateTime.now());

        // 保存更新
        cMemberMapper.updateById(member);

        return true;
    }

    /**
     * 查询达人认证申请列表
     * 
     * @param status 审核状态（0-待审核 1-已通过 2-已拒绝）
     * @return 申请列表
     */
    public List<TalentApplyDTO> listTalentApplies(Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<CTalentApply> queryWrapper = new LambdaQueryWrapper<CTalentApply>()
                .eq(CTalentApply::getDeleted, false)
                .orderByDesc(CTalentApply::getCreateTime);
        
        // 如果指定了状态，添加状态过滤
        if (status != null) {
            queryWrapper.eq(CTalentApply::getStatus, status);
        }
        
        // 查询申请记录
        List<CTalentApply> applies = cTalentApplyMapper.selectList(queryWrapper);
        
        // 转换为DTO
        return applies.stream().map(apply -> {
            TalentApplyDTO dto = new TalentApplyDTO();
            dto.setMemberId(apply.getMemberId());
            dto.setStatus(apply.getStatus());
            dto.setIntroduction(apply.getIntroduction());
            dto.setSkills(apply.getSkills());
            dto.setVideo(apply.getVideo());
            dto.setApplyTime(apply.getCreateTime());
            dto.setAuditTime(apply.getAuditTime());
            dto.setAuditorId(apply.getAuditorId());
            dto.setAuditComment(apply.getAuditComment());
            
            // 处理照片数组
            if (apply.getPhotos() != null && !apply.getPhotos().isEmpty()) {
                dto.setPhotos(apply.getPhotos().split(","));
            }
            
            // 获取会员基本信息
            try {
                CMember member = getMemberPo(apply.getMemberId());
                dto.setNickname(member.getNickname());
                dto.setAvatar(member.getAvatar());
                dto.setGender(member.getGender());
                dto.setAge(member.getAge());
                dto.setPhone(member.getTel());
            } catch (Exception e) {
                log.warn("获取会员信息失败: memberId={}, error={}", apply.getMemberId(), e.getMessage());
            }
            
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 审核达人认证申请
     * 
     * @param memberId 会员ID
     * @param result   审核结果（1-通过 2-拒绝）
     * @param comment  审核意见
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean auditTalentApply(String memberId, Integer result, String comment) {
        // 1.查询会员
        CMember member = getMemberPo(memberId);
        
        // 2.查询申请记录
        CTalentApply talentApply = new LambdaQueryChainWrapper<>(cTalentApplyMapper)
                .eq(CTalentApply::getMemberId, memberId)
                .eq(CTalentApply::getStatus, 0) // 待审核状态
                .eq(CTalentApply::getDeleted, false)
                .one();
        
        if (talentApply == null) {
            throw new GlobalException(ResultCode.InValid_Param, "未找到待审核的申请记录");
        }
        
        // 3.更新申请记录
        talentApply.setStatus(result == 1 ? 1 : 2); // 1-通过，2-拒绝
        talentApply.setAuditTime(LocalDateTime.now());
        talentApply.setAuditorId(PrincipalUtil.getMemberIdOrNull()); // 审核人ID
        talentApply.setAuditComment(comment);
        talentApply.setUpdateTime(LocalDateTime.now());
        cTalentApplyMapper.updateById(talentApply);

        // 4.更新会员状态
        member.setApplyStatus(
                result == 1 ? MemberApplyStatus.APPROVED.getCode() : MemberApplyStatus.REJECTED.getCode());
        member.setUpdateTime(LocalDateTime.now());
        cMemberMapper.updateById(member);

        // 5.如果通过申请，添加达人角色并切换到达人角色
        if (result == 1) {
            // 添加达人角色到会员角色表
            cMemberRoleService.addMemberRole(memberId, MemberRoleEnum.TALENT.getCode());
            
            // 切换当前角色为达人
            member.setCurrentRoleId(MemberRoleEnum.TALENT.getCode());
            cMemberMapper.updateById(member);
        }
        
        // 6.发送系统消息
        String message = result == 1 ? "恭喜您，您的达人认证已通过审核，现在可以切换到达人角色" 
                : "很遗憾，您的达人认证未通过审核，原因：" + (comment != null ? comment : "未满足达人条件");
        sysMessageService.sendMessage(memberId, message, MessageType.ORDER_AUDIT);

        return true;
    }

    /**
     * 获取会员金币余额
     *
     * @param memberId 会员ID
     * @return 金币余额
     */
    public Integer getMemberGoldBalance(String memberId) {
        if (StringUtils.isEmpty(memberId)) {
            throw new GlobalException(ResultCode.InValid_Param, "会员ID不能为空");
        }

        CMember member = getMemberPo(memberId);

        // 获取金币数量，如果为null则返回0
        Object goldObj = member.getGold();
        if (goldObj == null) {
            return 0;
        }
        // 根据类型转换为Integer
        if (goldObj instanceof BigDecimal) {
            return ((BigDecimal) goldObj).intValue();
        } else if (goldObj instanceof Integer) {
            return (Integer) goldObj;
        } else {
            // 尝试其他类型转换
            try {
                return Integer.valueOf(goldObj.toString());
            } catch (Exception e) {
                log.error("会员金币类型转换失败: {}", e.getMessage());
                return 0;
            }
        }
    }

    /**
     * 后台创建会员
     * 
     * @param dto 会员创建DTO
     * @return 创建的会员DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public CMemberDto createMember(AdminCreateMemberDTO dto) {
        // 1.检查手机号是否已存在
        boolean exists = new LambdaQueryChainWrapper<>(cMemberMapper)
                .eq(CMember::getTel, dto.getTel())
                .eq(CMember::getDeleted, false)
                .count() > 0;

        if (exists) {
            throw new GlobalException(ResultCode.InValid_Param, "该手机号已被注册");
        }

        // 2.创建会员
        CMember cMember = new CMember();
        cMember.setId(UUID.randomUUID().toString());
        cMember.setAvatar(dto.getAvatar() != null ? dto.getAvatar() : defaultAvatar);
        cMember.setSysId(getMemberSysNumber());
        cMember.setNickname(dto.getNickname() != null ? dto.getNickname() : fetchMemberNickname());
        cMember.setApplyStatus(MemberApplyStatus.NOT_APPLIED.getCode());
        cMember.setGold(dto.getGold() != null ? dto.getGold() : BigDecimal.ZERO);
        cMember.setCurrentRoleId(dto.getCurrentRoleId() != null ? dto.getCurrentRoleId() : defaultRole);
        cMember.setTel(dto.getTel());
        cMember.setGender(dto.getGender());
        if (dto.getAge() != null) {
            cMember.setAge(dto.getAge());
        }
        cMember.setConstellation(dto.getConstellation());
        cMember.setHeight(dto.getHeight());
        cMember.setContext(dto.getContext());
        // 初始化实名认证状态为未认证
        cMember.setRealNameStatus(0);
        cMember.setAvailable(true);
        cMember.setDeleted(false);
        cMember.setCreateTime(LocalDateTime.now());
        cMember.setUpdateTime(LocalDateTime.now());

        cMemberMapper.insert(cMember);

        // 3.如果指定了角色，添加角色关联
        if (dto.getRoleIds() != null && !dto.getRoleIds().isEmpty()) {
            for (String roleId : dto.getRoleIds()) {
                CMemberRole memberRole = new CMemberRole();
                memberRole.setMemberId(cMember.getId());
                memberRole.setRoleId(roleId);
                memberRoleMapper.insert(memberRole);
            }
        } else {
            // 默认添加普通用户角色
            CMemberRole memberRole = new CMemberRole();
            memberRole.setMemberId(cMember.getId());
            memberRole.setRoleId(defaultRole);
            memberRoleMapper.insert(memberRole);
        }

        log.info("后台创建会员成功：{}", cMember.getNickname() + cMember.getId());

        // 4.返回创建后的会员DTO
        return getMemberInfo(cMember.getId(), true, true);
    }

    /**
     * 搜索会员
     * 
     * @param keyword 搜索关键词（昵称或手机号）
     * @return 会员简单信息列表
     */
    public List<MemberSimpleVO> searchMembers(String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return new ArrayList<>();
        }

        // 构建查询条件
        LambdaQueryWrapper<CMember> queryWrapper = new LambdaQueryWrapper<CMember>()
                .like(CMember::getNickname, keyword)
                .or()
                .like(CMember::getTel, keyword)
                .eq(CMember::getDeleted, false)
                .eq(CMember::getAvailable, true)
                .last("LIMIT 10"); // 限制返回10条

        List<CMember> members = cMemberMapper.selectList(queryWrapper);

        // 转换为VO
        return members.stream().map(member -> {
            MemberSimpleVO vo = new MemberSimpleVO();
            vo.setId(member.getId());
            vo.setNickname(member.getNickname());
            vo.setAvatar(member.getAvatar());
            vo.setPhone(member.getTel());
            vo.setGoldBalance(member.getGold().intValue());
            return vo;
        }).collect(Collectors.toList());
    }
}
