<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  mounted() {
    // 添加全局样式
    this.addGlobalStyles();
    
    // 延迟执行，确保DOM已经渲染完成
    setTimeout(() => {
      this.fixMenuItemsUnderline();
    }, 1000);
    
    // 定期检查并修复样式
    setInterval(() => {
      this.fixMenuItemsUnderline();
    }, 3000);
  },
  methods: {
    addGlobalStyles() {
      // 动态添加全局样式
      const styleEl = document.createElement('style');
      styleEl.innerHTML = `
        /* 全局样式修复侧边栏下划线问题 */
        .sidebar-container .el-menu,
        .sidebar-container .el-menu-item,
        .sidebar-container .el-submenu__title,
        .sidebar-container .el-menu--inline {
          border: none !important;
          border-bottom: none !important;
        }
        
        /* 特别处理带有data-v属性的span元素 */
        .sidebar-container span,
        .sidebar-container span[slot="title"],
        .sidebar-container .el-submenu__title span,
        .sidebar-container .el-menu-item span,
        .sidebar-container span[data-v-10973580],
        .sidebar-container [class*="data-v-"] span,
        span[data-v-10973580] {
          border-bottom: none !important;
          text-decoration: none !important;
          border: none !important;
          box-shadow: none !important;
        }
        
        /* 处理所有带有data-v-属性的span元素 */
        [class*="data-v-"] span {
          border-bottom: none !important;
          text-decoration: none !important;
          border: none !important;
          box-shadow: none !important;
        }
        
        /* 处理伪元素 */
        .sidebar-container .el-menu-item::after,
        .sidebar-container .el-menu-item::before,
        .sidebar-container .el-submenu__title::after,
        .sidebar-container .el-submenu__title::before {
          display: none !important;
        }
      `;
      document.head.appendChild(styleEl);
    },
    fixMenuItemsUnderline() {
      // 查找所有侧边栏中的span元素
      const spans = document.querySelectorAll('.sidebar-container span');
      if (spans) {
        spans.forEach(span => {
          // 直接设置内联样式
          span.style.setProperty('border-bottom', 'none', 'important');
          span.style.setProperty('text-decoration', 'none', 'important');
          span.style.setProperty('border', 'none', 'important');
          span.style.setProperty('box-shadow', 'none', 'important');
        });
      }
      
      // 特别处理带有data-v-10973580属性的span
      const dataVSpans = document.querySelectorAll('span[data-v-10973580]');
      if (dataVSpans) {
        dataVSpans.forEach(span => {
          span.style.setProperty('border-bottom', 'none', 'important');
          span.style.setProperty('text-decoration', 'none', 'important');
          span.style.setProperty('border', 'none', 'important');
          span.style.setProperty('box-shadow', 'none', 'important');
        });
      }
      
      // 处理所有可能的data-v-*属性
      const allDataVSpans = document.querySelectorAll('[data-v-]');
      if (allDataVSpans) {
        allDataVSpans.forEach(el => {
          const spans = el.querySelectorAll('span');
          spans.forEach(span => {
            span.style.setProperty('border-bottom', 'none', 'important');
            span.style.setProperty('text-decoration', 'none', 'important');
            span.style.setProperty('border', 'none', 'important');
            span.style.setProperty('box-shadow', 'none', 'important');
          });
        });
      }
      
      // 特别处理菜单项
      const menuItems = [
        '控制台', '订单管理', '陪玩类型', '系统配置',
        '会员列表', '充值套餐', '充值记录', '会员充值'
      ];
      
      menuItems.forEach(text => {
        const elements = Array.from(document.querySelectorAll('.sidebar-container span'))
          .filter(span => span.textContent.includes(text));
        
        elements.forEach(span => {
          span.style.setProperty('border-bottom', 'none', 'important');
          span.style.setProperty('text-decoration', 'none', 'important');
          span.style.setProperty('border', 'none', 'important');
          span.style.setProperty('box-shadow', 'none', 'important');
        });
      });
    }
  }
};
</script>

<style>
html, body, #app {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局样式修复侧边栏下划线问题 */
.sidebar-container .el-menu,
.sidebar-container .el-menu-item,
.sidebar-container .el-submenu__title,
.sidebar-container .el-menu--inline {
  border: none !important;
  border-bottom: none !important;
}

/* 特别处理带有data-v属性的span元素 */
.sidebar-container span,
.sidebar-container span[slot="title"],
.sidebar-container .el-submenu__title span,
.sidebar-container .el-menu-item span,
.sidebar-container span[data-v-10973580],
.sidebar-container [class*="data-v-"] span,
span[data-v-10973580] {
  border-bottom: none !important;
  text-decoration: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 处理所有带有data-v-属性的span元素 */
[class*="data-v-"] span {
  border-bottom: none !important;
  text-decoration: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 处理伪元素 */
.sidebar-container .el-menu-item::after,
.sidebar-container .el-menu-item::before,
.sidebar-container .el-submenu__title::after,
.sidebar-container .el-submenu__title::before {
  display: none !important;
}
</style> 