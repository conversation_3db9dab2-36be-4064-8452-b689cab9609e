package com.play.xianshibusiness.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OSS配置类
 */
@Configuration
public class OssConfig {
    
    /**
     * OSS端点
     */
    private static final String ENDPOINT = "http://oss-cn-chengdu.aliyuncs.com";
    
    /**
     * 存储桶名称
     */
    private static final String BUCKET_NAME = "xiasnhi1";

    /**
     * 访问密钥ID
     */
    private static final String ACCESS_KEY_ID = "LTAI5tCKEkxzADMPLMQAHuHJ";
    
    /**
     * 访问密钥Secret
     */
    private static final String ACCESS_KEY_SECRET = "******************************";
    
    /**
     * 获取OSS端点
     */
    public String getEndpoint() {
        return ENDPOINT;
    }
    
    /**
     * 获取存储桶名称
     */
    public String getBucketName() {
        return BUCKET_NAME;
    }
    
    /**
     * 获取访问密钥ID
     */
    public String getAccessKeyId() {
        return ACCESS_KEY_ID;
    }
    
    /**
     * 获取访问密钥Secret
     */
    public String getAccessKeySecret() {
        return ACCESS_KEY_SECRET;
    }
    
    /**
     * 创建OSS客户端
     */
    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
    }
} 