<template>
  <view class="container">
    <view class="textarea-box">
      <textarea
        class="intro-textarea"
        placeholder="请输入自我简介"
        maxlength="500"
        @input="onInput"
        :value="introduction"
      ></textarea>
      <view class="word-count">{{ introduction.length }}/500</view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-btn-wrap">
      <button class="save-btn" @tap="saveIntroduction">保存</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      introduction: "",
    };
  },
  onLoad(options) {
    if (options.introduction) {
      this.introduction = decodeURIComponent(options.introduction);
    }
  },
  methods: {
    onInput(e) {
      this.introduction = e.detail.value;
    },

    async saveIntroduction() {
      try {
        uni.showLoading({ title: '保存中...' });
        
        // 调用API保存介绍
        const app = getApp().globalData;
        const res = await this.$requestHttp.put(app.commonApi.updateMemberInfo, {
          data: {
            context: this.introduction
          }
        });
        
        if (res && res.code === 200) {
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];

          // 更新上一页数据
          if (!prevPage.profileInfo) {
            prevPage.profileInfo = {};
          }
          prevPage.profileInfo.introduction = this.introduction;

          // 更新本地存储
          const profileData = uni.getStorageSync("profileData") || {};
          if (!profileData.profileInfo) {
            profileData.profileInfo = {};
          }
          profileData.profileInfo.introduction = this.introduction;
          uni.setStorageSync("profileData", profileData);

          // 触发上一页面的完整度更新
          if (prevPage.updateCompletion) {
            prevPage.updateCompletion();
          }
          
          uni.hideLoading();
          uni.showToast({ title: '保存成功', icon: 'success' });
          
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(res?.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存介绍失败:', error);
        uni.hideLoading();
        uni.showToast({ title: error.message || '保存失败，请重试', icon: 'none' });
      }
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #f7f7f7;
  padding: 30rpx;
}

.textarea-box {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
}

.intro-textarea {
  width: 100%;
  min-height: 400rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
}

.save-btn-wrap {
  margin-top: 48rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
