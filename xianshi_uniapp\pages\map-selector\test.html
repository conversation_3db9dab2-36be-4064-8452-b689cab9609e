<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图选择器测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        .api-test {
            margin-bottom: 20px;
        }
        .api-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin-bottom: 10px;
        }
        .test-btn {
            background: #ff8c00;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-btn:hover {
            background: #e67c00;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .loading {
            color: #856404;
            background: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ 腾讯地图API测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 逆地理编码测试</div>
            <div class="api-test">
                <div class="api-url">
                    https://apis.map.qq.com/ws/geocoder/v1/?location=30.572816,104.066803&key=LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP&get_poi=1&output=jsonp
                </div>
                <button class="test-btn" onclick="testReverseGeocoding()">测试逆地理编码</button>
                <div id="reverse-result" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 地点搜索测试</div>
            <div class="api-test">
                <div class="api-url">
                    https://apis.map.qq.com/ws/place/v1/search?boundary=nearby(30.572816,104.066803,1000)&orderby=_distance&page_size=10&page_index=1&key=LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP&output=jsonp
                </div>
                <button class="test-btn" onclick="testPlaceSearch()">测试附近地点搜索</button>
                <div id="search-result" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 搜索建议测试</div>
            <div class="api-test">
                <div class="api-url">
                    https://apis.map.qq.com/ws/place/v1/suggestion?keyword=天安门&region=全国&key=LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP&output=jsonp
                </div>
                <button class="test-btn" onclick="testSuggestion()">测试搜索建议</button>
                <div id="suggestion-result" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 地图选择器功能测试</div>
            <p>以下是地图选择器的主要功能特性：</p>
            <ul>
                <li>✅ 交互式地图显示</li>
                <li>✅ 实时位置定位</li>
                <li>✅ 搜索建议功能</li>
                <li>✅ 附近地点显示</li>
                <li>✅ 逆地理编码</li>
                <li>✅ 地图控制按钮</li>
                <li>✅ 现代化UI设计</li>
                <li>✅ 跨平台兼容</li>
            </ul>
        </div>
    </div>

    <script>
        const API_KEY = 'LHWBZ-ZINWC-O2K2L-AC65J-LZTI5-NUFAP';
        
        function showResult(elementId, content, type = 'success') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = content;
        }

        function testReverseGeocoding() {
            const resultId = 'reverse-result';
            showResult(resultId, '正在测试逆地理编码...', 'loading');
            
            const callbackName = 'reverseCallback' + Date.now();
            window[callbackName] = function(data) {
                if (data && data.status === 0) {
                    const result = data.result;
                    const info = {
                        地址: result.address,
                        省份: result.address_component.province,
                        城市: result.address_component.city,
                        区县: result.address_component.district,
                        街道: result.address_component.street,
                        推荐地址: result.formatted_addresses?.recommend,
                        POI数量: result.pois ? result.pois.length : 0
                    };
                    showResult(resultId, JSON.stringify(info, null, 2), 'success');
                } else {
                    showResult(resultId, '测试失败: ' + JSON.stringify(data), 'error');
                }
                
                // 清理
                delete window[callbackName];
                const script = document.getElementById(callbackName);
                if (script) document.head.removeChild(script);
            };
            
            const script = document.createElement('script');
            script.id = callbackName;
            script.src = `https://apis.map.qq.com/ws/geocoder/v1/?location=30.572816,104.066803&key=${API_KEY}&get_poi=1&output=jsonp&callback=${callbackName}`;
            script.onerror = () => {
                showResult(resultId, '网络请求失败', 'error');
                delete window[callbackName];
            };
            document.head.appendChild(script);
        }

        function testPlaceSearch() {
            const resultId = 'search-result';
            showResult(resultId, '正在搜索附近地点...', 'loading');
            
            const callbackName = 'searchCallback' + Date.now();
            window[callbackName] = function(data) {
                if (data && data.status === 0 && data.data) {
                    const places = data.data.slice(0, 5).map(item => ({
                        名称: item.title,
                        地址: item.address,
                        分类: item.category,
                        距离: item._distance ? item._distance + 'm' : '未知'
                    }));
                    showResult(resultId, JSON.stringify(places, null, 2), 'success');
                } else {
                    showResult(resultId, '搜索失败: ' + JSON.stringify(data), 'error');
                }
                
                // 清理
                delete window[callbackName];
                const script = document.getElementById(callbackName);
                if (script) document.head.removeChild(script);
            };
            
            const script = document.createElement('script');
            script.id = callbackName;
            script.src = `https://apis.map.qq.com/ws/place/v1/search?boundary=nearby(30.572816,104.066803,1000)&orderby=_distance&page_size=10&page_index=1&key=${API_KEY}&output=jsonp&callback=${callbackName}`;
            script.onerror = () => {
                showResult(resultId, '网络请求失败', 'error');
                delete window[callbackName];
            };
            document.head.appendChild(script);
        }

        function testSuggestion() {
            const resultId = 'suggestion-result';
            showResult(resultId, '正在获取搜索建议...', 'loading');
            
            const callbackName = 'suggestionCallback' + Date.now();
            window[callbackName] = function(data) {
                if (data && data.status === 0 && data.data) {
                    const suggestions = data.data.slice(0, 5).map(item => ({
                        名称: item.title,
                        地址: item.address,
                        类型: item.type,
                        省份: item.province,
                        城市: item.city
                    }));
                    showResult(resultId, JSON.stringify(suggestions, null, 2), 'success');
                } else {
                    showResult(resultId, '获取建议失败: ' + JSON.stringify(data), 'error');
                }
                
                // 清理
                delete window[callbackName];
                const script = document.getElementById(callbackName);
                if (script) document.head.removeChild(script);
            };
            
            const script = document.createElement('script');
            script.id = callbackName;
            script.src = `https://apis.map.qq.com/ws/place/v1/suggestion?keyword=天安门&region=全国&key=${API_KEY}&output=jsonp&callback=${callbackName}`;
            script.onerror = () => {
                showResult(resultId, '网络请求失败', 'error');
                delete window[callbackName];
            };
            document.head.appendChild(script);
        }
    </script>
</body>
</html>
