<template>
  <div class="app-container clay-container">
    <div class="filter-container clay-card">
      <el-form :inline="true" :model="listQuery" class="filter-form">
        <el-form-item label="类型名称">
          <el-input v-model="listQuery.name" placeholder="请输入类型名称" clearable class="clay-input" />
        </el-form-item>
        <el-form-item label="父级类型">
          <el-select v-model="listQuery.parentId" placeholder="请选择父级类型" clearable class="clay-select">
            <el-option v-for="item in parentOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" class="clay-button">查询</el-button>
          <el-button @click="resetQuery" class="clay-button clay-button-secondary">重置</el-button>
          <el-button type="success" @click="handleCreate" class="clay-button clay-button-success">新增类型</el-button>
        </el-form-item>
      </el-form>
      
      <div class="view-switch">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="list">列表视图</el-radio-button>
          <el-radio-button label="tree">树形视图</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    
    <!-- 列表视图 -->
    <el-table
      v-if="viewMode === 'list'"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      class="clay-table"
    >
      <el-table-column label="ID" align="center" width="220">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="类型名称" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="编码" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.code }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="所需金币" min-width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.needGold }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="父级类型" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ getParentName(row.parentId) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="排序" min-width="80" align="center">
        <template slot-scope="{row}">
          <span>{{ row.comparable || '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" min-width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createTime }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" min-width="200" class-name="fixed-width">
        <template slot-scope="{row}">
          <el-button 
            type="primary" 
            size="mini" 
            @click="handleUpdate(row)" 
            class="clay-button clay-button-sm"
          >编辑</el-button>
          
          <el-button 
            type="danger" 
            size="mini" 
            @click="handleDelete(row)" 
            class="clay-button clay-button-sm clay-button-danger"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 树形视图 -->
    <div v-if="viewMode === 'tree'" class="tree-container clay-card">
      <el-tree
        v-loading="treeLoading"
        :data="treeData"
        :props="defaultProps"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        class="clay-tree"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <div class="tree-node-content">
            <div class="tree-node-main">
              <span class="tree-node-label">{{ node.label }}</span>
              <span v-if="data.needGold" class="tree-node-badge">{{ data.needGold }} 金币</span>
              <span v-if="data.code" class="tree-node-code">{{ data.code }}</span>
            </div>
            <div class="tree-node-actions">
              <el-button
                type="primary"
                size="mini"
                @click="handleUpdate(data)"
                class="clay-button clay-button-sm"
              >编辑</el-button>
              <el-button
                type="danger"
                size="mini"
                @click="handleDelete(data)"
                class="clay-button clay-button-sm clay-button-danger"
              >删除</el-button>
              <el-button
                v-if="!data.parentId"
                type="success"
                size="mini"
                @click="handleCreateChild(data)"
                class="clay-button clay-button-sm clay-button-success"
              >添加子类型</el-button>
            </div>
          </div>
        </span>
      </el-tree>
    </div>
    
    <pagination
      v-if="viewMode === 'list' && total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
      class="clay-pagination"
    />
    
    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '新增陪玩类型' : '编辑陪玩类型'" :visible.sync="dialogVisible" width="500px" class="clay-dialog">
      <el-form ref="dataForm" :model="temp" :rules="rules" label-width="100px">
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="temp.name" placeholder="请输入类型名称" class="clay-input" />
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model="temp.code" placeholder="请输入编码" class="clay-input" />
        </el-form-item>
        <el-form-item label="所需金币" prop="needGold">
          <el-input-number v-model="temp.needGold" :min="0" :precision="2" :step="10" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="父级类型">
          <el-select v-model="temp.parentId" placeholder="请选择父级类型" clearable class="clay-select" style="width: 100%;">
            <el-option v-for="item in parentOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="temp.comparable" :min="0" style="width: 100%;" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" class="clay-button clay-button-secondary">取消</el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()" class="clay-button">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllTypes, getAllParentTypes, createType, updateType, deleteType, pageTypes, getTypeTree } from '@/api/activity-type';
import Pagination from '@/components/Pagination';

export default {
  name: 'ActivityType',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      treeLoading: false,
      treeData: [],
      viewMode: 'list', // 'list' 或 'tree'
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        parentId: ''
      },
      parentOptions: [],
      dialogVisible: false,
      dialogStatus: '',
      temp: {
        id: undefined,
        name: '',
        code: '',
        needGold: 0,
        parentId: '',
        comparable: 0
      },
      rules: {
        name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        needGold: [{ required: true, message: '请输入所需金币', trigger: 'blur' }]
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    };
  },
  watch: {
    viewMode(val) {
      if (val === 'tree' && this.treeData.length === 0) {
        this.getTreeData();
      } else if (val === 'list' && this.list.length === 0) {
        this.getList();
      }
    }
  },
  created() {
    this.getList();
    this.getParentOptions();
  },
  methods: {
    getList() {
      this.listLoading = true;
      pageTypes(this.listQuery).then(response => {
        this.list = response.data.records || [];
        this.total = response.data.total || 0;
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      });
    },
    
    getTreeData() {
      this.treeLoading = true;
      getTypeTree().then(response => {
        this.treeData = response.data || [];
        this.treeLoading = false;
      }).catch(() => {
        this.treeLoading = false;
      });
    },
    
    getParentOptions() {
      getAllParentTypes().then(response => {
        this.parentOptions = response.data || [];
      });
    },
    
    handleSearch() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    
    resetQuery() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        name: '',
        parentId: ''
      };
      this.getList();
    },
    
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        code: '',
        needGold: 0,
        parentId: '',
        comparable: 0
      };
    },
    
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = 'create';
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    
    handleCreateChild(parent) {
      this.resetTemp();
      this.temp.parentId = parent.id;
      this.dialogStatus = 'create';
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createType(this.temp).then(() => {
            this.dialogVisible = false;
            this.$message({
              type: 'success',
              message: '创建成功'
            });
            this.refreshData();
          });
        }
      });
    },
    
    handleUpdate(row) {
      this.temp = Object.assign({}, row);
      this.dialogStatus = 'update';
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp);
          updateType(tempData.id, tempData).then(() => {
            this.dialogVisible = false;
            this.$message({
              type: 'success',
              message: '更新成功'
            });
            this.refreshData();
          });
        }
      });
    },
    
    handleDelete(row) {
      this.$confirm('确认删除该陪玩类型吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteType(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          this.refreshData();
        });
      }).catch(() => {});
    },
    
    refreshData() {
      // 刷新当前视图数据
      if (this.viewMode === 'list') {
        this.getList();
      } else {
        this.getTreeData();
      }
      // 刷新父级选项
      this.getParentOptions();
    },
    
    getParentName(parentId) {
      if (!parentId) return '无';
      const parent = this.parentOptions.find(item => item.id === parentId);
      return parent ? parent.name : '未知';
    }
  }
};
</script>

<style lang="scss" scoped>
.clay-container {
  background-color: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
}

.clay-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.filter-container {
  position: relative;
}

.view-switch {
  position: absolute;
  top: 20px;
  right: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.clay-input, .clay-select {
  width: 220px;
}

.clay-table {
  border-radius: 8px;
  overflow: hidden;
  
  ::v-deep .el-table__header-wrapper {
    th {
      background-color: #f7f9fc;
      color: #606266;
      font-weight: 600;
    }
  }
  
  ::v-deep .el-table__body-wrapper {
    td {
      padding: 16px 0;
    }
  }
}

.tree-container {
  min-height: 400px;
}

.clay-tree {
  ::v-deep .el-tree-node__content {
    height: auto;
    padding: 10px 0;
  }
}

.custom-tree-node {
  width: 100%;
  display: block;
}

.tree-node-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.tree-node-main {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.tree-node-label {
  font-weight: 500;
  color: #303133;
}

.tree-node-badge {
  background-color: #ecf5ff;
  color: #409eff;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}

.tree-node-code {
  color: #909399;
  font-size: 12px;
}

.tree-node-actions {
  display: flex;
  gap: 8px;
}

.clay-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  &.clay-button-sm {
    padding: 7px 12px;
  }
  
  &.clay-button-secondary {
    background-color: #f5f7fa;
    color: #606266;
    border-color: #dcdfe6;
    
    &:hover {
      background-color: #e9ecf2;
    }
  }
  
  &.clay-button-success {
    background-color: #67c23a;
    color: #fff;
    border-color: #67c23a;
    
    &:hover {
      background-color: #85ce61;
      border-color: #85ce61;
    }
  }
  
  &.clay-button-danger {
    background-color: #f56c6c;
    color: #fff;
    border-color: #f56c6c;
    
    &:hover {
      background-color: #f78989;
      border-color: #f78989;
    }
  }
}

.clay-pagination {
  margin-top: 24px;
  text-align: right;
  
  ::v-deep .el-pagination {
    padding: 10px 0;
    
    button, li {
      background-color: #f9fafc;
      border-radius: 6px;
      margin: 0 3px;
      
      &.active {
        background-color: #409eff;
        color: white;
      }
      
      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}
</style> 