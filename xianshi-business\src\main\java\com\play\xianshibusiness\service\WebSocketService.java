package com.play.xianshibusiness.service;

import com.play.xianshibusiness.dto.ChatMessage;
import com.play.xianshibusiness.websocket.ChatWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * WebSocket服务类
 */
@Service
@Slf4j
public class WebSocketService {
    
    @Autowired
    private ChatWebSocketHandler chatWebSocketHandler;
    
    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return chatWebSocketHandler.getOnlineUserCount();
    }
    
    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(String userId) {
        return chatWebSocketHandler.isUserOnline(userId);
    }
    
    /**
     * 发送系统消息给指定用户
     */
    public void sendSystemMessage(String userId, String content) {
        // 这里可以实现向特定用户发送系统消息的逻辑
        log.info("发送系统消息给用户 {}: {}", userId, content);
    }
}