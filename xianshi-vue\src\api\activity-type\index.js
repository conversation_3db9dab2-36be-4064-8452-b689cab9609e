import request from '@/utils/request';

/**
 * 获取所有陪玩类型
 * @returns {Promise}
 */
export function getAllTypes() {
  return request({
    url: '/activity-type/list',
    method: 'get'
  });
}

/**
 * 获取所有父级陪玩类型
 * @returns {Promise}
 */
export function getAllParentTypes() {
  return request({
    url: '/activity-type/parent-list',
    method: 'get'
  });
}

/**
 * 根据父级ID获取子类型
 * @param {String} parentId 父级ID
 * @returns {Promise}
 */
export function getChildrenByParentId(parentId) {
  return request({
    url: `/activity-type/children/${parentId}`,
    method: 'get'
  });
}

/**
 * 获取陪玩类型详情
 * @param {String} id 类型ID
 * @returns {Promise}
 */
export function getTypeDetail(id) {
  return request({
    url: `/activity-type/detail/${id}`,
    method: 'get'
  });
}

/**
 * 创建陪玩类型
 * @param {Object} data 类型数据
 * @returns {Promise}
 */
export function createType(data) {
  return request({
    url: '/activity-type/create',
    method: 'post',
    data
  });
}

/**
 * 更新陪玩类型
 * @param {String} id 类型ID
 * @param {Object} data 类型数据
 * @returns {Promise}
 */
export function updateType(id, data) {
  return request({
    url: `/activity-type/update/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除陪玩类型
 * @param {String} id 类型ID
 * @returns {Promise}
 */
export function deleteType(id) {
  return request({
    url: `/activity-type/delete/${id}`,
    method: 'delete'
  });
}

/**
 * 分页查询陪玩类型
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function pageTypes(params) {
  return request({
    url: '/activity-type/page',
    method: 'get',
    params
  });
}

/**
 * 获取陪玩类型树形结构
 * @returns {Promise}
 */
export function getTypeTree() {
  return request({
    url: '/activity-type/tree',
    method: 'get'
  });
} 