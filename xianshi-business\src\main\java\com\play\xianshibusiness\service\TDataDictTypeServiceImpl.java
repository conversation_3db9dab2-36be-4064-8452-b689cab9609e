package com.play.xianshibusiness.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.play.xianshibusiness.dto.config.DataDictQueryDTO;
import com.play.xianshibusiness.dto.config.DataDictTypeDTO;
import com.play.xianshibusiness.exception.GlobalException;
import com.play.xianshibusiness.mapper.TDataDictTypeMapper;
import com.play.xianshibusiness.mapper.TDataDictValueMapper;
import com.play.xianshibusiness.pojo.TDataDictType;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 数据字典类型服务实现类
 */
@Service
public class TDataDictTypeServiceImpl implements TDataDictTypeService {

    @Resource
    private TDataDictTypeMapper dataDictTypeMapper;
    
    @Resource
    private TDataDictValueMapper dataDictValueMapper;
    
    @Override
    public Page<TDataDictType> pageDictTypes(DataDictQueryDTO queryDTO) {
        if (queryDTO == null) {
            queryDTO = new DataDictQueryDTO();
        }
        
        // 构建查询条件
        LambdaQueryWrapper<TDataDictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TDataDictType::getDeleted, false);
        queryWrapper.eq(TDataDictType::getAvailable, true);
        
        // 添加查询条件
        if (StringUtils.hasText(queryDTO.getKeyword())) {
            DataDictQueryDTO finalQueryDTO = queryDTO;
            queryWrapper.and(wrapper -> wrapper
                    .like(TDataDictType::getTypeName, finalQueryDTO.getKeyword())
                    .or()
                    .like(TDataDictType::getTypeCode, finalQueryDTO.getKeyword())
                    .or()
                    .like(TDataDictType::getDescription, finalQueryDTO.getKeyword())
            );
        }
        
        // 排序
        queryWrapper.orderByAsc(TDataDictType::getSort);
        
        // 分页查询
        Page<TDataDictType> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        return dataDictTypeMapper.selectPage(page, queryWrapper);
    }
    
    @Override
    public TDataDictType getDictTypeDetail(String id) {
        if (!StringUtils.hasText(id)) {
            throw new GlobalException(400, "ID不能为空");
        }
        
        TDataDictType dictType = dataDictTypeMapper.selectById(id);
        if (dictType == null || dictType.getDeleted() || !dictType.getAvailable()) {
            throw new GlobalException(400, "数据字典类型不存在");
        }
        
        return dictType;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createDictType(DataDictTypeDTO typeDTO) {
        if (typeDTO == null) {
            throw new GlobalException(400, "参数不能为空");
        }
        
        // 检查编码是否已存在
        if (checkTypeCodeExists(typeDTO.getTypeCode(), null)) {
            throw new GlobalException(400, "类型编码已存在");
        }
        
        // 创建新类型
        TDataDictType dictType = new TDataDictType();
        BeanUtils.copyProperties(typeDTO, dictType);
        dictType.setCreateTime(LocalDateTime.now());
        dictType.setUpdateTime(LocalDateTime.now());
        dictType.setDeleted(false);
        dictType.setAvailable(true);
        
        dataDictTypeMapper.insert(dictType);
        
        return dictType.getId();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDictType(String id, DataDictTypeDTO typeDTO) {
        if (!StringUtils.hasText(id) || typeDTO == null) {
            throw new GlobalException(400, "参数不能为空");
        }
        
        // 检查类型是否存在
        TDataDictType existType = getDictTypeDetail(id);
        
        // 检查编码是否已存在（排除自身）
        if (checkTypeCodeExists(typeDTO.getTypeCode(), id)) {
            throw new GlobalException(400, "类型编码已存在");
        }
        
        // 更新类型
        BeanUtils.copyProperties(typeDTO, existType);
        existType.setUpdateTime(LocalDateTime.now());
        
        return dataDictTypeMapper.updateById(existType) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDictType(String id) {
        if (!StringUtils.hasText(id)) {
            throw new GlobalException(400, "ID不能为空");
        }
        
        // 检查类型是否存在
        TDataDictType existType = getDictTypeDetail(id);
        
        // 逻辑删除
        existType.setDeleted(true);
        existType.setUpdateTime(LocalDateTime.now());
        
        return dataDictTypeMapper.updateById(existType) > 0;
    }
    
    /**
     * 检查类型编码是否已存在
     *
     * @param typeCode 类型编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    private boolean checkTypeCodeExists(String typeCode, String excludeId) {
        if (!StringUtils.hasText(typeCode)) {
            return false;
        }
        
        LambdaQueryWrapper<TDataDictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TDataDictType::getTypeCode, typeCode);
        queryWrapper.eq(TDataDictType::getDeleted, false);
        
        if (StringUtils.hasText(excludeId)) {
            queryWrapper.ne(TDataDictType::getId, excludeId);
        }
        
        return dataDictTypeMapper.selectCount(queryWrapper) > 0;
    }
} 