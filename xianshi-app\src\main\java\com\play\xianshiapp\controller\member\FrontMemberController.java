package com.play.xianshiapp.controller.member;

import com.play.xianshibusiness.annotation.RequireLogin;
import com.play.xianshibusiness.dto.member.CMemberDto;
import com.play.xianshibusiness.dto.member.LoginRequest;
import com.play.xianshibusiness.dto.member.LoginResponse;
import com.play.xianshibusiness.dto.member.MemberUpdateDTO;
import com.play.xianshibusiness.dto.member.TalentApplyDTO;
import com.play.xianshibusiness.result.Result;
import com.play.xianshibusiness.result.ResultUtils;
import com.play.xianshibusiness.service.CMemberService;
import com.play.xianshibusiness.service.CaptchaService;
import com.play.xianshibusiness.utils.PrincipalUtil;
import com.play.xianshiapp.dto.member.RealNameVerifyRequest;
import com.play.xianshiapp.dto.member.RealNameStatusResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/app/member")
@Api(tags = "会员模块-API")
public class FrontMemberController {
    @Resource
    private CMemberService cMemberService;

    @Resource
    private CaptchaService captchaService;

    @ApiOperation(value = "会员登录")
    @PostMapping("/login")
    public Result<LoginResponse> memberLogin(@RequestBody @Valid LoginRequest loginRequest) {
        return ResultUtils.success(cMemberService.memberLogin(loginRequest));
    }
    
    /**
     * 获取当前登录会员信息
     */
    @ApiOperation(value = "获取当前登录会员信息")
    @GetMapping("")
    @RequireLogin()
    public Result<CMemberDto> getCurrentMemberInfo(
            @ApiParam(value = "是否包含附加信息（如关注/粉丝数）") 
            @RequestParam(value = "withExtra", required = false, defaultValue = "true") Boolean withExtra,
            @ApiParam(value = "是否包含隐私信息") 
            @RequestParam(value = "withPrivacy", required = false, defaultValue = "true") Boolean withPrivacy) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(cMemberService.getMemberInfo(memberId, withExtra, withPrivacy));
    }
    
    /**
     * 发送短信验证码
     */
    @GetMapping("/mobile/captcha")
    @ApiOperation("发送短信验证码")
    public Result<String> phoneCaptchaCode(
            @ApiParam(value = "手机号", required = true) 
            @RequestParam("phone") String phone,
            @ApiParam(value = "验证码用途", example = "LOGIN, REGISTER, RESET_PASSWORD") 
            @RequestParam(value = "purpose", required = false, defaultValue = "LOGIN") String purpose) {
        return ResultUtils.success(captchaService.phoneCaptchaCode(phone, purpose));
    }
    
    /**
     * 更新会员资料
     */
    @ApiOperation(value = "更新会员资料")
    @PutMapping("/profile")
    @RequireLogin
    public Result<CMemberDto> updateProfile(@RequestBody @Valid MemberUpdateDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(cMemberService.updateMemberProfile(dto, memberId));
    }
    /**
     * 申请达人认证
     */
    @ApiOperation(value = "申请达人认证")
    @PostMapping("/talent/apply")
    @RequireLogin
    public Result<Boolean> applyForTalent(@RequestBody @Valid TalentApplyDTO dto) {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(cMemberService.applyForTalent(dto, memberId));
    }
    
    /**
     * 获取个人资料完整度
     */
    @ApiOperation(value = "获取个人资料完整度")
    @GetMapping("/profile/completion")
    @RequireLogin
    public Result<Integer> getProfileCompletion() {
        String memberId = PrincipalUtil.getMemberId();
        return ResultUtils.success(cMemberService.calculateProfileCompletion(memberId));
    }

    /**
     * 达人列表接口
     */
    @ApiOperation(value = "达人列表")
    @GetMapping("/talent/list")
    public Result<?> getTalentList(
            @ApiParam(value = "页码", required = false, defaultValue = "1") @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页数量", required = false, defaultValue = "10") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @ApiParam(value = "是否排除当前用户", required = false, defaultValue = "true") @RequestParam(value = "excludeCurrentUser", required = false, defaultValue = "true") Boolean excludeCurrentUser) {
        
        String currentMemberId = excludeCurrentUser ? PrincipalUtil.getMemberIdOrNull() : null;
        
        return ResultUtils.success(cMemberService.getTalentList(pageNum, pageSize, currentMemberId));
    }
    
    @ApiOperation(value = "实名认证")
    @PostMapping("/verify-real-name")
    @RequireLogin
    public Result<Boolean> verifyRealName(@RequestBody @Valid RealNameVerifyRequest request) {
        String memberId = PrincipalUtil.getMemberId();
        boolean result = cMemberService.verifyRealName(
            request.getRealName(), 
            request.getIdCardNumber(), 
            request.getPhone(), 
            memberId
        );
        return ResultUtils.success(result);
    }
    
    @ApiOperation(value = "获取实名认证状态")
    @GetMapping("/real-name-status")
    @RequireLogin
    public Result<RealNameStatusResponse> getRealNameStatus() {
        String memberId = PrincipalUtil.getMemberId();
        CMemberDto member = cMemberService.getMemberInfo(memberId, false, true);
        
        RealNameStatusResponse response = new RealNameStatusResponse();
        response.setRealNameStatus(member.getRealNameStatus());
        response.setRealName(member.getRealName());
        response.setRealNameVerifyTime(member.getRealNameVerifyTime());
        
        return ResultUtils.success(response);
    }
}
