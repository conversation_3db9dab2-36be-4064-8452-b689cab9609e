<template>
  <view class="container">
    <!-- 标签网格 -->
    <view class="tags-grid">
      <view
        v-for="item in tagsList"
        :key="item.name"
        class="tag-item"
        :class="{ selected: selectedTags[item.name] }"
        @tap="toggleTag"
        :data-tag="item.name"
      >
        <text>{{ item.name }}</text>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-btn-wrap">
      <button class="save-btn" @tap="saveTags">保存</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedTags: {},
      selectedTagsList: [],
      tagsList: [
        { name: "会英语" },
        { name: "会韩语" },
        { name: "会日语" },
        { name: "会跳舞" },
        { name: "会唱歌" },
        { name: "会主持" },
        { name: "会摄影" },
        { name: "会化妆" },
        { name: "能喝酒" },
        { name: "会开车" },
        { name: "有导游证" },
        { name: "会吉他" },
        { name: "会钢琴" },
        { name: "会游泳" },
        { name: "会瑜伽" },
        { name: "会街舞" },
        { name: "会魔术" },
        { name: "会调酒" },
      ],
    };
  },

  onLoad(options) {
    uni.setNavigationBarTitle({
      title: "编辑身份标签",
    });

    // 如果有已选择的标签，初始化选中状态
    if (options.tags) {
      const tags = JSON.parse(decodeURIComponent(options.tags));
      const selectedTags = {};

      tags.forEach((tag) => {
        selectedTags[tag] = true;
      });

      this.selectedTags = selectedTags;
    }
  },
  methods: {
    toggleTag(e) {
      const tag = e.currentTarget.dataset.tag;
      const selectedTags = { ...this.selectedTags };

      if (selectedTags[tag]) {
        delete selectedTags[tag];
      } else {
        selectedTags[tag] = true;
      }

      this.selectedTags = selectedTags;
    },

    async saveTags() {
      try {
        uni.showLoading({ title: '保存中...' });
        
        // 获取选中的标签列表
        const selectedTagsList = Object.keys(this.selectedTags);
        
        // 调用API保存标签
        const app = getApp().globalData;
        const res = await this.$requestHttp.put(app.commonApi.updateMemberInfo, {
          data: {
            identify: selectedTagsList.join(',')
          }
        });
        
        if (res && res.code === 200) {
          // 保存到当前页面数据中
          this.selectedTagsList = selectedTagsList;

          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];
          prevPage.tags = selectedTagsList;

          // 更新本地存储
          const profileData = uni.getStorageSync("profileData") || {};
          profileData.tags = selectedTagsList;
          uni.setStorageSync("profileData", profileData);

          // 触发上一页面的完整度更新
          if (prevPage.updateCompletion) {
            prevPage.updateCompletion();
          }
          
          uni.hideLoading();
          uni.showToast({ title: '保存成功', icon: 'success' });
          
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(res?.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存标签失败:', error);
        uni.hideLoading();
        uni.showToast({ title: error.message || '保存失败，请重试', icon: 'none' });
      }
    },
  },
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.tags-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16rpx;
  margin-bottom: 40rpx;
}

.tag-item {
  aspect-ratio: 1;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  padding: 8rpx;
  text-align: center;
  word-break: break-all;
}

.tag-item.selected {
  background: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
  font-weight: 500;
}

.save-btn-wrap {
  margin-top: auto;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #ff8c00;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
