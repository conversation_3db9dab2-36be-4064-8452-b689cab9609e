package com.play.xianshibusiness.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 后台登录响应DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "后台登录响应")
public class AdminLoginResponse implements Serializable {
    
    @ApiModelProperty(value = "访问令牌")
    private String token;
    
    @ApiModelProperty(value = "管理员ID")
    private String adminId;
    
    @ApiModelProperty(value = "用户名")
    private String username;
    
    @ApiModelProperty(value = "昵称")
    private String nickname;
    
    @ApiModelProperty(value = "头像")
    private String avatar;
    
    @ApiModelProperty(value = "角色列表")
    private List<String> roles;
    
    @ApiModelProperty(value = "权限列表")
    private List<String> permissions;
} 