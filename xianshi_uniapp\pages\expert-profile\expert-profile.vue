<template>
  <view class="profile-container">
    <!-- 个人基本信息 -->
    <view class="profile-header">
      <!-- 返回按钮 - 已删除 -->

      <image
        class="avatar"
        :src="expertInfo?.avatar"
        mode="aspectFill"
      ></image>
      <view class="username-row">
        <text class="username">{{ expertInfo.name }}</text>
        <text :class="['gender', expertInfo.gender === '女' ? 'female' : 'male']">
          {{
            expertInfo.gender === "女"
              ? "♀"
              : expertInfo.gender === "男"
              ? "♂"
              : ""
          }}
        </text>
      </view>
      <view class="user-type">达人</view>

      <!-- 用户数据统计 -->
      <view class="user-stats">
        <view class="stat-item">
          <view class="stat-num">{{ popularity }}</view>
          <view class="stat-label">人气</view>
        </view>
        <view class="stat-item">
          <view class="stat-num">{{ expertInfo.photos.length || 0 }}</view>
          <view class="stat-label">粉丝</view>
        </view>
        <view class="stat-item">
          <view class="stat-num">{{ followers }}</view>
          <view class="stat-label">关注</view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="action-btn chat" @tap="chatWithExpert">
          <text class="iconfont icon-message"></text>
          <text>私信</text>
        </view>
        <view
          :class="['action-btn', expertInfo.isFavorite ? 'favorite-active' : 'favorite']"
          @tap="toggleFavorite"
        >
          <text class="iconfont icon-star"></text>
          <text>{{ expertInfo.isFavorite ? "已关注" : "关注" }}</text>
        </view>
        <view class="action-btn order" @tap="makeOrder">
          <text class="iconfont icon-calendar"></text>
          <text>约单</text>
        </view>
      </view>
    </view>

    <!-- 页面标签栏 -->
    <view class="tab-bar">
      <view
        :class="['tab', activeTab === 'home' ? 'active' : '']"
        @tap="switchTab"
        data-tab="home"
      >
        主页
      </view>
      <view
        :class="['tab', activeTab === 'works' ? 'active' : '']"
        @tap="switchTab"
        data-tab="works"
      >
        作品
      </view>
      <view
        :class="['tab', activeTab === 'notice' ? 'active' : '']"
        @tap="switchTab"
        data-tab="notice"
      >
        通告
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-area">
      <!-- 主页内容 -->
      <view class="home-content" v-if="activeTab === 'home'">
        <!-- 形象信息 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">形象信息</text>
            <text class="edit-btn" v-if="isSelf">编辑</text>
          </view>
          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">{{ expertInfo.age || "--" }}</text>
              <text class="info-value">年龄</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{ expertInfo.height || "--" }}</text>
              <text class="info-value">身高</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{ expertInfo.weight || "--" }}</text>
              <text class="info-value">体重</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{ expertInfo.city || "--" }}</text>
              <text class="info-value">城市</text>
            </view>
          </view>
        </view>

        <!-- 自我简介 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">自我简介</text>
            <text class="edit-btn" v-if="isSelf">编辑</text>
          </view>
          <view class="intro-content">
            <text class="intro-text">{{ introduction }}</text>
          </view>
        </view>

        <!-- 形象照片 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">形象照片</text>
            <text class="add-btn" v-if="isSelf">添加</text>
          </view>
          <view
            class="photos-grid"
            v-if="expertInfo.photos && expertInfo.photos.length > 0"
          >
            <image
              v-for="(item, index) in expertInfo.photos"
              :key="index"
              class="profile-photo"
              :src="item"
              mode="aspectFill"
              @tap="previewImage"
              :data-index="index"
            ></image>
          </view>
          <view class="empty-photos-grid" v-else>
            <view class="empty-photo-item">
              <view class="placeholder-icon">+</view>
            </view>
            <view class="empty-photo-item">
              <view class="placeholder-icon">+</view>
            </view>
            <view class="empty-photo-item">
              <view class="placeholder-icon">+</view>
            </view>
          </view>
        </view>

        <!-- 形象风格 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">形象风格</text>
            <text class="edit-btn" v-if="isSelf">编辑</text>
          </view>
          <view
            class="tag-content"
            v-if="styleList && styleList.length > 0"
          >
            <view class="tag-item" v-for="(item, index) in styleList" :key="index">{{
              item
            }}</view>
          </view>
          <view class="empty-placeholder" v-else>
            <text class="placeholder-text">暂无形象风格</text>
          </view>
        </view>

        <!-- 身份标签 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">身份标签</text>
            <text class="edit-btn" v-if="isSelf">编辑</text>
          </view>
          <view class="tag-content" v-if="tagList && tagList.length > 0">
            <view class="tag-item" v-for="(item, index) in tagList" :key="index">{{
              item
            }}</view>
          </view>
          <view class="empty-placeholder" v-else>
            <text class="placeholder-text">暂无身份标签</text>
          </view>
        </view>
      </view>

      <!-- 作品内容 -->
      <view class="works-content" v-if="activeTab === 'works'">
        <view class="empty-placeholder">
          <text class="placeholder-text">暂无作品内容</text>
        </view>
      </view>

      <!-- 通告内容 -->
      <view class="notice-content" v-if="activeTab === 'notice'">
        <view class="empty-placeholder">
          <text class="placeholder-text">暂无通告内容</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" v-if="isLoading">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>
</template>

<script>
export default {
  data() {
    return {
      expertId: null,
      expertInfo: null,
      isLoading: true,
      activeTab: "home",
      isSelf: false,
      popularity: 0,
      followers: 0,
      introduction: "",
      styleList: [],
      tagList: [],
    };
  },

  onLoad(options) {
    console.log("达人资料页面加载，参数:", options);

    // 获取达人ID
    const expertId = options.id;
    if (!expertId) {
      uni.showToast({
        title: "达人ID不存在",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
      return;
    }

    this.expertId = expertId;

    // 加载达人信息
    this.loadExpertInfo(expertId);
  },
  methods: {
    // 加载达人信息
    loadExpertInfo(expertId) {
      // 先尝试从本地获取达人信息
      const expertInfo = uni.getStorageSync("currentExpertInfo");

      if (expertInfo && expertInfo.id == expertId) {
        console.log("从本地获取到达人信息:", expertInfo);
        this.processExpertInfo(expertInfo);
        return;
      }

      // 从全局数据中查找
      const app = getApp();
      const allExperts = app.globalData.expertsData || [];
      const expert = allExperts.find((e) => e.id == expertId);

      if (expert) {
        console.log("从全局数据中找到达人信息:", expert);
        this.processExpertInfo(expert);
      } else {
        // 模拟数据（实际项目中应改为从服务器获取）
        console.log("未找到达人信息，使用默认数据");

        // 创建默认数据
        const defaultExpert = {
          id: expertId,
          name: "达人用户",
          avatar: "/images/default-avatar.png",
          gender: "保密",
          age: "--",
          height: "--",
          weight: "--",
          city: "未知",
          photos: [],
        };

        this.processExpertInfo(defaultExpert);

        uni.showToast({
          title: "未找到达人资料",
          icon: "none",
        });
      }
    },

    // 处理达人信息
    processExpertInfo(expertInfo) {
      // 随机生成人气数和关注数
      const popularity = Math.floor(Math.random() * 100) + 1;
      const followers = Math.floor(Math.random() * 10);

      // 生成简介
      const introduction = this.generateRandomIntroduction(expertInfo);

      // 生成形象标签
      const styleList = this.generateStyleList(expertInfo);
      const tagList = this.generateTagList(expertInfo);

      this.expertInfo = expertInfo;
      this.isLoading = false;
      this.popularity = popularity;
      this.followers = followers;
      this.introduction = introduction;
      this.styleList = styleList;
      this.tagList = tagList;

      // 设置页面标题
      uni.setNavigationBarTitle({
        title: expertInfo.name + "的主页",
      });
    },

    // 生成随机自我介绍文本
    generateRandomIntroduction(expertInfo) {
      const intros = [
        `大家好，我是${expertInfo.name}，${expertInfo.age || ""}岁，来自${
          expertInfo.city || ""
        }。我热爱生活，喜欢结交新朋友，希望能在这个平台上认识更多志同道合的朋友。`,
        `嗨！我是${expertInfo.name}，${expertInfo.gender || ""}，${
          expertInfo.city || ""
        }人。性格开朗，喜欢${
          expertInfo.gender === "女"
            ? "旅行、看书、听音乐"
            : "运动、摄影、户外活动"
        }。希望能在这里遇见有趣的灵魂。`,
      ];

      // 随机选择一个介绍
      const randomIndex = Math.floor(Math.random() * intros.length);
      return intros[randomIndex];
    },

    // 生成形象风格列表
    generateStyleList(expertInfo) {
      const allStyles = [
        "甜美",
        "清新",
        "时尚",
        "成熟",
        "优雅",
        "可爱",
        "帅气",
        "阳光",
        "街头",
        "复古",
        "文艺",
        "日系",
        "欧美",
        "韩系",
        "古风",
        "运动",
      ];

      // 如果专家已有风格列表则直接使用
      if (expertInfo.styleList && expertInfo.styleList.length > 0) {
        return expertInfo.styleList;
      }

      // 根据性别选择风格
      let styles = [];
      if (expertInfo.gender === "女") {
        styles = ["甜美", "清新", "时尚"];
        if (expertInfo.age && parseInt(expertInfo.age) > 25) {
          styles.push("优雅");
        } else {
          styles.push("可爱");
        }
      } else {
        styles = ["帅气", "阳光", "时尚"];
        if (expertInfo.age && parseInt(expertInfo.age) > 25) {
          styles.push("成熟");
        } else {
          styles.push("街头");
        }
      }

      return styles;
    },

    // 生成身份标签列表
    generateTagList(expertInfo) {
      const allTags = [
        "学生",
        "设计师",
        "工程师",
        "模特",
        "摄影师",
        "美妆博主",
        "舞者",
        "旅行爱好者",
        "健身达人",
        "美食家",
        "艺术家",
        "音乐人",
        "教师",
        "医生",
        "律师",
        "自由职业",
      ];

      // 如果专家已有标签列表则直接使用
      if (expertInfo.tagList && expertInfo.tagList.length > 0) {
        return expertInfo.tagList;
      }

      // 随机选择2-3个标签
      const tags = [];
      const numTags = Math.floor(Math.random() * 2) + 2;
      const shuffledTags = [...allTags].sort(() => 0.5 - Math.random());
      for (let i = 0; i < numTags; i++) {
        tags.push(shuffledTags[i]);
      }

      return tags;
    },

    // 切换标签页
    switchTab(e) {
      const tab = e.currentTarget.dataset.tab;
      this.activeTab = tab;
    },

    // 关注达人
    toggleFavorite: function () {
      // 获取当前达人信息
      const expertInfo = this.expertInfo;
      if (!expertInfo) return;

      // 更新收藏状态
      expertInfo.isFavorite = !expertInfo.isFavorite;

      // 在UniApp中直接修改数据属性，不需要setData
      this.expertInfo = expertInfo;

      // 更新本地存储
      uni.setStorageSync("currentExpertInfo", expertInfo);

      // 提示信息
      uni.showToast({
        title: expertInfo.isFavorite ? "关注成功" : "取消关注",
        icon: "success",
      });
    },

    // 私聊
    chatWithExpert: function () {
      const expertId = this.expertId;
      const expertName = this.expertInfo ? this.expertInfo.name : "达人";

      uni.navigateTo({
        url: `/pages/chat/chat-detail?id=${expertId}&name=${expertName}`,
        fail: function (err) {
          console.error("导航到聊天页面失败", err);
          uni.showToast({
            title: "聊天功能暂未开放",
            icon: "none",
          });
        },
      });
    },

    // 下单
    makeOrder: function () {
      uni.navigateTo({
        url: `/pages/create-order/create-order?expertId=${this.expertId}`,
        fail: function (err) {
          console.error("导航到下单页面失败", err);
          uni.showToast({
            title: "下单功能暂未开放",
            icon: "none",
          });
        },
      });
    },

    // 预览图片
    previewImage: function (e) {
      const index = e.currentTarget.dataset.index;
      const photos = this.expertInfo.photos;

      if (!photos || photos.length === 0) return;

      uni.previewImage({
        current: photos[index],
        urls: photos,
      });
    },

    // 返回上一页 - 保留方法但移除按钮
    goBack: function () {
      uni.navigateBack();
    },
  },
};
</script>

<style scoped>

/* 主容器 */
.profile-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

/* 个人信息头部 */
.profile-header {
  background-color: #ffffff;
  padding: 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 60rpx;
}

/* 返回按钮 - 样式保留但按钮已移除 */
.back-btn-floating {
  position: absolute;
  left: 30rpx;
  top: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.back-icon {
  font-size: 32rpx;
  color: #333;
}

/* 头像 */
.avatar {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

/* 用户名行 */
.username-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.username {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 10rpx;
}

.gender {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.female {
  color: #ff5792;
}

.male {
  color: #4a9eff;
}

/* 用户类型标签 */
.user-type {
  font-size: 24rpx;
  color: #888;
  background-color: #f3f3f3;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 用户统计区域 */
.user-stats {
  display: flex;
  width: 100%;
  justify-content: space-around;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-num {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 24rpx;
  color: #888;
  margin-top: 4rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  width: 100%;
  justify-content: space-around;
  padding: 20rpx 0;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.action-btn text:first-child {
  font-size: 44rpx;
  margin-bottom: 8rpx;
}

.chat {
  color: #4a9eff;
}

.favorite {
  color: #ff5792;
}

.favorite-active {
  color: #ff5792;
}

.order {
  color: #5cd066;
}

/* 标签栏 */
.tab-bar {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  margin-top: 2rpx;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #ff5792;
  font-weight: 500;
}

.tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #ff5792;
  border-radius: 3rpx;
}

/* 内容区域 */
.content-area {
  padding: 20rpx 30rpx;
}

/* 信息区块通用样式 */
.info-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.edit-btn,
.add-btn {
  font-size: 24rpx;
  color: #4a9eff;
}

/* 信息网格 */
.info-grid {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.info-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.info-value {
  font-size: 24rpx;
  color: #888;
}

/* 自我介绍 */
.intro-content {
  padding: 10rpx 0;
}

.intro-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 照片网格 */
.photos-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.profile-photo {
  width: calc(33.33% - 20rpx);
  height: 200rpx;
  margin: 10rpx;
  border-radius: 8rpx;
}

.empty-photos-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.empty-photo-item {
  width: calc(33.33% - 20rpx);
  height: 200rpx;
  margin: 10rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.placeholder-icon {
  font-size: 60rpx;
  color: #ccc;
}

/* 标签内容 */
.tag-content {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.tag-item {
  background-color: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

/* 空白占位 */
.empty-placeholder {
  padding: 60rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.placeholder-text {
  font-size: 26rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff5792;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
