package com.play.xianshiadmin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableAsync
@EnableScheduling
@ComponentScan(basePackages = "com.play")
@MapperScan({"com.play.xianshibusiness.mapper", "com.play.xianshibusiness.mapper.*"})
public class XianshiAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(XianshiAdminApplication.class, args);
    }

}
