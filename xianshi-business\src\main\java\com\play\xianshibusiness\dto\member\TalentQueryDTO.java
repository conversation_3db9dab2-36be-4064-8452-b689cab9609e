package com.play.xianshibusiness.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 达人查询DTO
 */
@Data
@ApiModel("达人查询DTO")
public class TalentQueryDTO {
    
    @ApiModelProperty(value = "关键字", notes = "支持昵称、ID模糊查询")
    private String keyword;
    
    @ApiModelProperty(value = "性别：1-男，2-女")
    private Integer gender;
    
    @ApiModelProperty(value = "年龄下限")
    private Integer minAge;
    
    @ApiModelProperty(value = "年龄上限")
    private Integer maxAge;
    
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;
    
    @ApiModelProperty(value = "星座")
    private String constellation;
    
    @ApiModelProperty(value = "城市")
    private String city;
    
    @ApiModelProperty(value = "项目类型ID")
    private String projectTypeId;
    
    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;
    
    @ApiModelProperty(value = "每页条数", example = "10")
    private Integer pageSize = 10;
    
    @ApiModelProperty(value = "排序字段：createTime-创建时间，popularity-人气值")
    private String orderBy;
    
    @ApiModelProperty(value = "排序方式：asc-升序，desc-降序")
    private String orderDirection = "desc";
} 