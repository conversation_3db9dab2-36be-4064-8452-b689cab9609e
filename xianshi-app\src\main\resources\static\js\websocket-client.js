class WebSocketClient {
    constructor(options = {}) {
        this.options = Object.assign({
            // 修改端口从9012改为9019
            url: `ws://${window.location.hostname}:9019/ws`,
            reconnectInterval: 5000,
            heartbeatInterval: 30000,
            maxReconnectAttempts: 5,
            token: null,
            userId: null,
            onOpen: () => {},
            onMessage: () => {},
            onClose: () => {},
            onError: () => {},
            debug: false
        }, options);
        
        this.connected = false;
        this.socket = null;
        this.reconnectTimer = null;
        this.heartbeatTimer = null;
        
        // 消息处理函数映射
        this.messageHandlers = {
            'CHAT': this._handleChatMessage.bind(this),
            'SYSTEM': this._handleSystemMessage.bind(this),
            'PING': this._handlePingMessage.bind(this)
        };
        
        // 消息回调函数
        this.messageCallbacks = {};
    }
    
    connect() {
        if (this.socket) {
            this.disconnect();
        }
        
        if (!this.options.token || !this.options.userId) {
            this._log('Token or userId not provided');
            return;
        }
        
        // 确保token有Bearer前缀
        let token = this.options.token;
        if (!token.startsWith('Bearer ')) {
            token = 'Bearer ' + token;
        }
        
        const url = `${this.options.url}?token=${encodeURIComponent(token)}&userId=${this.options.userId}`;
        this.socket = new WebSocket(url);
        
        this.socket.onopen = this._onOpen.bind(this);
        this.socket.onmessage = this._onMessage.bind(this);
        this.socket.onclose = this._onClose.bind(this);
        this.socket.onerror = this._onError.bind(this);
    }
    
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        
        this._clearTimers();
        this.connected = false;
    }
    
    send(message, callback) {
        if (!this.connected) {
            this._log('Not connected');
            return false;
        }
        
        // 生成消息ID
        if (!message.id) {
            message.id = this._generateId();
        }
        
        // 设置发送时间
        if (!message.timestamp) {
            message.timestamp = new Date();
        }
        
        // 设置发送者ID
        if (!message.fromUserId) {
            message.fromUserId = this.options.userId;
        }
        
        // 设置消息状态
        message.status = 'SENDING';
        
        // 注册回调
        if (callback) {
            this.messageCallbacks[message.id] = callback;
        }
        
        // 发送消息
        this.socket.send(JSON.stringify(message));
        
        return true;
    }
    
    sendChatMessage(toUserId, content, callback) {
        const message = {
            type: 'CHAT',
            toUserId: toUserId,
            content: content
        };
        
        return this.send(message, callback);
    }
    
    sendHeartbeat() {
        const message = {
            type: 'PING',
            toUserId: 'server',
            content: 'ping'
        };
        
        return this.send(message);
    }
    
    _onOpen(event) {
        this.connected = true;
        this._log('WebSocket connected');
        
        // 启动心跳
        this._startHeartbeat();
        
        // 调用回调
        if (this.options.onOpen) {
            this.options.onOpen(event);
        }
    }
    
    _onMessage(event) {
        let message;
        try {
            message = JSON.parse(event.data);
        } catch (e) {
            this._log('Invalid message format', e);
            return;
        }
        
        this._log('Received message', message);
        
        // 根据消息类型处理
        const handler = this.messageHandlers[message.type];
        if (handler) {
            handler(message);
        }
        
        // 调用回调
        if (this.options.onMessage) {
            this.options.onMessage(message);
        }
        
        // 处理消息回调
        if (message.id && this.messageCallbacks[message.id]) {
            this.messageCallbacks[message.id](message);
            delete this.messageCallbacks[message.id];
        }
    }
    
    _onClose(event) {
        this.connected = false;
        this._log('WebSocket closed', event);
        
        // 清除定时器
        this._clearTimers();
        
        // 调用回调
        if (this.options.onClose) {
            this.options.onClose(event);
        }
        
        // 自动重连
        this._scheduleReconnect();
    }
    
    _onError(event) {
        this._log('WebSocket error', event);
        
        // 调用回调
        if (this.options.onError) {
            this.options.onError(event);
        }
    }
    
    _handleChatMessage(message) {
        // 更新消息状态
        if (message.fromUserId === this.options.userId) {
            if (message.status === 'DELIVERED') {
                this._log('Message delivered', message);
            }
        }
    }
    
    _handleSystemMessage(message) {
        this._log('System message', message);
    }
    
    _handlePingMessage(message) {
        this._log('Ping response', message);
    }
    
    _startHeartbeat() {
        this._clearTimers();
        
        this.heartbeatTimer = setInterval(() => {
            this.sendHeartbeat();
        }, this.options.heartbeatInterval);
    }
    
    _scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        
        this.reconnectTimer = setTimeout(() => {
            this._log('Reconnecting...');
            this.connect();
        }, this.options.reconnectInterval);
    }
    
    _clearTimers() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
        
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }
    
    _generateId() {
        return 'msg_' + Date.now() + '_' + Math.floor(Math.random() * 1000000);
    }
    
    _log(...args) {
        if (this.options.debug) {
            console.log('[WebSocketClient]', ...args);
        }
    }
}

// 导出客户端
window.WebSocketClient = WebSocketClient;