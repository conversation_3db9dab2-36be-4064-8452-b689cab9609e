<template>
	<view class="tabbar-container">
		<view class="content" :style="{zIndex}">
			<view class="tabber_box">
				<view v-for="(item,index) in tabBars" :key="index"
					:style="{width:`${ratio}%`,display:'flex',justifyContent:'space-around',margin: '24rpx 0'}">
					<view class="tabber_item" :class="item.name == '了解更多' ? 'more':''" @click="onNav(item)">
						<image v-if="activePath === item.pagePath" :src="item.selectedIconPath"></image>
						<image v-else :src="item.iconPath"></image>

						<text
							:style="{color:activePath === item.pagePath ? item.selectedColor : item.color}">{{item.name}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "Tabbar",
		props: {
			currentPath: { // 当前页面路径
				type: String,
				default: '/pages/home/<USER>',
			},
			zIndex: { // 界面层级
				type: Number,
				default: 9999  // 提高默认z-index值
			}
		},
		data() {
			const color = '#999999';
			const selectedColor = '#ff8c00';
			return {
				activePath: '', // 当前激活路径
				ratio: 0,
				isLogin: false,
				tabBars: [{
						pagePath: "/pages/home/<USER>",
						iconPath: "/static/images/tabbar/home.png",
						selectedIconPath: "/static/images/tabbar/home_sel.png",
						name: "首页",
						color,
						selectedColor,
					},
					{
						pagePath: "/pages/expert/expert",
						iconPath: "/static/images/tabbar/talentLib.png",
						selectedIconPath: "/static/images/tabbar/talentLib_sel.png",
						name: "达人库",
						color,
						selectedColor,
					},
					{
						pagePath: "/pages/publish/publish",
						iconPath: "/static/images/tabbar/publish.png",
						selectedIconPath: "/static/images/tabbar/publish_sel.png",
						name: "发布",
						color,
						selectedColor,
					},
					{
						pagePath: "/pages/message/message",
						iconPath: "/static/images/tabbar/message.png",
						selectedIconPath: "/static/images/tabbar/message_sel.png",
						name: "消息",
						color,
						selectedColor,
					},
					{
						pagePath: "/pages/mine/mine",
						iconPath: "/static/images/tabbar/mine.png",
						selectedIconPath: "/static/images/tabbar/mine_sel.png",
						name: "我的",
						color,
						selectedColor,
					},
				]
			};
		},
		watch: {
			// 监听currentPath属性变化
			currentPath: {
				immediate: true,
				handler(newVal) {
					this.activePath = newVal;
				}
			}
		},
		mounted() {
			this.ratio = 100 / this.tabBars.length;
			// 初始化激活路径
			this.activePath = this.currentPath;
			// 注册全局事件，用于页面间通信更新tabbar状态
			uni.$on('updateTabbar', (path) => {
				this.activePath = path;
			});
		},
		beforeDestroy() {
			// 解除事件监听
			uni.$off('updateTabbar');
		},
		methods: {
			onNav(item) {
				console.log("导航到:", item.pagePath);
				if (this.activePath !== item.pagePath && !item.goApp) {
					// 更新当前激活路径
					this.activePath = item.pagePath;
					// 跳转到对应页面
					uni.switchTab({
						url: item.pagePath,
						success: () => {
							// 通知其他页面更新tabbar状态
							uni.$emit('updateTabbar', item.pagePath);
						}
					});
				} else if (item.goApp) {
					uni.navigateToMiniProgram({
						appId: item.appId,
						path: item.appPath ? item.appPath : "/pages/home/<USER>",
						envVersion: "release", // 打开正式版
						success(res) {
							// 打开成功
							console.log("打开成功", res);
						},
						fail: function(err) {
							console.log(err);
						},
					});
				}
			}
		}
	}
</script>

<style scoped>
	.tabbar-container {
		width: 100%;
		height: auto; /* 改为自适应高度 */
		background-color: #ffffff; /* 确保有背景色 */
	}

	.content {
		position: fixed;
		bottom: 0;
		width: 100%;
		z-index: 9999; /* 提高z-index确保显示在原生tabbar之上 */
	}

	.content .tabber_box {
		display: flex;
		flex-direction: row;
		align-items: center;
		background: white;
		padding-bottom: calc(env(safe-area-inset-bottom));
		box-shadow: 0 -2rpx 6rpx rgba(0,0,0,0.1); /* 添加阴影效果 */
		min-height: 100rpx; /* 确保最小高度 */
	}

	.content .tabber_box .tabber_item {
		display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 22rpx;
	}

	.content .tabber_box .tabber_item image {
		width: 48rpx;
		height: 48rpx;
	}

	.content .tabber_box .tabber_item text {
		margin-top: 10rpx;
		line-height: 22rpx;
	}
</style>