# 通用编译输出目录
bin-debug/
bin-release/
[Bb]in/
[Oo]bj/

# Java编译输出
/bin/
/build/
/target/
/out/

# 前端依赖与构建产物
xianshi-vue/node_modules/
xianshi-vue/dist/

# IDE配置文件
.idea/
*.iml
*.ipr
*.iws
.vscode/
.settings/

# 可执行文件
*.swf
*.air
*.ipa
*.apk

# 系统文件
.DS_Store
Thumbs.db

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 其他临时/缓存文件
*.tmp
*.temp

# 忽略根目录及各子项目的 Maven wrapper
**/.mvn/
**/mvnw
**/mvnw.cmd

# 忽略各子项目 target 目录
xianshi-business/target/
xianshi-app/target/
xianshi-admin/target/

# 忽略本地配置文件
xianshi-vue/vue.config.local.js

# 忽略 IDE 工作区配置
.idea/
xianshi-vue/.idea/

# 可选：忽略本地开发脚本
xianshi-vue/start-local.js

# xianshi-vue 项目相关忽略
xianshi-vue/node_modules/   # 前端依赖
xianshi-vue/dist/           # 前端构建产物
xianshi-vue/.idea/          # WebStorm/IDEA 配置
xianshi-vue/env-config.js   # 已废弃环境配置
xianshi-vue/vue.config.local.js # 已废弃本地配置
xianshi-vue/start-local.js  # 已废弃本地开发脚本

# xianshi_uniapp 项目相关忽略
xianshi_uniapp/node_modules/
xianshi_uniapp/unpackage/
xianshi_uniapp/.hbuilderx/

