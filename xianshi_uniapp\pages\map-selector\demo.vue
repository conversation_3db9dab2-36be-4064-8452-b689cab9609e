<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @tap="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="nav-title">地图选择器演示</view>
      <view class="nav-right"></view>
    </view>

    <!-- 演示内容 -->
    <view class="demo-content">
      <!-- 当前选择的位置 -->
      <view class="section">
        <view class="section-title">当前选择的位置</view>
        <view class="location-card" v-if="selectedLocation.name">
          <view class="location-info">
            <view class="location-name">{{ selectedLocation.name }}</view>
            <view class="location-address">{{ selectedLocation.address }}</view>
            <view class="location-coords">
              经度: {{ selectedLocation.longitude?.toFixed(6) || '未知' }} | 
              纬度: {{ selectedLocation.latitude?.toFixed(6) || '未知' }}
            </view>
          </view>
          <view class="location-icon">📍</view>
        </view>
        <view class="empty-state" v-else>
          <text>暂未选择位置</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="section">
        <view class="section-title">操作演示</view>
        
        <view class="button-group">
          <button class="demo-btn primary" @tap="openMapSelector">
            📍 打开地图选择器
          </button>
          
          <button class="demo-btn secondary" @tap="openMapSelectorWithParams">
            🎯 从指定位置开始选择
          </button>
          
          <button class="demo-btn outline" @tap="clearSelection">
            🗑️ 清除选择
          </button>
        </view>
      </view>

      <!-- 功能说明 -->
      <view class="section">
        <view class="section-title">功能说明</view>
        <view class="feature-list">
          <view class="feature-item">
            <view class="feature-icon">🗺️</view>
            <view class="feature-content">
              <view class="feature-name">交互式地图</view>
              <view class="feature-desc">支持拖拽、缩放等地图操作</view>
            </view>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">🔍</view>
            <view class="feature-content">
              <view class="feature-name">智能搜索</view>
              <view class="feature-desc">实时搜索建议，快速找到目标位置</view>
            </view>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">📍</view>
            <view class="feature-content">
              <view class="feature-name">附近地点</view>
              <view class="feature-desc">显示周边兴趣点，方便选择</view>
            </view>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">🎨</view>
            <view class="feature-content">
              <view class="feature-name">现代化UI</view>
              <view class="feature-desc">美观的界面设计和流畅的交互体验</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="section">
        <view class="section-title">使用说明</view>
        <view class="usage-steps">
          <view class="step-item">
            <view class="step-number">1</view>
            <view class="step-content">点击"打开地图选择器"按钮</view>
          </view>
          
          <view class="step-item">
            <view class="step-number">2</view>
            <view class="step-content">在地图上拖拽或搜索选择位置</view>
          </view>
          
          <view class="step-item">
            <view class="step-number">3</view>
            <view class="step-content">点击"确认选择"完成位置选择</view>
          </view>
          
          <view class="step-item">
            <view class="step-number">4</view>
            <view class="step-content">返回页面查看选择结果</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedLocation: {
        name: '',
        address: '',
        latitude: null,
        longitude: null
      }
    }
  },
  
  onShow() {
    // 获取从地图选择页面返回的位置数据
    const selectedLocation = uni.getStorageSync('selectedLocation');
    if (selectedLocation) {
      console.log('从地图选择页面获取到位置数据:', selectedLocation);
      
      // 更新位置信息
      this.selectedLocation = {
        name: selectedLocation.name || '',
        address: selectedLocation.address || '',
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude
      };
      
      // 清除存储的位置数据
      uni.removeStorageSync('selectedLocation');
      
      uni.showToast({
        title: '位置选择成功',
        icon: 'success'
      });
    }
  },
  
  methods: {
    // 打开地图选择器
    openMapSelector() {
      uni.navigateTo({
        url: '/pages/map-selector/map-selector',
        success: () => {
          console.log('跳转到地图选择页面成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 从指定位置开始选择
    openMapSelectorWithParams() {
      // 天安门的坐标
      const longitude = 116.397428;
      const latitude = 39.90923;
      const title = encodeURIComponent('天安门');
      const address = encodeURIComponent('北京市东城区');
      
      uni.navigateTo({
        url: `/pages/map-selector/map-selector?longitude=${longitude}&latitude=${latitude}&title=${title}&address=${address}`,
        success: () => {
          console.log('跳转到地图选择页面成功（带参数）');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 清除选择
    clearSelection() {
      this.selectedLocation = {
        name: '',
        address: '',
        latitude: null,
        longitude: null
      };
      
      uni.showToast({
        title: '已清除选择',
        icon: 'success'
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 44rpx);
  z-index: 1000;
  border-bottom: 1rpx solid #eee;
}

.nav-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.nav-right {
  width: 60rpx;
}

/* 演示内容 */
.demo-content {
  padding: 30rpx;
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx));
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

/* 位置卡片 */
.location-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.location-info {
  flex: 1;
}

.location-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.location-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.location-coords {
  font-size: 22rpx;
  color: #999;
}

.location-icon {
  font-size: 48rpx;
  color: #ff8c00;
}

.empty-state {
  background: #fff;
  border-radius: 16rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.demo-btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.demo-btn.primary {
  background: #ff8c00;
  color: #fff;
}

.demo-btn.secondary {
  background: #007aff;
  color: #fff;
}

.demo-btn.outline {
  background: #fff;
  color: #666;
  border: 2rpx solid #ddd;
}

.demo-btn:active {
  opacity: 0.8;
}

/* 功能列表 */
.feature-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
}

/* 使用步骤 */
.usage-steps {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.step-item:last-child {
  border-bottom: none;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background: #ff8c00;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 24rpx;
}

.step-content {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
</style>
