package com.play.xianshibusiness.pojo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.play.xianshibusiness.pojo.basePojo.BaseObjPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * (BOrderType)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-03 22:00:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("b_order_type")
@ApiModel("订单分类表")
public class BOrderType extends BaseObjPo {

    //父类ID
    @ApiModelProperty(value = "父类ID")
    private String parentId;
    //分类名称
    @ApiModelProperty(value = "分类名称")
    private String name;
    //编码
    @ApiModelProperty(value = "编码")
    private String code;
    //所需金币
    @ApiModelProperty(value = "所需金币")
    private BigDecimal needGold;


}

