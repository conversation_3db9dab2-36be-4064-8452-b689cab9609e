<template>
  <view class="container">
   

    <!-- 用户信息横幅 -->
    <view class="user-banner">
      <view class="user-info">
        <image class="user-avatar" src="/static/images/default-avatar.png" />
        <view class="user-details">
          <text class="user-name">Jwen学长</text>
          <text class="user-subtitle">开通会员享更多权益</text>
        </view>
      </view>
      <view class="vip-bg-icon">V</view>
    </view>

    <!-- 会员权益 -->
    <view class="benefits-section">
      <view class="benefit-item">
        <view class="benefit-icon">🎁</view>
        <text class="benefit-text">会员福利</text>
      </view>
      <view class="benefit-item">
        <view class="benefit-icon">🛡️</view>
        <text class="benefit-text">免担保金</text>
      </view>
      <view class="benefit-item">
        <view class="benefit-icon">∞</view>
        <text class="benefit-text">每日可签到</text>
      </view>
      <view class="benefit-item">
        <view class="benefit-icon">💎</view>
        <text class="benefit-text">会员标识</text>
      </view>
    </view>

    <!-- 会员价格选择 -->
    <view class="pricing-section">
      <view 
        v-for="(plan, index) in membershipPlans" 
        :key="index"
        :class="['pricing-card', {'selected': selectedPlan === index}]"
        @click="selectPlan(index)"
      >
        <view class="discount-tag" v-if="plan.discount">{{ plan.discount }}</view>
        <view class="plan-duration">{{ plan.duration }}</view>
        <view class="plan-price">¥{{ plan.price }}</view>
        <view class="plan-original">原价{{ plan.originalPrice }}</view>
      </view>
    </view>

    <!-- 服务权益对比 -->
    <view class="privileges-section">
      <view class="section-title">服务权益</view>
      <view class="privileges-table">
        <view class="table-header">
          <view class="header-feature"></view>
          <view class="header-member">
            <text class="header-icon">✓</text>
            <text class="header-text">会员</text>
          </view>
          <view class="header-nonmember">
            <text class="header-icon">✗</text>
            <text class="header-text">非会员</text>
          </view>
        </view>
        <view class="table-row" v-for="(privilege, index) in privileges" :key="index">
          <view class="row-feature">{{ privilege.feature }}</view>
          <view class="row-member">
            <text v-if="privilege.member === true" class="check-icon">✓</text>
            <text v-else-if="privilege.member === false" class="cross-icon">✗</text>
            <text v-else class="coin-text">{{ privilege.member }}</text>
          </view>
          <view class="row-nonmember">
            <text v-if="privilege.nonMember === true" class="check-icon">✓</text>
            <text v-else-if="privilege.nonMember === false" class="cross-icon">✗</text>
            <text v-else class="coin-text">{{ privilege.nonMember }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="reminder-section">
      <view class="reminder-title">温馨提示:</view>
      <view class="reminder-list">
        <view class="reminder-item">
          <text class="item-number">1.</text>
          <text class="item-text">模卡会员为本平台向您提供的会员增值服务，充值成功后不支持转让，不支持退款，充值前请慎重选择。</text>
        </view>
        <view class="reminder-item">
          <text class="item-number">2.</text>
          <text class="item-text">模卡不鼓励未成年人使用充值服务。</text>
        </view>
        <view class="reminder-item">
          <text class="item-number">3.</text>
          <text class="item-text">充值即代表同意平台<text class="agreement-link">《用户充值协议》</text></text>
        </view>
      </view>
    </view>

    <!-- 开通按钮 -->
    <view class="action-section">
      <button class="subscribe-btn" @click="subscribeMembership">
        立即开通{{ selectedPlanDuration }}会员
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedPlan: 0, // 默认选择第一个套餐
      membershipPlans: [
        {
          duration: '1个月',
          price: '98',
          originalPrice: '98',
          discount: ''
        },
        {
          duration: '3个月',
          price: '198',
          originalPrice: '330',
          discount: '送30全币'
        },
        {
          duration: '1年',
          price: '588',
          originalPrice: '1300',
          discount: '送150全币'
        }
      ],
      privileges: [
        {
          feature: '会员醒目标识',
          member: true,
          nonMember: false
        },
        {
          feature: '免信用担保金',
          member: true,
          nonMember: false
        },
        {
          feature: '发起沟通消耗',
          member: '3金币',
          nonMember: false
        },
        {
          feature: '约单报名消耗',
          member: '3金币',
          nonMember: '5金币'
        },
        {
          feature: '极速联系消耗',
          member: '58金币',
          nonMember: '88金币'
        },
        {
          feature: '签到次数上限',
          member: '每日可签到(实名后)',
          nonMember: '有上限'
        }
      ]
    };
  },
  computed: {
    selectedPlanDuration() {
      return this.membershipPlans[this.selectedPlan].duration;
    }
  },
  methods: {
    // 选择套餐
    selectPlan(index) {
      this.selectedPlan = index;
    },

    // 开通会员
    subscribeMembership() {
      const plan = this.membershipPlans[this.selectedPlan];
      
      uni.showModal({
        title: '开通会员',
        content: `确认开通${plan.duration}会员，费用¥${plan.price}？`,
        confirmText: '确认开通',
        success: (res) => {
          if (res.confirm) {
            this.processMembershipPayment(plan);
          }
        }
      });
    },

    // 处理会员支付
    processMembershipPayment(plan) {
      uni.showLoading({
        title: '处理中...',
        mask: true
      });

      // 模拟支付过程
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '会员开通成功',
          icon: 'success',
          success: () => {
            // 更新用户会员状态
            uni.setStorageSync('isVipMember', true);
            uni.setStorageSync('vipExpireDate', this.getExpireDate(plan.duration));
            
            // 返回上一页
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        });
      }, 2000);
    },

    // 计算到期时间
    getExpireDate(duration) {
      const now = new Date();
      let expireDate;
      
      if (duration === '1个月') {
        expireDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      } else if (duration === '3个月') {
        expireDate = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
      } else if (duration === '1年') {
        expireDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
      }
      
      return expireDate.toISOString();
    },

    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: () => {
          uni.navigateTo({
            url: '/pages/mine/mine'
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #333;
  color: #fff;
}

.nav-back {
  width: 60rpx;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 15rpx;
}

.nav-icon {
  font-size: 24rpx;
  width: 30rpx;
  text-align: center;
}

/* 用户信息横幅 */
.user-banner {
  margin: 20rpx 30rpx;
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  z-index: 2;
  position: relative;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.user-subtitle {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.vip-bg-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 120rpx;
  color: rgba(255, 215, 0, 0.1);
  font-weight: bold;
}

/* 会员权益 */
.benefits-section {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  background-color: #fff;
  margin: 0 30rpx 20rpx;
  border-radius: 16rpx;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.benefit-icon {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #fff;
}

.benefit-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 价格选择 */
.pricing-section {
  display: flex;
  gap: 20rpx;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.pricing-card {
  flex: 1;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.pricing-card.selected {
  border: 2rpx solid #ffd700;
  box-shadow: 0 6rpx 20rpx rgba(255, 215, 0, 0.2);
}

.discount-tag {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.plan-duration {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.plan-price {
  font-size: 36rpx;
  color: #ffd700;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.plan-original {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

/* 服务权益对比 */
.privileges-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.privileges-table {
  border: 1rpx solid #eee;
  border-radius: 12rpx;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  background-color: #f8f8f8;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.header-feature {
  display: flex;
  align-items: center;
}

.header-member,
.header-nonmember {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.header-icon {
  font-size: 24rpx;
  font-weight: bold;
}

.header-member .header-icon {
  color: #ffd700;
}

.header-nonmember .header-icon {
  color: #999;
}

.header-text {
  font-size: 24rpx;
  font-weight: 500;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.table-row:last-child {
  border-bottom: none;
}

.row-feature {
  font-size: 24rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.row-member,
.row-nonmember {
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: #52c41a;
  font-weight: bold;
}

.cross-icon {
  color: #ff4757;
  font-weight: bold;
}

.coin-text {
  font-size: 22rpx;
  color: #ffd700;
  font-weight: 500;
}

/* 温馨提示 */
.reminder-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.reminder-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.reminder-list {
  margin-bottom: 20rpx;
}

.reminder-item {
  display: flex;
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.item-number {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
  flex-shrink: 0;
  width: 30rpx;
}

.item-text {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}

.agreement-link {
  color: #1677ff;
  text-decoration: underline;
}

/* 开通按钮 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}

.subscribe-btn {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  height: 88rpx;
  line-height: 88rpx;
  width: 100%;
  border: none;
}

.subscribe-btn:active {
  transform: scale(0.98);
}
</style>