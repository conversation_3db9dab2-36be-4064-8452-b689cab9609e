<script>
import commonApi from "@/api/src/common";
import { fixStorageFormat } from "./utils/fix-array-type.js";
import { url_all } from "./api/index.js";
export default {
	globalData: {
		baseUrl: url_all['DEV'],
		commonApi,
		userInfo: null,
		publishedOrders: null, // 添加发布订单全局存储
		currentUserId: null, // 当前用户ID，从API获取
		userRole: '普通用户', // 默认角色
		isVerified: false,  // 是否已认证
		hasExpertRole: false, // 是否拥有达人身份
		memberInfo: null // 会员信息，从API获取
	},
	data() {
		return {
		};
	},
	onLaunch: function () {
		// 修复数据格式问题
		try {
			// 强制重置数据，使用新的数据格式
			const forceReset = true; // 设置为true强制重置数据
			const success = fixStorageFormat(this, forceReset);
			if (!success && !forceReset) {
				// 如果修复失败，调用初始化示例订单
				this.initSampleOrders();
			}
		} catch (e) {
			console.error('修复数据格式失败，将重新初始化', e);
			this.initSampleOrders();
		}

		// 展示本地存储能力
		var logs = uni.getStorageSync('logs') || []
		logs.unshift(Date.now())
		uni.setStorageSync('logs', logs)

		// 初始化用户信息
		this.initUserInfo();

		// 登录
		uni.login({
			success: res => {
				// 发送 res.code 到后台换取 openId, sessionKey, unionId
				this.getUserInfo();
			}
		})

		// 加载已发布的订单到全局变量
		this.loadPublishedOrders();

		console.log('App Launch')
	},
	onShow: function () {
		console.log('App Show')
		// 重新加载已发布的订单到全局变量
		this.loadPublishedOrders();
		// 重新获取用户信息
		this.getUserInfo();
	},
	onHide: function () {
		console.log('App Hide')
	},
	methods: {
		// 初始化用户信息
		initUserInfo() {
			// 从本地存储加载用户信息
			let storedUserId = uni.getStorageSync('currentUserId');
			const storedUserInfo = uni.getStorageSync('memberInfo');
			const isVerified = uni.getStorageSync('isVerified') || false;
			const hasExpertRole = uni.getStorageSync('hasExpertRole') || false;
			const userRole = uni.getStorageSync('userRole') || '普通用户';
			
			// 如果没有找到 currentUserId，尝试从登录时保存的 userInfo 中获取
			if (!storedUserId) {
				const loginUserInfo = uni.getStorageSync('userInfo');
				if (loginUserInfo) {
					try {
						const userInfoObj = typeof loginUserInfo === 'string' ? JSON.parse(loginUserInfo) : loginUserInfo;
						storedUserId = userInfoObj.id || userInfoObj.memberId || userInfoObj.userId;
						console.log('从登录用户信息中提取用户ID:', storedUserId);
					} catch (e) {
						console.error('解析登录用户信息失败:', e);
					}
				}
			}

			if (storedUserId) {
				this.globalData.currentUserId = storedUserId;
				// 同时保存到 currentUserId 存储键，确保一致性
				uni.setStorageSync('currentUserId', storedUserId);
			}
			if (storedUserInfo) {
				try {
					this.globalData.memberInfo = typeof storedUserInfo === 'string' ? JSON.parse(storedUserInfo) : storedUserInfo;
				} catch (e) {
					console.error('解析用户信息失败:', e);
				}
			}
			
			this.globalData.isVerified = isVerified;
			this.globalData.hasExpertRole = hasExpertRole;
			this.globalData.userRole = userRole;

			console.log('初始化用户信息:', {
				currentUserId: this.globalData.currentUserId,
				userRole,
				isVerified,
				hasExpertRole
			});
		},

		// 从API获取用户信息
		async getUserInfo() {
			try {
				console.log('开始获取用户信息...');
				
				// 调用获取当前会员信息API
				const res = await this.$requestHttp.get(this.globalData.commonApi.getCurrentMemberInfo, {});
				
				if (res.code === 200 && res.data) {
					const memberInfo = res.data;
					
					// 更新全局数据
					this.globalData.currentUserId = memberInfo.id || memberInfo.memberId;
					this.globalData.memberInfo = memberInfo;
					this.globalData.userInfo = memberInfo;
					
					// 根据会员信息设置角色和认证状态
					this.globalData.isVerified = memberInfo.isVerified || (memberInfo.realNameStatus === 1) || false;
					this.globalData.hasExpertRole = memberInfo.hasExpertRole || memberInfo.isTalent || false;
					
					// 设置用户角色 - 优先使用后端返回的currentRoleName
					if (memberInfo.currentRoleName) {
						this.globalData.userRole = memberInfo.currentRoleName;
					} else if (memberInfo.roleType) {
						switch(memberInfo.roleType) {
							case 1:
							case '1':
								this.globalData.userRole = '普通用户';
								break;
							case 2:
							case '2':
								this.globalData.userRole = '闲时达人';
								break;
							case 3:
							case '3':
								this.globalData.userRole = '闲时公会';
								break;
							default:
								this.globalData.userRole = '普通用户';
						}
					} else if (this.globalData.hasExpertRole) {
						this.globalData.userRole = '闲时达人';
					} else {
						this.globalData.userRole = '普通用户';
					}
					
					// 保存角色相关信息
					this.globalData.currentRoleId = memberInfo.currentRoleId;
					this.globalData.roleType = memberInfo.roleType;
					this.globalData.hasGuildRole = memberInfo.hasGuildRole || false;
					
					// 保存到本地存储
					uni.setStorageSync('currentUserId', this.globalData.currentUserId);
					uni.setStorageSync('memberInfo', JSON.stringify(memberInfo));
					uni.setStorageSync('userInfo', JSON.stringify({
						...memberInfo,
						currentRole: this.globalData.userRole
					}));
					uni.setStorageSync('isVerified', this.globalData.isVerified);
					uni.setStorageSync('hasExpertRole', this.globalData.hasExpertRole);
					uni.setStorageSync('hasGuildRole', this.globalData.hasGuildRole);
					uni.setStorageSync('userRole', this.globalData.userRole);
					uni.setStorageSync('currentRoleId', this.globalData.currentRoleId);
					uni.setStorageSync('roleType', this.globalData.roleType);
					
					console.log('用户信息获取成功:', {
						currentUserId: this.globalData.currentUserId,
						userRole: this.globalData.userRole,
						isVerified: this.globalData.isVerified,
						hasExpertRole: this.globalData.hasExpertRole
					});
				} else {
					console.error('获取用户信息失败:', res);
				}
			} catch (error) {
				console.error('获取用户信息异常:', error);
				// 如果API调用失败，使用本地存储的信息
				this.initUserInfo();
			}
		},

		// 清除用户登录状态
		logout() {
			// 清除本地存储
			uni.removeStorageSync('token');
			uni.removeStorageSync('userInfo');
			uni.removeStorageSync('currentUserId');
			uni.removeStorageSync('memberInfo');
			uni.removeStorageSync('isVerified');
			uni.removeStorageSync('hasExpertRole');
			uni.removeStorageSync('hasGuildRole');
			uni.removeStorageSync('userRole');
			uni.removeStorageSync('currentRoleId');
			uni.removeStorageSync('roleType');
			
			// 重置全局数据
			this.globalData.currentUserId = null;
			this.globalData.userInfo = null;
			this.globalData.memberInfo = null;
			this.globalData.isVerified = false;
			this.globalData.hasExpertRole = false;
			this.globalData.hasGuildRole = false;
			this.globalData.userRole = '普通用户';
			this.globalData.currentRoleId = null;
			this.globalData.roleType = null;
			
			console.log('用户已退出登录');
			
			// 跳转到登录页面
			uni.navigateTo({
				url: '/pages/login/login'
			});
		},
		// 加载已发布的订单
		loadPublishedOrders() {
			try {
				// 获取当前用户ID
				const currentUserId = this.globalData.currentUserId;

				// 使用统一的存储键名
			const storageKey = 'publishedOrders';

				console.log(`开始加载 ${storageKey} 数据...`);

				// 从本地存储获取已发布的订单
				let publishedOrders = uni.getStorageSync(storageKey);

				// 确保publishedOrders是数组
				if (!publishedOrders) {
					publishedOrders = [];
				} else if (typeof publishedOrders === 'string') {
					try {
						publishedOrders = JSON.parse(publishedOrders);
						console.log(`app.js解析后的${storageKey}数量:`, publishedOrders.length);
					} catch (e) {
						console.error(`app.js解析${storageKey}失败:`, e);
						publishedOrders = [];
					}
				}

				// 确保是数组类型
				if (!Array.isArray(publishedOrders)) {
					publishedOrders = [];
				}

				// 使用nextTick延迟执行，避免UI卡顿
					// 确保每个订单都有creatorId
				const processedOrders = publishedOrders.map(order => {
					if (!order.creatorId) {
						order.creatorId = this.globalData.currentUserId || 'user_123';
					}
					return order;
				});

					// 更新全局数据
					this.globalData.publishedOrders = processedOrders;

					// 异步保存回存储，避免主线程阻塞
					setTimeout(() => {
						try {
							uni.setStorageSync(storageKey, JSON.stringify(processedOrders));
							console.log(`已保存 ${storageKey} 数据，共 ${processedOrders.length} 条`);
						} catch (error) {
							console.error(`保存 ${storageKey} 数据失败:`, error);
						}
					}, 50);
			} catch (error) {
				console.error('loadPublishedOrders执行失败:', error);
				// 恢复到空数组，确保应用可以继续运行
				this.globalData.publishedOrders = [];
			}
		},

		// 初始化样例订单 - 已移除假数据
		initSampleOrders() {
			// 移除所有假数据，仅初始化空数组
			this.globalData.publishedOrders = [];
			uni.setStorageSync('publishedOrders', JSON.stringify([]));
		},

		// 专家申请订单存储键获取函数
		getExpertAppliedOrdersKey() {
			// 使用当前用户ID获取专用存储键
			const userId = this.globalData.currentUserId;
			// 确保每个达人有自己独立的存储键，而不是共用
			return `expertAppliedOrders_${userId}`;
		},

		// 专家工作台订单存储键获取函数
		getExpertOrdersKey() {
			// 使用统一的存储键
			return 'expertOrders';
		},

		// 通知存储键获取函数
		getNotificationsKey(userId) {
			// 确保使用指定的用户ID
			const id = userId || this.globalData.currentUserId;
			// 使用统一的存储键格式
			return `notifications_${id}`;
		},

		// 未读通知计数存储键获取函数
		getUnreadNotificationsKey(userId) {
			// 确保使用指定的用户ID
			const id = userId || this.globalData.currentUserId;
			// 使用统一的存储键格式
			return `unreadNotifications_${id}`;
		},

		// 获取专家个人申请订单存储键
		getExpertPersonalAppliedOrdersKey(expertId) {
			// 确保使用指定的专家ID
			const id = expertId || this.globalData.currentUserId;
			// 使用统一的存储键格式
			return `expertAppliedOrders_${id}`;
		},
	}
}
</script>

<style>
/*每个页面公共css */
</style>
