import Cookies from 'js-cookie';

const TokenKey = 'Admin-Token';
const AvatarKey = 'Admin-Avatar';
const NameKey = 'Admin-Name';

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token);
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}

// 头像相关操作
export function getAvatar() {
  return localStorage.getItem(AvatarKey);
}

export function setAvatar(avatar) {
  return localStorage.setItem(AvatarKey, avatar);
}

export function removeAvatar() {
  return localStorage.removeItem(AvatarKey);
}

// 用户名相关操作
export function getName() {
  return localStorage.getItem(NameKey);
}

export function setName(name) {
  return localStorage.setItem(NameKey, name);
}

export function removeName() {
  return localStorage.removeItem(NameKey);
} 