package com.play.xianshibusiness.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单详情视图对象
 */
@Data
@ApiModel(value = "订单详情视图对象")
public class OrderDetailVO extends OrderVO {
    
    @ApiModelProperty(value = "订单描述")
    private String description;
    
    @ApiModelProperty(value = "经度")
    private String longitude;
    
    @ApiModelProperty(value = "纬度")
    private String latitude;
    
    @ApiModelProperty(value = "审核结果：0-未审核，1-通过，2-拒绝")
    private Integer auditResult;
    
    @ApiModelProperty(value = "审核意见")
    private String auditComment;
    
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;
    
    @ApiModelProperty(value = "审核人ID")
    private String auditorId;
    
    @ApiModelProperty(value = "审核人姓名")
    private String auditorName;
    
    @ApiModelProperty(value = "取消原因")
    private String cancelReason;
    
    @ApiModelProperty(value = "取消时间")
    private LocalDateTime cancelTime;
    
    @ApiModelProperty(value = "报名达人列表")
    private List<OrderEnrollVO> enrollList;
    
    @ApiModelProperty(value = "已选达人列表")
    private List<OrderEnrollVO> selectedList;
    
    @ApiModelProperty(value = "报名达人列表（与enrollList内容相同，用于与前端兼容）")
    private List<OrderEnrollVO> appliedExperts;
    
    @ApiModelProperty(value = "金币消耗")
    private Integer goldCost;
    
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    
    @ApiModelProperty(value = "会员昵称")
    private String memberNickname;
    
    @ApiModelProperty(value = "会员头像")
    private String memberAvatar;
    
    @ApiModelProperty(value = "活动类型名称")
    private String activityTypeName;
    
    @ApiModelProperty(value = "报名详情列表")
    private List<OrderEnrollDetailVO> enrollDetails;
}
