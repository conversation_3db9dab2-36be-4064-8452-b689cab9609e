<template>
  <view class="container">
    <!-- 订单统计卡片 -->
    <view class="stats-card">
      <view class="stat-item">
        <text class="stat-value">{{ orderStats.total }}</text>
        <text class="stat-label">总订单</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ orderStats.pending }}</text>
        <text class="stat-label">待处理</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ orderStats.inProgress }}</text>
        <text class="stat-label">进行中</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ orderStats.completed }}</text>
        <text class="stat-label">已完成</text>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab', { active: currentTab === index }]"
        @tap="switchTab(index)"
      >
        {{ tab }}
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list">
      <view 
        class="order-item" 
        v-for="(order, index) in filteredOrders" 
        :key="index"
        @tap="viewOrderDetail(order.id)"
      >
        <view class="order-header">
          <view class="order-title">
            <text class="order-project">{{ order.projectName }}</text>
            <text :class="['order-status', 'status-' + order.status]">{{ getStatusText(order.status) }}</text>
          </view>
          <text class="order-time">{{ order.createTime }}</text>
        </view>

        <view class="order-info">
          <view class="info-row">
            <text class="info-label">订单号：</text>
            <text class="info-value">{{ order.orderNo }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">服务成员：</text>
            <text class="info-value">{{ order.memberName }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">服务时间：</text>
            <text class="info-value">{{ order.serviceTime }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">服务地点：</text>
            <text class="info-value">{{ order.location }}</text>
          </view>
        </view>

        <view class="order-footer">
          <view class="price-info">
            <text class="price">¥{{ order.price }}</text>
            <text class="commission" v-if="order.commission">
              (公会分成: ¥{{ order.commission }})
            </text>
          </view>
          
          <view class="action-buttons" v-if="order.status === 'pending'">
            <button class="btn btn-assign" @tap.stop="assignOrder(order.id)">分配订单</button>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredOrders.length === 0">
        <image class="empty-icon" src="/static/images/empty-orders.png"></image>
        <text class="empty-text">暂无订单数据</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 订单统计
      orderStats: {
        total: 48,
        pending: 5,
        inProgress: 12,
        completed: 31
      },
      
      // 标签页
      tabs: ['全部订单', '待处理', '进行中', '已完成', '已取消'],
      currentTab: 0,
      
      // 订单数据
      orders: [
        {
          id: '1001',
          orderNo: 'GLD20230728001',
          projectName: 'KTV欢唱',
          status: 'pending',
          createTime: '2023-07-28 14:30',
          memberName: '张三',
          serviceTime: '2023-07-29 19:00-21:00',
          location: '成都市武侯区xxx路xx号',
          price: 400,
          commission: 80
        },
        {
          id: '1002',
          orderNo: 'GLD20230727002',
          projectName: '篮球陪练',
          status: 'inProgress',
          createTime: '2023-07-27 10:15',
          memberName: '李四',
          serviceTime: '2023-07-28 16:00-18:00',
          location: '成都市锦江区xxx公园',
          price: 300,
          commission: 60
        },
        {
          id: '1003',
          orderNo: 'GLD20230726003',
          projectName: '桌游聚会',
          status: 'completed',
          createTime: '2023-07-26 18:45',
          memberName: '王五',
          serviceTime: '2023-07-27 19:00-22:00',
          location: '成都市青羊区xxx咖啡厅',
          price: 250,
          commission: 50
        }
      ]
    };
  },
  computed: {
    filteredOrders() {
      if (this.currentTab === 0) return this.orders;
      
      const statusMap = ['all', 'pending', 'inProgress', 'completed', 'cancelled'];
      const status = statusMap[this.currentTab];
      
      if (status === 'all') return this.orders;
      return this.orders.filter(order => order.status === status);
    }
  },
  methods: {
    switchTab(index) {
      this.currentTab = index;
    },
    
    getStatusText(status) {
      const statusMap = {
        'pending': '待处理',
        'inProgress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    
    viewOrderDetail(orderId) {
      uni.navigateTo({
        url: `/pages/order-detail/order-detail?id=${orderId}&type=guild`
      });
    },
    
    assignOrder(orderId) {
      uni.showActionSheet({
        itemList: ['分配给成员', '公开接单', '取消订单'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 分配给成员
            uni.navigateTo({
              url: `/pages/guild-orders/assign-order?id=${orderId}`
            });
          } else if (res.tapIndex === 1) {
            // 公开接单
            uni.showModal({
              title: '确认操作',
              content: '是否将此订单设为公开接单？',
              success: (res) => {
                if (res.confirm) {
                  uni.showToast({
                    title: '操作成功',
                    icon: 'success'
                  });
                }
              }
            });
          } else {
            // 取消订单
            uni.showModal({
              title: '确认操作',
              content: '是否取消此订单？',
              success: (res) => {
                if (res.confirm) {
                  uni.showToast({
                    title: '订单已取消',
                    icon: 'success'
                  });
                }
              }
            });
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}

/* 订单统计卡片 */
.stats-card {
  display: flex;
  background-color: #ffffff;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.stat-item:not(:last-child):after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background-color: #eee;
}

.stat-value {
  font-size: 36rpx;
  color: #ff8c00;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background-color: #ffffff;
  padding: 0 20rpx;
  overflow-x: auto;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tab {
  display: inline-block;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #ff8c00;
  font-weight: bold;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff8c00;
  border-radius: 2rpx;
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.order-title {
  display: flex;
  align-items: center;
}

.order-project {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.order-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status-pending {
  background-color: rgba(255, 140, 0, 0.1);
  color: #ff8c00;
}

.status-inProgress {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.status-completed {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.status-cancelled {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-info {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  margin-bottom: 8rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 36rpx;
  color: #ff8c00;
  font-weight: bold;
}

.commission {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.action-buttons {
  display: flex;
}

.btn {
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-left: 16rpx;
}

.btn-assign {
  color: #ffffff;
  background-color: #ff8c00;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 