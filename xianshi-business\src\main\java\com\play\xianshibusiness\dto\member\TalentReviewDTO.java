package com.play.xianshibusiness.dto.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * 达人审核DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "达人审核DTO")
public class TalentReviewDTO {
    
    @NotEmpty(message = "会员ID不能为空")
    @ApiModelProperty(value = "会员ID", required = true)
    private String memberId;
    
    @ApiModelProperty(value = "是否通过", required = true)
    private boolean passed;
    
    @ApiModelProperty(value = "拒绝原因（如果不通过）")
    private String reason;
} 