/* eslint-disable */
import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
// 引入JS样式文件而不是SCSS
import applyStyles from './assets/css/index.js';
import './permission'; // 权限控制

Vue.config.productionTip = false;

Vue.use(ElementUI, {
  size: 'medium', // 设置组件默认尺寸
});

// 应用样式
applyStyles();

// 确保菜单默认展开
store.dispatch('app/toggleSideBar', true);

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app'); 